<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.FlightplanMapper">

    <resultMap type="Flightplan" id="FlightplanResult">
        <result property="flightplanId" column="flightplan_id"/>
        <result property="companyCode" column="company_code"/>
        <result property="flightType" column="flight_type"/>
        <result property="flightPurpose" column="flight_purpose"/>
        <result property="routeType" column="route_type"/>
        <result property="flightStatus" column="flight_status"/>
        <result property="flyStatus" column="fly_status"/>
        <result property="taskProgress" column="task_progress"/>
        <result property="flightDate" column="flight_date"/>
        <result property="flightNo" column="flight_no"/>
        <result property="departAirportCode" column="depart_airport_code"/>
        <result property="departCity" column="depart_city"/>
        <result property="arriveAirportCode" column="arrive_airport_code"/>
        <result property="arriveCity" column="arrive_city"/>
        <result property="createBy" column="create_By"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="callSign" column="call_sign"/>
        <result property="runNo" column="run_no"/>
        <result property="isSend" column="is_send"/>
        <result property="alternateAirportCode" column="alternate_airport_code"/>
        <result property="alternateCity" column="alternate_city"/>
        <result property="generalleaderUserId" column="generalleader_user_id"/>
        <result property="leaderUserId" column="leader_user_id"/>
        <result property="estimatedTotalSailingTime" column="estimated_total_sailing_time"/>
        <result property="dperationType" column="dperation_type"/>
        <result property="dperationStandard" column="dperation_standard"/>
        <result property="preApprovalStatus" column="pre_approval_status"/>
        <result property="preApprovalUserId" column="pre_approval_user_id"/>
        <result property="preApprovalUserName" column="pre_approval_user_name"/>
        <result property="approvalComments" column="approval_comments"/>
        <result property="planDepartTime" column="plan_depart_time"/>
        <result property="planArriveTime" column="plan_arrive_time"/>
        <result property="captainUserId" column="captain_user_id"/>
        <result property="copilotUserId" column="copilot_user_id"/>
        <result property="pilotType" column="pilot_type"/>
        <result property="copilotType" column="copilot_type"/>
        <result property="maintenanceId" column="maintenance_id"/>
        <result property="safetyOfficerId" column="safety_officer_id"/>
        <result property="ocUserId" column="oc_user_id"/>
        <result property="mechanicId" column="mechanic_id"/>
        <result property="mechanicMasterId" column="mechanic_master_Id"/>
        <result property="organizationId" column="organization_id"/>
        <result property="inspectorId" column="inspector_id"/>
        <result property="conductorId" column="conductor_id"/>
        <result property="dutyManagerId" column="duty_manager_id"/>
        <result property="aircraftId" column="aircraft_id"/>
        <result property="aircraftTailNo" column="aircraft_tail_no"/>
        <result property="flightFrequency" column="flight_frequency"/>
        <result property="slideStartTime" column="slide_start_time"/>
        <result property="flyStartTime" column="fly_start_time"/>
        <result property="flyEndTime" column="fly_end_time"/>
        <result property="slideEndTime" column="slide_end_time"/>
        <result property="flyTime" column="fly_time"/>
        <result property="slideTime" column="slide_time"/>
        <result property="nightFlyTime" column="night_fly_time"/>
        <result property="fuelExpend" column="fuel_expend"/>
        <result property="passengerNumber" column="passenger_number"/>
        <result property="secondSlideStartTime" column="second_slide_start_time"/>
        <result property="secondFlyStartTime" column="second_fly_start_time"/>
        <result property="secondFlyEndTime" column="second_fly_end_time"/>
        <result property="secondSlideEndTime" column="second_slide_end_time"/>
        <result property="secondFlyTime" column="second_fly_time"/>
        <result property="secondSlideTime" column="second_slide_time"/>
        <result property="secondNightFlyTime" column="second_night_fly_time"/>
        <result property="secondFuelExpend" column="second_fuel_expend"/>
        <result property="secondPassengerNumber" column="second_passenger_number"/>
        <result property="flightDelay" column="flight_delay"/>
        <result property="delayReason" column="delay_reason"/>
        <result property="secondFlightDelay" column="second_flight_delay"/>
        <result property="secondDelayReason" column="second_delay_reason"/>
        <result property="flightCancelReason" column="flight_cancel_reason"/>
        <result property="aircraftStyle" column="aircraft_style"/>
        <result property="route" column="route"/>
        <result property="businessHours" column="business_hours"/>
    </resultMap>

    <resultMap id="FlightplanAircraftResult" type="Flightplan" extends="FlightplanResult">
        <collection property="aircraftList" notNullColumn="sub_aircraft_id" javaType="java.util.List"
                    resultMap="AircraftResult"/>
    </resultMap>

    <resultMap type="Aircraft" id="AircraftResult">
        <result property="aircraftId" column="sub_aircraft_id"/>
        <result property="aircraftStyle" column="sub_aircraft_style"/>
        <result property="aircraftTailNo" column="sub_aircraft_tail_no"/>
        <result property="aircraftInfo" column="sub_aircraft_info"/>
        <result property="aircraftCompany" column="sub_aircraft_company"/>
        <result property="aircraftSeat" column="sub_aircraft_seat"/>
        <result property="aircraftLength" column="sub_aircraft_length"/>
        <result property="aircraftHeight" column="sub_aircraft_height"/>
        <result property="aircraftWidth" column="sub_aircraft_width"/>
        <result property="aircraftMfRange" column="sub_aircraft_mf_range"/>
    </resultMap>

    <resultMap type="PilotVO" id="pilotResult">
        <result property="userId" column="user_id"/>
        <result property="userName" column="user_name"/>
        <result property="flyTime" column="fly_time"/>
        <result property="flightFrequency" column="flight_frequency"/>
    </resultMap>

    <sql id="selectFlightplanVo">
        select flightplan_id,
               company_code,
               flight_type,
               flight_purpose,
               route_type,
               flight_status,
               fly_status,
               task_progress,
               flight_date,
               flight_no,
               depart_airport_code,
               depart_city,
               arrive_airport_code,
               arrive_city,
               create_By,
               create_time,
               update_by,
               update_time,
               remark,
               call_sign,
               run_no,
               is_send,
               alternate_airport_code,
               alternate_city,
               generalleader_user_id,
               leader_user_id,
               estimated_total_sailing_time,
               dperation_type,
               dperation_standard,
               pre_approval_status,
               pre_approval_user_id,
               pre_approval_user_name,
               approval_comments,
               plan_depart_time, plan_arrive_time,
               aircraft_id, aircraft_tail_no,
               captain_user_id, copilot_user_id, pilot_type, copilot_type, maintenance_id, safety_officer_id, oc_user_id,
               mechanic_id, mechanic_master_Id, organization_id, inspector_id, conductor_id, duty_manager_id,
               flight_frequency, slide_start_time, fly_start_time, fly_end_time, slide_end_time, fly_time, slide_time, night_fly_time, fuel_expend, passenger_number,
               second_slide_start_time, second_fly_start_time, second_fly_end_time, second_fly_time, second_slide_time, second_slide_end_time, second_night_fly_time, second_fuel_expend, second_passenger_number,
               flight_delay, delay_reason, second_flight_delay, second_delay_reason, flight_cancel_reason, aircraft_style, route, business_hours
        from oc_flightplan
    </sql>

    <sql id="selectFlightPlanVo2">
        select flightplan_id,
               company_code,
               flight_purpose,
               route_type,
               flight_status,
               fly_status,
               task_progress,
               flight_date,
               flight_no,
               depart_airport_code,
               depart_city,
               arrive_airport_code,
               arrive_city,
               call_sign,
               alternate_airport_code,
               alternate_city,
               aircraft_id, aircraft_tail_no,
               flight_frequency, fly_time, slide_time, night_fly_time, fuel_expend, passenger_number,
               second_fly_time, second_slide_time, second_night_fly_time, second_fuel_expend, second_passenger_number
        from oc_flightplan
    </sql>


    <select id="selectFlightplanList" parameterType="Flightplan" resultMap="FlightplanResult">
        <include refid="selectFlightplanVo"/>
        <where>
            <if test="companyCode != null  and companyCode != ''">and company_code = #{companyCode}</if>
            <if test="flightPurpose != null  and flightPurpose != ''">and flight_purpose = #{flightPurpose}</if>
            <if test="flightStatus != null ">and flight_status = #{flightStatus}</if>
            <if test="flightDate != null ">and flight_date = #{flightDate}</if>
            <if test="flightNo != null  and flightNo != ''">and flight_no like concat(concat('%',#{flightNo}),'%')</if>
            <if test="callSign != null  and callSign != ''">and call_sign like concat(concat('%',#{callSign}),'%')</if>
            <if test="runNo != null  and runNo != ''">and run_no like concat(concat('%',#{runNo}),'%')</if>
            <if test="departAirportCode != null  and departAirportCode != ''">and depart_airport_code like
                concat(concat('%',#{departAirportCode}),'%')
            </if>
            <if test="departCity != null  and departCity != ''">and depart_city like
                concat(concat('%',#{departCity}),'%')
            </if>
            <if test="arriveAirportCode != null  and arriveAirportCode != ''">and arrive_airport_code like
                concat(concat('%',#{arriveAirportCode}),'%')
            </if>
            <if test="arriveCity != null  and arriveCity != ''">and arrive_city like
                concat(concat('%',#{arriveCity}),'%')
            </if>
            <if test="aircraftId != null ">and aircraft_id = #{aircraftId}</if>
            <if test="aircraftTailNo != null ">and aircraft_tail_no = #{aircraftTailNo}</if>
            <if test="planDepartTime != null">and plan_depart_time = #{planDepartTime}</if>
            <if test="planArriveTime != null">and plan_arrive_time = #{planArriveTime}</if>
            <if test="createBy != null  and createBy != ''">and create_By = #{createBy}</if>
            <if test="isSend != null">and is_send = #{isSend}</if>
            <if test="alternateAirportCode != null">and alternate_airport_code = #{alternateAirportCode}</if>
            <if test="alternateCity != null">and alternate_city = #{alternateCity}</if>
            <if test="captainUserId != null"> and captain_user_id = #{captainUserId}</if>
            <if test="copilotUserId != null"> and copilot_user_id = #{copilotUserId}</if>
            <if test="maintenanceId != null"> and maintenance_id = #{maintenanceId}</if>
            <if test="safetyOfficerId != null"> and safety_officer_id = #{safetyOfficerId}</if>
            <if test="ocUserId != null"> and oc_user_id = #{ocUserId}</if>
            <if test="generalleaderUserId != null">and generalleader_user_id = #{generalleaderUserId}</if>
            <if test="leaderUserId != null">and leader_user_id = #{leaderUserId}</if>
            <if test="estimatedTotalSailingTime != null">and estimated_total_sailing_time =
                #{estimatedTotalSailingTime}
            </if>
            <if test="dperationType != null">and dperation_type = #{dperationType}</if>
            <if test="dperationStandard != null">and dperation_standard = #{dperationStandard}</if>
            <if test="preApprovalStatus != null">and pre_approval_status = #{preApprovalStatus}</if>
            <if test="preApprovalUserId != null">and pre_approval_user_id = #{preApprovalUserId}</if>
            <if test="preApprovalUserName != null">and pre_approval_user_name = #{preApprovalUserName}</if>
            <if test="approvalComments != null">and approval_comments = #{approvalComments}</if>
        </where>
        order by flight_date desc
    </select>

    <select id="queryFlightPlanList" parameterType="QueryFlightPlanDTO" resultMap="FlightplanResult">
        <include refid="selectFlightplanVo"/>
        <where>
            company_code = #{companyCode}
            and flight_type = #{flightType}
            <if test="flightPurpose != null">and flight_purpose = #{flightPurpose}</if>
            <if test="routeType != null">and route_type = #{routeType}</if>
            <if test="departCity != null  and departCity != ''">and depart_city like concat(concat('%',#{departCity}),'%') </if>
            <if test="arriveCity != null  and arriveCity != ''">and arrive_city like concat(concat('%',#{arriveCity}),'%') </if>
            <if test="flightNo != null  and flightNo != ''">and flight_no = #{flightNo}</if>
            <if test="callSign != null  and callSign != ''">and call_sign = #{callSign}</if>
            <if test="aircraftTailNo != null ">and aircraft_tail_no = #{aircraftTailNo}</if>
            <if test="validDate != null and expireDate == null">and flight_date &gt;= #{validDate}</if>
            <if test="validDate == null and expireDate != null">and flight_date &lt;= #{expireDate}</if>
            <if test="validDate != null and expireDate != null">and flight_date between #{validDate} and  #{expireDate}</if>
        </where>
        order by flight_date, plan_depart_time Asc
    </select>

    <select id="selectFlightplanByFlightplanId" parameterType="Long" resultMap="FlightplanResult">
        <include refid="selectFlightplanVo"/>
        where flightplan_id = #{flightplanId}
    </select>

    <select id="selectByFlightDateBetween" resultMap="FlightplanResult">
        <include refid="selectFlightPlanVo2"/>
        where flight_date between #{startDate} and #{endDate}
        and flight_status = 2
        and company_code = #{companyCode}
    </select>

    <select id="selectByFlightPurpose" resultMap="FlightplanResult">
        <include refid="selectFlightPlanVo2"/>
        where flight_date between #{startDate} and #{endDate}
        and flight_status = 2
        and flight_purpose = #{flightPurpose}
        and company_code = #{companyCode}
    </select>

    <select id="selectByFlightPurposes" resultMap="FlightplanResult">
        <include refid="selectFlightPlanVo2"/>
        where flight_date between #{startDate} and #{endDate}
        and flight_status = 2
        and company_code = #{companyCode}
        and flight_purpose in
        <foreach collection="flightPurposes" separator="," open="(" close=")" item="item">
            #{item}
        </foreach>
    </select>

    <select id="selectByFlightDate" resultMap="FlightplanResult">
        <include refid="selectFlightplanVo"/>
        where flight_date = #{flightDate}
        and company_code = #{companyCode}
    </select>

    <select id="selectPilotsByFlightPlanIds" parameterType="Long" resultMap="pilotResult">
        SELECT fpp.user_id, fpp.user_name, sum(if(fpp.route_type=0, fpp.slide_time, fpp.slide_time + fpp.second_slide_time)) AS fly_time, sum(fpp.flight_frequency) AS flight_frequency FROM
        (select u.user_id, u.user_name, fp.flight_status, fp.route_type, fp.slide_time, fp.second_slide_time, fp.flight_frequency from oc_flightplan fp
        left join oc_wx_user u on u.user_id = fp.copilot_user_id or u.user_id = fp.captain_user_id
        where fp.flightplan_id in
        <foreach collection="list" separator="," open="(" close=")" item="flightPlanId">
            #{flightPlanId}
        </foreach>
        ) fpp
        WHERE fpp.flight_status = 2
        GROUP BY fpp.user_id
        ORDER BY fly_time desc
        limit 15
    </select>
    <select id="selectByUserIdAndFlightDateBetween" resultMap="FlightplanResult">
        <include refid="selectFlightPlanVo2"/>
        WHERE flight_date BETWEEN #{startDate} and #{endDate}
        and (find_in_set(#{userId}, captain_user_id) > 0 OR find_in_set(#{userId}, copilot_user_id) > 0)
        and flight_status = 2
    </select>

    <insert id="insertFlightplan" parameterType="Flightplan">
        insert into oc_flightplan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyCode != null">company_code,</if>
            <if test="flightType != null">flight_type,</if>
            <if test="flightPurpose != null">flight_purpose,</if>
            <if test="routeType != null">route_type,</if>
            <if test="flightStatus != null">flight_status,</if>
            <if test="flyStatus != null">fly_status,</if>
            <if test="taskProgress != null">task_progress,</if>
            <if test="flightDate != null">flight_date,</if>
            <if test="flightNo != null">flight_no,</if>
            <if test="callSign != null">call_sign,</if>
            <if test="runNo != null">run_no,</if>
            <if test="departAirportCode != null">depart_airport_code,</if>
            <if test="departCity != null">depart_city,</if>
            <if test="arriveAirportCode != null">arrive_airport_code,</if>
            <if test="arriveCity != null">arrive_city,</if>
            <if test="createBy != null">create_By,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
            <if test="alternateAirportCode != null">alternate_airport_code,</if>
            <if test="alternateCity != null">alternate_city,</if>
            <if test="generalleaderUserId != null">generalleader_user_id,</if>
            <if test="leaderUserId != null">leader_user_id,</if>
            <if test="estimatedTotalSailingTime != null">estimated_total_sailing_time,</if>
            <if test="dperationType != null">dperation_type,</if>
            <if test="dperationStandard != null">dperation_standard,</if>
            <if test="preApprovalStatus != null">pre_approval_status,</if>
            <if test="preApprovalUserId != null">pre_approval_user_id,</if>
            <if test="preApprovalUserName != null">pre_approval_user_name,</if>
            <if test="approvalComments != null">approval_comments,</if>
            <if test="planDepartTime != null">plan_depart_time,</if>
            <if test="planArriveTime != null">plan_arrive_time,</if>
            <if test="captainUserId != null">captain_user_id,</if>
            <if test="copilotUserId != null">copilot_user_id,</if>
            <if test="pilotType != null">pilot_type,</if>
            <if test="copilotType != null">copilot_type,</if>
            <if test="maintenanceId != null">maintenance_id,</if>
            <if test="safetyOfficerId != null">safety_officer_id,</if>
            <if test="ocUserId != null">oc_user_id,</if>
            <if test="mechanicId != null">mechanic_id,</if>
            <if test="mechanicMasterId != null">mechanic_master_Id,</if>
            <if test="organizationId != null">organization_id,</if>
            <if test="inspectorId != null">inspector_id,</if>
            <if test="conductorId != null">conductor_id,</if>
            <if test="dutyManagerId != null">duty_manager_id,</if>
            <if test="aircraftId != null">aircraft_id,</if>
            <if test="aircraftTailNo != null">aircraft_tail_no,</if>
            <if test="aircraftStyle != null">aircraft_style,</if>
            <if test="route != null">route,</if>
            <if test="businessHours != null">business_hours,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyCode != null">#{companyCode},</if>
            <if test="flightType != null">#{flightType},</if>
            <if test="flightPurpose != null">#{flightPurpose},</if>
            <if test="routeType != null">#{routeType},</if>
            <if test="flightStatus != null">#{flightStatus},</if>
            <if test="flyStatus != null">#{flyStatus},</if>
            <if test="taskProgress != null">#{taskProgress},</if>
            <if test="flightDate != null">#{flightDate},</if>
            <if test="flightNo != null">#{flightNo},</if>
            <if test="callSign != null">#{callSign},</if>
            <if test="runNo != null">#{runNo},</if>
            <if test="departAirportCode != null">#{departAirportCode},</if>
            <if test="departCity != null">#{departCity},</if>
            <if test="arriveAirportCode != null">#{arriveAirportCode},</if>
            <if test="arriveCity != null">#{arriveCity},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
            <if test="alternateAirportCode != null">#{alternateAirportCode},</if>
            <if test="alternateCity != null">#{alternateCity},</if>
            <if test="generalleaderUserId != null">#{generalleaderUserId},</if>
            <if test="leaderUserId != null">#{leaderUserId},</if>
            <if test="estimatedTotalSailingTime != null">#{estimatedTotalSailingTime},</if>
            <if test="dperationType != null">#{dperationType},</if>
            <if test="dperationStandard != null">#{dperationStandard},</if>
            <if test="preApprovalStatus != null">#{preApprovalStatus},</if>
            <if test="preApprovalUserId != null">#{preApprovalUserId},</if>
            <if test="preApprovalUserName != null">#{preApprovalUserName},</if>
            <if test="approvalComments != null">#{approvalComments},</if>
            <if test="planDepartTime != null">#{planDepartTime},</if>
            <if test="planArriveTime != null">#{planArriveTime},</if>
            <if test="captainUserId != null">#{captainUserId},</if>
            <if test="copilotUserId != null">#{copilotUserId},</if>
            <if test="pilotType != null">#{pilotType},</if>
            <if test="copilotType != null">#{copilotType},</if>
            <if test="maintenanceId != null">#{maintenanceId},</if>
            <if test="safetyOfficerId != null">#{safetyOfficerId},</if>
            <if test="ocUserId != null">#{ocUserId},</if>
            <if test="mechanicId != null">#{mechanicId},</if>
            <if test="mechanicMasterId != null">#{mechanicMasterId},</if>
            <if test="organizationId != null">#{organizationId},</if>
            <if test="inspectorId != null">#{inspectorId},</if>
            <if test="conductorId != null">#{conductorId},</if>
            <if test="dutyManagerId != null">#{dutyManagerId},</if>
            <if test="aircraftId != null">#{aircraftId},</if>
            <if test="aircraftTailNo != null">#{aircraftTailNo},</if>
            <if test="aircraftStyle != null">#{aircraftStyle},</if>
            <if test="route != null">#{route},</if>
            <if test="businessHours != null">#{businessHours},</if>
        </trim>
    </insert>


    <insert id="batchInsertFlightplan">
        insert into oc_flightplan(flight_purpose,flight_date,flight_no,call_sign,run_no,depart_airport_code,depart_city
        ,arrive_airport_code,arrive_city,aircraft_id,alternate_airport_code,
        alternate_city,
--         captain_user_id,copilot_user_id,maintenance_id,oc_user_id,
        generalleader_user_id,leader_user_id,estimated_total_sailing_time,dperation_type,
        dperation_standard,pre_approval_status,pre_approval_user_id,pre_approval_user_name,
        approval_comments,create_By,create_time,update_by,update_time,remark) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.flightPurpose}, #{item.flightDate},
            #{item.flightNo},#{item.callSign},#{item.runNo}, #{item.departAirportCode},
            #{item.departCity}, #{item.arriveAirportCode},
            #{item.arriveCity}, #{item.aircraftId}
            , #{item.alternateAirportCode}, #{item.alternateCity},
--              #{item.captainUserId}, #{item.copilotUserId},
--             #{item.maintenanceId},#{item.ocUserId},
             #{item.generalleaderUserId}, #{item.leaderUserId}, #{item.estimatedTotalSailingTime},
            #{item.dperationType},
            #{item.dperationStandard}, #{item.preApprovalStatus}, #{item.preApprovalUserId},
            #{item.preApprovalUserName}, #{item.approvalComments}
            ),
        </foreach>
    </insert>

    <update id="updateFlightplan" parameterType="Flightplan">
        update oc_flightplan
        <trim prefix="SET" suffixOverrides=",">
            <if test="flightType != null">flight_type = #{flightType},</if>
            <if test="flightPurpose != null">flight_purpose = #{flightPurpose},</if>
            <if test="routeType != null">route_type = #{routeType},</if>
            <if test="flightStatus != null">flight_status = #{flightStatus},</if>
            <if test="flyStatus != null">fly_status = #{flyStatus},</if>
            <if test="taskProgress != null">task_progress = #{taskProgress},</if>
            <if test="flightDate != null">flight_date = #{flightDate},</if>
            <if test="flightNo != null">flight_no = #{flightNo},</if>
            <if test="callSign != null">call_sign = #{callSign},</if>
            <if test="runNo != null">run_no = #{runNo},</if>
            <if test="departAirportCode != null">depart_airport_code = #{departAirportCode},</if>
            <if test="departCity != null">depart_city = #{departCity},</if>
            <if test="arriveAirportCode != null">arrive_airport_code = #{arriveAirportCode},</if>
            <if test="arriveCity != null">arrive_city = #{arriveCity},</if>
            <if test="aircraftId != null">aircraft_id = #{aircraftId},</if>
            <if test="aircraftTailNo != null">aircraft_tail_no = #{aircraftTailNo},</if>
            <if test="createBy != null">create_By = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="alternateAirportCode != null">alternate_airport_code = #{alternateAirportCode},</if>
            <if test="alternateCity != null">alternate_city = #{alternateCity},</if>
            <if test="generalleaderUserId != null">generalleader_user_id = #{generalleaderUserId},</if>
            <if test="leaderUserId != null">leader_user_id = #{leaderUserId},</if>
            <if test="estimatedTotalSailingTime != null">estimated_total_sailing_time = #{estimatedTotalSailingTime},
            </if>
            <if test="dperationType != null">dperation_type = #{dperationType},</if>
            <if test="dperationStandard != null">dperation_standard = #{dperationStandard},</if>
            <if test="preApprovalStatus != null">pre_approval_status = #{preApprovalStatus},</if>
            <if test="preApprovalUserId != null">pre_approval_user_id = #{preApprovalUserId},</if>
            <if test="preApprovalUserName != null">pre_approval_user_name = #{preApprovalUserName},</if>
            <if test="approvalComments != null">approval_comments = #{approvalComments},</if>
            <if test="planDepartTime != null">plan_depart_time = #{planDepartTime},</if>
            <if test="planArriveTime != null">plan_arrive_time = #{planArriveTime},</if>
            <if test="captainUserId != null">captain_user_id = #{captainUserId},</if>
            <if test="copilotUserId != null">copilot_user_id = #{copilotUserId},</if>
            <if test="pilotType != null">pilot_type = #{pilotType},</if>
            <if test="copilotType != null">copilot_type = #{copilotType},</if>
            <if test="maintenanceId != null">maintenance_id = #{maintenanceId},</if>
            <if test="safetyOfficerId != null">safety_officer_id = #{safetyOfficerId},</if>
            <if test="ocUserId != null">oc_user_id = #{ocUserId},</if>
            <if test="mechanicId != null">mechanic_id = #{mechanicId},</if>
            <if test="mechanicMasterId != null">mechanic_master_Id = #{mechanicMasterId},</if>
            <if test="organizationId != null">organization_id = #{organizationId},</if>
            <if test="inspectorId != null">inspector_id = #{inspectorId},</if>
            <if test="conductorId != null">conductor_id = #{conductorId},</if>
            <if test="dutyManagerId != null">duty_manager_id = #{dutyManagerId},</if>
            <if test="aircraftStyle != null">aircraft_style = #{aircraftStyle},</if>
            <if test="route != null">route = #{route},</if>
            <if test="businessHours != null">business_hours = #{businessHours},</if>
        </trim>
        where flightplan_id = #{flightplanId}
    </update>


    <delete id="deleteFlightplanByFlightplanId" parameterType="Long">
        delete
        from oc_flightplan
        where flightplan_id = #{flightplanId}
    </delete>

    <delete id="deleteFlightplanByFlightplanIds" parameterType="String">
        delete from oc_flightplan where flightplan_id in
        <foreach item="flightplanId" collection="array" open="(" separator="," close=")">
            #{flightplanId}
        </foreach>
    </delete>

    <delete id="deleteAircraftByAircraftIds" parameterType="String">
        delete from oc_aircraft where aircraft_id in
        <foreach item="aircraftId" collection="array" open="(" separator="," close=")">
            #{aircraftId}
        </foreach>
    </delete>

    <delete id="deleteSortiesByflightplanIds" parameterType="String">
        delete from oc_flight_sorties where flightplan_id in
        <foreach item="flightplanId" collection="array" open="(" separator="," close=")">
            #{flightplanId}
        </foreach>
    </delete>

    <delete id="deleteAircraftByAircraftId" parameterType="Long">
        delete
        from oc_aircraft
        where aircraft_id = #{aircraftId}
    </delete>

</mapper>
