<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.FlightSortiesMapper">
    <resultMap type="FlightSorties" id="FlightSortiesResult">
        <result property="flightSortiesId" column="flightSorties_id"/>
        <result property="flightSortiesNo" column="flightSorties_no"/>
        <result property="flightplanId" column="flightplan_id"/>
        <result property="flightSortiesStatus" column="flight_sorties_status"/>
        <result property="planDepartTime" column="plan_depart_time"/>
        <result property="planArriveTime" column="plan_arrive_time"/>
        <result property="fuel" column="fuel"/>
        <result property="flyQaStatus" column="fly_qa_status"/>
        <result property="routeInfoStatus" column="route_info_status"/>
        <result property="meteReviewStatus" column="mete_review_status"/>
        <result property="eleManifestStatus" column="ele_manifest_status"/>
        <result property="releasenoteStatus" column="releasenote_status"/>
        <result property="flyMomentStatus" column="fly_moment_status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
        <result property="captainUserId" column="captain_user_id"/>
        <result property="copilotUserId" column="copilot_user_id"/>
        <result property="maintenanceId" column="maintenance_id"/>
        <result property="ocUserId" column="oc_user_id"/>
        <result property="generalleaderUserId" column="generalleader_user_id"/>
        <result property="leaderUserId" column="leader_user_id"/>
        <result property="seatNum" column="seat_num"/>
        <result property="flyTime" column="fly_time"/>
        <result property="slideTime" column="slide_time"/>
        <result property="aircraftId" column="aircraft_id"/>
        <result property="aircraftTailNo" column="aircraft_tail_no"/>
        <result property="flightAltitude" column="flight_altitude"/>
        <result property="releaseStatus" column="release_status"/>
        <result property="flightNormalStatus" column="flight_normal_status"/>
        <result property="flightSummaryComments" column="flight_summary_comments"/>
        <result property="uploadAssignmentStatus" column="upload_assignment_status"/>
        <result property="uploadAssignmentFileUrl" column="upload_assignment_file_url"/>
        <result property="uploadAssignmentFileName" column="upload_assignment_file_name"/>
        <result property="taxiStartTime" column="taxi_start_time"/>
        <result property="departureTime" column="departure_time"/>
        <result property="landingTime" column="landing_time"/>
        <result property="shutdownTime" column="shutdown_time"/>
        <result property="flightTime" column="flight_time"/>
        <result property="exceptionalCase" column="exceptional_case"/>
        <result property="specialCaseSendingStatus" column="special_case_sending_status"/>
        <result property="exceptionalCaseForCaptain" column="exceptional_case_for_captain"/>
        <result property="exceptionalCaseForCopilot" column="exceptional_case_for_copilot"/>
        <result property="exceptionalCaseForMaintenance" column="exceptional_case_for_maintenance"/>
        <result property="exceptionalCaseForOc" column="exceptional_case_for_oc"/>
        <result property="copilotType" column="copilot_type"/>
        <result property="sortiesDate" column="sorties_date"/>
        <result property="preApprovalStatus" column="pre_approval_status"/>
        <result property="preApprovalUserId" column="pre_approval_user_id"/>
        <result property="preApprovalUserName" column="pre_approval_user_name"/>
        <result property="approvalComments" column="approval_comments"/>
        <result property="flyStatus" column="fly_status"/>
    </resultMap>

    <resultMap type="HashMap" id="monthlyFlightTimeMap">
        <result column="month" property="month"/>
        <result column="times" property="times"/>
    </resultMap>

    <sql id="selectFlightSortiesVo">
        select flightSorties_id,
               flightSorties_no,
               flightplan_id,
               flight_sorties_status,
               plan_depart_time,
               plan_arrive_time,
               fuel,
               fly_qa_status,
               route_info_status,
               mete_review_status,
               ele_manifest_status,
               releasenote_status,
               fly_moment_status,
               create_by,
               create_time,
               update_by,
               update_time,
               remark,
               captain_user_id,
               copilot_user_id,
               maintenance_id,
               oc_user_id,
               generalleader_user_id,
               leader_user_id,
               seat_num,
               fly_time,
               slide_time,
               aircraft_id,
               aircraft_tail_no,
               flight_altitude,
               release_status,
               flight_normal_status,
               flight_summary_comments,
               upload_assignment_status,
               upload_assignment_file_url,
               upload_assignment_file_name,
               taxi_start_time,
               departure_time,
               landing_time,
               shutdown_time,
               flight_time,
               exceptional_case,
               special_case_sending_status,
               exceptional_case_for_captain,
               exceptional_case_for_copilot,
               exceptional_case_for_maintenance,
               exceptional_case_for_oc,
               copilot_type,
               sorties_date,
               pre_approval_status,
               pre_approval_user_id,
               pre_approval_user_name,
               approval_comments,
               fly_status
        from oc_flight_sorties
    </sql>

    <select id="selectFlightSortiesList" parameterType="FlightSorties" resultMap="FlightSortiesResult">
        <include refid="selectFlightSortiesVo"/>
        <where>
            <if test="flightSortiesNo != null">
                and flightSorties_no = #{flightSortiesNo}
            </if>
            <if test="flightplanId != null">
                and flightplan_id = #{flightplanId}
            </if>
            <if test="flightSortiesStatus != null">
                and flight_sorties_status = #{flightSortiesStatus}
            </if>
            <if test="planDepartTime != null  and planDepartTime != ''">
                and plan_depart_time = #{planDepartTime}
            </if>
            <if test="planArriveTime != null  and planArriveTime != ''">
                and plan_arrive_time = #{planArriveTime}
            </if>
            <if test="fuel != null">
                and fuel = #{fuel}
            </if>
            <if test="flyQaStatus != null">
                and fly_qa_status = #{flyQaStatus}
            </if>
            <if test="routeInfoStatus != null">
                and route_info_status = #{routeInfoStatus}
            </if>
            <if test="meteReviewStatus != null">
                and mete_review_status = #{meteReviewStatus}
            </if>
            <if test="eleManifestStatus != null">
                and ele_manifest_status = #{eleManifestStatus}
            </if>
            <if test="releasenoteStatus != null">
                and releasenote_status = #{releasenoteStatus}
            </if>
            <if test="flyMomentStatus != null">
                and fly_moment_status = #{flyMomentStatus}
            </if>
            <if test="captainUserId != null">
                and captain_user_id = #{captainUserId}
            </if>
            <if test="copilotUserId != null">
                and copilot_user_id = #{copilotUserId}
            </if>
            <if test="maintenanceId != null  and maintenanceId != ''">
                and maintenance_id = #{maintenanceId}
            </if>
            <if test="ocUserId != null">
                and oc_user_id = #{ocUserId}
            </if>
            <if test="generalleaderUserId != null">
                and generalleader_user_id = #{generalleaderUserId}
            </if>
            <if test="leaderUserId != null">
                and leader_user_id = #{leaderUserId}
            </if>
            <if test="seatNum != null">
                and seat_num = #{seatNum}
            </if>
            <if test="flyTime != null">
                and fly_time = #{flyTime}
            </if>
            <if test="slideTime != null">
                and slide_time = #{slideTime}
            </if>
            <if test="aircraftId != null">
                and aircraft_id = #{aircraftId}
            </if>
            <if test="aircraftTailNo != null">
                and aircraft_tail_no = #{aircraftTailNo}
            </if>
            <if test="flightAltitude != null">
                and flight_altitude = #{flightAltitude}
            </if>
            <if test="releaseStatus != null">
                and release_status = #{releaseStatus}
            </if>
            <if test="flightNormalStatus != null">
                and flight_normal_status = #{flightNormalStatus}
            </if>
            <if test="flightSummaryComments != null">
                and flight_summary_comments = #{flightSummaryComments}
            </if>
            <if test="uploadAssignmentStatus != null">
                and upload_assignment_status = #{uploadAssignmentStatus}
            </if>
            <if test="uploadAssignmentFileUrl != null">
                and upload_assignment_file_url = #{uploadAssignmentFileUrl}
            </if>
            <if test="uploadAssignmentFileName != null">
                and upload_assignment_file_name = #{uploadAssignmentFileName}
            </if>
            <if test="taxiStartTime != null">
                and taxi_start_time = #{taxiStartTime}
            </if>
            <if test="departureTime != null">
                and departure_time = #{departureTime}
            </if>
            <if test="landingTime != null">
                and landing_time = #{landingTime}
            </if>
            <if test="shutdownTime != null">
                and shutdown_time = #{shutdownTime}
            </if>
            <if test="flightTime != null">
                and flight_time = #{flightTime}
            </if>
            <if test="exceptionalCase != null">
                and exceptional_case = #{exceptionalCase}
            </if>
            <if test="specialCaseSendingStatus != null">
                and special_case_sending_status = #{specialCaseSendingStatus}
            </if>
            <if test="exceptionalCaseForCaptain != null">
                and exceptional_case_for_captain =
                    #{exceptionalCaseForCaptain}
            </if>
            <if test="exceptionalCaseForCopilot != null">
                and exceptional_case_for_copilot =
                    #{exceptionalCaseForCopilot}
            </if>
            <if test="exceptionalCaseForMaintenance != null">
                and exceptional_case_for_maintenance =
                    #{exceptionalCaseForMaintenance}
            </if>
            <if test="exceptionalCaseForOc != null">
                and exceptional_case_for_oc = #{exceptionalCaseForOc}
            </if>
            <if test="copilotType != null">
                and copilot_type = #{copilotType}
            </if>
            <if test="sortiesDate != null">
                and sorties_date = #{sortiesDate}
            </if>
            <if test="preApprovalStatus != null">
                and pre_approval_status = #{preApprovalStatus}
            </if>
            <if test="preApprovalUserId != null">
                and pre_approval_user_id = #{preApprovalUserId}
            </if>
            <if test="preApprovalUserName != null">
                and pre_approval_user_name = #{preApprovalUserName}
            </if>
            <if test="approvalComments != null">
                and approval_comments = #{approvalComments}
            </if>
            <if test="flyStatus != null">
                and fly_status = #{flyStatus}
            </if>
        </where>
    </select>


    <select id="selectFlightSortiesByFlighplanId" parameterType="Long" resultMap="FlightSortiesResult">
        <include refid="selectFlightSortiesVo"/>
        <where>
            <if test="flightplanId != null">
                and flightplan_id = #{flightplanId}
            </if>
        </where>
    </select>


    <select id="selectFlightSortiesByCaptainUserIdOrCopilotUserId" parameterType="String"
            resultMap="FlightSortiesResult">
        <include refid="selectFlightSortiesVo"/>
        <where>
            <if test="userId != null">
                (captain_user_id like concat('%', #{userId}, '%') or
                 copilot_user_id like concat('%', #{userId}, '%'))
            </if>
        </where>
    </select>

    <select id="selectFlightSortiesByUserIdAndStatus" resultMap="FlightSortiesResult">
        <include refid="selectFlightSortiesVo"/>
        <where>
            <if test="userId != null">
                (captain_user_id like concat('%', #{userId}, '%') or copilot_user_id like concat('%', #{userId}, '%'))
            </if>
            <if test="status != 0">
                and flight_sorties_status = #{status}
            </if>
        </where>
    </select>

    <select id="selectFlightSortiesByFlightsortiesId" parameterType="Long" resultMap="FlightSortiesResult">
        <include refid="selectFlightSortiesVo"/>
        where flightSorties_id = #{flightSortiesId}
    </select>


    <select id="selectBySortiesDateBetween" parameterType="string" resultMap="FlightSortiesResult">
        <include refid="selectFlightSortiesVo"/>
        where sorties_date between #{startDate} and #{endDate}
          and flight_sorties_status = 2
    </select>
    <select id="selectMonthlyFlightTimes" resultMap="monthlyFlightTimeMap">
        select a.month,
               ifnull(b.times, 0) times
        from (SELECT CONCAT('01') AS month
              UNION
              SELECT CONCAT('02') AS month
              UNION
              SELECT CONCAT('03') AS month
              UNION
              SELECT CONCAT('04') AS month
              UNION
              SELECT CONCAT('05') AS month
              UNION
              SELECT CONCAT('06') AS month
              UNION
              SELECT CONCAT('07') AS month
              UNION
              SELECT CONCAT('08') AS month
              UNION
              SELECT CONCAT('09') AS month
              UNION
              SELECT CONCAT('10') AS month
              UNION
              SELECT CONCAT('11') AS month
              UNION
              SELECT CONCAT('12') AS month) a
                 left join
             (select count(*) as times, DATE_FORMAT(sorties_date, '%m') as month
              from oc_flight_sorties
              where sorties_date between #{startDate} and #{endDate}
                and flight_sorties_status = 2
              group by month) b
             on a.month = b.month
    </select>

    <select id="selectBySortiesDate" parameterType="string" resultMap="FlightSortiesResult">
        <include refid="selectFlightSortiesVo"/>
        where sorties_date = #{sortiesDate}
    </select>

    <insert id="insertFlightSorties" parameterType="FlightSorties" useGeneratedKeys="true"
            keyProperty="flightSortiesId">
        insert into oc_flight_sorties
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flightSortiesNo != null">
                flightSorties_no,
            </if>
            <if test="flightplanId != null">
                flightplan_id,
            </if>
            <if test="flightSortiesStatus != null">
                flight_sorties_status,
            </if>
            <if test="planDepartTime != null">
                plan_depart_time,
            </if>
            <if test="planArriveTime != null">
                plan_arrive_time,
            </if>
            <if test="fuel != null">
                fuel,
            </if>
            <if test="flyQaStatus != null">
                fly_qa_status,
            </if>
            <if test="routeInfoStatus != null">
                route_info_status,
            </if>
            <if test="meteReviewStatus != null">
                mete_review_status,
            </if>
            <if test="eleManifestStatus != null">
                ele_manifest_status,
            </if>
            <if test="releasenoteStatus != null">
                releasenote_status,
            </if>
            <if test="flyMomentStatus != null">
                fly_moment_status,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="captainUserId != null">
                captain_user_id,
            </if>
            <if test="copilotUserId != null">
                copilot_user_id,
            </if>
            <if test="maintenanceId != null">
                maintenance_id,
            </if>
            <if test="ocUserId != null">
                oc_user_id,
            </if>
            <if test="generalleaderUserId != null">
                generalleader_user_id,
            </if>
            <if test="leaderUserId != null">
                leader_user_id,
            </if>
            <if test="seatNum != null">
                seat_num,
            </if>
            <if test="flyTime != null">
                fly_time,
            </if>
            <if test="slideTime != null">
                slide_time,
            </if>
            <if test="aircraftId != null">
                aircraft_id,
            </if>
            <if test="aircraftTailNo != null">
                aircraft_tail_no,
            </if>
            <if test="flightAltitude != null">
                flight_altitude,
            </if>
            <if test="releaseStatus != null">
                release_status,
            </if>
            <if test="flightNormalStatus != null">
                flight_normal_status,
            </if>
            <if test="flightSummaryComments != null">
                flight_summary_comments,
            </if>
            <if test="uploadAssignmentStatus != null">
                upload_assignment_status,
            </if>
            <if test="uploadAssignmentFileUrl != null">
                upload_assignment_file_url,
            </if>
            <if test="uploadAssignmentFileName != null">
                upload_assignment_file_name,
            </if>
            <if test="taxiStartTime != null">
                taxi_start_time,
            </if>
            <if test="departureTime != null">
                departure_time,
            </if>
            <if test="landingTime != null">
                landing_time,
            </if>
            <if test="shutdownTime != null">
                shutdown_time,
            </if>
            <if test="flightTime != null">
                flight_time,
            </if>
            <if test="exceptionalCase != null">
                exceptional_case,
            </if>
            <if test="specialCaseSendingStatus != null">
                special_case_sending_status,
            </if>
            <if test="exceptionalCaseForCaptain != null">
                exceptional_case_for_captain,
            </if>
            <if test="exceptionalCaseForCopilot != null">
                exceptional_case_for_copilot,
            </if>
            <if test="exceptionalCaseForMaintenance != null">
                exceptional_case_for_maintenance,
            </if>
            <if test="exceptionalCaseForOc != null">
                exceptional_case_for_oc,
            </if>
            <if test="copilotType != null">
                copilot_type,
            </if>
            <if test="sortiesDate != null">
                sorties_date,
            </if>
            <if test="preApprovalStatus != null">
                pre_approval_status,
            </if>
            <if test="preApprovalUserId != null">
                pre_approval_user_id,
            </if>
            <if test="preApprovalUserName != null">
                pre_approval_user_name,
            </if>
            <if test="approvalComments != null">
                approval_comments,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flightSortiesNo != null">
                #{flightSortiesNo},
            </if>
            <if test="flightplanId != null">
                #{flightplanId},
            </if>
            <if test="flightSortiesStatus != null">
                #{flightSortiesStatus},
            </if>
            <if test="planDepartTime != null">
                #{planDepartTime},
            </if>
            <if test="planArriveTime != null">
                #{planArriveTime},
            </if>
            <if test="fuel != null">
                #{fuel},
            </if>
            <if test="flyQaStatus != null">
                #{flyQaStatus},
            </if>
            <if test="routeInfoStatus != null">
                #{routeInfoStatus},
            </if>
            <if test="meteReviewStatus != null">
                #{meteReviewStatus},
            </if>
            <if test="eleManifestStatus != null">
                #{eleManifestStatus},
            </if>
            <if test="releasenoteStatus != null">
                #{releasenoteStatus},
            </if>
            <if test="flyMomentStatus != null">
                #{flyMomentStatus},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="captainUserId != null">
                #{captainUserId},
            </if>
            <if test="copilotUserId != null">
                #{copilotUserId},
            </if>
            <if test="maintenanceId != null">
                #{maintenanceId},
            </if>
            <if test="ocUserId != null">
                #{ocUserId},
            </if>
            <if test="generalleaderUserId != null">
                #{generalleaderUserId},
            </if>
            <if test="leaderUserId != null">
                #{leaderUserId},
            </if>
            <if test="seatNum != null">
                #{seatNum},
            </if>
            <if test="flyTime != null">
                #{flyTime},
            </if>
            <if test="slideTime != null">
                #{slideTime},
            </if>
            <if test="aircraftId != null">
                #{aircraftId},
            </if>
            <if test="aircraftTailNo != null">
                #{aircraftTailNo},
            </if>
            <if test="flightAltitude != null">
                #{flightAltitude},
            </if>
            <if test="releaseStatus != null">
                #{releaseStatus},
            </if>
            <if test="flightNormalStatus != null">
                #{flightNormalStatus},
            </if>
            <if test="flightSummaryComments != null">
                #{flightSummaryComments},
            </if>
            <if test="uploadAssignmentStatus != null">
                #{uploadAssignmentStatus},
            </if>
            <if test="uploadAssignmentFileUrl != null">
                #{uploadAssignmentFileUrl},
            </if>
            <if test="uploadAssignmentFileName != null">
                #{uploadAssignmentFileName},
            </if>
            <if test="taxiStartTime != null">
                #{taxiStartTime},
            </if>
            <if test="departureTime != null">
                #{departureTime},
            </if>
            <if test="landingTime != null">
                #{landingTime},
            </if>
            <if test="shutdownTime != null">
                #{shutdownTime},
            </if>
            <if test="flightTime != null">
                #{flightTime},
            </if>
            <if test="exceptionalCase != null">
                #{exceptionalCase},
            </if>
            <if test="specialCaseSendingStatus != null">
                #{specialCaseSendingStatus},
            </if>
            <if test="exceptionalCaseForCaptain != null">
                #{exceptionalCaseForCaptain},
            </if>
            <if test="exceptionalCaseForCopilot != null">
                #{exceptionalCaseForCopilot},
            </if>
            <if test="exceptionalCaseForMaintenance != null">
                #{exceptionalCaseForMaintenance},
            </if>
            <if test="exceptionalCaseForOc != null">
                #{exceptionalCaseForOc},
            </if>
            <if test="copilotType != null">
                #{copilotType},
            </if>
            <if test="sortiesDate != null">
                #{sortiesDate},
            </if>
            <if test="preApprovalStatus != null">
                #{preApprovalStatus},
            </if>
            <if test="preApprovalUserId != null">
                #{preApprovalUserId},
            </if>
            <if test="preApprovalUserName != null">
                #{preApprovalUserName},
            </if>
            <if test="approvalComments != null">
                #{approvalComments},
            </if>
        </trim>
    </insert>

    <update id="updateFlightSorties" parameterType="FlightSorties">
        update oc_flight_sorties
        <trim prefix="SET" suffixOverrides=",">
            <if test="flightSortiesNo != null">
                flightSorties_no = #{flightSortiesNo},
            </if>
            <if test="flightplanId != null">
                flightplan_id = #{flightplanId},
            </if>
            <if test="flightSortiesStatus != null">
                flight_sorties_status = #{flightSortiesStatus},
            </if>
            <if test="planDepartTime != null">
                plan_depart_time = #{planDepartTime},
            </if>
            <if test="planArriveTime != null">
                plan_arrive_time = #{planArriveTime},
            </if>
            <if test="fuel != null">
                fuel = #{fuel},
            </if>
            <if test="flyQaStatus != null">
                fly_qa_status = #{flyQaStatus},
            </if>
            <if test="routeInfoStatus != null">
                route_info_status = #{routeInfoStatus},
            </if>
            <if test="meteReviewStatus != null">
                mete_review_status = #{meteReviewStatus},
            </if>
            <if test="eleManifestStatus != null">
                ele_manifest_status = #{eleManifestStatus},
            </if>
            <if test="releasenoteStatus != null">
                releasenote_status = #{releasenoteStatus},
            </if>
            <if test="flyMomentStatus != null">
                fly_moment_status = #{flyMomentStatus},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="captainUserId != null">
                captain_user_id = #{captainUserId},
            </if>
            <if test="copilotUserId != null">
                copilot_user_id = #{copilotUserId},
            </if>
            <if test="maintenanceId != null">
                maintenance_id = #{maintenanceId},
            </if>
            <if test="ocUserId != null">
                oc_user_id = #{ocUserId},
            </if>
            <if test="generalleaderUserId != null">
                generalleader_user_id = #{generalleaderUserId},
            </if>
            <if test="leaderUserId != null">
                leader_user_id = #{leaderUserId},
            </if>
            <if test="seatNum != null">
                seat_num = #{seatNum},
            </if>
            <if test="flyTime != null">
                fly_time = #{flyTime},
            </if>
            <if test="slideTime != null">
                slide_time = #{slideTime},
            </if>
            <if test="aircraftId != null">
                aircraft_id = #{aircraftId},
            </if>
            <if test="aircraftTailNo != null">
                aircraft_tail_no = #{aircraftTailNo},
            </if>
            <if test="flightAltitude != null">
                flight_altitude = #{flightAltitude},
            </if>
            <if test="releaseStatus != null">
                release_status = #{releaseStatus},
            </if>
            <if test="flightNormalStatus != null">
                flight_normal_status = #{flightNormalStatus},
            </if>
            <if test="flightSummaryComments != null">
                flight_summary_comments = #{flightSummaryComments},
            </if>
            <if test="uploadAssignmentStatus != null">
                upload_assignment_status = #{uploadAssignmentStatus},
            </if>
            <if test="uploadAssignmentFileUrl != null">
                upload_assignment_file_url = #{uploadAssignmentFileUrl},
            </if>
            <if test="uploadAssignmentFileName != null">
                upload_assignment_file_name = #{uploadAssignmentFileName},
            </if>
            <if test="taxiStartTime != null">
                taxi_start_time = #{taxiStartTime},
            </if>
            <if test="departureTime != null">
                departure_time = #{departureTime},
            </if>
            <if test="landingTime != null">
                landing_time = #{landingTime},
            </if>
            <if test="shutdownTime != null">
                shutdown_time = #{shutdownTime},
            </if>
            <if test="flightTime != null">
                flight_time = #{flightTime},
            </if>
            <if test="exceptionalCase != null">
                exceptional_case = #{exceptionalCase},
            </if>
            <if test="specialCaseSendingStatus != null">
                special_case_sending_status = #{specialCaseSendingStatus},
            </if>
            <if test="exceptionalCaseForCaptain != null">
                exceptional_case_for_captain = #{exceptionalCaseForCaptain},
            </if>
            <if test="exceptionalCaseForCopilot != null">
                exceptional_case_for_copilot = #{exceptionalCaseForCopilot},
            </if>
            <if test="exceptionalCaseForMaintenance != null">
                exceptional_case_for_maintenance =
                    #{exceptionalCaseForMaintenance},
            </if>
            <if test="exceptionalCaseForOc != null">
                exceptional_case_for_oc = #{exceptionalCaseForOc},
            </if>
            <if test="copilotType != null">
                copilot_type = #{copilotType},
            </if>
            <if test="sortiesDate != null">
                sorties_date = #{sortiesDate},
            </if>
            <if test="preApprovalStatus != null">
                pre_approval_status = #{preApprovalStatus},
            </if>
            <if test="preApprovalUserId != null">
                pre_approval_user_id = #{preApprovalUserId},
            </if>
            <if test="preApprovalUserName != null">
                pre_approval_user_name = #{preApprovalUserName},
            </if>
            <if test="approvalComments != null">
                approval_comments = #{approvalComments},
            </if>
        </trim>
        where flightSorties_id = #{flightSortiesId}
    </update>

    <delete id="deleteFlightSortiesByFlightsortiesId" parameterType="Long">
        delete
        from oc_flight_sorties
        where flightSorties_id = #{flightSortiesId}
    </delete>

    <delete id="deleteFlightSortiesByFlightsortiesIds" parameterType="String">
        delete
        from oc_flight_sorties where flightSorties_id in
        <foreach item="flightSortiesId" collection="array" open="(" separator="," close=")">
            #{flightSortiesId}
        </foreach>
    </delete>
</mapper>
