<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.WxUserRoleMapper">
    <resultMap type="WxUserRole" id="WxUserRoleResult">
        <result property="roleId" column="role_id"/>
        <result property="userId" column="user_id"/>
    </resultMap>

    <insert id="batchInsert">
        insert into oc_wx_user_role(role_id, user_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.roleId}, #{item.userId})
        </foreach>
    </insert>

    <delete id="deleteByUserId" parameterType="long">
        delete
        from oc_wx_user_role
        where user_id = #{userId}
    </delete>
</mapper>