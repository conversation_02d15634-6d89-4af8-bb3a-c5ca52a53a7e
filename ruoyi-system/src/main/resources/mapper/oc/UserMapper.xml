<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.UserMapper">

    <resultMap type="SysUser" id="SysUserResult">
        <result property="userId"    column="user_id"    />
        <result property="deptId"    column="dept_id"    />
        <result property="userName"    column="user_name"    />
        <result property="nickName"    column="nick_name"    />
        <result property="userType"    column="user_type"    />
        <result property="email"    column="email"    />
        <result property="phonenumber"    column="phonenumber"    />
        <result property="sex"    column="sex"    />
        <result property="avatar"    column="avatar"    />
        <result property="password"    column="password"    />
        <result property="status"    column="status"    />
        <result property="delFlag"    column="del_flag"    />
        <result property="loginIp"    column="login_ip"    />
        <result property="loginDate"    column="login_date"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="remark"    column="remark"    />
        <result property="updateTime"    column="update_time"    />
        <result property="userWxOpenId"    column="user_wx_open_id"    />
        <result property="flyNumber"    column="fly_number"    />
        <result property="flyTime"    column="fly_time"    />
        <result property="carrierCode"    column="carrier_code"    />
        <result property="nationality" column="nationality"/>
        <result property="roleType" column="role_type"/>
    </resultMap>

    <sql id="selectSysUserVo">
        select user_id, dept_id, user_name, nick_name, user_type, email, phonenumber, sex, avatar, password, status, del_flag, login_ip, login_date,
               create_by, create_time, update_by, remark, update_time, user_wx_open_id, fly_number, fly_time, carrier_code, nationality, role_type from sys_user
    </sql>

    <select id="selectSysUserList" parameterType="SysUser" resultMap="SysUserResult">
        <include refid="selectSysUserVo"/>
        <where>
            user_id in (select user_id from sys_user_role where role_id in (2,3))
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="phonenumber != null  and phonenumber != ''"> and phonenumber = #{phonenumber}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="loginIp != null  and loginIp != ''"> and login_ip = #{loginIp}</if>
            <if test="loginDate != null "> and login_date = #{loginDate}</if>
            <if test="userWxOpenId != null  and userWxOpenId != ''"> and user_wx_open_id = #{userWxOpenId}</if>
            <if test="flyNumber != null "> and fly_number = #{flyNumber}</if>
            <if test="flyTime != null "> and fly_time = #{flyTime}</if>
            <if test="carrierCode != null "> and carrier_code = #{carrierCode}</if>
            <if test="nationality != null "> and nationality = #{nationality}</if>
            <if test="roleType != null "> and role_type = #{roleType}</if>
        </where>
    </select>

    <select id="selectList" parameterType="SysUser" resultMap="SysUserResult">
        <include refid="selectSysUserVo"/>
        <where>
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="deptId != null "> and dept_id = #{deptId}</if>
            <if test="userName != null  and userName != ''"> and user_name like concat('%', #{userName}, '%')</if>
            <if test="nickName != null  and nickName != ''"> and nick_name like concat('%', #{nickName}, '%')</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="email != null  and email != ''"> and email = #{email}</if>
            <if test="phonenumber != null  and phonenumber != ''"> and phonenumber = #{phonenumber}</if>
            <if test="sex != null  and sex != ''"> and sex = #{sex}</if>
            <if test="avatar != null  and avatar != ''"> and avatar = #{avatar}</if>
            <if test="password != null  and password != ''"> and password = #{password}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="loginIp != null  and loginIp != ''"> and login_ip = #{loginIp}</if>
            <if test="loginDate != null "> and login_date = #{loginDate}</if>
            <if test="userWxOpenId != null  and userWxOpenId != ''"> and user_wx_open_id = #{userWxOpenId}</if>
            <if test="flyNumber != null "> and fly_number = #{flyNumber}</if>
            <if test="flyTime != null "> and fly_time = #{flyTime}</if>
            <if test="carrierCode != null "> and carrier_code = #{carrierCode}</if>
            <if test="nationality != null "> and nationality = #{nationality}</if>
            <if test="roleType != null "> and role_type = #{roleType}</if>
        </where>
    </select>

    <select id="selectSysUserByUserId" parameterType="Long" resultMap="SysUserResult">
        <include refid="selectSysUserVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertSysUser" parameterType="SysUser" useGeneratedKeys="true" keyProperty="userId">
        insert into sys_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="deptId != null">dept_id,</if>
            <if test="userName != null and userName != ''">user_name,</if>
            <if test="nickName != null and nickName != ''">nick_name,</if>
            <if test="userType != null">user_type,</if>
            <if test="email != null">email,</if>
            <if test="phonenumber != null">phonenumber,</if>
            <if test="sex != null">sex,</if>
            <if test="avatar != null">avatar,</if>
            <if test="password != null">password,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="loginIp != null">login_ip,</if>
            <if test="loginDate != null">login_date,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="remark != null">remark,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="userWxOpenId != null">user_wx_open_id,</if>
            <if test="flyNumber != null">fly_number,</if>
            <if test="flyTime != null">fly_time,</if>
            <if test="carrierCode != null">carrier_code,</if>
            <if test="nationality != null">nationality,</if>
            <if test="roleType != null">role_type,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="deptId != null">#{deptId},</if>
            <if test="userName != null and userName != ''">#{userName},</if>
            <if test="nickName != null and nickName != ''">#{nickName},</if>
            <if test="userType != null">#{userType},</if>
            <if test="email != null">#{email},</if>
            <if test="phonenumber != null">#{phonenumber},</if>
            <if test="sex != null">#{sex},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="password != null">#{password},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="loginIp != null">#{loginIp},</if>
            <if test="loginDate != null">#{loginDate},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="remark != null">#{remark},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="userWxOpenId != null">#{userWxOpenId},</if>
            <if test="flyNumber != null">#{flyNumber},</if>
            <if test="flyTime != null">#{flyTime},</if>
            <if test="carrierCode != null">#{carrierCode},</if>
            <if test="nationality != null">#{nationality},</if>
            <if test="roleType != null">#{roleType},</if>
         </trim>
    </insert>

    <update id="updateSysUser" parameterType="SysUser">
        update sys_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="deptId != null">dept_id = #{deptId},</if>
            <if test="userName != null and userName != ''">user_name = #{userName},</if>
            <if test="nickName != null and nickName != ''">nick_name = #{nickName},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="email != null">email = #{email},</if>
            <if test="phonenumber != null">phonenumber = #{phonenumber},</if>
            <if test="sex != null">sex = #{sex},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="password != null">password = #{password},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="loginIp != null">login_ip = #{loginIp},</if>
            <if test="loginDate != null">login_date = #{loginDate},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userWxOpenId != null">user_wx_open_id = #{userWxOpenId},</if>
            <if test="flyNumber != null">fly_number = #{flyNumber},</if>
            <if test="flyTime != null">fly_time = #{flyTime},</if>
            <if test="carrierCode != null">carrier_code = #{carrierCode},</if>
            <if test="nationality != null">nationality = #{nationality},</if>
            <if test="roleType != null">role_type = #{roleType},</if>
        </trim>
        where user_id = #{userId}
    </update>

    <delete id="deleteSysUserByUserId" parameterType="Long">
        delete from sys_user where user_id = #{userId}
    </delete>

    <delete id="deleteSysUserByUserIds" parameterType="String">
        delete from sys_user where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
</mapper>
