<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.WxUserMapper">
    <resultMap type="WxUser" id="WxUserResult">
        <result property="userId" column="user_id"/>
        <result property="openId" column="open_id"/>
        <result property="userName" column="user_name"/>
        <result property="avatar" column="avatar"/>
        <result property="userStatus" column="user_status"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyName" column="company_name"/>
        <result property="phoneNumber" column="phone_number"/>
        <result property="remark" column="remark"/>
        <result property="flyTime" column="fly_time"/>
        <result property="flyNumber" column="fly_number"/>
    </resultMap>

    <sql id="selectWxUserVo">
        select user_id,
               open_id,
               user_name,
               avatar,
               user_status,
               company_code,
               company_name,
               phone_number,
               remark,
               fly_time,
               fly_number
        from oc_wx_user
    </sql>

    <sql id="selectWxUserVo2">
        select u.user_id,
               u.open_id,
               u.user_name,
               u.avatar,
               u.user_status,
               u.company_code,
               u.company_name,
               u.phone_number,
               u.remark,
               u.fly_time,
               u.fly_number
        from oc_wx_user u
    </sql>

    <select id="selectWxUsersByRoleId" resultMap="WxUserResult">
        select u.user_id, u.user_name
        from oc_wx_user u
                 left join oc_wx_user_role ur on u.user_id = ur.user_id
        where ur.role_id = #{roleId}
          and u.company_code = #{companyCode}
          and u.user_status = 1
    </select>

    <select id="selectWxUserByIds" parameterType="Long" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        where user_id in
        <foreach item="userId" collection="array" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </select>

    <select id="selectList" parameterType="WxUser" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        <where>
            <if test="userId != null">and user_id = #{userId}</if>
            <if test="openId != null">and open_id = #{openId}</if>
            <if test="userName != null  and userName != ''">and user_name like concat('%', #{userName}, '%')</if>
            <if test="userStatus != null">and user_status = #{userStatus}</if>
            <if test="companyCode != null  and companyCode != ''">and company_code = #{companyCode}</if>
            <if test="companyName != null  and companyName != ''">and company_name = #{companyName}</if>
            <if test="phoneNumber != null  and phoneNumber != ''">and phone_number = #{phoneNumber}</if>
        </where>
    </select>

    <select id="selectByUserId" parameterType="long" resultMap="WxUserResult">
        <include refid="selectWxUserVo"/>
        where user_id = #{userId}
    </select>

    <select id="selectPilotList" parameterType="queryPilotQualificationDTO" resultMap="WxUserResult">
        <include refid="selectWxUserVo2"/>
        left join oc_wx_user_role ur on u.user_id = ur.user_id
        left join oc_wx_role r on r.role_id = ur.role_id
        <where>
            u.company_code = #{companyCode}
            <if test="userName != null and userName != ''">and u.user_name like concat('%', #{userName}, '%')</if>
            <choose>
                <when test="roleKey != null and roleKey != ''">and r.role_key = #{roleKey}</when>
                <otherwise>and r.role_key in('pilot','maintenance')</otherwise>
            </choose>
        </where>
    </select>

    <update id="updateWxUser" parameterType="WxUser">
        update oc_wx_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="userName != null">user_name = #{userName},</if>
            <if test="userStatus != null">user_status = #{userStatus},</if>
            <if test="flyTime != null">fly_time = #{flyTime},</if>
            <if test="flyNumber != null">fly_number = #{flyNumber},</if>
        </trim>
        where user_id = #{userId}
    </update>

</mapper>