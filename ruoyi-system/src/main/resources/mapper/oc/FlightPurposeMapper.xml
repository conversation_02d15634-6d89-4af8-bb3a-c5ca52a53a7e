<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.FlightPurposeMapper">

    <resultMap type="FlightPurpose" id="FlightPurposeResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="companyCode" column="company_code"/>
    </resultMap>

    <sql id="selectFlightPurposeVo">
        select id, name, company_code
        from oc_flight_purpose
    </sql>

    <select id="selectList" parameterType="FlightPurpose" resultMap="FlightPurposeResult">
        <include refid="selectFlightPurposeVo"/>
        <where>
            <if test="id != null  and id != ''">and id = #{id}</if>
            <if test="name != null  and name != ''">and name = #{name}</if>
            <if test="companyCode != null  and companyCode != ''">and company_code = #{companyCode}</if>
        </where>
    </select>
    <select id="selectOneById" parameterType="Long" resultMap="FlightPurposeResult">
        <include refid="selectFlightPurposeVo"/>
        where id = #{id}
    </select>
    <insert id="insertOne" parameterType="FlightPurpose" useGeneratedKeys="true" keyProperty="id">
        insert into oc_flight_purpose
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="companyCode != null">company_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="companyCode != null">#{companyCode},</if>
        </trim>
    </insert>
    <update id="updateOne" parameterType="FlightPurpose">
        update oc_flight_purpose
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="companyCode != null">company_code = #{companyCode},</if>
        </trim>
        where id = #{id}
    </update>
    <delete id="deleteOneById" parameterType="Long">
        delete
        from oc_flight_purpose
        where id = #{id}
    </delete>
    <delete id="deleteAllByIds" parameterType="String">
        delete from oc_flight_purpose where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
