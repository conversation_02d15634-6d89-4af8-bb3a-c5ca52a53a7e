<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.FlightTaskBookMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.oc.FlightTaskBook">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="aircraft_type" jdbcType="VARCHAR" property="aircraftType" />
    <result column="registration_number" jdbcType="VARCHAR" property="registrationNumber" />
    <result column="call_sign" jdbcType="VARCHAR" property="callSign" />
    <result column="transponder_code" jdbcType="VARCHAR" property="transponderCode" />
    <result column="flight_date" jdbcType="VARCHAR" property="flightDate" />
    <result column="departure" jdbcType="VARCHAR" property="departure" />
    <result column="planned_time" jdbcType="VARCHAR" property="plannedTime" />
    <result column="first_landing_point" jdbcType="VARCHAR" property="firstLandingPoint" />
    <result column="estimated_time" jdbcType="VARCHAR" property="estimatedTime" />
    <result column="fuel_endurance" jdbcType="VARCHAR" property="fuelEndurance" />
    <result column="circuit_speed" jdbcType="VARCHAR" property="circuitSpeed" />
    <result column="captain" jdbcType="VARCHAR" property="captain" />
    <result column="phone" jdbcType="VARCHAR" property="phone" />
    <result column="address" jdbcType="VARCHAR" property="address" />
    <result column="issue_date" jdbcType="VARCHAR" property="issueDate" />
    <result column="task_book_number" jdbcType="VARCHAR" property="taskBookNumber" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="task_status" jdbcType="INTEGER" property="taskStatus" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
    <result column="version_number" jdbcType="VARCHAR" property="versionNumber" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, aircraft_type, registration_number, call_sign, transponder_code, flight_date, 
    departure, planned_time, first_landing_point, estimated_time, fuel_endurance, circuit_speed, 
    captain, phone, address, issue_date, task_book_number, created_at, updated_at, task_status, 
    company_code, version_number
  </sql>
  <select id="selectByExample" parameterType="com.ruoyi.system.domain.oc.FlightTaskBookExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from flight_task_book
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by #{orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from flight_task_book
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from flight_task_book
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ruoyi.system.domain.oc.FlightTaskBookExample">
    delete from flight_task_book
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ruoyi.system.domain.oc.FlightTaskBook">
    insert into flight_task_book (id, aircraft_type, registration_number, 
      call_sign, transponder_code, flight_date, 
      departure, planned_time, first_landing_point, 
      estimated_time, fuel_endurance, circuit_speed, 
      captain, phone, address, 
      issue_date, task_book_number, created_at, 
      updated_at, task_status, company_code, 
      version_number)
    values (#{id,jdbcType=BIGINT}, #{aircraftType,jdbcType=VARCHAR}, #{registrationNumber,jdbcType=VARCHAR}, 
      #{callSign,jdbcType=VARCHAR}, #{transponderCode,jdbcType=VARCHAR}, #{flightDate,jdbcType=VARCHAR}, 
      #{departure,jdbcType=VARCHAR}, #{plannedTime,jdbcType=VARCHAR}, #{firstLandingPoint,jdbcType=VARCHAR}, 
      #{estimatedTime,jdbcType=VARCHAR}, #{fuelEndurance,jdbcType=VARCHAR}, #{circuitSpeed,jdbcType=VARCHAR}, 
      #{captain,jdbcType=VARCHAR}, #{phone,jdbcType=VARCHAR}, #{address,jdbcType=VARCHAR}, 
      #{issueDate,jdbcType=VARCHAR}, #{taskBookNumber,jdbcType=VARCHAR}, #{createdAt,jdbcType=TIMESTAMP}, 
      #{updatedAt,jdbcType=TIMESTAMP}, #{taskStatus,jdbcType=INTEGER}, #{companyCode,jdbcType=VARCHAR}, 
      #{versionNumber,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.ruoyi.system.domain.oc.FlightTaskBook">
    insert into flight_task_book
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="aircraftType != null">
        aircraft_type,
      </if>
      <if test="registrationNumber != null">
        registration_number,
      </if>
      <if test="callSign != null">
        call_sign,
      </if>
      <if test="transponderCode != null">
        transponder_code,
      </if>
      <if test="flightDate != null">
        flight_date,
      </if>
      <if test="departure != null">
        departure,
      </if>
      <if test="plannedTime != null">
        planned_time,
      </if>
      <if test="firstLandingPoint != null">
        first_landing_point,
      </if>
      <if test="estimatedTime != null">
        estimated_time,
      </if>
      <if test="fuelEndurance != null">
        fuel_endurance,
      </if>
      <if test="circuitSpeed != null">
        circuit_speed,
      </if>
      <if test="captain != null">
        captain,
      </if>
      <if test="phone != null">
        phone,
      </if>
      <if test="address != null">
        address,
      </if>
      <if test="issueDate != null">
        issue_date,
      </if>
      <if test="taskBookNumber != null">
        task_book_number,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="taskStatus != null">
        task_status,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
      <if test="versionNumber != null">
        version_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="aircraftType != null">
        #{aircraftType,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="callSign != null">
        #{callSign,jdbcType=VARCHAR},
      </if>
      <if test="transponderCode != null">
        #{transponderCode,jdbcType=VARCHAR},
      </if>
      <if test="flightDate != null">
        #{flightDate,jdbcType=VARCHAR},
      </if>
      <if test="departure != null">
        #{departure,jdbcType=VARCHAR},
      </if>
      <if test="plannedTime != null">
        #{plannedTime,jdbcType=VARCHAR},
      </if>
      <if test="firstLandingPoint != null">
        #{firstLandingPoint,jdbcType=VARCHAR},
      </if>
      <if test="estimatedTime != null">
        #{estimatedTime,jdbcType=VARCHAR},
      </if>
      <if test="fuelEndurance != null">
        #{fuelEndurance,jdbcType=VARCHAR},
      </if>
      <if test="circuitSpeed != null">
        #{circuitSpeed,jdbcType=VARCHAR},
      </if>
      <if test="captain != null">
        #{captain,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        #{phone,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        #{address,jdbcType=VARCHAR},
      </if>
      <if test="issueDate != null">
        #{issueDate,jdbcType=VARCHAR},
      </if>
      <if test="taskBookNumber != null">
        #{taskBookNumber,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="taskStatus != null">
        #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="versionNumber != null">
        #{versionNumber,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ruoyi.system.domain.oc.FlightTaskBookExample" resultType="java.lang.Long">
    select count(*) from flight_task_book
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update flight_task_book
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.aircraftType != null">
        aircraft_type = #{row.aircraftType,jdbcType=VARCHAR},
      </if>
      <if test="row.registrationNumber != null">
        registration_number = #{row.registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.callSign != null">
        call_sign = #{row.callSign,jdbcType=VARCHAR},
      </if>
      <if test="row.transponderCode != null">
        transponder_code = #{row.transponderCode,jdbcType=VARCHAR},
      </if>
      <if test="row.flightDate != null">
        flight_date = #{row.flightDate,jdbcType=VARCHAR},
      </if>
      <if test="row.departure != null">
        departure = #{row.departure,jdbcType=VARCHAR},
      </if>
      <if test="row.plannedTime != null">
        planned_time = #{row.plannedTime,jdbcType=VARCHAR},
      </if>
      <if test="row.firstLandingPoint != null">
        first_landing_point = #{row.firstLandingPoint,jdbcType=VARCHAR},
      </if>
      <if test="row.estimatedTime != null">
        estimated_time = #{row.estimatedTime,jdbcType=VARCHAR},
      </if>
      <if test="row.fuelEndurance != null">
        fuel_endurance = #{row.fuelEndurance,jdbcType=VARCHAR},
      </if>
      <if test="row.circuitSpeed != null">
        circuit_speed = #{row.circuitSpeed,jdbcType=VARCHAR},
      </if>
      <if test="row.captain != null">
        captain = #{row.captain,jdbcType=VARCHAR},
      </if>
      <if test="row.phone != null">
        phone = #{row.phone,jdbcType=VARCHAR},
      </if>
      <if test="row.address != null">
        address = #{row.address,jdbcType=VARCHAR},
      </if>
      <if test="row.issueDate != null">
        issue_date = #{row.issueDate,jdbcType=VARCHAR},
      </if>
      <if test="row.taskBookNumber != null">
        task_book_number = #{row.taskBookNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.taskStatus != null">
        task_status = #{row.taskStatus,jdbcType=INTEGER},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
      <if test="row.versionNumber != null">
        version_number = #{row.versionNumber,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update flight_task_book
    set id = #{row.id,jdbcType=BIGINT},
      aircraft_type = #{row.aircraftType,jdbcType=VARCHAR},
      registration_number = #{row.registrationNumber,jdbcType=VARCHAR},
      call_sign = #{row.callSign,jdbcType=VARCHAR},
      transponder_code = #{row.transponderCode,jdbcType=VARCHAR},
      flight_date = #{row.flightDate,jdbcType=VARCHAR},
      departure = #{row.departure,jdbcType=VARCHAR},
      planned_time = #{row.plannedTime,jdbcType=VARCHAR},
      first_landing_point = #{row.firstLandingPoint,jdbcType=VARCHAR},
      estimated_time = #{row.estimatedTime,jdbcType=VARCHAR},
      fuel_endurance = #{row.fuelEndurance,jdbcType=VARCHAR},
      circuit_speed = #{row.circuitSpeed,jdbcType=VARCHAR},
      captain = #{row.captain,jdbcType=VARCHAR},
      phone = #{row.phone,jdbcType=VARCHAR},
      address = #{row.address,jdbcType=VARCHAR},
      issue_date = #{row.issueDate,jdbcType=VARCHAR},
      task_book_number = #{row.taskBookNumber,jdbcType=VARCHAR},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      task_status = #{row.taskStatus,jdbcType=INTEGER},
      company_code = #{row.companyCode,jdbcType=VARCHAR},
      version_number = #{row.versionNumber,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.system.domain.oc.FlightTaskBook">
    update flight_task_book
    <set>
      <if test="aircraftType != null">
        aircraft_type = #{aircraftType,jdbcType=VARCHAR},
      </if>
      <if test="registrationNumber != null">
        registration_number = #{registrationNumber,jdbcType=VARCHAR},
      </if>
      <if test="callSign != null">
        call_sign = #{callSign,jdbcType=VARCHAR},
      </if>
      <if test="transponderCode != null">
        transponder_code = #{transponderCode,jdbcType=VARCHAR},
      </if>
      <if test="flightDate != null">
        flight_date = #{flightDate,jdbcType=VARCHAR},
      </if>
      <if test="departure != null">
        departure = #{departure,jdbcType=VARCHAR},
      </if>
      <if test="plannedTime != null">
        planned_time = #{plannedTime,jdbcType=VARCHAR},
      </if>
      <if test="firstLandingPoint != null">
        first_landing_point = #{firstLandingPoint,jdbcType=VARCHAR},
      </if>
      <if test="estimatedTime != null">
        estimated_time = #{estimatedTime,jdbcType=VARCHAR},
      </if>
      <if test="fuelEndurance != null">
        fuel_endurance = #{fuelEndurance,jdbcType=VARCHAR},
      </if>
      <if test="circuitSpeed != null">
        circuit_speed = #{circuitSpeed,jdbcType=VARCHAR},
      </if>
      <if test="captain != null">
        captain = #{captain,jdbcType=VARCHAR},
      </if>
      <if test="phone != null">
        phone = #{phone,jdbcType=VARCHAR},
      </if>
      <if test="address != null">
        address = #{address,jdbcType=VARCHAR},
      </if>
      <if test="issueDate != null">
        issue_date = #{issueDate,jdbcType=VARCHAR},
      </if>
      <if test="taskBookNumber != null">
        task_book_number = #{taskBookNumber,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="taskStatus != null">
        task_status = #{taskStatus,jdbcType=INTEGER},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
      <if test="versionNumber != null">
        version_number = #{versionNumber,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.system.domain.oc.FlightTaskBook">
    update flight_task_book
    set aircraft_type = #{aircraftType,jdbcType=VARCHAR},
      registration_number = #{registrationNumber,jdbcType=VARCHAR},
      call_sign = #{callSign,jdbcType=VARCHAR},
      transponder_code = #{transponderCode,jdbcType=VARCHAR},
      flight_date = #{flightDate,jdbcType=VARCHAR},
      departure = #{departure,jdbcType=VARCHAR},
      planned_time = #{plannedTime,jdbcType=VARCHAR},
      first_landing_point = #{firstLandingPoint,jdbcType=VARCHAR},
      estimated_time = #{estimatedTime,jdbcType=VARCHAR},
      fuel_endurance = #{fuelEndurance,jdbcType=VARCHAR},
      circuit_speed = #{circuitSpeed,jdbcType=VARCHAR},
      captain = #{captain,jdbcType=VARCHAR},
      phone = #{phone,jdbcType=VARCHAR},
      address = #{address,jdbcType=VARCHAR},
      issue_date = #{issueDate,jdbcType=VARCHAR},
      task_book_number = #{taskBookNumber,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      task_status = #{taskStatus,jdbcType=INTEGER},
      company_code = #{companyCode,jdbcType=VARCHAR},
      version_number = #{versionNumber,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
</mapper>