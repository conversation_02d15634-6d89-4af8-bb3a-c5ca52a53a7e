<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.FileDirMapper">
    <resultMap type="FileDir" id="FileDirResult">
        <result property="fileDirId" column="file_dir_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="ancestors" column="ancestors"/>
        <result property="fileDirName" column="file_dir_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="saveUrl" column="save_url"/>
        <result property="fileName" column="file_name"/>
        <result property="type" column="type"/>
        <result property="haveChildren" column="have_children"/>
    </resultMap>

    <sql id="selectFileDirVo">
        select file_dir_id,
               parent_id,
               ancestors,
               save_url,
               file_dir_name,
               order_num,
               create_by,
               create_time,
               update_by,
               update_time,
               file_name,
               type,
               have_children
        from oc_file_dir t
    </sql>

    <select id="selectFileDirList" parameterType="FileDir" resultMap="FileDirResult">
        <include refid="selectFileDirVo"/>
        <where>
            <if test="parentId != null">
                and parent_id = #{parentId}
            </if>
            <if test="fileDirId != null">
                and file_dir_id = #{fileDirId}
            </if>
            <if test="ancestors != null  and ancestors != ''">
                and ancestors = #{ancestors}
            </if>
            <if test="fileDirName != null  and fileDirName != ''">
                and file_dir_name like concat('%', #{fileDirName},
                                              '%')
            </if>
            <if test="orderNum != null">
                and order_num = #{orderNum}
            </if>
            <if test="saveUrl != null">
                and save_url = #{saveUrl}
            </if>
            <if test="fileName != null">
                and file_name like concat('%', #{fileName}, '%')
            </if>
            <if test="type != null">
                and t.type = #{type}
            </if>
            <if test="haveChildren != null">
                and have_children = #{haveChildren}
            </if>
        </where>
    </select>

    <select id="selectFileDirByFileDirId" parameterType="Long" resultMap="FileDirResult">
        <include refid="selectFileDirVo"/>
        where file_dir_id = #{fileDirId}
    </select>


    <select id="selectFileDirByParentId" parameterType="Long" resultMap="FileDirResult">
        <include refid="selectFileDirVo"/>
        where parent_id = #{parentId}
    </select>

    <insert id="insertFileDir" parameterType="FileDir" useGeneratedKeys="true" keyProperty="fileDirId">
        insert into oc_file_dir(
        <if test="parentId != null">
            parent_id,
        </if>
        <if test="ancestors != null">
            ancestors,
        </if>
        <if test="saveUrl != null">
            save_url,
        </if>
        <if test="fileDirName != null">
            file_dir_name,
        </if>
        <if test="orderNum != null">
            order_num,
        </if>
        <if test="createBy != null">
            create_by,
        </if>
        <if test="updateBy != null">
            update_by,
        </if>
        <if test="updateTime != null">
            update_time,
        </if>
        <if test="fileName != null">
            file_name,
        </if>
        <if test="type != null">
            type,
        </if>
        <if test="haveChildren != null">
            have_children,
        </if>
        create_time)values(
        <if test="parentId != null">
            #{parentId},
        </if>
        <if test="ancestors != null">
            #{ancestors},
        </if>
        <if test="saveUrl != null">
            #{saveUrl},
        </if>
        <if test="fileDirName != null">
            #{fileDirName},
        </if>
        <if test="orderNum != null">
            #{orderNum},
        </if>
        <if test="createBy != null">
            #{createBy},
        </if>
        <if test="updateBy != null">
            #{updateBy},
        </if>
        <if test="updateTime != null">
            #{updateTime},
        </if>
        <if test="fileName != null">
            #{fileName},
        </if>
        <if test="type != null">
            #{type},
        </if>
        <if test="haveChildren != null">
            #{haveChildren},
        </if>
        sysdate())
    </insert>

    <update id="updateFileDir" parameterType="FileDir">
        update oc_file_dir
        <set>
            <if test="parentId != null">
                parent_id = #{parentId},
            </if>
            <if test="ancestors != null">
                ancestors = #{ancestors},
            </if>
            <if test="saveUrl != null">
                save_url = #{saveUrl},
            </if>
            <if test="fileDirName != null">
                file_dir_name = #{fileDirName},
            </if>
            <if test="orderNum != null">
                order_num = #{orderNum},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
            <if test="fileName != null">
                file_name =#{fileDirName},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="haveChildren != null">
                have_children=#{haveChildren},
            </if>
            update_time = sysdate()
        </set>
        where file_dir_id = #{fileDirId}
    </update>

    <delete id="deleteFileDirByFileDirId" parameterType="Long">
        delete
        from oc_file_dir
        where file_dir_id = #{fileDirId}
    </delete>

    <delete id="deleteFileDirByFileDirIds" parameterType="String">
        delete
        from oc_file_dir where file_dir_id in
        <foreach item="fileDirId" collection="array" open="(" separator="," close=")">
            #{fileDirId}
        </foreach>
    </delete>
</mapper>
