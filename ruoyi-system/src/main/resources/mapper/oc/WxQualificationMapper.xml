<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.WxQualificationMapper">
    <resultMap type="WxQualification" id="WxQualificationResult">
        <result property="id" column="id"/>
        <result property="userId" column="user_id"/>
        <result property="validityOfLicense" column="validity_of_license"/>
        <result property="validityPeriodOfPhysicalExamination" column="validity_period_of_physical_examination"/>
        <result property="proficiencyCheckStandardMonth" column="proficiency_check_standard_month"/>
        <result property="proficiencyCheckValidity" column="proficiency_check_validity"/>
        <result property="validityPeriodOfRouteTrainingInspection" column="validity_period_of_route_training_inspection"/>
        <result property="dangerTrainingInspection" column="danger_training_inspection"/>
        <result property="chineseLanguageValidity" column="chinese_language_validity"/>
        <result property="validityOfRegistrationCertificate" column="validity_of_registration_certificate"/>
        <result property="y12Inspection" column="y12_validity_period_of_release_authorization"/>
        <result property="c208Inspection" column="c208_release_authorization_validity"/>
        <result property="b300Inspection" column="b300_release_authorization_validity"/>
        <result property="aircraftStyle" column="aircraft_style"/>
    </resultMap>

    <sql id="selectWxUserVo">
        select id,
               user_id,
               validity_of_license,
               validity_period_of_physical_examination,
               proficiency_check_standard_month,
               proficiency_check_validity,
               validity_period_of_route_training_inspection,
               danger_training_inspection,
               chinese_language_validity,
               validity_of_registration_certificate,
               y12_validity_period_of_release_authorization,
               c208_release_authorization_validity,
               b300_release_authorization_validity,
               aircraft_style
        from oc_wx_qualification
    </sql>

    <select id="selectByUserId" resultMap="WxQualificationResult">
        <include refid="selectWxUserVo"/>
        where user_id = #{userId}
    </select>

    <insert id="insertWxQualification" parameterType="WxQualification" useGeneratedKeys="true" keyProperty="id">
        insert into oc_wx_qualification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="validityOfLicense != null">validity_of_license,</if>
            <if test="validityPeriodOfPhysicalExamination != null">validity_period_of_physical_examination,</if>
            <if test="proficiencyCheckStandardMonth != null">proficiency_check_standard_month,</if>
            <if test="proficiencyCheckValidity != null">proficiency_check_validity,</if>
            <if test="validityPeriodOfRouteTrainingInspection != null">validity_period_of_route_training_inspection,</if>
            <if test="dangerTrainingInspection != null">danger_training_inspection,</if>
            <if test="chineseLanguageValidity != null">chinese_language_validity,</if>
            <if test="validityOfRegistrationCertificate != null">validity_of_registration_certificate,</if>
            <if test="y12Inspection != null">y12_validity_period_of_release_authorization,</if>
            <if test="c208Inspection != null">c208_release_authorization_validity,</if>
            <if test="b300Inspection != null">b300_release_authorization_validity,</if>
            <if test="aircraftStyle != null">aircraft_style,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="validityOfLicense != null">#{validityOfLicense},</if>
            <if test="validityPeriodOfPhysicalExamination != null">#{validityPeriodOfPhysicalExamination},</if>
            <if test="proficiencyCheckStandardMonth != null">#{proficiencyCheckStandardMonth},</if>
            <if test="proficiencyCheckValidity != null">#{proficiencyCheckValidity},</if>
            <if test="validityPeriodOfRouteTrainingInspection != null">#{validityPeriodOfRouteTrainingInspection},</if>
            <if test="dangerTrainingInspection != null">#{dangerTrainingInspection},</if>
            <if test="chineseLanguageValidity != null">#{chineseLanguageValidity},</if>
            <if test="validityOfRegistrationCertificate != null">#{validityOfRegistrationCertificate},</if>
            <if test="y12Inspection != null">#{y12Inspection},</if>
            <if test="c208Inspection != null">#{c208Inspection},</if>
            <if test="b300Inspection != null">#{b300Inspection},</if>
            <if test="aircraftStyle != null">#{aircraftStyle},</if>
        </trim>
    </insert>

    <update id="updateWxQualification" parameterType="WxQualification">
        update oc_wx_qualification
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="validityOfLicense != null">validity_of_license = #{validityOfLicense},</if>
            <if test="validityPeriodOfPhysicalExamination != null">validity_period_of_physical_examination = #{validityPeriodOfPhysicalExamination},</if>
            <if test="proficiencyCheckStandardMonth != null">proficiency_check_standard_month = #{proficiencyCheckStandardMonth},</if>
            <if test="proficiencyCheckValidity != null">proficiency_check_validity = #{proficiencyCheckValidity},</if>
            <if test="validityPeriodOfRouteTrainingInspection != null">validity_period_of_route_training_inspection = #{validityPeriodOfRouteTrainingInspection},</if>
            <if test="dangerTrainingInspection != null">danger_training_inspection = #{dangerTrainingInspection},</if>
            <if test="chineseLanguageValidity != null">chinese_language_validity = #{chineseLanguageValidity},</if>
            <if test="validityOfRegistrationCertificate != null">validity_of_registration_certificate = #{validityOfRegistrationCertificate},</if>
            <if test="y12Inspection != null">y12_validity_period_of_release_authorization = #{y12Inspection},</if>
            <if test="c208Inspection != null">c208_release_authorization_validity = #{c208Inspection},</if>
            <if test="b300Inspection != null">b300_release_authorization_validity = #{b300Inspection},</if>
            <if test="aircraftStyle != null">aircraft_style = #{aircraftStyle},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>