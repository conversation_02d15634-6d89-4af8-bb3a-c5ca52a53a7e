<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.CompanyMapper">

    <resultMap type="Company" id="CompanyResult">
        <result property="id" column="id"/>
        <result property="companyName" column="company_name"/>
        <result property="companyCode" column="company_code"/>
        <result property="companyType" column="company_type"/>
        <result property="existAdmin" column="exist_admin"/>
    </resultMap>

    <sql id="selectCompanyVo">
        select id, company_name, company_code, company_type, exist_admin
        from oc_company
    </sql>

    <select id="selectList" parameterType="Company" resultMap="CompanyResult">
        <include refid="selectCompanyVo"/>
        <where>
            <if test="id != null  and id != ''">and id = #{id}</if>
            <if test="companyName != null  and companyName != ''">and company_name = #{companyName}</if>
            <if test="companyCode != null  and companyCode != ''">and company_code = #{companyCode}</if>
            <if test="companyType != null">and company_type = #{companyType}</if>
            <if test="existAdmin != null">and exist_admin = #{existAdmin}</if>
        </where>
    </select>

    <select id="selectById" parameterType="Long" resultMap="CompanyResult">
        <include refid="selectCompanyVo"/>
        where id = #{id}
    </select>

    <select id="selectByCompanyCode" parameterType="string" resultMap="CompanyResult">
        <include refid="selectCompanyVo"/>
        where company_code = #{companyCode}
    </select>

    <insert id="insertCompany" parameterType="Company" useGeneratedKeys="true" keyProperty="id">
        insert into oc_company
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null">company_name,</if>
            <if test="companyCode != null">company_code,</if>
            <if test="companyType != null">company_type,</if>
            <if test="existAdmin != null">exist_admin,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null">#{companyName},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="companyType != null">#{companyType},</if>
            <if test="existAdmin != null">#{existAdmin},</if>
        </trim>
    </insert>

    <update id="updateCompany" parameterType="Company">
        update oc_company
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">company_name = #{companyName},</if>
            <if test="companyCode != null">company_code = #{companyCode},</if>
            <if test="companyType != null">company_type = #{companyType},</if>
            <if test="existAdmin != null">exist_admin = #{existAdmin},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteCompany" parameterType="Long">
        delete from oc_company where id = #{id}
    </delete>

</mapper>
