<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.WxRoleMapper">
    <resultMap type="WxRole" id="WxRoleResult">
        <result property="roleId" column="role_id"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleName" column="role_name"/>
    </resultMap>

    <sql id="selectWxRoleVo">
        select role_id, role_key, role_name
        from oc_wx_role
    </sql>

    <select id="selectWxRoleList" resultMap="WxRoleResult">
        <include refid="selectWxRoleVo"/>
    </select>

    <select id="selectRoleByUserId" parameterType="Long" resultMap="WxRoleResult">
        select r.role_id, r.role_name, r.role_key
        from oc_wx_role r
                 left join oc_wx_user_role ur on ur.role_id = r.role_id
        where ur.user_id = #{userId}
    </select>
</mapper>