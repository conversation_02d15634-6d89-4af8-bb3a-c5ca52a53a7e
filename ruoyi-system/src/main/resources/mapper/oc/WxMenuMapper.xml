<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.WxMenuMapper">

    <resultMap type="WxMenu" id="WxMenuResult">
        <result property="id"    column="id"    />
        <result property="menuCode" column="menu_code"    />
        <result property="menuName" column="menu_name"    />
    </resultMap>
    <select id="selectList" resultMap="WxMenuResult">
        select *
        from oc_wx_menu
    </select>

    <select id="selectMenuByUserId" parameterType="Long" resultMap="WxMenuResult">
        select r.id, r.menu_code, r.menu_name
        from oc_wx_menu r
                 left join oc_wx_user_menu ur on ur.menu_id = r.id
        where ur.user_id = #{userId}
    </select>

</mapper>
