<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.FlightTaskInfoMapper">
  <resultMap id="BaseResultMap" type="com.ruoyi.system.domain.oc.FlightTaskInfo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="batch" jdbcType="VARCHAR" property="batch" />
    <result column="task_type" jdbcType="VARCHAR" property="taskType" />
    <result column="route_or_airspace" jdbcType="VARCHAR" property="routeOrAirspace" />
    <result column="route_or_airspace_code" jdbcType="VARCHAR" property="routeOrAirspaceCode" />
    <result column="altitude" jdbcType="VARCHAR" property="altitude" />
    <result column="alternate_airport" jdbcType="VARCHAR" property="alternateAirport" />
    <result column="flight_rules" jdbcType="VARCHAR" property="flightRules" />
    <result column="captain_weather_standard" jdbcType="VARCHAR" property="captainWeatherStandard" />
    <result column="crew_role_1" jdbcType="VARCHAR" property="crewRole1" />
    <result column="crew_name_1" jdbcType="VARCHAR" property="crewName1" />
    <result column="crew_role_2" jdbcType="VARCHAR" property="crewRole2" />
    <result column="crew_name_2" jdbcType="VARCHAR" property="crewName2" />
    <result column="crew_passenger_count" jdbcType="VARCHAR" property="crewPassengerCount" />
    <result column="task_book_number" jdbcType="VARCHAR" property="taskBookNumber" />
    <result column="created_at" jdbcType="TIMESTAMP" property="createdAt" />
    <result column="updated_at" jdbcType="TIMESTAMP" property="updatedAt" />
    <result column="company_code" jdbcType="VARCHAR" property="companyCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, batch, task_type, route_or_airspace, route_or_airspace_code, altitude, alternate_airport,
    flight_rules, captain_weather_standard, crew_role_1, crew_name_1, crew_role_2, crew_name_2,
    crew_passenger_count, task_book_number, created_at, updated_at, company_code
  </sql>
  <select id="selectByExample" parameterType="com.ruoyi.system.domain.oc.FlightTaskInfoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from flight_task_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select
    <include refid="Base_Column_List" />
    from flight_task_info
    where id = #{id,jdbcType=BIGINT}
  </select>


  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from flight_task_info
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.ruoyi.system.domain.oc.FlightTaskInfoExample">
    delete from flight_task_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.ruoyi.system.domain.oc.FlightTaskInfo">
    insert into flight_task_info (id, batch, task_type,
      route_or_airspace, route_or_airspace_code, altitude,
      alternate_airport, flight_rules, captain_weather_standard,
      crew_role_1, crew_name_1, crew_role_2,
      crew_name_2, crew_passenger_count, task_book_number,
      created_at, updated_at, company_code
      )
    values (#{id,jdbcType=BIGINT}, #{batch,jdbcType=VARCHAR}, #{taskType,jdbcType=VARCHAR},
      #{routeOrAirspace,jdbcType=VARCHAR}, #{routeOrAirspaceCode,jdbcType=VARCHAR}, #{altitude,jdbcType=VARCHAR},
      #{alternateAirport,jdbcType=VARCHAR}, #{flightRules,jdbcType=VARCHAR}, #{captainWeatherStandard,jdbcType=VARCHAR},
      #{crewRole1,jdbcType=VARCHAR}, #{crewName1,jdbcType=VARCHAR}, #{crewRole2,jdbcType=VARCHAR},
      #{crewName2,jdbcType=VARCHAR}, #{crewPassengerCount,jdbcType=VARCHAR}, #{taskBookNumber,jdbcType=VARCHAR},
      #{createdAt,jdbcType=TIMESTAMP}, #{updatedAt,jdbcType=TIMESTAMP}, #{companyCode,jdbcType=VARCHAR}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.ruoyi.system.domain.oc.FlightTaskInfo">
    insert into flight_task_info
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="id != null">
        id,
      </if>
      <if test="batch != null">
        batch,
      </if>
      <if test="taskType != null">
        task_type,
      </if>
      <if test="routeOrAirspace != null">
        route_or_airspace,
      </if>
      <if test="routeOrAirspaceCode != null">
        route_or_airspace_code,
      </if>
      <if test="altitude != null">
        altitude,
      </if>
      <if test="alternateAirport != null">
        alternate_airport,
      </if>
      <if test="flightRules != null">
        flight_rules,
      </if>
      <if test="captainWeatherStandard != null">
        captain_weather_standard,
      </if>
      <if test="crewRole1 != null">
        crew_role_1,
      </if>
      <if test="crewName1 != null">
        crew_name_1,
      </if>
      <if test="crewRole2 != null">
        crew_role_2,
      </if>
      <if test="crewName2 != null">
        crew_name_2,
      </if>
      <if test="crewPassengerCount != null">
        crew_passenger_count,
      </if>
      <if test="taskBookNumber != null">
        task_book_number,
      </if>
      <if test="createdAt != null">
        created_at,
      </if>
      <if test="updatedAt != null">
        updated_at,
      </if>
      <if test="companyCode != null">
        company_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="id != null">
        #{id,jdbcType=BIGINT},
      </if>
      <if test="batch != null">
        #{batch,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="routeOrAirspace != null">
        #{routeOrAirspace,jdbcType=VARCHAR},
      </if>
      <if test="routeOrAirspaceCode != null">
        #{routeOrAirspaceCode,jdbcType=VARCHAR},
      </if>
      <if test="altitude != null">
        #{altitude,jdbcType=VARCHAR},
      </if>
      <if test="alternateAirport != null">
        #{alternateAirport,jdbcType=VARCHAR},
      </if>
      <if test="flightRules != null">
        #{flightRules,jdbcType=VARCHAR},
      </if>
      <if test="captainWeatherStandard != null">
        #{captainWeatherStandard,jdbcType=VARCHAR},
      </if>
      <if test="crewRole1 != null">
        #{crewRole1,jdbcType=VARCHAR},
      </if>
      <if test="crewName1 != null">
        #{crewName1,jdbcType=VARCHAR},
      </if>
      <if test="crewRole2 != null">
        #{crewRole2,jdbcType=VARCHAR},
      </if>
      <if test="crewName2 != null">
        #{crewName2,jdbcType=VARCHAR},
      </if>
      <if test="crewPassengerCount != null">
        #{crewPassengerCount,jdbcType=VARCHAR},
      </if>
      <if test="taskBookNumber != null">
        #{taskBookNumber,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCode != null">
        #{companyCode,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.ruoyi.system.domain.oc.FlightTaskInfoExample" resultType="java.lang.Long">
    select count(*) from flight_task_info
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update flight_task_info
    <set>
      <if test="row.id != null">
        id = #{row.id,jdbcType=BIGINT},
      </if>
      <if test="row.batch != null">
        batch = #{row.batch,jdbcType=VARCHAR},
      </if>
      <if test="row.taskType != null">
        task_type = #{row.taskType,jdbcType=VARCHAR},
      </if>
      <if test="row.routeOrAirspace != null">
        route_or_airspace = #{row.routeOrAirspace,jdbcType=VARCHAR},
      </if>
      <if test="row.routeOrAirspaceCode != null">
        route_or_airspace_code = #{row.routeOrAirspaceCode,jdbcType=VARCHAR},
      </if>
      <if test="row.altitude != null">
        altitude = #{row.altitude,jdbcType=VARCHAR},
      </if>
      <if test="row.alternateAirport != null">
        alternate_airport = #{row.alternateAirport,jdbcType=VARCHAR},
      </if>
      <if test="row.flightRules != null">
        flight_rules = #{row.flightRules,jdbcType=VARCHAR},
      </if>
      <if test="row.captainWeatherStandard != null">
        captain_weather_standard = #{row.captainWeatherStandard,jdbcType=VARCHAR},
      </if>
      <if test="row.crewRole1 != null">
        crew_role_1 = #{row.crewRole1,jdbcType=VARCHAR},
      </if>
      <if test="row.crewName1 != null">
        crew_name_1 = #{row.crewName1,jdbcType=VARCHAR},
      </if>
      <if test="row.crewRole2 != null">
        crew_role_2 = #{row.crewRole2,jdbcType=VARCHAR},
      </if>
      <if test="row.crewName2 != null">
        crew_name_2 = #{row.crewName2,jdbcType=VARCHAR},
      </if>
      <if test="row.crewPassengerCount != null">
        crew_passenger_count = #{row.crewPassengerCount,jdbcType=VARCHAR},
      </if>
      <if test="row.taskBookNumber != null">
        task_book_number = #{row.taskBookNumber,jdbcType=VARCHAR},
      </if>
      <if test="row.createdAt != null">
        created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.updatedAt != null">
        updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="row.companyCode != null">
        company_code = #{row.companyCode,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update flight_task_info
    set id = #{row.id,jdbcType=BIGINT},
      batch = #{row.batch,jdbcType=VARCHAR},
      task_type = #{row.taskType,jdbcType=VARCHAR},
      route_or_airspace = #{row.routeOrAirspace,jdbcType=VARCHAR},
      route_or_airspace_code = #{row.routeOrAirspaceCode,jdbcType=VARCHAR},
      altitude = #{row.altitude,jdbcType=VARCHAR},
      alternate_airport = #{row.alternateAirport,jdbcType=VARCHAR},
      flight_rules = #{row.flightRules,jdbcType=VARCHAR},
      captain_weather_standard = #{row.captainWeatherStandard,jdbcType=VARCHAR},
      crew_role_1 = #{row.crewRole1,jdbcType=VARCHAR},
      crew_name_1 = #{row.crewName1,jdbcType=VARCHAR},
      crew_role_2 = #{row.crewRole2,jdbcType=VARCHAR},
      crew_name_2 = #{row.crewName2,jdbcType=VARCHAR},
      crew_passenger_count = #{row.crewPassengerCount,jdbcType=VARCHAR},
      task_book_number = #{row.taskBookNumber,jdbcType=VARCHAR},
      created_at = #{row.createdAt,jdbcType=TIMESTAMP},
      updated_at = #{row.updatedAt,jdbcType=TIMESTAMP},
      company_code = #{row.companyCode,jdbcType=VARCHAR}
    <if test="example != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.ruoyi.system.domain.oc.FlightTaskInfo">
    update flight_task_info
    <set>
      <if test="batch != null">
        batch = #{batch,jdbcType=VARCHAR},
      </if>
      <if test="taskType != null">
        task_type = #{taskType,jdbcType=VARCHAR},
      </if>
      <if test="routeOrAirspace != null">
        route_or_airspace = #{routeOrAirspace,jdbcType=VARCHAR},
      </if>
      <if test="routeOrAirspaceCode != null">
        route_or_airspace_code = #{routeOrAirspaceCode,jdbcType=VARCHAR},
      </if>
      <if test="altitude != null">
        altitude = #{altitude,jdbcType=VARCHAR},
      </if>
      <if test="alternateAirport != null">
        alternate_airport = #{alternateAirport,jdbcType=VARCHAR},
      </if>
      <if test="flightRules != null">
        flight_rules = #{flightRules,jdbcType=VARCHAR},
      </if>
      <if test="captainWeatherStandard != null">
        captain_weather_standard = #{captainWeatherStandard,jdbcType=VARCHAR},
      </if>
      <if test="crewRole1 != null">
        crew_role_1 = #{crewRole1,jdbcType=VARCHAR},
      </if>
      <if test="crewName1 != null">
        crew_name_1 = #{crewName1,jdbcType=VARCHAR},
      </if>
      <if test="crewRole2 != null">
        crew_role_2 = #{crewRole2,jdbcType=VARCHAR},
      </if>
      <if test="crewName2 != null">
        crew_name_2 = #{crewName2,jdbcType=VARCHAR},
      </if>
      <if test="crewPassengerCount != null">
        crew_passenger_count = #{crewPassengerCount,jdbcType=VARCHAR},
      </if>
      <if test="taskBookNumber != null">
        task_book_number = #{taskBookNumber,jdbcType=VARCHAR},
      </if>
      <if test="createdAt != null">
        created_at = #{createdAt,jdbcType=TIMESTAMP},
      </if>
      <if test="updatedAt != null">
        updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      </if>
      <if test="companyCode != null">
        company_code = #{companyCode,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.ruoyi.system.domain.oc.FlightTaskInfo">
    update flight_task_info
    set batch = #{batch,jdbcType=VARCHAR},
      task_type = #{taskType,jdbcType=VARCHAR},
      route_or_airspace = #{routeOrAirspace,jdbcType=VARCHAR},
      route_or_airspace_code = #{routeOrAirspaceCode,jdbcType=VARCHAR},
      altitude = #{altitude,jdbcType=VARCHAR},
      alternate_airport = #{alternateAirport,jdbcType=VARCHAR},
      flight_rules = #{flightRules,jdbcType=VARCHAR},
      captain_weather_standard = #{captainWeatherStandard,jdbcType=VARCHAR},
      crew_role_1 = #{crewRole1,jdbcType=VARCHAR},
      crew_name_1 = #{crewName1,jdbcType=VARCHAR},
      crew_role_2 = #{crewRole2,jdbcType=VARCHAR},
      crew_name_2 = #{crewName2,jdbcType=VARCHAR},
      crew_passenger_count = #{crewPassengerCount,jdbcType=VARCHAR},
      task_book_number = #{taskBookNumber,jdbcType=VARCHAR},
      created_at = #{createdAt,jdbcType=TIMESTAMP},
      updated_at = #{updatedAt,jdbcType=TIMESTAMP},
      company_code = #{companyCode,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>

  <select id="selectByTaskBookNumber" resultMap="BaseResultMap">
      select
      <include refid="Base_Column_List" />
      from flight_task_info
      where task_book_number = #{taskBookNumber,jdbcType=VARCHAR}
      and company_code = #{companyCode,jdbcType=VARCHAR}
  </select>
</mapper>