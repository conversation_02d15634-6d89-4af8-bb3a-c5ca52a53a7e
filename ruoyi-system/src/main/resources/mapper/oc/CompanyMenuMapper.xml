<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.CompanyMenuMapper">
    <resultMap type="CompanyMenu" id="CompanyMenuResult">
        <result property="companyCode" column="company_code"/>
        <result property="menuId" column="menu_id"/>
    </resultMap>

    <sql id="selectCompanyMenuVo">
        select company_code, menu_id
        from oc_company_menu
    </sql>

    <select id="selectByCompanyCode" resultMap="CompanyMenuResult">
        <include refid="selectCompanyMenuVo"/>
        where company_code = #{companyCode}
    </select>

    <insert id="batchInsertCompanyMenu">
        insert into oc_company_menu(company_code, menu_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.companyCode}, #{item.menuId})
        </foreach>
    </insert>


    <delete id="deleteByCompanyCode">
        delete
        from oc_company_menu
        where company_code = #{companyCode}
    </delete>
</mapper>