<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.AirportBaseMapper">

    <resultMap type="AirportBase" id="AirportResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="threeAirportCode" column="three_airport_code"/>
        <result property="fourAirportCode" column="four_airport_code"/>
        <result property="companyCode" column="company_code"/>
    </resultMap>

    <sql id="selectAirportVo">
        select id, name, three_airport_code, four_airport_code, company_code
        from oc_airport
    </sql>

    <select id="selectList" parameterType="AirportBase" resultMap="AirportResult">
        <include refid="selectAirportVo"/>
        <where>
            <if test="id != null  and id != ''">and id = #{id}</if>
            <if test="name != null  and name != ''">and name = #{name}</if>
            <if test="threeAirportCode != null  and threeAirportCode != ''">and three_airport_code = #{threeAirportCode}</if>
            <if test="fourAirportCode != null  and fourAirportCode != ''">and four_airport_code = #{fourAirportCode}</if>
            <if test="companyCode != null  and companyCode != ''">and company_code = #{companyCode}</if>
        </where>
    </select>

    <select id="selectOneById" parameterType="Long" resultMap="AirportResult">
        <include refid="selectAirportVo"/>
        where id = #{id}
    </select>
    <insert id="insertOne" parameterType="AirportBase" useGeneratedKeys="true" keyProperty="id">
        insert into oc_airport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
            <if test="threeAirportCode != null">three_airport_code,</if>
            <if test="fourAirportCode != null">four_airport_code,</if>
            <if test="companyCode != null">company_code,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
            <if test="threeAirportCode != null">#{threeAirportCode},</if>
            <if test="fourAirportCode != null">#{fourAirportCode},</if>
            <if test="companyCode != null">#{companyCode},</if>
        </trim>
    </insert>
    <update id="updateOne" parameterType="AirportBase">
        update oc_airport
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
            <if test="threeAirportCode != null">three_airport_code = #{threeAirportCode},</if>
            <if test="fourAirportCode != null">four_airport_code = #{fourAirportCode},</if>
            <if test="companyCode != null">company_code = #{companyCode},</if>
        </trim>
        where id = #{id}
    </update>
    <delete id="deleteOneById" parameterType="Long">
        delete
        from oc_airport
        where id = #{id}
    </delete>
    <delete id="deleteAllByIds" parameterType="String">
        delete from oc_airport where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
