<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.DperationStandardMapper">

    <resultMap type="DperationStandard" id="DperationStandardResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
    </resultMap>

    <sql id="selectDperationStandardVo">
        select id, name
        from oc_dperation_standard
    </sql>

    <select id="selectList" parameterType="DperationStandard" resultMap="DperationStandardResult">
        <include refid="selectDperationStandardVo"/>
        <where>
            <if test="id != null  and id != ''">and id = #{id}</if>
            <if test="name != null  and name != ''">and name = #{name}</if>
        </where>
    </select>
    <select id="selectOneById" parameterType="Long" resultMap="DperationStandardResult">
        <include refid="selectDperationStandardVo"/>
        where id = #{id}
    </select>
    <insert id="insertOne" parameterType="DperationStandard" useGeneratedKeys="true" keyProperty="id">
        insert into oc_dperation_standard
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">name,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">#{name},</if>
        </trim>
    </insert>
    <update id="updateOne" parameterType="DperationStandard">
        update oc_dperation_standard
        <trim prefix="SET" suffixOverrides=",">
            <if test="name != null">name = #{name},</if>
        </trim>
        where id = #{id}
    </update>
    <delete id="deleteOneById" parameterType="Long">
        delete
        from oc_dperation_standard
        where id = #{id}
    </delete>
    <delete id="deleteAllByIds" parameterType="String">
        delete from oc_dperation_standard where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
