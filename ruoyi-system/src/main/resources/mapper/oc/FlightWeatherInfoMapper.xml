<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.FlightWeatherInfoMapper">
    <resultMap type="FlightWeatherDynamicVo" id="FlightWeatherDynamicVoResult">
        <result property="aircraftType" column="aircraft_type"/>
        <result property="registrationNumber" column="registration_number"/>
        <result property="flightDate" column="flight_date"/>
        <result property="taskBookNumber" column="task_book_number"/>
        <collection property="flightWeatherInfoVos" ofType="com.ruoyi.system.domain.oc.vo.FlightWeatherInfoVo">
            <result property="flightTaskInfoId" column="flight_task_info_id"/>
            <result property="batch" column="batch"/>
            <result property="taskBookNumber" column="task_book_number"/>
            <result property="crewName1" column="crew_name_1"/>
            <result property="crewName2" column="crew_name_2"/>
            <result property="crewRole1" column="crew_role_1"/>
            <result property="crewRole2" column="crew_role_2"/>
            <result property="locationType" column="location_type"/>
            <result property="locationName" column="location_name"/>
            <result property="weather" column="weather"/>
            <result property="cloudHeight" column="cloud_height"/>
            <result property="temperature" column="temperature"/>
            <result property="windDirection" column="wind_direction"/>
            <result property="windSpeed" column="wind_speed"/>
            <result property="visibility" column="visibility"/>
            <result property="qnh" column="qnh"/>
        </collection>
    </resultMap>

    <select id="selectFlightWeatherInfoById" resultMap="FlightWeatherDynamicVoResult" >
        SELECT ftb.aircraft_type,
               ftb.registration_number,
               ftb.flight_date,
               ftb.task_book_number,
               fti.batch,
               fti.crew_role_1,
               fti.crew_name_1,
               fti.crew_role_2,
               fti.crew_name_2,
               fti.id as flight_task_info_id,
               fti.task_book_number,
               ofwi.batch,
               ofwi.location_type,
               ofwi.location_name,
               ofwi.weather,
               ofwi.cloud_height,
               ofwi.temperature,
               ofwi.wind_direction,
               ofwi.wind_speed,
               ofwi.visibility,
               ofwi.qnh
        FROM flight_task_book ftb
                 LEFT JOIN flight_task_info fti ON fti.task_book_number = ftb.task_book_number
                 LEFT JOIN oc_flight_weather_info ofwi ON ofwi.batch = fti.batch
            AND fti.id = ofwi.flight_task_info_id
        WHERE ftb.id = #{id}
          AND ftb.company_code = #{companyCode}
        order by ofwi.batch ASC
    </select>
</mapper>