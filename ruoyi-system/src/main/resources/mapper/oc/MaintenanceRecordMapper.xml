<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.MaintenanceRecordMapper">

    <resultMap type="MaintenanceRecord" id="MaintenanceRecordResult">
        <result property="maintenanceRecordId" column="maintenance_record_id"/>
        <result property="aircraftId" column="aircraft_id"/>
        <result property="repairman" column="repairman"/>
        <result property="maintenanceTime" column="maintenance_time"/>
        <result property="maintenanceAirport" column="maintenance_airport"/>
        <result property="remarks" column="remarks"/>
    </resultMap>

    <sql id="selectMaintenanceRecordVo">
        select maintenance_record_id, aircraft_id, repairman, maintenance_time, maintenance_airport, remarks
        from oc_maintenance_record
    </sql>

    <select id="selectMaintenanceRecordList" parameterType="MaintenanceRecord" resultMap="MaintenanceRecordResult">
        <include refid="selectMaintenanceRecordVo"/>
        <where>
            <if test="aircraftId != null  and aircraftId != 0">and aircraft_id = #{aircraftId}</if>
            <if test="repairman != null  and repairman != ''">and repairman = #{repairman}</if>
            <if test="maintenanceTime != null  and maintenanceTime != ''">and maintenance_time = #{maintenanceTime}</if>
            <if test="maintenanceAirport != null  and maintenanceAirport != ''">and maintenance_airport = #{maintenanceAirport}
            </if>
        </where>
    </select>

    <select id="selectMaintenanceRecordByMaintenanceRecordId" parameterType="Long" resultMap="MaintenanceRecordResult">
        <include refid="selectMaintenanceRecordVo"/>
        where maintenance_record_id = #{maintenanceRecordId}
    </select>

    <insert id="insertMaintenanceRecord" parameterType="MaintenanceRecord" useGeneratedKeys="true"
            keyProperty="maintenanceRecordId">
        insert into oc_maintenance_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aircraftId != null">aircraft_id,</if>
            <if test="repairman != null">repairman,</if>
            <if test="maintenanceTime != null">maintenance_time,</if>
            <if test="maintenanceAirport != null">maintenance_airport,</if>
            <if test="remarks != null">remarks,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aircraftId != null">#{aircraftId},</if>
            <if test="repairman != null">#{repairman},</if>
            <if test="maintenanceTime != null">#{maintenanceTime},</if>
            <if test="maintenanceAirport != null">#{maintenanceAirport},</if>
            <if test="remarks != null">#{remarks},</if>
        </trim>
    </insert>

    <update id="updateMaintenanceRecord" parameterType="MaintenanceRecord">
        update oc_maintenance_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="aircraftId != null">aircraft_id = #{aircraftId},</if>
            <if test="repairman != null">repairman = #{repairman},</if>
            <if test="maintenanceTime != null">maintenance_time = #{maintenanceTime},</if>
            <if test="maintenanceAirport != null">maintenance_airport = #{maintenanceAirport},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where maintenance_record_id = #{maintenanceRecordId}
    </update>

    <delete id="deleteMaintenanceRecordByMaintenanceRecordId" parameterType="Long">
        delete
        from oc_maintenance_record
        where maintenance_record_id = #{maintenanceRecordId}
    </delete>

    <delete id="deleteMaintenanceRecordByMaintenanceRecordIds" parameterType="String">
        delete from oc_maintenance_record where maintenance_record_id in
        <foreach item="maintenanceRecordId" collection="array" open="(" separator="," close=")">
            #{maintenanceRecordId}
        </foreach>
    </delete>
</mapper>
