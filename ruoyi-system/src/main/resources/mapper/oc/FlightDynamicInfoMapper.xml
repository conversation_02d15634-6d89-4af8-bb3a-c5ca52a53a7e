<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.FlightDynamicInfoMapper">
    <select id="selectFlightDynamicInfoByTaskBookNumber"
            resultType="com.ruoyi.system.domain.oc.entity.FlightDynamicInfo">
        select *
        from oc_flight_dynamic_info
        where task_book_number = #{taskBookNumber}
          and company_code = #{companyCode}
    </select>
    <resultMap type="FlightDynamicVo" id="FlightDynamicVoResult">
        <result property="aircraftType" column="aircraft_type"/>
        <result property="registrationNumber" column="registration_number"/>
        <result property="flightDate" column="flight_date"/>
        <result property="taskBookNumber" column="task_book_number"/>
        <collection property="flightDynamicInfoVos" ofType="com.ruoyi.system.domain.oc.vo.FlightDynamicInfoVo">
            <result property="id" column="id"/>
            <result property="batch" column="batch"/>
            <result property="departureLocation" column="departure_location"/>
            <result property="arrivalLocation" column="arrival_location"/>
            <result property="carStartTime" column="car_start_time"/>
            <result property="takeOffTime" column="take_off_time"/>
            <result property="carStopTime" column="car_stop_time"/>
            <result property="groundTimeMin" column="ground_time_min"/>
            <result property="airTimeMin" column="air_time_min"/>
            <result property="totalTimeMin" column="total_time_min"/>
            <result property="sortieCount" column="sortie_count"/>
        </collection>
    </resultMap>
    <select id="selectFlightDynamicVoByTaskBookNumber" resultMap="FlightDynamicVoResult">
        SELECT ftb.aircraft_type,
               ftb.registration_number,
               ftb.flight_date,
               ftb.task_book_number,
               fti.batch,
               fti.task_book_number,
        ofdi.id,
               ofdi.batch,
               ofdi.departure_location,
               ofdi.arrival_location,
               ofdi.car_start_time,
               ofdi.take_off_time,
               ofdi.car_stop_time,
               ofdi.ground_time_min,
               ofdi.air_time_min,
               ofdi.total_time_min,
               ofdi.sortie_count
        FROM flight_task_book ftb
                 LEFT JOIN flight_task_info fti ON fti.task_book_number = ftb.task_book_number
                 LEFT JOIN oc_flight_dynamic_info ofdi ON ofdi.batch = fti.batch

        WHERE ftb.id = #{taskBookId}
          AND ftb.company_code = #{companyCode}
        order by fti.batch ASC
    </select>
</mapper>