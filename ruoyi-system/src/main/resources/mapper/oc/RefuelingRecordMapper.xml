<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.RefuelingRecordMapper">

    <resultMap type="RefuelingRecord" id="RefuelingRecordResult">
        <result property="refuelingRecordId" column="refueling_record_id"/>
        <result property="aircraftId" column="aircraft_id"/>
        <result property="oiler" column="oiler"/>
        <result property="oilQuantity" column="oil_quantity"/>
        <result property="refuelingTime" column="refueling_time"/>
        <result property="refuelingAirport" column="refueling_airport"/>
        <result property="remarks" column="remarks"/>
    </resultMap>

    <sql id="selectRefuelingRecordVo">
        select refueling_record_id, aircraft_id, oiler, oil_quantity, refueling_time, refueling_airport, remarks
        from oc_refueling_record
    </sql>

    <select id="selectRefuelingRecordList" parameterType="RefuelingRecord" resultMap="RefuelingRecordResult">
        <include refid="selectRefuelingRecordVo"/>
        <where>
            <if test="aircraftId != null  and aircraftId != 0">and aircraft_id = #{aircraftId}</if>
            <if test="oiler != null  and oiler != ''">and oiler = #{oiler}</if>
            <if test="oilQuantity != null  and oilQuantity != 0">and oil_quantity = #{oilQuantity}</if>
            <if test="refuelingTime != null  and refuelingTime != ''">and refueling_time = #{refuelingTime}</if>
            <if test="refuelingAirport != null  and refuelingAirport != ''">and refueling_airport = #{refuelingAirport}
            </if>
        </where>
    </select>

    <select id="selectRefuelingRecordByRefuelingRecordId" parameterType="Long" resultMap="RefuelingRecordResult">
        <include refid="selectRefuelingRecordVo"/>
        where refueling_record_id = #{refuelingRecordId}
    </select>

    <insert id="insertRefuelingRecord" parameterType="RefuelingRecord" useGeneratedKeys="true"
            keyProperty="refuelingRecordId">
        insert into oc_refueling_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aircraftId != null">aircraft_id,</if>
            <if test="oiler != null">oiler,</if>
            <if test="oilQuantity != null">oil_quantity,</if>
            <if test="refuelingTime != null">refueling_time,</if>
            <if test="refuelingAirport != null">refueling_airport,</if>
            <if test="remarks != null">remarks,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aircraftId != null">#{aircraftId},</if>
            <if test="oiler != null">#{oiler},</if>
            <if test="oilQuantity != null">#{oilQuantity},</if>
            <if test="refuelingTime != null">#{refuelingTime},</if>
            <if test="refuelingAirport != null">#{refuelingAirport},</if>
            <if test="remarks != null">#{remarks},</if>
        </trim>
    </insert>

    <update id="updateRefuelingRecord" parameterType="RefuelingRecord">
        update oc_refueling_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="aircraftId != null">aircraft_id = #{aircraftId},</if>
            <if test="oiler != null">oiler = #{oiler},</if>
            <if test="oilQuantity != null">oil_quantity = #{oilQuantity},</if>
            <if test="refuelingTime != null">refueling_time = #{refuelingTime},</if>
            <if test="refuelingAirport != null">refueling_airport = #{refuelingAirport},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
        </trim>
        where refueling_record_id = #{refuelingRecordId}
    </update>

    <delete id="deleteRefuelingRecordByRefuelingRecordId" parameterType="Long">
        delete
        from oc_refueling_record
        where refueling_record_id = #{refuelingRecordId}
    </delete>

    <delete id="deleteRefuelingRecordByRefuelingRecordIds" parameterType="String">
        delete from oc_refueling_record where refueling_record_id in
        <foreach item="refuelingRecordId" collection="array" open="(" separator="," close=")">
            #{refuelingRecordId}
        </foreach>
    </delete>
</mapper>
