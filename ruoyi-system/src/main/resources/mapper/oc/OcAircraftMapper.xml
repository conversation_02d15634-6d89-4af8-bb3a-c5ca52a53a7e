<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.OcAircraftMapper">

    <resultMap type="OcAircraft" id="OcAircraftResult">
        <result property="aircraftId"    column="aircraft_id"    />
        <result property="aircraftStyle"    column="aircraft_style"    />
        <result property="aircraftTailNo"    column="aircraft_tail_no"    />
        <result property="aircraftInfo"    column="aircraft_info"    />
        <result property="aircraftCompany"    column="aircraft_company"    />
        <result property="aircraftSeat"    column="aircraft_seat"    />
        <result property="aircraftLength"    column="aircraft_length"    />
        <result property="aircraftHeight"    column="aircraft_height"    />
        <result property="aircraftWidth"    column="aircraft_width"    />
        <result property="aircraftMfRange"    column="aircraft_mf_range"    />
        <result property="machineNumber"    column="machine_number"    />
        <result property="companyCode"    column="company_code"    />
    </resultMap>

    <sql id="selectAircraftVo">
        select aircraft_id, aircraft_style, aircraft_tail_no, aircraft_info, aircraft_company,
               aircraft_seat, aircraft_length, aircraft_height, aircraft_width, aircraft_mf_range,
               machine_number, company_code from oc_aircraft
    </sql>

    <select id="selectAircraftList" parameterType="OcAircraft" resultMap="OcAircraftResult">
        <include refid="selectAircraftVo"/>
        <where>
            <if test="aircraftStyle != null  and aircraftStyle != ''"> and aircraft_style = #{aircraftStyle}</if>
            <if test="aircraftTailNo != null  and aircraftTailNo != ''"> and aircraft_tail_no = #{aircraftTailNo}</if>
            <if test="aircraftInfo != null  and aircraftInfo != ''"> and aircraft_info = #{aircraftInfo}</if>
            <if test="aircraftCompany != null  and aircraftCompany != ''"> and aircraft_company = #{aircraftCompany}</if>
            <if test="aircraftSeat != null "> and aircraft_seat = #{aircraftSeat}</if>
            <if test="aircraftLength != null "> and aircraft_length = #{aircraftLength}</if>
            <if test="aircraftHeight != null "> and aircraft_height = #{aircraftHeight}</if>
            <if test="aircraftWidth != null "> and aircraft_width = #{aircraftWidth}</if>
            <if test="aircraftMfRange != null "> and aircraft_mf_range = #{aircraftMfRange}</if>
            <if test="machineNumber != null "> and machine_number = #{machineNumber}</if>
            <if test="companyCode != null "> and company_code = #{companyCode}</if>
        </where>
    </select>

</mapper>
