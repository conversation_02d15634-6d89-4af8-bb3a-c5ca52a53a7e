<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.WxUserMenuMapper">

    <resultMap type="WxUserMenu" id="WxUserMenuResult">
        <result property="id"    column="id"    />
        <result property="menuId" column="menu_id"    />
        <result property="userId" column="user_id"    />
    </resultMap>

    <insert id="batchInsert">
        insert into oc_wx_user_menu(user_id, menu_id) values
        <foreach item="item" index="index" collection="list" separator=",">
            (#{item.userId},#{item.menuId})
        </foreach>
    </insert>


    <delete id="deleteByUserId" parameterType="long">
        delete
        from oc_wx_user_menu
        where user_id = #{userId}
    </delete>

</mapper>
