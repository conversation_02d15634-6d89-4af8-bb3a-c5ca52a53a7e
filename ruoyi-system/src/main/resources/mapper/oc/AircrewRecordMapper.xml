<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.oc.AircrewRecordMapper">

    <resultMap type="AircrewRecord" id="AircrewRecordResult">
        <result property="aircrewRecordId"    column="aircrewRecord_id"    />
        <result property="userId"    column="user_id"    />
        <result property="firstFileType"    column="first_file_type"    />
        <result property="secondFileType"    column="second_file_type"    />
        <result property="fileName"    column="file_name"    />
        <result property="fileSuffix"    column="file_suffix"    />
        <result property="saveUrl"    column="save_url"    />
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="remark" column="remark"/>
    </resultMap>

    <sql id="selectAircrewRecordVo">
        select aircrewRecord_id, user_id, first_file_type, second_file_type, file_name, file_suffix, save_url,
               create_by, create_time, update_by, update_time, remark from oc_aircrew_record
    </sql>

    <select id="selectById" parameterType="Long" resultMap="AircrewRecordResult">
        <include refid="selectAircrewRecordVo"/>
        where aircrewRecord_id = #{aircrewRecordId}
    </select>
    <select id="selectByUserIdAndFirstFileType" resultMap="AircrewRecordResult">
        <include refid="selectAircrewRecordVo"/>
        where user_id = #{userId}
        and first_file_type = #{firstFileType}
    </select>

    <insert id="insertAircrewRecord" parameterType="AircrewRecord" useGeneratedKeys="true" keyProperty="aircrewRecordId">
        insert into oc_aircrew_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="firstFileType != null">first_file_type,</if>
            <if test="secondFileType != null">second_file_type,</if>
            <if test="fileName != null">file_name,</if>
            <if test="fileSuffix != null">file_suffix,</if>
            <if test="saveUrl != null">save_url,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="firstFileType != null">#{firstFileType},</if>
            <if test="secondFileType != null">#{secondFileType},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="fileSuffix != null">#{fileSuffix},</if>
            <if test="saveUrl != null">#{saveUrl},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
        </trim>
    </insert>

    <update id="updateAircrewRecord" parameterType="AircrewRecord">
        update oc_aircrew_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="firstFileType != null">first_file_type = #{firstFileType},</if>
            <if test="secondFileType != null">second_file_type = #{secondFileType},</if>
            <if test="fileName != null">file_name = #{fileName},</if>
            <if test="fileSuffix != null">file_suffix = #{fileSuffix},</if>
            <if test="saveUrl != null">save_url = #{saveUrl},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where aircrewRecord_id = #{aircrewRecordId}
    </update>

    <delete id="deleteById" parameterType="Long">
        delete from oc_aircrew_record where aircrewRecord_id = #{aircrewRecordId}
    </delete>
</mapper>
