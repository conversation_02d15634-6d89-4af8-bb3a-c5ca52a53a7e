<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.RWeatherresMapper">

    <resultMap type="RWeatherres" id="RWeatherresResult">
        <result property="id" column="id"/>
        <result property="decodeReportId" column="decodeReportId"/>
        <result property="intensity" column="intensity"/>
        <result property="description" column="description"/>
        <result property="phenomenon" column="phenomenon"/>
        <result property="other" column="other"/>
        <result property="colourType" column="colourType"/>
    </resultMap>

    <sql id="selectRWeatherresVo">
        select id, decodeReportId, intensity, description, phenomenon, other, colourType
        from r_weatherres
    </sql>

    <select id="selectRWeatherresList" parameterType="RWeatherres" resultMap="RWeatherresResult">
        <include refid="selectRWeatherresVo"/>
        <where>
            <if test="decodeReportId != null ">and decodeReportId = #{decodeReportId}</if>
            <if test="intensity != null  and intensity != ''">and intensity = #{intensity}</if>
            <if test="description != null  and description != ''">and description = #{description}</if>
            <if test="phenomenon != null  and phenomenon != ''">and phenomenon = #{phenomenon}</if>
            <if test="other != null  and other != ''">and other = #{other}</if>
            <if test="colourType != null  and colourType != ''">and colourType = #{colourType}</if>
        </where>
    </select>

    <select id="selectRWeatherresById" parameterType="Long" resultMap="RWeatherresResult">
        <include refid="selectRWeatherresVo"/>
        where id = #{id}
    </select>

    <insert id="insertRWeatherres" parameterType="RWeatherres" useGeneratedKeys="true" keyProperty="id">
        insert into r_weatherres
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId,</if>
            <if test="intensity != null">intensity,</if>
            <if test="description != null">description,</if>
            <if test="phenomenon != null">phenomenon,</if>
            <if test="other != null">other,</if>
            <if test="colourType != null">colourType,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">#{decodeReportId},</if>
            <if test="intensity != null">#{intensity},</if>
            <if test="description != null">#{description},</if>
            <if test="phenomenon != null">#{phenomenon},</if>
            <if test="other != null">#{other},</if>
            <if test="colourType != null">#{colourType},</if>
        </trim>
    </insert>
    <insert id="insertRWeatherresList">
        insert into r_weatherres (decodeReportId, intensity, description, phenomenon, other,colourType) values
        <foreach collection="param" item="item" index="index" separator=",">
            (#{item.decodeReportId}, #{item.intensity}, #{item.description}, #{item.phenomenon},
            #{item.other},#{item.colourType})
        </foreach>
    </insert>

    <update id="updateRWeatherres" parameterType="RWeatherres">
        update r_weatherres
        <trim prefix="SET" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId = #{decodeReportId},</if>
            <if test="intensity != null">intensity = #{intensity},</if>
            <if test="description != null">description = #{description},</if>
            <if test="phenomenon != null">phenomenon = #{phenomenon},</if>
            <if test="other != null">other = #{other},</if>
            <if test="colourType != null">colourType = #{colourType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRWeatherresById" parameterType="Long">
        delete
        from r_weatherres
        where id = #{id}
    </delete>

    <delete id="deleteRWeatherresByIds" parameterType="String">
        delete from r_weatherres where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
