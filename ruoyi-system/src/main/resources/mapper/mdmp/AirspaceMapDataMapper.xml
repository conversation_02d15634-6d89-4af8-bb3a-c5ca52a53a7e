<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.AirspaceMapDataMapper">

    <resultMap type="AirspaceMapData" id="AirspaceMapDataResult">
        <result property="id" column="id"/>
        <result property="airspaceId" column="airspaceId"/>
        <result property="value" column="color"/>
        <result property="index" column="sort"/>
    </resultMap>

    <sql id="selectAirspaceMapDataVo">
        select id, airspaceId, color as 'value',sort as 'index'
        from airspace_map_data
    </sql>

    <select id="selectAirspaceMapDataList" parameterType="AirspaceMapData" resultMap="AirspaceMapDataResult">
        <include refid="selectAirspaceMapDataVo"/>
        <where>
            <if test="airspaceId != null ">and airspaceId = #{airspaceId}</if>
            <if test="sort != null ">and sort = #{index}</if>
        </where>
    </select>

    <select id="selectAirspaceMapDataById" parameterType="Long" resultMap="AirspaceMapDataResult">
        <include refid="selectAirspaceMapDataVo"/>
        where id = #{id}
    </select>

    <select id="selectAirspaceMapData" resultMap="AirspaceMapDataResult">
        SELECT amd.*
        FROM airspace a
                 JOIN airspace_map_data amd ON a.id = amd.airspaceId
                 JOIN map_data md ON md.sort = amd.sort
        WHERE (a.airspaceType = 3 OR (a.airspaceType = 1 AND a.colorType = 55))
          AND md.longitudeStart &lt;= #{param.longitude}
          AND md.longitudeEnd >= #{param.longitude}
          AND md.latitudeStart &lt;= #{param.latitude}
          AND md.latitudeEnd >= #{param.latitude}
          AND md.pressureStart = #{param.altitude}
        UNION ALL
        SELECT amd.*
        FROM airspace a
                 JOIN airspace_map_data amd ON a.id = amd.airspaceId
                 JOIN map_data md ON md.sort = amd.sort
        WHERE a.airspaceType = 1
          AND a.colorType = 35
          AND md.longitudeStart &lt;= #{param.longitude}
          AND md.longitudeEnd >= #{param.longitude}
          AND md.latitudeStart &lt;= #{param.latitude}
          AND md.latitudeEnd >= #{param.latitude}
          AND md.pressureStart = #{param.altitude}
          AND NOT EXISTS(
                SELECT 1
                FROM airspace a2
                         JOIN airspace_map_data amd2 ON a2.id = amd2.airspaceId
                         JOIN map_data md2 ON md2.sort = amd2.sort
                WHERE (a2.airspaceType = 3 OR (a2.airspaceType = 1 AND a2.colorType = 55))
                  AND md2.longitudeStart &lt;= #{param.longitude}
                  AND md2.longitudeEnd >= #{param.longitude}
                  AND md2.latitudeStart &lt;= #{param.latitude}
                  AND md2.latitudeEnd >= #{param.latitude}
                  AND md2.pressureStart = #{param.altitude}
            )
            LIMIT 1
    </select>

    <insert id="insertAirspaceMapData" parameterType="AirspaceMapData">
        insert into airspace_map_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="airspaceId != null">airspaceId,</if>
            <if test="index != null">sort,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="airspaceId != null">#{airspaceId},</if>
            <if test="index != null">#{sort},</if>
        </trim>
    </insert>

    <insert id="insertAirspaceListMapData" useGeneratedKeys="true" keyProperty="id">
        insert into airspace_map_data(airspaceId,sort,color)
        values
        <foreach collection="airspaceMapDataList" item="item" index="index" separator=",">
            (#{item.airspaceId}, #{item.index}, #{item.value})
        </foreach>
    </insert>

    <update id="updateAirspaceMapData" parameterType="AirspaceMapData">
        update airspace_map_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="airspaceId != null">airspaceId = #{airspaceId},</if>
            <if test="index != null">sort = #{index},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAirspaceMapDataById" parameterType="Long">
        delete
        from airspace_map_data
        where id = #{id}
    </delete>

    <delete id="deleteAirspaceMapDataByIds" parameterType="String">
        delete from airspace_map_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteAirspaceMapDataByAirspaceId">
        delete
        from airspace_map_data
        where airspaceId = #{airspaceId}
    </delete>
</mapper>
