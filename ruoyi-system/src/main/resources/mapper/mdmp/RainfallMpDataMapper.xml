<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.RainfallMpDataMapper">

    <resultMap type="RainfallMpData" id="RainfallMpDataResult">
        <result property="dataValue" column="dataValue"/>
        <result property="fcstTimeSequence" column="fcstTimeSequence"/>
        <result property="reatimeTimeSequence" column="reatimeTimeSequence"/>
        <result property="longitudeMin" column="longitudeMin"/>
        <result property="longitudeMax" column="longitudeMax"/>
        <result property="latitude" column="latitude"/>
        <result property="altitude" column="altitude"/>
        <result property="addTime" column="addTime"/>
    </resultMap>

    <sql id="selectRainfallMpDataVo">
        select dataValue,
               fcstTimeSequence,
               reatimeTimeSequence,
               longitudeMin,
               longitudeMax,
               latitude,
               altitude,
               addTime
        from rainfall_mp_data
    </sql>

    <select id="selectRainfallMpDataList" parameterType="RainfallMpData" resultMap="RainfallMpDataResult">
        <include refid="selectRainfallMpDataVo"/>
        <where>
            <if test="dataValue != null ">and dataValue = #{dataValue}</if>
            <if test="fcstTimeSequence != null ">and fcstTimeSequence = #{fcstTimeSequence}</if>
            <if test="reatimeTimeSequence != null ">and reatimeTimeSequence = #{reatimeTimeSequence}</if>
            <if test="addTime != null ">and addTime = #{addTime}</if>
            <if test="longitudeMin != null ">and longitudeMin = #{longitudeMin}</if>
            <if test="longitudeMax != null ">and longitudeMax = #{longitudeMax}</if>
            <if test="latitude != null ">and latitude = #{latitude}</if>
            <if test="altitude != null ">and altitude = #{altitude}</if>
        </where>
    </select>

    <select id="selectRainfallMpDataById" parameterType="Long" resultMap="RainfallMpDataResult">
        <include refid="selectRainfallMpDataVo"/>
        where id = #{id}
    </select>
    <select id="selectOldVMpDataList" resultType="java.lang.Long">
        SELECT id
        from rainfall_mp_data
        WHERE fcstTimeSequence &lt; UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY));
    </select>

    <select id="selectRainfallMpDataByMeteInfo" resultType="com.ruoyi.system.domain.mdmp.RainfallMpData">
        <include refid="selectRainfallMpDataVo"/>
        where latitude=#{param.latitude}
        ORDER BY ABS(TIMESTAMPDIFF(SECOND, fcstTimeSequence, NOW())) ASC
        LIMIT 1;
    </select>

    <select id="getList" resultType="java.lang.Double">
        SELECT a.dataValue
        FROM (
                 SELECT DISTINCT m.id, u.dataValue
                 FROM map_data m
                          JOIN rainfall_mp_data u
                               ON m.latitudeStart = u.latitude
                                   AND m.longitudeStart BETWEEN u.longitudeMin AND u.longitudeMax
                          JOIN (
                     SELECT fcstTimeSequence,
                            MAX(addTime) AS latest_addTime
                     FROM rainfall_mp_data
                     where altitude = 0.0
                       and fcstTimeSequence = #{param.time}
                 ) latest_fcst
                               ON u.fcstTimeSequence = latest_fcst.fcstTimeSequence
                                   AND u.addTime = latest_fcst.latest_addTime
                 WHERE m.pressureStart = 950
                   AND u.altitude = 0.0
             ) a
    </select>
    <select id="getRainfallMpMapData" resultType="com.ruoyi.system.domain.mdmp.vo.MapDataVo">
        WITH latest_rainfall AS (
        SELECT rmd.*,
        MAX(addTime) OVER (PARTITION BY fcstTimeSequence) as max_addtime
        FROM rainfall_mp_data rmd
        WHERE fcstTimeSequence = #{time}
        <if test="param.mpMin != null ">and dataValue>= #{param.mpMin}</if>
        <if test="param.mpMax != null ">and dataValue&lt;= #{param.mpMax}</if>
        )
        SELECT md.sort AS 'index', lr.dataValue,
        55 AS statusCodes
        FROM latest_rainfall lr
        JOIN map_data md
        ON md.latitudeStart = lr.latitude
        AND md.longitudeStart BETWEEN lr.longitudeMin AND lr.longitudeMax
        WHERE lr.addTime = lr.max_addtime
        ORDER BY md.sort
    </select>
    <select id="getRainfallAlerts" resultType="com.ruoyi.system.domain.mdmp.vo.MapDataVo">
        SELECT m.sort as 'index',
        55 AS statusCodes
        FROM map_data m
        INNER JOIN
        rainfall_mp_data r
        ON
        m.latitudeStart = r.latitude
        AND m.longitudeStart BETWEEN r.longitudeMin AND r.longitudeMax
        WHERE m.latitudeStart = #{flightData.latitude}
        AND m.longitudeStart = #{flightData.longitude}
        AND m.pressureStart = (
        SELECT pressure
        FROM height_pressure
        WHERE heightStart
        &lt; #{flightData.elevation}
        AND heightEnd >= #{flightData.elevation}
        )
        AND r.fcstTimeSequence = #{time}
        AND r.addTime = (
        SELECT MAX(addTime)
        FROM rainfall_mp_data
        WHERE fcstTimeSequence = #{time}
        )
        <if test="aircraftMp.mpMin != null ">and dataValue>= #{aircraftMp.mpMin}</if>
        <if test="aircraftMp.mpMax != null ">and dataValue&lt;= #{aircraftMp.mpMax}</if>
        LIMIT 1
    </select>

    <insert id="insertRainfallMpData" parameterType="RainfallMpData" useGeneratedKeys="true" keyProperty="id">
        insert into rainfall_mp_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">dataValue,</if>
            <if test="fcstTimeSequence != null">fcstTimeSequence,</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence,</if>
            <if test="addTime != null">addTime,</if>
            <if test="longitudeMin != null">longitudeMin,</if>
            <if test="longitudeMax != null">longitudeMax,</if>
            <if test="latitude != null">latitude,</if>
            <if test="altitude != null">altitude,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">#{dataValue},</if>
            <if test="fcstTimeSequence != null">#{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">#{reatimeTimeSequence},</if>
            <if test="addTime != null">#{addTime},</if>
            <if test="longitudeMin != null">#{longitudeMin},</if>
            <if test="longitudeMax != null">#{longitudeMax},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="altitude != null">#{altitude},</if>
        </trim>
    </insert>

    <insert id="insertVisibilityMpDataList">
        insert into rainfall_mp_data
        (dataValue,fcstTimeSequence,reatimeTimeSequence,addTime,longitudeMin,longitudeMax,latitude,altitude)
        values
        <foreach collection="param" item="item" index="index" separator=",">
            (#{item.dataValue},#{item.fcstTimeSequence},#{item.reatimeTimeSequence},#{item.addTime},#{item.longitudeMin},#{item.longitudeMax},#{item.latitude},#{item.altitude})
        </foreach>
    </insert>

    <update id="updateRainfallMpData" parameterType="RainfallMpData">
        update rainfall_mp_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataValue != null">dataValue = #{dataValue},</if>
            <if test="fcstTimeSequence != null">fcstTimeSequence = #{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence = #{reatimeTimeSequence},</if>
            <if test="addTime != null">addTime = #{addTime},</if>
            <if test="longitudeMin != null">longitudeMin = #{longitudeMin},</if>
            <if test="longitudeMax != null">longitudeMax = #{longitudeMax},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="altitude != null">altitude = #{altitude},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRainfallMpDataById" parameterType="Long">
        delete
        from rainfall_mp_data
        where id = #{id}
    </delete>

    <delete id="deleteRainfallMpDataByIds" parameterType="String">
        delete from rainfall_mp_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteVMpDataByIds">
        delete from rainfall_mp_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteRainfallMpDataByFcstTimeSequence" parameterType="Long">
        delete
        from rainfall_mp_data
        where fcstTimeSequence = #{time}
    </delete>

    <delete id="deleteOldMpDataList" parameterType="Integer">
        delete
        from rainfall_mp_data
        WHERE addTime &lt; NOW() - INTERVAL 1 DAY
        ORDER BY fcstTimeSequence
            LIMIT 10000
    </delete>
</mapper>
