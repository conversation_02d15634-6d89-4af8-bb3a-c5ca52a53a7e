<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.NextDayFlightPlanMapper">

    <resultMap type="NextDayFlightPlan" id="NextDayFlightPlanResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="longTermFlightPlanId" column="longTermFlightPlanId"/>
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="taskType" column="taskType"/>
        <result property="flightDate" column="flightDate"/>
        <result property="temporary" column="temporary"/>
        <result property="remark" column="remark"/>
        <result property="refusalExplain" column="refusalExplain"/>
        <result property="isDelete" column="isDelete"/>
        <result property="auditor" column="auditor"/>
        <result property="auditTime" column="auditTime"/>
        <result property="creator" column="creator"/>
        <result property="creationTime" column="creationTime"/>
        <result property="contactName" column="contactName"/>
        <result property="contactPhone" column="contactPhone"/>
        <result property="serialNo" column="serialNo"/>
        <result property="status" column="status"/>
        <result property="answeringMachineCode" column="answeringMachineCode"/>
        <result property="explainDepartureTime" column="explainDepartureTime"/>
        <result property="explainArrivalTime" column="explainArrivalTime"/>
        <result property="radioFrequency" column="radioFrequency"/>
        <result property="isWork" column="isWork"/>
        <result property="agreementType" column="agreementType"/>
        <result property="agreementStartDate" column="agreementStartDate"/>
        <result property="agreementEndDate" column="agreementEndDate"/>
        <result property="planType" column="planType"/>
        <result property="deptCode" column="deptCode"/>

        <!--        * 计划 机型机号关联表-->
        <collection property="planModelAircraftList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanAircraftModelMapper.selectAircraftByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

        <!--        * 计划 机场关联表-->
        <collection property="flightPlanAirportList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanAirportMapper.selectAirportByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

        <!--        * 计划 航线关联表-->
        <collection property="flightPlanRouteList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanRouteMapper.selectRouteByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

        <!--        * 计划 附件关联表-->
        <collection property="flightPlanFileList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanFileMapper.selectFileByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>
        <!--        * 计划 作业区关联表-->
        <collection property="flightPlanWorkList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanWorkMapper.selectWorkByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

    </resultMap>


    <!--查询list-->
    <resultMap type="NextDayFlightPlan" id="NextDayFlightPlanListResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="longTermFlightPlanId" column="longTermFlightPlanId"/>
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="taskType" column="taskType"/>
        <result property="flightDate" column="flightDate"/>
        <result property="temporary" column="temporary"/>
        <result property="remark" column="remark"/>
        <result property="refusalExplain" column="refusalExplain"/>
        <result property="isDelete" column="isDelete"/>
        <result property="auditor" column="auditor"/>
        <result property="auditTime" column="auditTime"/>
        <result property="planType" column="planType"/>
        <result property="creator" column="creator"/>
        <result property="creationTime" column="creationTime"/>
        <result property="contactName" column="contactName"/>
        <result property="contactPhone" column="contactPhone"/>
        <result property="serialNo" column="serialNo"/>
        <result property="status" column="status"/>
        <result property="answeringMachineCode" column="answeringMachineCode"/>
        <result property="explainDepartureTime" column="explainDepartureTime"/>
        <result property="explainArrivalTime" column="explainArrivalTime"/>
        <result property="radioFrequency" column="radioFrequency"/>
        <result property="isWork" column="isWork"/>
        <result property="agreementType" column="agreementType"/>
        <result property="agreementStartDate" column="agreementStartDate"/>
        <result property="agreementEndDate" column="agreementEndDate"/>
        <result property="deptCode" column="deptCode"/>
        <!--        * 计划 机型机号关联表-->
        <collection property="planModelAircraftList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanAircraftModelMapper.selectAircraftByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>
        <!--        * 计划 附件关联表-->
        <collection property="flightPlanFileList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanFileMapper.selectFileByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>
    </resultMap>

    <sql id="selectNextDayFlightPlanVo">
        select id,
               name,
               2 as planType,
               longTermFlightPlanId,
               companyName,
               companyCode,
               taskType,
               flightDate, temporary, remark, refusalExplain, isDelete, auditor, auditTime, creator, creationTime, contactName, contactPhone, serialNo, status, answeringMachineCode, explainDepartureTime, explainArrivalTime, radioFrequency, isWork, agreementType, agreementStartDate, agreementEndDate, deptCode
        from next_day_flight_plan
    </sql>

    <select id="selectNextDayFlightPlanList" resultMap="NextDayFlightPlanListResult">
        <include refid="selectNextDayFlightPlanVo"/>
        <where>
            and isDelete =0
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="companyCode != null  and companyCode != ''">and companyCode = #{companyCode}</if>
            <if test="name != null  and name != ''">and name = #{name}</if>
            <if test="taskType != null  and taskType != ''">and taskType = #{taskType}</if>
            <if test="flightDate != null  and flightDate != ''">and flightDate = #{flightDate}</if>
            <if test="temporary != null ">and temporary = #{temporary}</if>
            <if test="contactName != null  and contactName != ''">and contactName like concat('%', #{contactName},
                '%')
            </if>
            <if test="contactPhone != null  and contactPhone != ''">and contactPhone = #{contactPhone}</if>
            <if test="serialNo != null  and serialNo != ''">and serialNo = #{serialNo}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="answeringMachineCode != null  and answeringMachineCode != ''">and answeringMachineCode =
                #{answeringMachineCode}
            </if>
            <if test="radioFrequency != null  and radioFrequency != ''">and radioFrequency = #{radioFrequency}</if>
            <if test="isWork != null ">and isWork = #{isWork}</if>
            <if test="deptCodeList != null and !deptCodeList.isEmpty()">
                and deptCode in
                <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
                    #{deptCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectNextDayFlightPlanById" parameterType="Long" resultMap="NextDayFlightPlanResult">
        <include refid="selectNextDayFlightPlanVo"/>
        where id = #{id} and isDelete=0
    </select>

    <select id="selectNextDayFlightPlanByInterval" resultMap="NextDayFlightPlanResult">
        <include refid="selectNextDayFlightPlanVo"/>
        where isDelete = 0
        and status in (1,2,3)
        and flightDate =#{flightDate}
        and explainDepartureTime &lt;=#{explainArrivalTime}
        and explainArrivalTime>=#{explainDepartureTime}
    </select>


    <select id="selectNextDayFlightPlanByIdAndStatus"
            parameterType="Long" resultMap="NextDayFlightPlanResult">
        <include refid="selectNextDayFlightPlanVo"/>
        where id = #{id} and status=1 and isDelete=0
    </select>

    <insert id="insertNextDayFlightPlan" parameterType="NextDayFlightPlan" useGeneratedKeys="true" keyProperty="id">
        insert into next_day_flight_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="longTermFlightPlanId != null">longTermFlightPlanId,</if>
            <if test="name != null">name,</if>
            <if test="companyName != null">companyName,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="taskType != null">taskType,</if>
            <if test="flightDate != null">flightDate,</if>
            <if test="temporary != null">temporary,</if>
            <if test="remark != null">remark,</if>
            <if test="refusalExplain != null">refusalExplain,</if>
            <if test="auditor != null">auditor,</if>
            <if test="auditTime != null">auditTime,</if>
            <if test="creator != null">creator,</if>
            <if test="creationTime != null">creationTime,</if>
            <if test="contactName != null">contactName,</if>
            <if test="contactPhone != null">contactPhone,</if>
            <if test="serialNo != null">serialNo,</if>
            <if test="status != null">status,</if>
            <if test="answeringMachineCode != null">answeringMachineCode,</if>
            <if test="explainDepartureTime != null">explainDepartureTime,</if>
            <if test="explainArrivalTime != null">explainArrivalTime,</if>
            <if test="radioFrequency != null">radioFrequency,</if>
            <if test="isWork != null">isWork,</if>

            <if test="agreementType != null">agreementType,</if>
            <if test="agreementStartDate != null">agreementStartDate,</if>
            <if test="agreementEndDate != null">agreementEndDate,</if>
            <if test="deptCode != null">deptCode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="longTermFlightPlanId != null">#{longTermFlightPlanId},</if>
            <if test="name != null">#{name},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="flightDate != null">#{flightDate},</if>
            <if test="temporary != null">#{temporary},</if>
            <if test="remark != null">#{remark},</if>
            <if test="refusalExplain != null">#{refusalExplain},</if>
            <if test="auditor != null">#{auditor},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="serialNo != null">#{serialNo},</if>
            <if test="status != null">#{status},</if>
            <if test="answeringMachineCode != null">#{answeringMachineCode},</if>
            <if test="explainDepartureTime != null">#{explainDepartureTime},</if>
            <if test="explainArrivalTime != null">#{explainArrivalTime},</if>
            <if test="radioFrequency != null">#{radioFrequency},</if>
            <if test="isWork != null">#{isWork},</if>
            <if test="agreementType != null">#{agreementType},</if>
            <if test="agreementStartDate != null">#{agreementStartDate},</if>
            <if test="agreementEndDate != null">#{agreementEndDate},</if>
            <if test="deptCode != null">#{deptCode},</if>
        </trim>
    </insert>

    <update id="updateNextDayFlightPlan" parameterType="NextDayFlightPlan">
        update next_day_flight_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="longTermFlightPlanId != null">longTermFlightPlanId = #{longTermFlightPlanId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="taskType != null">taskType = #{taskType},</if>
            <if test="flightDate != null">flightDate = #{flightDate},</if>
            <if test="temporary != null">temporary = #{temporary},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="refusalExplain != null">refusalExplain = #{refusalExplain},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
            <if test="auditor != null">auditor = #{auditor},</if>
            <if test="auditTime != null">auditTime = #{auditTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="creationTime != null">creationTime = #{creationTime},</if>
            <if test="contactName != null">contactName = #{contactName},</if>
            <if test="contactPhone != null">contactPhone = #{contactPhone},</if>
            <if test="serialNo != null">serialNo = #{serialNo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="answeringMachineCode != null">answeringMachineCode = #{answeringMachineCode},</if>
            <if test="explainDepartureTime != null">explainDepartureTime = #{explainDepartureTime},</if>
            <if test="explainArrivalTime != null">explainArrivalTime = #{explainArrivalTime},</if>
            <if test="radioFrequency != null">radioFrequency = #{radioFrequency},</if>
            <if test="isWork != null">isWork = #{isWork},</if>
            <if test="agreementType != null">agreementType = #{agreementType},</if>
            <if test="agreementStartDate != null">agreementStartDate = #{agreementStartDate},</if>
            <if test="agreementEndDate != null">agreementEndDate = #{agreementEndDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteNextDayFlightPlanById" parameterType="Long">
        delete
        from next_day_flight_plan
        where id = #{id}
    </delete>
    <update id="deleteNextDayFlightPlanByIds">
        update next_day_flight_plan set isDelete =1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
