<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.MpTimeListMapper">

    <resultMap type="MpTimeList" id="MpTimeListResult">
        <result property="id" column="id"/>
        <result property="fcstTimeSequence" column="fcstTimeSequence"/>
        <result property="reatimeTimeSequence" column="reatimeTimeSequence"/>
        <result property="mpTimeLineId" column="mpTimeLineId"/>
    </resultMap>

    <sql id="selectMpTimeListVo">
        select id, fcstTimeSequence, reatimeTimeSequence, mpTimeLineId
        from mp_time_list
    </sql>

    <select id="selectMpTimeListList" parameterType="MpTimeList" resultMap="MpTimeListResult">
        <include refid="selectMpTimeListVo"/>
        <where>
            <if test="fcstTimeSequence != null ">and fcstTimeSequence = #{fcstTimeSequence}</if>
            <if test="reatimeTimeSequence != null ">and reatimeTimeSequence = #{reatimeTimeSequence}</if>
            <if test="mpTimeLineId != null ">and mpTimeLineId = #{mpTimeLineId}</if>
        </where>
    </select>

    <select id="selectMpTimeListById" parameterType="Long" resultMap="MpTimeListResult">
        <include refid="selectMpTimeListVo"/>
        where id = #{id}
    </select>

    <select id="selectMpTimeListByMpTimeLine" resultType="MpTimeList">
        SELECT t1.*
        FROM mp_time_list t1
        WHERE t1.mpTimeLineId = (
            SELECT MAX(id)
            FROM mp_time_line
            WHERE dateType = #{dateType}
              and dataSource = #{dataSource}
              and altitude = #{altitude}
        )
    </select>

    <insert id="insertMpTimeList" parameterType="MpTimeList" useGeneratedKeys="true" keyProperty="id">
        insert into mp_time_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="fcstTimeSequence != null">fcstTimeSequence,</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence,</if>
            <if test="mpTimeLineId != null">mpTimeLineId,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="fcstTimeSequence != null">#{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">#{reatimeTimeSequence},</if>
            <if test="mpTimeLineId != null">#{mpTimeLineId},</if>
        </trim>
    </insert>

    <update id="updateMpTimeList" parameterType="MpTimeList">
        update mp_time_list
        <trim prefix="SET" suffixOverrides=",">
            <if test="fcstTimeSequence != null">fcstTimeSequence = #{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence = #{reatimeTimeSequence},</if>
            <if test="mpTimeLineId != null">mpTimeLineId = #{mpTimeLineId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMpTimeListById" parameterType="Long">
        delete
        from mp_time_list
        where id = #{id}
    </delete>

    <delete id="deleteMpTimeListByIds" parameterType="String">
        delete from mp_time_list where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteMpTimeListByFcstTimeSequence" parameterType="Long">
        delete
        from mp_time_list
        where fcstTimeSequence = #{time}
    </delete>
</mapper>
