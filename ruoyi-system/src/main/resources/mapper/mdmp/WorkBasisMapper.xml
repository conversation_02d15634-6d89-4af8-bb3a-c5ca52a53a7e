<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.WorkBasisMapper">

    <resultMap type="WorkBasis" id="WorkBasisResult">
        <result property="id" column="id"/>
        <result property="workType" column="workType"/>
        <result property="graphType" column="graphType"/>
        <result property="workOffset" column="workOffset"/>
        <result property="circleCenterLong" column="circleCenterLong"/>
        <result property="radius" column="radius"/>
        <result property="workName" column="workName"/>
        <result property="circleCenterLat" column="circleCenterLat"/>
        <result property="minHeight" column="minHeight"/>
        <result property="maxHeight" column="maxHeight"/>
        <result property="positiveOrNegative" column="positiveOrNegative"/>
        <result property="deptCode" column="deptCode"/>
        <collection property="workInBasisList"
                    select="com.ruoyi.system.mapper.mdmp.WorkInBasisMapper.selectWorkInBasisList"
                    column="{workBasisId=id}">
        </collection>
        <collection property="workBasisLongLatList"
                    select="com.ruoyi.system.mapper.mdmp.WorkBasisLongLatMapper.selectWorkBasisLongLatList"
                    column="{workBasisId=id}">
        </collection>

        <collection property="workMapDataList" select="com.ruoyi.system.mapper.mdmp.WorkMapDataMapper.selectAllByWorkId"
                    column="{workId=id}">
        </collection>

    </resultMap>


    <resultMap type="WorkBasis" id="WorkBasisResultList">
        <result property="id" column="id"/>
        <result property="workType" column="workType"/>
        <result property="graphType" column="graphType"/>
        <result property="workOffset" column="workOffset"/>
        <result property="circleCenterLong" column="circleCenterLong"/>
        <result property="radius" column="radius"/>
        <result property="workName" column="workName"/>
        <result property="circleCenterLat" column="circleCenterLat"/>
        <result property="minHeight" column="minHeight"/>
        <result property="maxHeight" column="maxHeight"/>
        <result property="positiveOrNegative" column="positiveOrNegative"/>
        <collection property="workBasisLongLatList"
                    select="com.ruoyi.system.mapper.mdmp.WorkBasisLongLatMapper.selectWorkBasisLongLatList"
                    column="{workBasisId=id}"/>
    </resultMap>


    <sql id="selectWorkBasisVo">
        select id,
               workType,
               graphType,
               graphType,
               workOffset,
               circleCenterLong,
               radius,
               workName,
               circleCenterLat,
               minHeight,
               maxHeight,
               positiveOrNegative,
               deptCode
        from work_basis
    </sql>

    <select id="selectWorkBasisList" parameterType="WorkBasis" resultMap="WorkBasisResultList">
        <include refid="selectWorkBasisVo"/>
        <where>
            <if test="workType != null ">and workType = #{graphType}</if>
            <if test="graphType != null ">and graphType = #{graphType}</if>
            <if test="workOffset != null ">and workOffset = #{workOffset}</if>
            <if test="positiveOrNegative != null ">and positiveOrNegative = #{positiveOrNegative}</if>
            <if test="circleCenterLong != null  and circleCenterLong != ''">and circleCenterLong = #{circleCenterLong}
            </if>
            <if test="radius != null  and radius != ''">and radius = #{radius}</if>
            <if test="workName != null  and workName != ''">and workName like concat('%', #{workName}, '%')</if>
            <if test="circleCenterLat != null  and circleCenterLat != ''">and circleCenterLat = #{circleCenterLat}</if>
            <if test="deptCodeList != null and !deptCodeList.isEmpty()">
                and deptCode in
                <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
                    #{deptCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectWorkBasisById" parameterType="Long" resultMap="WorkBasisResult">
        <include refid="selectWorkBasisVo"/>
        where id = #{id}
    </select>

    <insert id="insertWorkBasis" parameterType="WorkBasis" useGeneratedKeys="true" keyColumn="id" keyProperty="id">
        insert into work_basis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="workType != null">workType,</if>
            <if test="graphType != null">graphType,</if>
            <if test="workOffset != null">workOffset,</if>
            <if test="circleCenterLong != null">circleCenterLong,</if>
            <if test="radius != null">radius,</if>
            <if test="workName != null">workName,</if>
            <if test="circleCenterLat != null">circleCenterLat,</if>
            <if test="minHeight != null">minHeight,</if>
            <if test="maxHeight != null">maxHeight,</if>
            <if test="positiveOrNegative != null">positiveOrNegative,</if>
            <if test="deptCode != null">deptCode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="workType != null">#{workType},</if>
            <if test="graphType != null">#{graphType},</if>
            <if test="workOffset != null">#{workOffset},</if>
            <if test="circleCenterLong != null">#{circleCenterLong},</if>
            <if test="radius != null">#{radius},</if>
            <if test="workName != null">#{workName},</if>
            <if test="circleCenterLat != null">#{circleCenterLat},</if>
            <if test="minHeight != null">#{minHeight},</if>
            <if test="maxHeight != null">#{maxHeight},</if>
            <if test="positiveOrNegative != null">#{positiveOrNegative},</if>
            <if test="deptCode != null">#{deptCode},</if>
        </trim>
    </insert>

    <update id="updateWorkBasis" parameterType="WorkBasis">
        update work_basis
        <trim prefix="SET" suffixOverrides=",">
            <if test="workType != null">workType = #{workType},</if>
            <if test="graphType != null">graphType = #{graphType},</if>
            <if test="workOffset != null">workOffset = #{workOffset},</if>
            <if test="circleCenterLong != null">circleCenterLong = #{circleCenterLong},</if>
            <if test="radius != null">radius = #{radius},</if>
            <if test="workName != null">workName = #{workName},</if>
            <if test="circleCenterLat != null">circleCenterLat = #{circleCenterLat},</if>
            <if test="minHeight != null">minHeight = #{minHeight},</if>
            <if test="maxHeight != null">maxHeight = #{maxHeight},</if>
            <if test="positiveOrNegative != null">positiveOrNegative = #{positiveOrNegative},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWorkBasisById" parameterType="Long">
        delete
        from work_basis
        where id = #{id}
    </delete>

    <delete id="deleteWorkBasisByIds" parameterType="String">
        delete from work_basis where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
