<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.RRunwayrvrresMapper">

    <resultMap type="RRunwayrvrres" id="RRunwayrvrresResult">
        <result property="id" column="id"/>
        <result property="decodeReportId" column="decodeReportId"/>
        <result property="runwayNO" column="runwayNO"/>
        <result property="rvrMin" column="rvrMin"/>
        <result property="rvrMax" column="rvrMax"/>
        <result property="trend" column="trend"/>
        <result property="colourType" column="colourType"/>
    </resultMap>

    <sql id="selectRRunwayrvrresVo">
        select id, decodeReportId, runwayNO, rvrMin, rvrMax, trend,colourType
        from r_runwayrvrres
    </sql>

    <select id="selectRRunwayrvrresList" parameterType="RRunwayrvrres" resultMap="RRunwayrvrresResult">
        <include refid="selectRRunwayrvrresVo"/>
        <where>
            <if test="decodeReportId != null ">and decodeReportId = #{decodeReportId}</if>
            <if test="runwayNO != null  and runwayNO != ''">and runwayNO = #{runwayNO}</if>
            <if test="rvrMin != null ">and rvrMin = #{rvrMin}</if>
            <if test="rvrMax != null ">and rvrMax = #{rvrMax}</if>
            <if test="trend != null  and trend != ''">and trend = #{trend}</if>
            <if test="colourType != null  and colourType != ''">and colourType = #{colourType}</if>
        </where>
    </select>

    <select id="selectRRunwayrvrresById" parameterType="Long" resultMap="RRunwayrvrresResult">
        <include refid="selectRRunwayrvrresVo"/>
        where id = #{id}
    </select>

    <insert id="insertRRunwayrvrres" parameterType="RRunwayrvrres" useGeneratedKeys="true" keyProperty="id">
        insert into r_runwayrvrres
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId,</if>
            <if test="runwayNO != null">runwayNO,</if>
            <if test="rvrMin != null">rvrMin,</if>
            <if test="rvrMax != null">rvrMax,</if>
            <if test="trend != null">trend,</if>
            <if test="colourType != null">colourType,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">#{decodeReportId},</if>
            <if test="runwayNO != null">#{runwayNO},</if>
            <if test="rvrMin != null">#{rvrMin},</if>
            <if test="rvrMax != null">#{rvrMax},</if>
            <if test="trend != null">#{trend},</if>
            <if test="colourType != null">#{colourType},</if>
        </trim>
    </insert>
    <insert id="insertRRunwayrvrresList">
        insert into r_runwayrvrres (decodeReportId, runwayNO, rvrMin, rvrMax, trend,colourType)
        values
        <foreach collection="param" item="item" index="index" separator=",">
            (#{item.decodeReportId}, #{item.runwayNO}, #{item.rvrMin}, #{item.rvrMax}, #{item.trend},#{item.colourType})
        </foreach>
    </insert>

    <update id="updateRRunwayrvrres" parameterType="RRunwayrvrres">
        update r_runwayrvrres
        <trim prefix="SET" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId = #{decodeReportId},</if>
            <if test="runwayNO != null">runwayNO = #{runwayNO},</if>
            <if test="rvrMin != null">rvrMin = #{rvrMin},</if>
            <if test="rvrMax != null">rvrMax = #{rvrMax},</if>
            <if test="trend != null">trend = #{trend},</if>
            <if test="colourType != null">colourType = #{colourType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRRunwayrvrresById" parameterType="Long">
        delete
        from r_runwayrvrres
        where id = #{id}
    </delete>

    <delete id="deleteRRunwayrvrresByIds" parameterType="String">
        delete from r_runwayrvrres where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
