<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.RouteMapDataMapper">

    <resultMap type="RouteMapData" id="RouteMapDataResult">
        <result property="id" column="id"/>
        <result property="routeId" column="routeId"/>
        <result property="value" column="color"/>
        <result property="index" column="sort"/>
    </resultMap>

    <sql id="selectRouteMapDataVo">
        select id, routeId, color as 'value',sort as 'index'
        from route_map_data
    </sql>
    <insert id="insertAllRouteMapData">
        insert into route_map_data(routeId,color,sort)
        values
        <foreach collection="routeMapData" item="item" index="index" separator=",">
            (#{item.routeId},#{item.value},#{item.index})
        </foreach>
    </insert>
    <delete id="delByRouteId">
        delete
        from route_map_data
        where routeId = #{routeId}
    </delete>
    <select id="selectAllByRouteId" resultType="com.ruoyi.system.domain.mdmp.RouteMapData">
        <include refid="selectRouteMapDataVo"/>
        where routeId = #{routeId}
    </select>


</mapper>
