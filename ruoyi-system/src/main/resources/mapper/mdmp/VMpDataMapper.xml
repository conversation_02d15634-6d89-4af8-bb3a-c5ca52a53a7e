<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.VMpDataMapper">

    <resultMap type="VMpData" id="VMpDataResult">
        <result property="dataValue" column="dataValue"/>
        <result property="fcstTimeSequence" column="fcstTimeSequence"/>
        <result property="reatimeTimeSequence" column="reatimeTimeSequence"/>
        <result property="longitudeMin" column="longitudeMin"/>
        <result property="longitudeMax" column="longitudeMax"/>
        <result property="latitude" column="latitude"/>
        <result property="altitude" column="altitude"/>
        <result property="addTime" column="addTime"/>
    </resultMap>

    <sql id="selectVMpDataVo">
        select dataValue,
               fcstTimeSequence,
               reatimeTimeSequence,
               longitudeMin,
               longitudeMax,
               latitude,
               altitude,
               addTime
        from v_mp_data
    </sql>

    <select id="selectVMpDataList" parameterType="VMpData" resultMap="VMpDataResult">
        <include refid="selectVMpDataVo"/>
        <where>
            <if test="dataValue != null ">and dataValue = #{dataValue}</if>
            <if test="fcstTimeSequence != null ">and fcstTimeSequence = #{fcstTimeSequence}</if>
            <if test="reatimeTimeSequence != null ">and reatimeTimeSequence = #{reatimeTimeSequence}</if>
            <if test="addTime != null ">and addTime = #{addTime}</if>
            <if test="longitudeMin != null ">and longitudeMin = #{longitudeMin}</if>
            <if test="longitudeMax != null ">and longitudeMax = #{longitudeMax}</if>
            <if test="latitude != null ">and latitude = #{latitude}</if>
            <if test="altitude != null ">and altitude = #{altitude}</if>
        </where>
    </select>

    <select id="selectVMpDataById" parameterType="Long" resultMap="VMpDataResult">
        <include refid="selectVMpDataVo"/>
        where id = #{id}
    </select>

    <select id="selectVMpDataByMeteInfo" resultMap="VMpDataResult">
        <include refid="selectVMpDataVo"/>
        where latitude=#{param.latitude}
        and altitude=#{param.altitude}
        ORDER BY ABS(TIMESTAMPDIFF(SECOND, fcstTimeSequence, NOW())) ASC
        LIMIT 1;
    </select>

    <select id="selectOldVMpDataList" resultType="java.lang.Long">
        SELECT id
        from v_mp_data
        WHERE fcstTimeSequence &lt; UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY));
    </select>

    <select id="getList" resultMap="VMpDataResult">
        SELECT DISTINCT m.id, u.dataValue
        FROM map_data m
                 JOIN v_mp_data u
                      ON m.latitudeStart = u.latitude
                          AND m.longitudeStart BETWEEN u.longitudeMin AND u.longitudeMax
                 JOIN (
            SELECT fcstTimeSequence,
                   MAX(addTime) AS latest_addTime
            FROM v_mp_data
            where altitude = #{param.altitude}
              and fcstTimeSequence = #{param.time}
        ) latest_fcst
                      ON u.fcstTimeSequence = latest_fcst.fcstTimeSequence
                          AND u.addTime = latest_fcst.latest_addTime
        WHERE m.pressureStart = #{param.altitude}
          AND u.altitude = #{param.altitude}
    </select>

    <insert id="insertVMpData" parameterType="VMpData" useGeneratedKeys="true" keyProperty="id">
        insert into v_mp_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">dataValue,</if>
            <if test="fcstTimeSequence != null">fcstTimeSequence,</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence,</if>
            <if test="addTime != null">addTime,</if>
            <if test="longitudeMin != null">longitudeMin,</if>
            <if test="longitudeMax != null">longitudeMax,</if>
            <if test="latitude != null">latitude,</if>
            <if test="altitude != null">altitude,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">#{dataValue},</if>
            <if test="fcstTimeSequence != null">#{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">#{reatimeTimeSequence},</if>
            <if test="addTime != null">#{addTime},</if>
            <if test="longitudeMin != null">#{longitudeMin},</if>
            <if test="longitudeMax != null">#{longitudeMax},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="altitude != null">#{altitude},</if>
        </trim>
    </insert>

    <insert id="insertMpDataList">
        insert into v_mp_data
        (dataValue,fcstTimeSequence,reatimeTimeSequence,addTime,longitudeMin,longitudeMax,latitude,altitude)
        values
        <foreach collection="param" item="item" index="index" separator=",">
            (#{item.dataValue},#{item.fcstTimeSequence},#{item.reatimeTimeSequence},#{item.addTime},#{item.longitudeMin},#{item.longitudeMax},#{item.latitude},#{item.altitude})
        </foreach>
    </insert>

    <update id="updateVMpData" parameterType="VMpData">
        update v_mp_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataValue != null">dataValue = #{dataValue},</if>
            <if test="fcstTimeSequence != null">fcstTimeSequence = #{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence = #{reatimeTimeSequence},</if>
            <if test="addTime != null">addTime = #{addTime},</if>
            <if test="longitudeMin != null">longitudeMin = #{longitudeMin},</if>
            <if test="longitudeMax != null">longitudeMax = #{longitudeMax},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="altitude != null">altitude = #{altitude},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVMpDataById" parameterType="Long">
        delete
        from v_mp_data
        where id = #{id}
    </delete>

    <delete id="deleteVMpDataByIds" parameterType="String">
        delete from v_mp_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteMpDataByFcstTimeSequence">
        delete
        from v_mp_data
        where fcstTimeSequence = #{time}
          AND altitude = #{altitude}
    </delete>
    <delete id="deleteOldVMpDataList" parameterType="Integer">
        delete
        from v_mp_data
        WHERE addTime &lt; NOW() - INTERVAL 1 DAY
        ORDER BY fcstTimeSequence
            LIMIT 10000
    </delete>
</mapper>
