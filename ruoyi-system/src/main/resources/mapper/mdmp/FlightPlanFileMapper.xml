<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightPlanFileMapper">

    <resultMap type="FlightPlanFile" id="FlightPlanFileResult">
        <result property="id"    column="id"    />
        <result property="planType"    column="planType"    />
        <result property="flightPlanId"    column="flightPlanId"    />
        <result property="fileName"    column="fileName"    />
        <result property="filePath"    column="filePath"    />
        <result property="approvalNumber"    column="approvalNumber"    />
    </resultMap>

    <sql id="selectFlightPlanFileVo">
        select id, planType, flightPlanId, fileName, filePath, approvalNumber from flight_plan_file
    </sql>

    <select id="selectFlightPlanFileList" parameterType="FlightPlanFile" resultMap="FlightPlanFileResult">
        <include refid="selectFlightPlanFileVo"/>
        <where>
            <if test="planType != null "> and planType = #{planType}</if>
            <if test="flightPlanId != null "> and flightPlanId = #{flightPlanId}</if>
            <if test="fileName != null  and fileName != ''"> and fileName like concat('%', #{fileName}, '%')</if>
            <if test="filePath != null  and filePath != ''"> and filePath = #{filePath}</if>
            <if test="approvalNumber != null  and approvalNumber != ''"> and approvalNumber = #{approvalNumber}</if>
        </where>
    </select>

    <select id="selectFlightPlanFileById" parameterType="Long" resultMap="FlightPlanFileResult">
        <include refid="selectFlightPlanFileVo"/>
        where id = #{id}
    </select>

    <select id="selectFileByFlightPlanId"  resultMap="FlightPlanFileResult">
        <include refid="selectFlightPlanFileVo"/>
        where flightPlanId = #{flightPlanId}
        and planType=#{planType}
    </select>

    <insert id="insertFlightPlanFile" parameterType="FlightPlanFile">
        insert into flight_plan_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="planType != null">planType,</if>
            <if test="flightPlanId != null">flightPlanId,</if>
            <if test="fileName != null">fileName,</if>
            <if test="filePath != null">filePath,</if>
            <if test="approvalNumber != null">approvalNumber,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="planType != null">#{planType},</if>
            <if test="flightPlanId != null">#{flightPlanId},</if>
            <if test="fileName != null">#{fileName},</if>
            <if test="filePath != null">#{filePath},</if>
            <if test="approvalNumber != null">#{approvalNumber},</if>
         </trim>
    </insert>
    <insert id="insertAllFlightPlanFile">
        insert into flight_plan_file(planType,
        flightPlanId,fileName,filePath,approvalNumber)
        values
        <foreach item="item" index="index" collection="flightPlanFileList" separator=",">
            (#{item.planType},#{item.flightPlanId},#{item.fileName},#{item.filePath},#{item.approvalNumber})
        </foreach>

    </insert>

    <update id="updateFlightPlanFile" parameterType="FlightPlanFile">
        update flight_plan_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="planType != null">planType = #{planType},</if>
            <if test="flightPlanId != null">flightPlanId = #{flightPlanId},</if>
            <if test="fileName != null">fileName = #{fileName},</if>
            <if test="filePath != null">filePath = #{filePath},</if>
            <if test="approvalNumber != null">approvalNumber = #{approvalNumber},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFlightPlanFileById" parameterType="Long">
        delete from flight_plan_file where id = #{id}
    </delete>

    <delete id="deleteFlightPlanFileByIds" parameterType="Long">
        delete from flight_plan_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteAllByFlightPlanId">
        delete
        from flight_plan_file
        where flightPlanId = #{flightPlanId}
          and planType = #{planType}
    </delete>
</mapper>
