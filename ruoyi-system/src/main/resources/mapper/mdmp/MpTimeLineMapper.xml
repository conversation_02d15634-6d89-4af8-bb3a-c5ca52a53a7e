<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.MpTimeLineMapper">

    <resultMap type="MpTimeLine" id="MpTimeLineResult">
        <result property="id"    column="id"    />
        <result property="altitude"    column="altitude"    />
        <result property="dateType"    column="dateType"    />
        <result property="dataSource"    column="dataSource"    />
        <result property="addTime"    column="addTime"    />
    </resultMap>

    <sql id="selectMpTimeLineVo">
        select id, altitude, dateType, dataSource, addTime from mp_time_line
    </sql>

    <select id="selectMpTimeLineList" parameterType="MpTimeLine" resultMap="MpTimeLineResult">
        <include refid="selectMpTimeLineVo"/>
        <where>
            <if test="altitude != null "> and altitude = #{altitude}</if>
            <if test="dateType != null "> and dateType = #{dateType}</if>
            <if test="dataSource != null "> and dataSource = #{dataSource}</if>
            <if test="addTime != null "> and addTime = #{addTime}</if>
        </where>
    </select>

    <select id="selectMpTimeLineById" parameterType="Long" resultMap="MpTimeLineResult">
        <include refid="selectMpTimeLineVo"/>
        where id = #{id}
    </select>

    <insert id="insertMpTimeLine" parameterType="MpTimeLine" useGeneratedKeys="true" keyProperty="id">
        insert into mp_time_line
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="altitude != null">altitude,</if>
            <if test="dateType != null">dateType,</if>
            <if test="dataSource != null">dataSource,</if>
            <if test="addTime != null">addTime,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="altitude != null">#{altitude},</if>
            <if test="dateType != null">#{dateType},</if>
            <if test="dataSource != null">#{dataSource},</if>
            <if test="addTime != null">#{addTime},</if>
         </trim>
    </insert>

    <update id="updateMpTimeLine" parameterType="MpTimeLine">
        update mp_time_line
        <trim prefix="SET" suffixOverrides=",">
            <if test="altitude != null">altitude = #{altitude},</if>
            <if test="dateType != null">dateType = #{dateType},</if>
            <if test="dataSource != null">dataSource = #{dataSource},</if>
            <if test="addTime != null">addTime = #{addTime},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMpTimeLineById" parameterType="Long">
        delete from mp_time_line where id = #{id}
    </delete>

    <delete id="deleteMpTimeLineByIds" parameterType="String">
        delete from mp_time_line where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
