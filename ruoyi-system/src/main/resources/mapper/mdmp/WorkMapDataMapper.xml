<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.WorkMapDataMapper">

    <resultMap type="WorkMapData" id="WorkMapDataResult">
        <result property="id" column="id"/>
        <result property="workId" column="workId"/>
        <result property="value" column="color"/>
        <result property="index" column="sort"/>
        <result property="groupType" column="groupType"/>
    </resultMap>

    <sql id="selectWorkMapDataVo">
        select id, workId, color, sort, groupType
        from work_map_data
    </sql>
    <insert id="insertAllWorkMapData">
        insert into work_map_data(workId,color,sort,groupType)
        values
        <foreach collection="workMapData" item="item" index="index" separator=",">
            (#{item.workId},#{item.value},#{item.index},#{item.groupType})
        </foreach>
    </insert>
    <delete id="delByWorkId">
        delete
        from work_map_data
        where workId = #{workId}
    </delete>
    <select id="selectAllByWorkId" resultMap="WorkMapDataResult">
        <include refid="selectWorkMapDataVo"/>
        where workId = #{workId}
    </select>
    <select id="selectByIndex" resultType="com.ruoyi.system.domain.mdmp.WorkMapData">
        <include refid="selectWorkMapDataVo"/>
        WHERE sort = #{index}
    </select>
    <select id="selectWorkMap" resultMap="WorkMapDataResult">
        SELECT *
        FROM daily_flight_plan dfp
                 JOIN flight_plan_work fpw ON dfp.id = fpw.flightPlanId AND fpw.planType = 3
                 JOIN work_map_data wmd on wmd.workId = fpw.ID
                 JOIN flight_plan_aircraft_model fpam on dfp.id = fpam.flightPlanId and fpam.planType = 3
                 JOIN map_data md ON md.sort = wmd.sort
            and dfp.isDelete = 0 and dfp.status = 2
            and wmd.sort = #{index}
            and fpam.tailNumber = #{param.aircraftReg} and dfp.flightDate = #{param.flightDate} limit 1
    </select>


</mapper>
