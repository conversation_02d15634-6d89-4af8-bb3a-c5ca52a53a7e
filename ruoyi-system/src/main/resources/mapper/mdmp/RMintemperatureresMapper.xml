<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.RMintemperatureresMapper">

    <resultMap type="RMintemperatureres" id="RMintemperatureresResult">
        <result property="id" column="id"/>
        <result property="decodeReportId" column="decodeReportId"/>
        <result property="temperature" column="temperature"/>
        <result property="time" column="time"/>
        <result property="colourType" column="colourType"/>
    </resultMap>

    <sql id="selectRMintemperatureresVo">
        select id, decodeReportId, temperature, time, colourType
        from r_mintemperatureres
    </sql>

    <select id="selectRMintemperatureresList" parameterType="RMintemperatureres" resultMap="RMintemperatureresResult">
        <include refid="selectRMintemperatureresVo"/>
        <where>
            <if test="decodeReportId != null ">and decodeReportId = #{decodeReportId}</if>
            <if test="temperature != null ">and temperature = #{temperature}</if>
            <if test="time != null ">and time = #{time}</if>
            <if test="colourType != null ">and colourType = #{colourType}</if>
        </where>
    </select>

    <select id="selectRMintemperatureresById" parameterType="Long" resultMap="RMintemperatureresResult">
        <include refid="selectRMintemperatureresVo"/>
        where id = #{id}
    </select>

    <insert id="insertRMintemperatureres" parameterType="RMintemperatureres" useGeneratedKeys="true" keyProperty="id">
        insert into r_mintemperatureres
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId,</if>
            <if test="temperature != null">temperature,</if>
            <if test="time != null">time,</if>
            <if test="colourType != null">colourType,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">#{decodeReportId},</if>
            <if test="temperature != null">#{temperature},</if>
            <if test="time != null">#{time},</if>
            <if test="colourType != null">#{colourType},</if>
        </trim>
    </insert>

    <update id="updateRMintemperatureres" parameterType="RMintemperatureres">
        update r_mintemperatureres
        <trim prefix="SET" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId = #{decodeReportId},</if>
            <if test="temperature != null">temperature = #{temperature},</if>
            <if test="time != null">time = #{time},</if>
            <if test="colourType != null">colourType = #{colourType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRMintemperatureresById" parameterType="Long">
        delete
        from r_mintemperatureres
        where id = #{id}
    </delete>

    <delete id="deleteRMintemperatureresByIds" parameterType="String">
        delete from r_mintemperatureres where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
