<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.AircraftMapper">

    <resultMap type="Aircraft" id="AircraftResult">
        <result property="id" column="id"/>
        <result property="aircraftType" column="aircraftType"/>
        <result property="aircraftReg" column="aircraftReg"/>
        <result property="callSign" column="callSign"/>
        <result property="isCivil" column="isCivil"/>
        <result property="validityDate" column="validityDate"/>
        <result property="expirationDate" column="expirationDate"/>
    </resultMap>

    <sql id="selectAircraftVo">
        select id,
               aircraftType,
               aircraftReg,
               callSign,
               isCivil,
               validityDate,
               expirationDate,
               deptCode
        from aircraft
    </sql>

    <select id="selectAircraftList" parameterType="Aircraft" resultMap="AircraftResult">
        <include refid="selectAircraftVo"/>
        <where>
            <if test="aircraftType != null  and aircraftType != ''">and aircraftType = #{aircraftType}</if>
            <if test="aircraftReg != null  and aircraftReg != ''">and aircraftReg = #{aircraftReg}</if>
            <if test="callSign != null  and callSign != ''">and callSign = #{callSign}</if>
            <if test="isCivil != null">and isCivil = #{isCivil}</if>
            <if test="validityDate != null  and validityDate != ''">and validityDate = #{validityDate}</if>
            <if test="expirationDate != null  and expirationDate != ''">and expirationDate = #{expirationDate}</if>
            <if test="deptCodeList != null and !deptCodeList.isEmpty()">
                and deptCode in
                <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
                    #{deptCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectAircraftById" parameterType="Long" resultMap="AircraftResult">
        <include refid="selectAircraftVo"/>
        where id = #{id}
    </select>

    <select id="selectByAircraftId" resultMap="AircraftResult">
        SELECT t1.*
        FROM aircraft t1
                 JOIN aircraft_airspace t2
                      on t1.id = t2.aircraftId
        where t2.airspaceId = #{aircraftId}
    </select>

    <select id="checkAircraftUnique" resultType="com.ruoyi.system.domain.mdmp.Aircraft">
        select id,
               aircraftType,
               aircraftReg,
               callSign,
               isCivil,
               validityDate,
               expirationDate,
               deptCode
        from aircraft
        where aircraftReg = #{aircraftReg}
          and callSign = #{callSign}
    </select>
    <select id="selectAircraft" resultMap="AircraftResult">
        select *
        from aircraft
        where aircraftReg = #{aircraftReg}
          AND CURRENT_DATE BETWEEN validityDate AND expirationDate
    </select>
    <select id="checkAircraftOverlap" parameterType="Aircraft" resultMap="AircraftResult">
        <include refid="selectAircraftVo"/>
        where aircraftReg = #{aircraftReg}
        and callSign = #{callSign}
        and (
        (#{validityDate} between validityDate and expirationDate)
        or (#{expirationDate} between validityDate and expirationDate)
        or (validityDate between #{validityDate} and #{expirationDate})
        or (expirationDate between #{validityDate} and #{expirationDate})
        )
    </select>

    <insert id="insertAircraft" parameterType="Aircraft" useGeneratedKeys="true" keyProperty="id">
        insert into aircraft
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aircraftType != null">aircraftType,</if>
            <if test="aircraftReg != null">aircraftReg,</if>
            <if test="callSign != null">callSign,</if>
            <if test="isCivil != null">isCivil,</if>
            <if test="validityDate != null">validityDate,</if>
            <if test="expirationDate != null">expirationDate,</if>
            <if test="deptCode != null">deptCode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aircraftType != null">#{aircraftType},</if>
            <if test="aircraftReg != null">#{aircraftReg},</if>
            <if test="callSign != null">#{callSign},</if>
            <if test="isCivil != null">#{isCivil},</if>
            <if test="validityDate != null">#{validityDate},</if>
            <if test="expirationDate != null">#{expirationDate},</if>
            <if test="deptCode != null">#{deptCode},</if>
        </trim>
    </insert>

    <update id="updateAircraft" parameterType="Aircraft">
        update aircraft
        <trim prefix="SET" suffixOverrides=",">
            <if test="aircraftType != null">aircraftType = #{aircraftType},</if>
            <if test="aircraftReg != null">aircraftReg = #{aircraftReg},</if>
            <if test="callSign != null">callSign = #{callSign},</if>
            <if test="isCivil != null">isCivil = #{isCivil},</if>
            <if test="validityDate != null">validityDate = #{validityDate},</if>
            <if test="expirationDate != null">expirationDate = #{expirationDate},</if>
            <if test="deptCode != null">deptCode = #{deptCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAircraftById" parameterType="Long">
        delete
        from aircraft
        where id = #{id}
    </delete>

    <delete id="deleteAircraftByIds" parameterType="String">
        delete from aircraft where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
