<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightPlanRouteMapper">

    <resultMap type="FlightPlanRoute" id="FlightPlanRouteResult">
        <result property="id" column="id"/>
        <result property="flightPlanId" column="flightPlanId"/>
        <result property="routeId" column="routeId"/>
        <result property="routeCode" column="routeCode"/>
        <result property="remarks" column="remarks"/>
        <result property="planType" column="planType"/>
        <!-- 关联查询 -->
        <collection property="flightPlanRouteLongLatList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanRouteLongLatMapper.selectByFlightPlanRouteId"
                    column="{flightPlanRouteId=id}">
        </collection>

        <collection property="routeMapDataList"
                    select="com.ruoyi.system.mapper.mdmp.RouteMapDataMapper.selectAllByRouteId"
                    column="{routeId=id}">
        </collection>


    </resultMap>

    <sql id="selectFlightPlanRouteVo">
        select id,
               flightPlanId,
               routeCode,
               remarks,
               planType,
               routeId
        from flight_plan_route
    </sql>

    <select id="selectFlightPlanRouteList" parameterType="FlightPlanRoute" resultMap="FlightPlanRouteResult">
        <include refid="selectFlightPlanRouteVo"/>
        <where>
            <if test="flightPlanId != null ">and flightPlanId = #{flightPlanId}</if>
            <if test="routeCode != null  and routeCode != ''">and routeCode = #{routeCode}</if>
            <if test="remarks != null  and remarks != ''">and remarks like concat('%', #{remarks}, '%')</if>
        </where>
    </select>

    <select id="selectFlightPlanRouteById" parameterType="Long" resultMap="FlightPlanRouteResult">
        <include refid="selectFlightPlanRouteVo"/>
        where id = #{id}
    </select>

    <select id="selectRouteByFlightPlanId" resultMap="FlightPlanRouteResult">
        <include refid="selectFlightPlanRouteVo"/>
        where flightPlanId = #{flightPlanId}
        and planType=#{planType}
    </select>

    <select id="selectFlightPlanRouteByFlightPlanId" resultMap="FlightPlanRouteResult">
        <include refid="selectFlightPlanRouteVo"/>
        where flightPlanId = #{flightPlanId} and planType=#{planType}
    </select>

    <select id="selectByFlightPlanIdAndRouteCode" resultMap="FlightPlanRouteResult">
        <include refid="selectFlightPlanRouteVo"/>
        where flightPlanId = #{flightPlanId}
        and routeCode = #{routeCode}
    </select>

    <insert id="insertFlightPlanRoute" parameterType="FlightPlanRoute" useGeneratedKeys="true" keyProperty="id">
        insert into flight_plan_route
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flightPlanId != null">flightPlanId,</if>
            <if test="routeId != null and routeId != ''">routeId,</if>
            <if test="routeCode != null and routeCode != ''">routeCode,</if>
            <if test="remarks != null and remarks != ''">remarks,</if>
            <if test="planType != null and planType != ''">planType,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flightPlanId != null">#{flightPlanId},</if>
            <if test="routeId != null and routeId != ''">#{routeId},</if>
            <if test="routeCode != null and routeCode != ''">#{routeCode},</if>
            <if test="remarks != null and remarks != ''">#{remarks},</if>
            <if test="planType != null and planType != ''">#{planType},</if>
        </trim>
    </insert>
    <insert id="insertAllFlightPlanRoute">
        insert into flight_plan_route(flightPlanId,
        routeCode,remarks)
        values
        <foreach item="item" index="index" collection="flightPlanRouteList" separator=",">
            (#{item.flightPlanId},#{item.routeCode},#{item.remarks})
        </foreach>

    </insert>

    <update id="updateFlightPlanRoute" parameterType="FlightPlanRoute">
        update flight_plan_route
        <trim prefix="SET" suffixOverrides=",">
            <if test="flightPlanId != null">flightPlanId = #{flightPlanId},</if>
            <if test="routeCode != null and routeCode != ''">routeCode = #{routeCode},</if>
            <if test="remarks != null and remarks != ''">remarks = #{remarks},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFlightPlanRouteById" parameterType="String">
        delete
        from flight_plan_route
        where id = #{id}
    </delete>

    <delete id="deleteFlightPlanRouteByIds" parameterType="String">
        delete from flight_plan_route where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteAllByFlightPlanId">
        delete
        from flight_plan_route
        where flightPlanId = #{flightPlanId}
          and planType = #{planType}
    </delete>
</mapper>
