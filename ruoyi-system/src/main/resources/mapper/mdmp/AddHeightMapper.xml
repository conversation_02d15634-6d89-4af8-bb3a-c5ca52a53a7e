<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.AddHeightMapper">
    <resultMap type="AddHeight" id="AddHeightMap">
        <result property="id" column="id"/>
        <result property="enterLeaveId" column="enter_leave_id"/>
        <result property="attribute" column="attribute"/>
        <result property="height" column="height"/>
        <result property="heightType" column="height_type"/>
        <result property="keep" column="keep"/>
    </resultMap>

    <select id="queryByEnterLeaveId" resultMap="AddHeightMap">
        select id,enter_leave_id,attribute,height,height_type,keep
        from add_height
        where enter_leave_id = #{enterLeaveId}
    </select>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into add_height(id,enter_leave_id,attribute,height,height_type,keep)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.enterLeaveId},#{entity.attribute},#{entity.height},#{entity.heightType},#{entity.keep})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into add_height(id,enter_leave_id,attribute,height,height_type,keep)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.enterLeaveId},#{entity.attribute},#{entity.height},#{entity.heightType},#{entity.keep})
        </foreach>
        on duplicate key update
        id=values(id),
        enter_leave_id=values(enter_leave_id),
        attribute=values(attribute),
        height=values(height),
        height_type=values(height_type),
        keep=values(keep)
    </insert>

    <insert id="insert" parameterType="AddHeight" useGeneratedKeys="true" keyProperty="id">
        insert into add_height
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="enterLeaveId != null">enter_leave_id,</if>
            <if test="attribute != null">attribute,</if>
            <if test="height != null">height,</if>
            <if test="heightType != null">height_type,</if>
            <if test="keep != null">keep,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="enterLeaveId != null">#{enterLeaveId},</if>
            <if test="attribute != null">#{attribute},</if>
            <if test="height != null">#{height},</if>
            <if test="heightType != null">#{heightType},</if>
            <if test="keep != null">#{keep},</if>
        </trim>
    </insert>

    <update id="update" parameterType="AddHeight">
        update add_height
        <trim prefix="SET" suffixOverrides=",">
            <if test="attribute != null">attribute = #{attribute},</if>
            <if test="height != null">height = #{height},</if>
            <if test="heightType != null">height_type = #{heightType},</if>
            <if test="keep != null">keep = #{keep},</if>
        </trim>
        where id = #{id}
    </update>


    <!--通过主键删除-->
    <delete id="deleteByEnterLeaveId">
        delete from add_height where enter_leave_id = #{enterLeaveId}
    </delete>
</mapper>
