<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.WorkInBasisLongLatMapper">

    <resultMap type="WorkInBasisLongLat" id="WorkInBasisLongLatResult">
        <result property="id" column="id"/>
        <result property="workInBasisId" column="workInBasisId"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="height" column="height"/>
        <result property="sortNumber" column="sortNumber"/>
        <result property="doubleLongitude" column="doubleLongitude"/>
        <result property="doubleLatitude" column="doubleLatitude"/>
        <result property="coordinateName" column="coordinateName"/>
    </resultMap>

    <sql id="selectWorkInBasisLongLatVo">
        select id,
               workInBasisId,
               longitude,
               latitude,
               height,
               sortNumber,
               doubleLongitude,
               doubleLatitude,
               coordinateName
        from work_in_basis_long_lat
    </sql>

    <select id="selectWorkInBasisLongLatList" parameterType="WorkInBasisLongLat" resultMap="WorkInBasisLongLatResult">
        <include refid="selectWorkInBasisLongLatVo"/>
        <where>
            <if test="workInBasisId != null ">and workInBasisId = #{workInBasisId}</if>
            <if test="longitude != null  and longitude != ''">and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''">and latitude = #{latitude}</if>
            <if test="height != null ">and height = #{height}</if>
            <if test="sortNumber != null ">and sortNumber = #{sortNumber}</if>
            <if test="doubleLongitude != null ">and doubleLongitude = #{doubleLongitude}</if>
            <if test="doubleLatitude != null ">and doubleLatitude = #{doubleLatitude}</if>
        </where>
    </select>

    <select id="selectWorkInBasisLongLatById" parameterType="Long" resultMap="WorkInBasisLongLatResult">
        <include refid="selectWorkInBasisLongLatVo"/>
        where id = #{id}
    </select>

    <insert id="insertWorkInBasisLongLat" parameterType="WorkInBasisLongLat">
        insert into work_in_basis_long_lat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="workInBasisId != null">workInBasisId,</if>
            <if test="longitude != null and longitude != ''">longitude,</if>
            <if test="latitude != null and latitude != ''">latitude,</if>
            <if test="height != null">height,</if>
            <if test="sortNumber != null">sortNumber,</if>
            <if test="doubleLongitude != null">doubleLongitude,</if>
            <if test="doubleLatitude != null">doubleLatitude,</if>
            <if test="coordinateName != null">coordinateName,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="workInBasisId != null">#{workInBasisId},</if>
            <if test="longitude != null and longitude != ''">#{longitude},</if>
            <if test="latitude != null and latitude != ''">#{latitude},</if>
            <if test="height != null">#{height},</if>
            <if test="sortNumber != null">#{sortNumber},</if>
            <if test="doubleLongitude != null">#{doubleLongitude},</if>
            <if test="doubleLatitude != null">#{doubleLatitude},</if>
            <if test="coordinateName != null">#{coordinateName},</if>
        </trim>
    </insert>
    <insert id="insertWorkBasisLongLatList">
        insert into work_in_basis_long_lat(workInBasisId, longitude, latitude, height, sortNumber, doubleLongitude,
        doubleLatitude,coordinateName)
        values
        <foreach collection="workInBasisLongLatList" item="item" index="index" separator=",">
            (#{item.workInBasisId}, #{item.longitude}, #{item.latitude}, #{item.height}, #{item.sortNumber},
            #{item.doubleLongitude}, #{item.doubleLatitude},#{item.coordinateName})
        </foreach>
    </insert>


    <update id="updateWorkInBasisLongLat" parameterType="WorkInBasisLongLat">
        update work_in_basis_long_lat
        <trim prefix="SET" suffixOverrides=",">
            <if test="workInBasisId != null">workInBasisId = #{workInBasisId},</if>
            <if test="longitude != null and longitude != ''">longitude = #{longitude},</if>
            <if test="latitude != null and latitude != ''">latitude = #{latitude},</if>
            <if test="height != null">height = #{height},</if>
            <if test="sortNumber != null">sortNumber = #{sortNumber},</if>
            <if test="doubleLongitude != null">doubleLongitude = #{doubleLongitude},</if>
            <if test="doubleLatitude != null">doubleLatitude = #{doubleLatitude},</if>
            <if test="coordinateName != null">coordinateName = #{coordinateName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWorkInBasisLongLatById" parameterType="Long">
        delete
        from work_in_basis_long_lat
        where id = #{id}
    </delete>

    <delete id="deleteWorkInBasisLongLatByIds" parameterType="String">
        delete from work_in_basis_long_lat where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteWorkInBasisLongLatByWorkInBasisIds" parameterType="String">
        delete from work_in_basis_long_lat where workInBasisId in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>