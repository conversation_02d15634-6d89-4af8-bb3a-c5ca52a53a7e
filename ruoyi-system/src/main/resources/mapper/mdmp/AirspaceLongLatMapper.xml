<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.AirspaceLongLatMapper">

    <resultMap type="AirspaceLongLat" id="AirspaceLongLatResult">
        <result property="id" column="id"/>
        <result property="airspaceId" column="airspaceId"/>
        <result property="sortNumber" column="sortNumber"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="height" column="height"/>
        <result property="doubleLongitude" column="doubleLongitude"/>
        <result property="doubleLatitude" column="doubleLatitude"/>
        <result property="groupNumber" column="groupNumber"/>
        <result property="coordinateName" column="coordinateName"/>
    </resultMap>

    <sql id="selectAirspaceLongLatVo">
        select id,
               airspaceId,
               sortNumber,
               longitude,
               latitude,
               height,
               doubleLongitude,
               doubleLatitude,
               groupNumber,
               coordinateName
        from airspace_long_lat
    </sql>

    <select id="selectAirspaceLongLatList" parameterType="AirspaceLongLat" resultMap="AirspaceLongLatResult">
        <include refid="selectAirspaceLongLatVo"/>
        <where>
            <if test="airspaceId != null ">and airspaceId = #{airspaceId}</if>
            <if test="sortNumber != null ">and sortNumber = #{sortNumber}</if>
            <if test="longitude != null  and longitude != ''">and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''">and latitude = #{latitude}</if>
            <if test="doubleLongitude != null  and doubleLongitude != ''">and doubleLongitude = #{doubleLongitude}</if>
            <if test="doubleLatitude != null  and doubleLatitude != ''">and doubleLatitude = #{doubleLatitude}</if>
            <if test="height != null ">and height = #{height}</if>
            <if test="groupNumber != null ">and groupNumber = #{groupNumber}</if>
        </where>
    </select>

    <select id="selectAirspaceLongLatById" parameterType="Long" resultMap="AirspaceLongLatResult">
        <include refid="selectAirspaceLongLatVo"/>
        where id = #{id}
    </select>

    <select id="selectByAirspaceId" parameterType="Long" resultMap="AirspaceLongLatResult">
        <include refid="selectAirspaceLongLatVo"/>
        where airspaceId = #{airspaceId}
    </select>

    <insert id="insertAirspaceLongLat" parameterType="AirspaceLongLat" useGeneratedKeys="true" keyProperty="id">
        insert into airspace_long_lat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="airspaceId != null">airspaceId,</if>
            <if test="sortNumber != null">sortNumber,</if>
            <if test="longitude != null and longitude != ''">longitude,</if>
            <if test="latitude != null and latitude != ''">latitude,</if>
            <if test="height != null">height,</if>
            <if test="doubleLongitude != null and doubleLongitude != ''">doubleLongitude,</if>
            <if test="doubleLatitude != null and doubleLatitude != ''">doubleLatitude,</if>
            <if test="groupNumber != null and groupNumber != ''">groupNumber,</if>
            <if test="coordinateName != null and coordinateName != ''">coordinateName,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="airspaceId != null">#{airspaceId},</if>
            <if test="sortNumber != null">#{sortNumber},</if>
            <if test="longitude != null and longitude != ''">#{longitude},</if>
            <if test="latitude != null and latitude != ''">#{latitude},</if>
            <if test="height != null">#{height},</if>
            <if test="doubleLongitude != null and doubleLongitude != ''">doubleLongitude,</if>
            <if test="doubleLatitude != null and doubleLatitude != ''">doubleLatitude,</if>
            <if test="groupNumber != null and groupNumber != ''">groupNumber,</if>
            <if test="coordinateName != null and coordinateName != ''">#{coordinateName},</if>
        </trim>
    </insert>

    <insert id="insertAirspaceLongLatList">
        insert into airspace_long_lat(airspaceId, sortNumber, longitude, latitude, height,
        doubleLongitude,doubleLatitude,groupNumber,coordinateName)
        values
        <foreach collection="airspaceLongLatList" item="item" index="index" separator=",">
            (#{item.airspaceId}, #{item.sortNumber}, #{item.longitude}, #{item.latitude}, #{item.height},
            #{item.doubleLongitude}, #{item.doubleLatitude}, #{item.groupNumber},#{item.coordinateName})
        </foreach>
    </insert>

    <update id="updateAirspaceLongLat" parameterType="AirspaceLongLat">
        update airspace_long_lat
        <trim prefix="SET" suffixOverrides=",">
            <if test="airspaceId != null">airspaceId = #{airspaceId},</if>
            <if test="sortNumber != null">sortNumber = #{sortNumber},</if>
            <if test="longitude != null and longitude != ''">longitude = #{longitude},</if>
            <if test="latitude != null and latitude != ''">latitude = #{latitude},</if>
            <if test="height != null">height = #{height},</if>
            <if test="doubleLongitude != null and doubleLongitude != ''">doubleLongitude,</if>
            <if test="doubleLatitude != null and doubleLatitude != ''">doubleLatitude,</if>
            <if test="groupNumber != null and groupNumber != ''">groupNumber,</if>
            <if test="coordinateName != null and coordinateName != ''">coordinateName,</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAirspaceLongLatById" parameterType="Long">
        delete
        from airspace_long_lat
        where id = #{id}
    </delete>

    <delete id="deleteAirspaceLongLatByIds" parameterType="String">
        delete from airspace_long_lat where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteAirspaceLongLatByAirspaceId">
        delete
        from airspace_long_lat
        where airspaceId = #{id}
    </delete>
</mapper>
