<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.ThunderstormMpDataMapper">

    <resultMap type="ThunderstormMpData" id="ThunderstormMpDataResult">
        <result property="dataValue" column="dataValue"/>
        <result property="fcstTimeSequence" column="fcstTimeSequence"/>
        <result property="reatimeTimeSequence" column="reatimeTimeSequence"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="altitude" column="altitude"/>
    </resultMap>

    <sql id="selectThunderstormMpDataVo">
        select dataValue,
               fcstTimeSequence,
               reatimeTimeSequence,
               mpTimeListId,
               longitude,
               latitude,
               altitude
        from thunderstorm_mp_data
    </sql>

    <select id="selectThunderstormMpDataList" parameterType="ThunderstormMpData" resultMap="ThunderstormMpDataResult">
        <include refid="selectThunderstormMpDataVo"/>
        <where>
            <if test="dataValue != null  and dataValue != ''">and dataValue = #{dataValue}</if>
            <if test="fcstTimeSequence != null ">and fcstTimeSequence = #{fcstTimeSequence}</if>
            <if test="reatimeTimeSequence != null ">and reatimeTimeSequence = #{reatimeTimeSequence}</if>
            <if test="mpTimeListId != null ">and mpTimeListId = #{mpTimeListId}</if>
            <if test="longitude != null ">and longitude = #{longitude}</if>
            <if test="latitude != null ">and latitude = #{latitude}</if>
            <if test="altitude != null ">and altitude = #{altitude}</if>
        </where>
    </select>

    <select id="selectThunderstormMpDataById" parameterType="Long" resultMap="ThunderstormMpDataResult">
        <include refid="selectThunderstormMpDataVo"/>
        where id = #{id}
    </select>

    <select id="selectOldVMpDataList" resultType="java.lang.Long">
        SELECT id
        from thunderstorm_mp_data
        WHERE fcstTimeSequence &lt; UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY));
    </select>
    <select id="getList" resultMap="ThunderstormMpDataResult">
        <include refid="selectThunderstormMpDataVo"/>
        where fcstTimeSequence=#{param.time}
        ORDER BY latitude asc
    </select>
    <select id="getThunderstormMapData" resultType="com.ruoyi.system.domain.mdmp.vo.MapDataVo">
        WITH latest_data AS (
        SELECT reatimeTimeSequence,
        addTime AS latest_addTime
        FROM (
        SELECT reatimeTimeSequence,
        addTime,
        ROW_NUMBER() OVER (PARTITION BY fcstTimeSequence ORDER BY addTime DESC) AS rn
        FROM thunderstorm_mp_data
        ) ranked_data
        WHERE rn = 1
        ORDER BY ABS(reatimeTimeSequence - UNIX_TIMESTAMP())
        LIMIT 1
        )
        SELECT u.dataValue,
        m.sort,
        55 AS statusCodes
        FROM latest_data ld
        JOIN thunderstorm_mp_data u
        ON u.reatimeTimeSequence = ld.reatimeTimeSequence
        AND u.addTime = ld.latest_addTime
        JOIN map_data m
        ON m.latitudeStart = u.latitude
        AND m.longitudeStart = u.longitude
        WHERE u.dataValue!=254
        and u.dataValue!=0
        <if test="param.mpMin != null ">and u.dataValue >=#{param.mpMin}</if>
        <if test="param.mpMax != null ">and u.dataValue &lt;= #{param.mpMax}</if>
        ORDER BY sort
    </select>
    <select id="getThunderstormAlerts" resultType="com.ruoyi.system.domain.mdmp.vo.MapDataVo">
        WITH height_pressure_cte AS (
        SELECT pressure
        FROM height_pressure
        WHERE heightStart
        &lt;#{flightData.elevation}
        AND heightEnd >= #{flightData.elevation}
        LIMIT 1
        ),
        latest_data AS (
        SELECT
        reatimeTimeSequence,
        MAX(addTime) AS latest_addTime
        FROM thunderstorm_mp_data
        GROUP BY reatimeTimeSequence
        ORDER BY ABS(reatimeTimeSequence - UNIX_TIMESTAMP())
        LIMIT 1
        )
        SELECT
        u.dataValue,
        m.sort as 'index',
        55 AS statusCodes
        FROM latest_data ld
        JOIN thunderstorm_mp_data u
        ON u.reatimeTimeSequence = ld.reatimeTimeSequence
        AND u.addTime = ld.latest_addTime
        AND u.latitude = #{flightData.latitude}
        AND u.longitude =#{flightData.longitude}
        JOIN map_data m
        ON m.latitudeStart = u.latitude
        AND m.longitudeStart = u.longitude
        AND m.pressureStart = (SELECT pressure FROM height_pressure_cte)
        WHERE m.latitudeStart = #{flightData.latitude}
        AND m.longitudeStart = #{flightData.longitude}
        and u.dataValue!=254
        and u.dataValue!=0
        <if test="aircraftMp.mpMin != null ">and u.dataValue >=#{aircraftMp.mpMin}</if>
        <if test="aircraftMp.mpMax != null ">and u.dataValue &lt;= #{aircraftMp.mpMax}</if>
        LIMIT 1
    </select>

    <insert id="insertThunderstormMpData" parameterType="ThunderstormMpData" useGeneratedKeys="true" keyProperty="id">
        insert into thunderstorm_mp_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">dataValue,</if>
            <if test="fcstTimeSequence != null">fcstTimeSequence,</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="altitude != null">altitude,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">#{dataValue},</if>
            <if test="fcstTimeSequence != null">#{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">#{reatimeTimeSequence},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="altitude != null">#{altitude},</if>
        </trim>
    </insert>

    <insert id="insertMpDataList">
        insert into thunderstorm_mp_data
        (dataValue,fcstTimeSequence,reatimeTimeSequence,longitude,latitude,altitude,addTime)
        values
        <foreach collection="param" item="item" index="index" separator=",">
            (#{item.dataValue},#{item.fcstTimeSequence},#{item.reatimeTimeSequence},#{item.longitude},#{item.latitude},#{item.altitude},#{item.addTime})
        </foreach>
    </insert>

    <update id="updateThunderstormMpData" parameterType="ThunderstormMpData">
        update thunderstorm_mp_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataValue != null">dataValue = #{dataValue},</if>
            <if test="fcstTimeSequence != null">fcstTimeSequence = #{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence = #{reatimeTimeSequence},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="altitude != null">altitude = #{altitude},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteThunderstormMpDataById" parameterType="Long">
        delete
        from thunderstorm_mp_data
        where id = #{id}
    </delete>

    <delete id="deleteThunderstormMpDataByIds" parameterType="String">
        delete from thunderstorm_mp_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteThunderstormMpDataByFcstTimeSequence">
        delete
        from thunderstorm_mp_data
        where fcstTimeSequence = #{time}
          and altitude = #{altitude}
    </delete>
    <delete id="deleteThunderstormMpDataByReatimeTimeSequence">
        delete
        from thunderstorm_mp_data
        where reatimeTimeSequence = #{time}
          and altitude = #{altitude}
    </delete>

    <delete id="deleteVMpDataByIds">
        delete from thunderstorm_mp_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteOldMpDataList" parameterType="Integer">
        delete
        from thunderstorm_mp_data
        WHERE addTime &lt; NOW() - INTERVAL 1 DAY
        ORDER BY reatimeTimeSequence
            LIMIT 10000
    </delete>
</mapper>
