<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightDataMapper">

    <resultMap type="FlightData" id="FlightDataResult">
        <result property="id" column="id"/>
        <result property="uniqueId" column="unique_id"/>
        <result property="flightPlanId" column="flight_plan_id"/>
        <result property="targetIdentification" column="target_identification"/>
        <result property="callSign" column="call_sign"/>
        <result property="aircraftReg" column="aircraft_reg"/>
        <result property="operationAgency" column="operation_agency"/>
        <result property="deviceId" column="deviceId"/>
        <result property="utcYear" column="utc_year"/>
        <result property="utcMonth" column="utc_month"/>
        <result property="utcDay" column="utc_day"/>
        <result property="utcHour" column="utc_hour"/>
        <result property="utcMinute" column="utc_minute"/>
        <result property="utcSecond" column="utc_second"/>
        <result property="flightDate" column="flight_date"/>
        <result property="dynamicTime" column="dynamic_time"/>
        <result property="gpsFlag" column="gps_flag"/>
        <result property="latitude" column="latitude"/>
        <result property="longitude" column="longitude"/>
        <result property="elevation" column="elevation"/>
        <result property="correctElevation" column="correct_elevation"/>
        <result property="geometricHeight" column="geometric_height"/>
        <result property="selectedHeight" column="selected_height"/>
        <result property="givenHeight" column="given_height"/>
        <result property="speed" column="speed"/>
        <result property="givenSpeed" column="given_speed"/>
        <result property="northSpeed" column="north_speed"/>
        <result property="eastSpeed" column="east_speed"/>
        <result property="verticalSpeed" column="vertical_speed"/>
        <result property="headingAngle" column="heading_angle"/>
        <result property="pitchAngle" column="pitch_angle"/>
        <result property="rollAngle" column="roll_angle"/>
        <result property="realTime" column="real_time"/>
        <result property="withinTheScope" column="within_the_scope"/>
        <result property="isCivil" column="isCivil"/>
    </resultMap>

    <sql id="selectFlightDataVo">
        select id,
               unique_id,
               flight_plan_id,
               target_identification,
               call_sign,
               aircraft_reg,
               operation_agency,
               deviceId,
               utc_year,
               utc_month,
               utc_day,
               utc_hour,
               utc_minute,
               utc_second,
               flight_date,
               dynamic_time,
               gps_flag,
               latitude,
               longitude,
               elevation,
               correct_elevation,
               geometric_height,
               selected_height,
               given_height,
               speed,
               given_speed,
               north_speed,
               east_speed,
               vertical_speed,
               heading_angle,
               pitch_angle,
               roll_angle,
               real_time,
               within_the_scope
        from flight_data
    </sql>

    <select id="selectByFlightDateAndTailNumber" resultMap="FlightDataResult">
        <include refid="selectFlightDataVo"/>
        where flight_date = #{flightDate}
        and aircraft_reg = #{aircraftReg}
    </select>

    <select id="selectByFlightDateAndAircraftReg" resultMap="FlightDataResult">
        <include refid="selectFlightDataVo"/>
        where flight_date = #{flightDate}
        and aircraft_reg = #{aircraftReg}
        and dynamic_time &lt;= #{dynamicTime}
        and (latitude &lt;&gt; 0 or longitude &lt;&gt; 0)
        ORDER BY dynamic_time ASC
    </select>
    <select id="getFlightAlerts" resultMap="FlightDataResult">
        SELECT u.*, COALESCE(air.isCivil, 1) AS isCivil
        FROM (SELECT sub.*
              FROM (
                       SELECT a.*,
                              ROW_NUMBER() OVER (PARTITION BY a.aircraft_reg ORDER BY a.real_time DESC) AS rn
                       FROM flight_data a
                                INNER JOIN (
                           SELECT aircraft_reg, MAX(real_time) AS latest_time
                           FROM flight_data
                           WHERE aircraft_reg != #{param.aircraftReg}
                             AND real_time >= NOW() - INTERVAL 60 SECOND
                             AND real_time &lt;= NOW()
                           GROUP BY aircraft_reg
                       ) b ON a.aircraft_reg = b.aircraft_reg AND a.real_time = b.latest_time
                       WHERE a.aircraft_reg != #{param.aircraftReg}
                   ) AS sub
                       LEFT JOIN flight_alerts_in_out fai ON sub.aircraft_reg = fai.aircraftReg
              WHERE sub.rn = 1) u
                 LEFT JOIN aircraft air
                           ON air.aircraftReg = u.aircraft_reg
    </select>

    <select id="getConnectionInfoList" resultMap="FlightDataResult">
        SELECT u.*
        FROM (
                 SELECT sub.*
                 FROM (
                          SELECT a.*,
                                 ROW_NUMBER() OVER (PARTITION BY a.aircraft_reg ORDER BY a.real_time DESC) AS rn
                          FROM flight_data a
                                   INNER JOIN (
                              SELECT aircraft_reg,
                                     MAX(real_time) AS latest_time
                              FROM flight_data
                              WHERE aircraft_reg != #{param.aircraftReg}
                                AND real_time >= NOW() - INTERVAL 60 SECOND
                                AND real_time&lt;= NOW()
                              GROUP BY aircraft_reg
                          ) b ON a.aircraft_reg = b.aircraft_reg
                              AND a.real_time = b.latest_time
                          WHERE a.aircraft_reg !=#{param.aircraftReg}
                      ) AS sub
                          LEFT JOIN flight_alerts_in_out fai
                                    ON sub.aircraft_reg = fai.aircraftReg
                 WHERE sub.rn = 1
                   AND sub.real_time &lt;= NOW()
                   AND ABS(sub.elevation - #{param.elevation})
                     &lt; #{height}
             ) u
                 LEFT JOIN aircraft air
                           ON air.aircraftReg = u.aircraft_reg
    </select>
    <select id="getFlightAlertsIsCivilTh" resultMap="FlightDataResult">
        SELECT u.*
        FROM (
                 SELECT sub.*
                 FROM (
                          SELECT a.*,
                                 ROW_NUMBER() OVER (PARTITION BY a.aircraft_reg ORDER BY a.real_time DESC) AS rn
                          FROM flight_data a
                                   INNER JOIN (
                              SELECT aircraft_reg,
                                     MAX(real_time) AS latest_time
                              FROM flight_data
                              WHERE aircraft_reg != #{param.aircraftReg}
                                AND real_time >= NOW() - INTERVAL 60 SECOND
                                AND real_time&lt;= NOW()
                              GROUP BY aircraft_reg
                          ) b ON a.aircraft_reg = b.aircraft_reg
                              AND a.real_time = b.latest_time
                          WHERE a.aircraft_reg !=#{param.aircraftReg}
                      ) AS sub
                          LEFT JOIN flight_alerts_in_out fai
                                    ON sub.aircraft_reg = fai.aircraftReg
                 WHERE sub.rn = 1
                   AND sub.real_time &lt;= NOW()
                   AND ABS(sub.elevation - #{param.elevation})
                     &lt; #{height}
             ) u
                 LEFT JOIN aircraft air
                           ON air.aircraftReg = u.aircraft_reg
        WHERE air.isCivil != 0
    </select>

    <select id="selectFlightDataListByOneMinute" resultType="com.ruoyi.system.param.mdmp.AircraftRegParam">
        SELECT aircraft_reg      as aircraftReg,
               longitude         as longitude,
               latitude          as latitude,
               correct_elevation as correctElevation
        FROM flight_data
        WHERE aircraft_reg = #{aircraftReg}
          AND real_time >= CURRENT_TIMESTAMP - INTERVAL '2' MINUTE
          AND real_time &lt;= CURRENT_TIMESTAMP
        order by real_time asc
    </select>


    <insert id="insertFlightData" parameterType="FlightData" useGeneratedKeys="true" keyProperty="id">
        insert into flight_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="uniqueId != null">unique_id,</if>
            <if test="flightPlanId != null">flight_plan_id,</if>
            <if test="targetIdentification != null">target_identification,</if>
            <if test="callSign != null">call_sign,</if>
            <if test="aircraftReg != null">aircraft_reg,</if>
            <if test="operationAgency != null">operation_agency,</if>
            <if test="deviceId != null">deviceId,</if>
            <if test="utcYear != null">utc_year,</if>
            <if test="utcMonth != null">utc_month,</if>
            <if test="utcDay != null">utc_day,</if>
            <if test="utcHour != null">utc_hour,</if>
            <if test="utcMinute != null">utc_minute,</if>
            <if test="utcSecond != null">utc_second,</if>
            <if test="flightDate != null">flight_date,</if>
            <if test="dynamicTime != null">dynamic_time,</if>
            <if test="gpsFlag != null">gps_flag,</if>
            <if test="latitude != null">latitude,</if>
            <if test="longitude != null">longitude,</if>
            <if test="elevation != null">elevation,</if>
            <if test="northSpeed != null">north_speed,</if>
            <if test="eastSpeed != null">east_speed,</if>
            <if test="verticalSpeed != null">vertical_speed,</if>
            <if test="headingAngle != null">heading_angle,</if>
            <if test="pitchAngle != null">pitch_angle,</if>
            <if test="rollAngle != null">roll_angle,</if>
            <if test="realTime != null">real_time,</if>
            <if test="withinTheScope != null">within_the_scope,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="uniqueId != null">#{uniqueId},</if>
            <if test="flightPlanId != null">#{flightPlanId},</if>
            <if test="targetIdentification != null">#{targetIdentification},</if>
            <if test="callSign != null">#{callSign},</if>
            <if test="aircraftReg != null">#{aircraftReg},</if>
            <if test="operationAgency != null">#{operationAgency},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="utcYear != null">#{utcYear},</if>
            <if test="utcMonth != null">#{utcMonth},</if>
            <if test="utcDay != null">#{utcDay},</if>
            <if test="utcHour != null">#{utcHour},</if>
            <if test="utcMinute != null">#{utcMinute},</if>
            <if test="utcSecond != null">#{utcSecond},</if>
            <if test="flightDate != null">#{flightDate},</if>
            <if test="dynamicTime != null">#{dynamicTime},</if>
            <if test="gpsFlag != null">#{gpsFlag},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="elevation != null">#{elevation},</if>
            <if test="northSpeed != null">#{northSpeed},</if>
            <if test="eastSpeed != null">#{eastSpeed},</if>
            <if test="verticalSpeed != null">#{verticalSpeed},</if>
            <if test="headingAngle != null">#{headingAngle},</if>
            <if test="pitchAngle != null">#{pitchAngle},</if>
            <if test="rollAngle != null">#{rollAngle},</if>
            <if test="realTime != null">#{realTime},</if>
            <if test="withinTheScope != null">#{withinTheScope},</if>
        </trim>
    </insert>

    <update id="updateFlightData" parameterType="FlightData">
        update flight_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="uniqueId != null">unique_id = #{uniqueId},</if>
            <if test="flightPlanId != null">flight_plan_id = #{flightPlanId},</if>
            <if test="targetIdentification != null">target_identification = #{targetIdentification},</if>
            <if test="callSign != null">call_sign = #{callSign},</if>
            <if test="aircraftReg != null">aircraft_reg = #{aircraftReg},</if>
            <if test="operationAgency != null">operation_agency = #{operationAgency},</if>
            <if test="deviceId != null">deviceId = #{deviceId},</if>
            <if test="utcYear != null">utc_year = #{utcYear},</if>
            <if test="utcMonth != null">utc_month = #{utcMonth},</if>
            <if test="utcDay != null">utc_day = #{utcDay},</if>
            <if test="utcHour != null">utc_hour = #{utcHour},</if>
            <if test="utcMinute != null">utc_minute = #{utcMinute},</if>
            <if test="utcSecond != null">utc_second = #{utcSecond},</if>
            <if test="flightDate != null">flight_date = #{flightDate},</if>
            <if test="dynamicTime != null">dynamic_time = #{dynamicTime},</if>
            <if test="gpsFlag != null">gps_flag = #{gpsFlag},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="elevation != null">elevation = #{elevation},</if>
            <if test="northSpeed != null">north_speed = #{northSpeed},</if>
            <if test="eastSpeed != null">east_speed = #{eastSpeed},</if>
            <if test="verticalSpeed != null">vertical_speed = #{verticalSpeed},</if>
            <if test="headingAngle != null">heading_angle = #{headingAngle},</if>
            <if test="pitchAngle != null">pitch_angle = #{pitchAngle},</if>
            <if test="rollAngle != null">roll_angle = #{rollAngle},</if>
            <if test="realTime != null">real_time = #{realTime},</if>
            <if test="withinTheScope != null">within_the_scope = #{withinTheScope},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAll">
        delete
        from flight_data
        where flight_date = #{flightDate}
          and target_identification = #{targetIdentification}
    </delete>

</mapper>
