<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.AircraftMpMapper">

    <resultMap type="AircraftMp" id="AircraftMpResult">
        <result property="id" column="id"/>
        <result property="alarmType" column="alarmType"/>
        <result property="aircraftId" column="aircraftId"/>
        <result property="alarmLevel" column="alarmLevel"/>
        <result property="mpMin" column="mpMin"/>
        <result property="mpMax" column="mpMax"/>
    </resultMap>

    <sql id="selectAircraftMpVo">
        select id, alarmType, aircraftId, alarmLevel, mpMin, mpMax
        from aircraft_mp
    </sql>

    <select id="selectAircraftMpList" parameterType="AircraftMp" resultMap="AircraftMpResult">
        <include refid="selectAircraftMpVo"/>
        <where>
            <if test="alarmType != null ">and alarmType = #{alarmType}</if>
            <if test="aircraftId != null ">and aircraftId = #{aircraftId}</if>
            <if test="alarmLevel != null ">and alarmLevel = #{alarmLevel}</if>
            <if test="mpMin != null ">and mpMin = #{mpMin}</if>
            <if test="mpMax != null ">and mpMax = #{mpMax}</if>
        </where>
    </select>

    <select id="selectAircraftMpById" parameterType="Long" resultMap="AircraftMpResult">
        <include refid="selectAircraftMpVo"/>
        where id = #{id}
    </select>

    <select id="getAircraftMpListByAircraftReg" resultMap="AircraftMpResult">
        SELECT am.*
        from aircraft a
                 join aircraft_mp am on a.id = am.aircraftId
        where a.aircraftReg = #{aircraftReg}
          and alarmLevel = 1
    </select>

    <insert id="insertAircraftMp" parameterType="AircraftMp" useGeneratedKeys="true" keyProperty="id">
        insert into aircraft_mp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="alarmType != null">alarmType,</if>
            <if test="aircraftId != null">aircraftId,</if>
            <if test="alarmLevel != null">alarmLevel,</if>
            <if test="mpMin != null">mpMin,</if>
            <if test="mpMax != null">mpmax,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="alarmType != null">#{alarmType},</if>
            <if test="aircraftId != null">#{aircraftId},</if>
            <if test="alarmLevel != null">#{alarmLevel},</if>
            <if test="mpMin != null">#{mpMin},</if>
            <if test="mpMax != null">#{mpmax},</if>
        </trim>
    </insert>

    <insert id="insertAircraftMpList">
        insert into aircraft_mp (alarmType,aircraftId,alarmLevel,mpMin,mpMax)
        values
        <foreach collection="param" item="item" index="index" separator=",">
            (#{item.alarmType}, #{item.aircraftId}, #{item.alarmLevel}, #{item.mpMin}, #{item.mpMax})
        </foreach>
    </insert>

    <update id="updateAircraftMp" parameterType="AircraftMp">
        update aircraft_mp
        <trim prefix="SET" suffixOverrides=",">
            <if test="alarmType != null">alarmType = #{alarmType},</if>
            <if test="aircraftId != null">aircraftId = #{aircraftId},</if>
            <if test="alarmLevel != null">alarmLevel = #{alarmLevel},</if>
            <if test="mpMin != null">mpMin = #{mpMin},</if>
            <if test="mpMax != null">mpMax = #{mpMax},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAircraftMpById" parameterType="Long">
        delete
        from aircraft_mp
        where id = #{id}
    </delete>

    <delete id="deleteAircraftMpByIds" parameterType="String">
        delete from aircraft_mp where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteByAircraftId">
        delete
        from aircraft_mp
        where aircraftId = #{id}
    </delete>
</mapper>
