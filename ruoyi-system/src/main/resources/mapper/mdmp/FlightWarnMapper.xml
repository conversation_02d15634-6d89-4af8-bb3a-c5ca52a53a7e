<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightWarnMapper">

    <resultMap type="FlightWarn" id="FlightWarnResult">
        <result property="id" column="id"/>
        <result property="flightDataId" column="flight_data_id"/>
        <result property="warnDate" column="warn_date"/>
        <result property="warnTime" column="warn_time"/>
        <result property="warnAircraft" column="warn_aircraft"/>
        <result property="anotherAircraft" column="another_aircraft"/>
        <result property="warnMessage" column="warn_message"/>
    </resultMap>

    <sql id="selectFlightWarnVo">
        select id, flight_data_id, warn_date, warn_time, warn_aircraft, another_aircraft, warn_message
        from flight_warn
    </sql>

    <select id="selectByWarnDate" resultMap="FlightWarnResult">
        <include refid="selectFlightWarnVo"/>
        where warn_date = #{warnDate}
        <if test="deptCodeList != null and !deptCodeList.isEmpty()">
            and deptCode in
            <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
                #{deptCode}
            </foreach>
        </if>
    </select>

</mapper>
