<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.LongTermFlightPlanMapper">

    <resultMap type="LongTermFlightPlan" id="LongTermFlightPlanResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="taskType" column="taskType"/>
        <result property="startDate" column="startDate"/>
        <result property="endDate" column="endDate"/>
        <result property="remark" column="remark"/>
        <result property="refusalExplain" column="refusalExplain"/>
        <result property="isDelete" column="isDelete"/>
        <result property="auditor" column="auditor"/>
        <result property="auditTime" column="auditTime"/>
        <result property="creator" column="creator"/>
        <result property="creationTime" column="creationTime"/>
        <result property="contactName" column="contactName"/>
        <result property="contactPhone" column="contactPhone"/>
        <result property="serialNo" column="serialNo"/>
        <result property="status" column="status"/>
        <result property="isWork" column="isWork"/>
        <result property="planType" column="planType"/>
        <result property="deptCode" column="deptCode"/>
        <!--        * 计划 机型机号关联表-->
        <collection property="planModelAircraftList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanAircraftModelMapper.selectAircraftByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

        <!--        * 计划 机场关联表-->
        <collection property="flightPlanAirportList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanAirportMapper.selectAirportByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

        <!--        * 计划 航线关联表-->
        <collection property="flightPlanRouteList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanRouteMapper.selectRouteByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

        <!--        * 计划 附件关联表-->
        <collection property="flightPlanFileList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanFileMapper.selectFileByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>
        <!--        * 计划 作业区关联表-->
        <collection property="flightPlanWorkList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanWorkMapper.selectWorkByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

    </resultMap>


    <!--展示 list-->
    <resultMap type="LongTermFlightPlan" id="LongTermFlightPlanListResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="planType" column="planType"/>
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="taskType" column="taskType"/>
        <result property="startDate" column="startDate"/>
        <result property="endDate" column="endDate"/>
        <result property="remark" column="remark"/>
        <result property="refusalExplain" column="refusalExplain"/>
        <result property="isDelete" column="isDelete"/>
        <result property="auditor" column="auditor"/>
        <result property="auditTime" column="auditTime"/>
        <result property="creator" column="creator"/>
        <result property="creationTime" column="creationTime"/>
        <result property="contactName" column="contactName"/>
        <result property="contactPhone" column="contactPhone"/>
        <result property="serialNo" column="serialNo"/>
        <result property="status" column="status"/>
        <result property="isWork" column="isWork"/>
        <result property="deptCode" column="deptCode"/>

        <!--        * 计划 机型机号关联表-->
        <collection property="planModelAircraftList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanAircraftModelMapper.selectAircraftByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

        <!--        * 计划 附件关联表-->
        <collection property="flightPlanFileList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanFileMapper.selectFileByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

    </resultMap>


    <sql id="selectLongTermFlightPlanVo">
        select id,
               name,
               1 as planType,
               companyName,
               companyCode,
               taskType,
               startDate,
               endDate,
               remark,
               refusalExplain,
               isDelete,
               auditor,
               auditTime,
               creator,
               creationTime,
               contactName,
               contactPhone,
               serialNo,
               status,
               isWork,
               deptCode
        from long_term_flight_plan
    </sql>

    <select id="selectLongTermFlightPlanList" resultMap="LongTermFlightPlanListResult">
        <include refid="selectLongTermFlightPlanVo"/>
        <where>
            and isDelete = 0
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="name != null  and name != ''">and name = #{name}</if>
            <if test="companyCode != null  and companyCode != ''">and companyCode = #{companyCode}</if>
            <if test="taskType != null  and taskType != ''">and taskType = #{taskType}</if>
            <if test="startDate != null  and startDate != ''">and startDate = #{startDate}</if>
            <if test="endDate != null  and endDate != ''">and endDate = #{endDate}</if>
            <if test="contactName != null  and contactName != ''">and contactName like concat('%', #{contactName},
                '%')
            </if>
            <if test="contactPhone != null  and contactPhone != ''">and contactPhone = #{contactPhone}</if>
            <if test="serialNo != null  and serialNo != ''">and serialNo = #{serialNo}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="isWork != null ">and isWork = #{isWork}</if>
            <if test="deptCodeList != null and !deptCodeList.isEmpty()">
                and deptCode in
                <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
                    #{deptCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectLongTermFlightPlanById" parameterType="Long" resultMap="LongTermFlightPlanResult">
        <include refid="selectLongTermFlightPlanVo"/>
        where id = #{id} and isDelete = 0
    </select>
    <select id="selectLongTermFlightPlanByInterval"
            resultMap="LongTermFlightPlanResult">
        <include refid="selectLongTermFlightPlanVo"/>
        where isDelete = 0
        and status in (1,2,3)
        and (endDate >=#{longTermFlightPlan.startDate}
        and startDate &lt;= #{longTermFlightPlan.endDate})
    </select>

    <select id="selectLongTermFlightPlanByIdAndStatus" resultMap="LongTermFlightPlanResult">
        <include refid="selectLongTermFlightPlanVo"/>
        where id = #{id}
        and status = 1
        and isDelete = 0
    </select>

    <insert id="insertLongTermFlightPlan" parameterType="LongTermFlightPlan" useGeneratedKeys="true" keyProperty="id">
        insert into long_term_flight_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="companyName != null">companyName,</if>
            <if test="name != null">name,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="taskType != null">taskType,</if>
            <if test="startDate != null">startDate,</if>
            <if test="endDate != null">endDate,</if>
            <if test="remark != null">remark,</if>
            <if test="refusalExplain != null">refusalExplain,</if>
            <if test="isDelete != null">isDelete,</if>
            <if test="auditor != null">auditor,</if>
            <if test="auditTime != null">auditTime,</if>
            <if test="creator != null">creator,</if>
            <if test="creationTime != null">creationTime,</if>
            <if test="contactName != null">contactName,</if>
            <if test="contactPhone != null">contactPhone,</if>
            <if test="serialNo != null">serialNo,</if>
            <if test="status != null">status,</if>
            <if test="isWork != null">isWork,</if>
            <if test="deptCode != null">deptCode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="companyName != null">#{companyName},</if>
            <if test="name != null">#{name},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="startDate != null">#{startDate},</if>
            <if test="endDate != null">#{endDate},</if>
            <if test="remark != null">#{remark},</if>
            <if test="refusalExplain != null">#{refusalExplain},</if>
            <if test="isDelete != null">#{isDelete},</if>
            <if test="auditor != null">#{auditor},</if>
            <if test="auditTime != null">#{auditTime},</if>
            <if test="creator != null">#{creator},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="serialNo != null">#{serialNo},</if>
            <if test="status != null">#{status},</if>
            <if test="isWork != null">#{isWork},</if>
            <if test="deptCode != null">#{deptCode},</if>
        </trim>
    </insert>

    <update id="updateLongTermFlightPlan" parameterType="LongTermFlightPlan">
        update long_term_flight_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="name != null">name = #{name},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="taskType != null">taskType = #{taskType},</if>
            <if test="startDate != null">startDate = #{startDate},</if>
            <if test="endDate != null">endDate = #{endDate},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="refusalExplain != null">refusalExplain = #{refusalExplain},</if>
            <if test="contactName != null">contactName = #{contactName},</if>
            <if test="contactPhone != null">contactPhone = #{contactPhone},</if>
            <if test="serialNo != null">serialNo = #{serialNo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="isWork != null">isWork = #{isWork},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteLongTermFlightPlanById" parameterType="Long">
        delete
        from long_term_flight_plan
        where id = #{id}
    </delete>

    <update id="deleteLongTermFlightPlanByIds">
        update long_term_flight_plan set isDelete =1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


</mapper>
