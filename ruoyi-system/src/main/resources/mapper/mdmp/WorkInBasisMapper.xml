<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.WorkInBasisMapper">

    <resultMap type="WorkInBasis" id="WorkInBasisResult">
        <result property="id" column="id"/>
        <result property="workBasisId" column="workBasisId"/>
        <result property="regionType" column="regionType"/>
        <result property="graphType" column="graphType"/>
        <result property="radius" column="radius"/>
        <result property="circleCenterLong" column="circleCenterLong"/>
        <result property="circleCenterLat" column="circleCenterLat"/>
        <result property="minHeight" column="minHeight"/>
        <result property="maxHeight" column="maxHeight"/>
        <result property="workInName" column="workInName"/>
        <collection property="workInBasisLongLatList"
                    select="com.ruoyi.system.mapper.mdmp.WorkInBasisLongLatMapper.selectWorkInBasisLongLatList"
                    column="{workInBasisId=id}">
        </collection>


        <collection property="workInMapDataList"
                    select="com.ruoyi.system.mapper.mdmp.WorkInMapDataMapper.selectAllByWorkInId"
                    column="{workInId=id}">
        </collection>
    </resultMap>

    <sql id="selectWorkInBasisVo">
        select id, workBasisId,workInName, regionType, graphType, radius, circleCenterLong, circleCenterLat,minHeight,maxHeight
        from work_in_basis
    </sql>

    <select id="selectWorkInBasisList" parameterType="WorkInBasis" resultMap="WorkInBasisResult">
        <include refid="selectWorkInBasisVo"/>
        <where>
            <if test="workBasisId != null ">and workBasisId = #{workBasisId}</if>
            <if test="regionType != null ">and regionType = #{regionType}</if>
            <if test="graphType != null ">and graphType = #{graphType}</if>
            <if test="radius != null ">and radius = #{radius}</if>
            <if test="circleCenterLong != null  and circleCenterLong != ''">and circleCenterLong = #{circleCenterLong}
            </if>
            <if test="circleCenterLat != null  and circleCenterLat != ''">and circleCenterLat = #{circleCenterLat}</if>
            <if test="workInName != null ">and workInName = #{workInName}</if>
        </where>
    </select>

    <select id="selectWorkInBasisById" parameterType="Long" resultMap="WorkInBasisResult">
        <include refid="selectWorkInBasisVo"/>
        where id = #{id}
    </select>

    <insert id="insertWorkInBasis" parameterType="WorkInBasis" useGeneratedKeys="true" keyProperty="id">
        insert into work_in_basis
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="workBasisId != null">workBasisId,</if>
            <if test="regionType != null">regionType,</if>
            <if test="graphType != null">graphType,</if>
            <if test="radius != null">radius,</if>
            <if test="circleCenterLong != null">circleCenterLong,</if>
            <if test="circleCenterLat != null">circleCenterLat,</if>
            <if test="minHeight != null">minHeight,</if>
            <if test="maxHeight != null">maxHeight,</if>
            <if test="workInName != null">workInName,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="workBasisId != null">#{workBasisId},</if>
            <if test="regionType != null">#{regionType},</if>
            <if test="graphType != null">#{graphType},</if>
            <if test="radius != null">#{radius},</if>
            <if test="circleCenterLong != null">#{circleCenterLong},</if>
            <if test="circleCenterLat != null">#{circleCenterLat},</if>
            <if test="minHeight != null">#{minHeight},</if>
            <if test="maxHeight != null">#{maxHeight},</if>
            <if test="workInName != null">#{workInName},</if>
        </trim>
    </insert>

    <update id="updateWorkInBasis" parameterType="WorkInBasis">
        update work_in_basis
        <trim prefix="SET" suffixOverrides=",">
            <if test="workBasisId != null">workBasisId = #{workBasisId},</if>
            <if test="regionType != null">regionType = #{regionType},</if>
            <if test="graphType != null">graphType = #{graphType},</if>
            <if test="radius != null">radius = #{radius},</if>
            <if test="circleCenterLong != null">circleCenterLong = #{circleCenterLong},</if>
            <if test="circleCenterLat != null">circleCenterLat = #{circleCenterLat},</if>
            <if test="minHeight != null">minHeight = #{minHeight},</if>
            <if test="maxHeight != null">maxHeight = #{maxHeight},</if>
            <if test="workInName != null">workInName = #{workInName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWorkInBasisById" parameterType="Long">
        delete
        from work_in_basis
        where id = #{id}
    </delete>

    <delete id="deleteWorkInBasisByIds" parameterType="String">
        delete from work_in_basis where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteWorkInBasisByWorkId">
        delete from work_in_basis where workBasisId = #{id}
    </delete>
</mapper>
