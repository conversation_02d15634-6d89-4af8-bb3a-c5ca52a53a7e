<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.RouteMapper">

    <resultMap type="Route" id="RouteResult">
        <result property="id" column="id"/>
        <result property="routeCode" column="routeCode"/>
        <result property="remarks" column="remarks"/>
        <result property="deptCode" column="deptCode"/>
        <collection property="routeLongLatList"
                    select="com.ruoyi.system.mapper.mdmp.RouteLongLatMapper.selectRouteLongLatList"
                    column="{routeId=id}">
        </collection>
    </resultMap>

    <resultMap type="Route" id="RouteMapDataResult">
        <result property="id" column="id"/>
        <result property="routeCode" column="routeCode"/>
        <result property="remarks" column="remarks"/>
        <result property="deptCode" column="deptCode"/>
        <collection property="routeLongLatList"
                    select="com.ruoyi.system.mapper.mdmp.RouteLongLatMapper.selectRouteLongLatList"
                    column="{routeId=id}">
        </collection>
        <collection property="routeMapDataList"
                    select="com.ruoyi.system.mapper.mdmp.RouteMapDataMapper.selectAllByRouteId"
                    column="{routeId=id}">
        </collection>
    </resultMap>


    <sql id="selectRouteVo">
        select id, routeCode, remarks,deptCode
        from route
    </sql>

    <select id="selectRouteList" parameterType="Route" resultMap="RouteResult">
        <include refid="selectRouteVo"/>
        <where>
            <if test="routeCode != null  and routeCode != ''">and routeCode = #{routeCode}</if>
            <if test="remarks != null  and remarks != ''">and remarks = #{remarks}</if>
            <if test="deptCodeList != null and !deptCodeList.isEmpty()">
                and deptCode in
                <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
                    #{deptCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectRouteById" parameterType="Long" resultMap="RouteMapDataResult">
        <include refid="selectRouteVo"/>
        where id = #{id}
    </select>

    <select id="selectRouteByrRuteCode" parameterType="string" resultMap="RouteResult">
        <include refid="selectRouteVo"/>
        where routeCode = #{routeCode}
    </select>

    <select id="selectRouteListByIds" resultMap="RouteResult">
        <include refid="selectRouteVo"/>
        where id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <select id="selectAll" resultMap="RouteResult">
        <include refid="selectRouteVo"/>
    </select>

    <insert id="insertRoute" parameterType="Route" useGeneratedKeys="true" keyProperty="id">
        insert into route
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="routeCode != null and routeCode != ''">routeCode,</if>
            <if test="remarks != null">remarks,</if>
            <if test="deptCode != null and deptCode != ''">deptCode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="routeCode != null and routeCode != ''">#{routeCode},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="deptCode != null and deptCode != ''">#{deptCode},</if>
        </trim>
    </insert>

    <update id="updateRoute" parameterType="Route">
        update route
        <trim prefix="SET" suffixOverrides=",">
            <if test="routeCode != null and routeCode != ''">routeCode = #{routeCode},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="deptCode != null">deptCode = #{deptCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRouteById" parameterType="Long">
        delete
        from route
        where id = #{id}
    </delete>

    <delete id="deleteRouteByIds" parameterType="String">
        delete from route where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
