<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.MpDataMapper">

    <resultMap type="MpData" id="MpDataResult">
        <result property="dataValue" column="dataValue"/>
        <result property="fcstTimeSequence" column="fcstTimeSequence"/>
        <result property="reatimeTimeSequence" column="reatimeTimeSequence"/>
        <result property="longitudeMin" column="longitudeMin"/>
        <result property="longitudeMax" column="longitudeMax"/>
        <result property="latitude" column="latitude"/>
        <result property="altitude" column="altitude"/>
        <result property="addTime" column="addTime"/>
    </resultMap>

    <sql id="selectMpDataVo">
        select dataValue,
               fcstTimeSequence,
               reatimeTimeSequence,
               longitudeMin,
               longitudeMax,
               latitude,
               altitude,
               addTime
        from u_mp_data
    </sql>

    <select id="selectMpDataList" parameterType="MpData" resultMap="MpDataResult">
        <include refid="selectMpDataVo"/>
        <where>
            <if test="dataValue != null ">and dataValue = #{dataValue}</if>
            <if test="fcstTimeSequence != null ">and fcstTimeSequence = #{fcstTimeSequence}</if>
            <if test="reatimeTimeSequence != null ">and reatimeTimeSequence = #{reatimeTimeSequence}</if>
            <if test="addTime != null ">and addTime = #{addTime}</if>
            <if test="longitudeMin != null ">and longitudeMin = #{longitudeMin}</if>
            <if test="longitudeMax != null ">and longitudeMax = #{longitudeMax}</if>
            <if test="latitude != null ">and latitude = #{latitude}</if>
            <if test="altitude != null ">and altitude = #{altitude}</if>
        </where>
    </select>

    <select id="selectMpDataById" parameterType="Long" resultMap="MpDataResult">
        <include refid="selectMpDataVo"/>
        where id = #{id}
    </select>

    <select id="selectUMpDataByMeteInfo" resultMap="MpDataResult">
        <include refid="selectMpDataVo"/>
        where latitude=#{param.latitude}
        and altitude=#{param.altitude}
        ORDER BY ABS(TIMESTAMPDIFF(SECOND, fcstTimeSequence, NOW())) ASC
        LIMIT 1;
    </select>

    <select id="selectOldMpDataList" resultType="java.lang.Long">
        SELECT id
        from u_mp_data
        WHERE fcstTimeSequence &lt; UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY));
    </select>

    <select id="getList" resultMap="MpDataResult">
        SELECT DISTINCT m.id, u.dataValue
        FROM map_data m
                 JOIN u_mp_data u
                      ON m.latitudeStart = u.latitude
                          AND m.longitudeStart BETWEEN u.longitudeMin AND u.longitudeMax
                 JOIN (
            SELECT fcstTimeSequence,
                   MAX(addTime) AS latest_addTime
            FROM u_mp_data
            where altitude = #{param.altitude}
              and fcstTimeSequence = #{param.time}
        ) latest_fcst
                      ON u.fcstTimeSequence = latest_fcst.fcstTimeSequence
                          AND u.addTime = latest_fcst.latest_addTime
        WHERE m.pressureStart = #{param.altitude}
          AND u.altitude = #{param.altitude}
    </select>

    <insert id="insertMpData" parameterType="MpData">
        insert into u_mp_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">dataValue,</if>
            <if test="fcstTimeSequence != null">fcstTimeSequence,</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence,</if>
            <if test="addTime != null">addTime,</if>
            <if test="longitudeMin != null">longitudeMin,</if>
            <if test="longitudeMax != null">longitudeMax,</if>
            <if test="latitude != null">latitude,</if>
            <if test="altitude != null">altitude,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">#{dataValue},</if>
            <if test="fcstTimeSequence != null">#{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">#{reatimeTimeSequence},</if>
            <if test="addTime != null">#{addTime},</if>
            <if test="longitudeMin != null">#{longitudeMin},</if>
            <if test="longitudeMax != null">#{longitudeMax},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="altitude != null">#{altitude},</if>
        </trim>
    </insert>

    <insert id="insertMpDataList">
        insert into u_mp_data
        (dataValue,fcstTimeSequence,reatimeTimeSequence,addTime,longitudeMin,longitudeMax,latitude,altitude)
        values
        <foreach collection="param" item="item" index="index" separator=",">
            (#{item.dataValue},#{item.fcstTimeSequence},#{item.reatimeTimeSequence},#{item.addTime},#{item.longitudeMin},#{item.longitudeMax},#{item.latitude},#{item.altitude})
        </foreach>
    </insert>

    <update id="updateMpData" parameterType="MpData">
        update u_mp_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataValue != null">dataValue = #{dataValue},</if>
            <if test="fcstTimeSequence != null">fcstTimeSequence = #{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence = #{reatimeTimeSequence},</if>
            <if test="addTime != null">addTime = #{addTime},</if>
            <if test="longitudeMin != null">longitudeMin = #{longitudeMin},</if>
            <if test="longitudeMax != null">longitudeMax = #{longitudeMax},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="altitude != null">altitude = #{altitude},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteMpDataById" parameterType="Long">
        delete
        from u_mp_data
        where id = #{id}
    </delete>

    <delete id="deleteMpDataByIds" parameterType="String">
        delete from u_mp_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteMpDataByFcstTimeSequence">
        delete
        from u_mp_data
        where fcstTimeSequence = #{time}
          and altitude = #{altitude}
    </delete>
    <delete id="deleteOldMpDataList" parameterType="Integer">
        delete
        from u_mp_data
        WHERE addTime &lt; NOW() - INTERVAL 1 DAY
        ORDER BY fcstTimeSequence
            LIMIT 10000
    </delete>
</mapper>
