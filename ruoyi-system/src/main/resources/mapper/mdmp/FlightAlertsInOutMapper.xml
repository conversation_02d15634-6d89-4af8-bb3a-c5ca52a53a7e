<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightAlertsInOutMapper">

    <resultMap type="FlightAlertsInOut" id="FlightAlertsInOutResult">
        <result property="id" column="id"/>
        <result property="aircraftReg" column="aircraftReg"/>
        <result property="addTime" column="addTime"/>
        <result property="circleId" column="circleId"/>
        <result property="statusCode" column="statusCode"/>
        <result property="oddOrEven" column="oddOrEven"/>
        <result property="workId" column="workId"/>
    </resultMap>

    <insert id="insertFlightAlertsInOut" parameterType="FlightAlertsInOut" useGeneratedKeys="true" keyProperty="id">
        insert into flight_alerts_in_out
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="aircraftReg != null">aircraftReg,</if>
            <if test="addTime != null">addTime,</if>
            <if test="circleId != null">circleId,</if>
            <if test="statusCode != null">statusCode,</if>
            <if test="oddOrEven != null">oddOrEven,</if>
            <if test="workId != null">workId,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="aircraftReg != null">#{aircraftReg},</if>
            <if test="addTime != null">#{addTime},</if>
            <if test="circleId != null">#{circleId},</if>
            <if test="statusCode != null">#{statusCode},</if>
            <if test="oddOrEven != null">#{oddOrEven},</if>
            <if test="workId != null">#{workId},</if>
        </trim>
    </insert>
    <update id="updateFlightAlertsInOut">
        update flight_alerts_in_out
        <trim prefix="SET" suffixOverrides=",">
            <if test="aircraftReg != null">aircraftReg = #{aircraftReg},</if>
            <if test="addTime != null">addTime = #{addTime},</if>
            <if test="circleId != null">circleId = #{circleId},</if>
            <if test="statusCode != null">statusCode = #{statusCode},</if>
            <if test="oddOrEven != null">oddOrEven = #{oddOrEven},</if>
            <if test="workId != null">workId = #{workId},</if>
        </trim>
        where id = #{id}
    </update>

    <select id="selectByAircraftRegAndWorkId" resultType="com.ruoyi.system.domain.mdmp.FlightAlertsInOut">
        select *
        from flight_alerts_in_out
        where aircraftReg = #{aircraftReg}
    </select>
    <select id="selectFlightAlertsInOut" resultType="com.ruoyi.system.domain.mdmp.AircraftFlightAlertsInOut">
        select a.aircraftReg as aircraftReg, a.isCivil as isCivil, aio.statusCode as statusCode
        from aircraft a
                 left join flight_alerts_in_out aio on a.aircraftReg = aio.aircraftReg
        where CURRENT_DATE BETWEEN a.validityDate AND a.expirationDate
          and a.aircraftReg = #{aircraftReg}
    </select>

</mapper>
