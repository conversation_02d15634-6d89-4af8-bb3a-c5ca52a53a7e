<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightPlanWorkLongLatMapper">

    <resultMap type="FlightPlanWorkLongLat" id="FlightPlanWorkLongLatResult">
        <result property="id" column="id"/>
        <result property="flightPlanWorkId" column="flightPlanWorkId"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="height" column="height"/>
        <result property="sortNumber" column="sortNumber"/>
        <result property="doubleLongitude" column="doubleLongitude"/>
        <result property="doubleLatitude" column="doubleLatitude"/>
        <result property="groupNumber" column="groupNumber"/>
        <result property="coordinateName" column="coordinateName"/>
    </resultMap>

    <sql id="selectFlightPlanWorkLongLatVo">
        select id,
               flightPlanWorkId,
               longitude,
               latitude,
               height,
               sortNumber,
               doubleLongitude,
               doubleLatitude,
               groupNumber,
               coordinateName
        from work_long_lat
    </sql>

    <select id="selectFlightPlanWorkLongLatList" parameterType="FlightPlanWorkLongLat"
            resultMap="FlightPlanWorkLongLatResult">
        <include refid="selectFlightPlanWorkLongLatVo"/>
        <where>
            <if test="flightPlanWorkId != null ">and flightPlanWorkId = #{flightPlanWorkId}</if>
            <if test="longitude != null  and longitude != ''">and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''">and latitude = #{latitude}</if>
            <if test="height != null ">and height = #{height}</if>
            <if test="sortNumber != null ">and sortNumber = #{sortNumber}</if>
            <if test="doubleLongitude != null ">and doubleLongitude = #{doubleLongitude}</if>
            <if test="doubleLatitude != null ">and doubleLatitude = #{doubleLatitude}</if>
            <if test="groupNumber != null ">and groupNumber = #{groupNumber}</if>
            <if test="coordinateName != null ">and coordinateName = #{coordinateName}</if>
        </where>
    </select>

    <select id="selectFlightPlanWorkLongLatById" parameterType="Long" resultMap="FlightPlanWorkLongLatResult">
        <include refid="selectFlightPlanWorkLongLatVo"/>
        where id = #{id}
    </select>

    <select id="selectWorkLongLatByFlightPlanWorkId"  resultMap="FlightPlanWorkLongLatResult">
        <include refid="selectFlightPlanWorkLongLatVo"/>
        where flightPlanWorkId = #{flightPlanWorkId}
    </select>

    <insert id="insertFlightPlanWorkLongLat" parameterType="FlightPlanWorkLongLat">
        insert into work_long_lat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="flightPlanWorkId != null">flightPlanWorkId,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="height != null">height,</if>
            <if test="sortNumber != null">sortNumber,</if>
            <if test="doubleLongitude != null">doubleLongitude,</if>
            <if test="doubleLatitude != null">doubleLatitude,</if>
            <if test="groupNumber != null">groupNumber,</if>
            <if test="coordinateName != null">coordinateName,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="flightPlanWorkId != null">#{flightPlanWorkId},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="height != null">#{height},</if>
            <if test="sortNumber != null">#{sortNumber},</if>
            <if test="doubleLongitude != null">#{doubleLongitude},</if>
            <if test="doubleLatitude != null">#{doubleLatitude},</if>
            <if test="groupNumber != null">#{groupNumber},</if>
            <if test="coordinateName != null">#{coordinateName},</if>
        </trim>
    </insert>
    <insert id="insertAllFlightPlanWorkLongLat">
        insert into work_long_lat(flightPlanWorkId,
        longitude,latitude,height,sortNumber,doubleLongitude,doubleLatitude,groupNumber,coordinateName)
        values
        <foreach item="item" index="index" collection="flightPlanWorkLongLatList" separator=",">
            (#{item.flightPlanWorkId},#{item.longitude},#{item.latitude},#{item.height},#{item.sortNumber},
             #{item.doubleLongitude},#{item.doubleLatitude},#{item.groupNumber},#{item.coordinateName})
        </foreach>
    </insert>

    <update id="updateFlightPlanWorkLongLat" parameterType="FlightPlanWorkLongLat">
        update work_long_lat
        <trim prefix="SET" suffixOverrides=",">
            <if test="flightPlanWorkId != null">flightPlanWorkId = #{flightPlanWorkId},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="height != null">height = #{height},</if>
            <if test="sortNumber != null">sortNumber = #{sortNumber},</if>
            <if test="doubleLongitude != null">doubleLongitude = #{doubleLongitude},</if>
            <if test="doubleLatitude != null">doubleLatitude = #{doubleLatitude},</if>
            <if test="groupNumber != null">groupNumber = #{groupNumber},</if>
            <if test="coordinateName != null">coordinateName = #{coordinateName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFlightPlanWorkLongLatById" parameterType="Long">
        delete
        from work_long_lat
        where id = #{id}
    </delete>

    <delete id="deleteFlightPlanWorkLongLatByIds" parameterType="String">
        delete from work_long_lat where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
