<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.MapDataObstacleMapper">

    <resultMap type="MapDataObstacle" id="MapDataObstacleResult">
        <result property="id" column="id"/>
        <result property="mapDataIndex" column="mapDataIndex"/>
        <result property="airspaceId" column="airspaceId"/>
        <result property="airspaceMapDataId" column="airspaceMapDataId"/>
        <result property="longitudeStart" column="longitudeStart"/>
        <result property="longitudeEnd" column="longitudeEnd"/>
        <result property="latitudeStart" column="latitudeStart"/>
        <result property="latitudeEnd" column="latitudeEnd"/>
        <result property="heightStart" column="heightStart"/>
        <result property="heightEnd" column="heightEnd"/>
        <result property="pressureStart" column="pressureStart"/>
        <result property="pressureEnd" column="pressureEnd"/>
        <result property="layer" column="layer"/>
        <result property="longitudeIncrease" column="longitudeIncrease"/>
        <result property="latitudeIncrease" column="latitudeIncrease"/>
        <result property="statusCode" column="statusCode"/>
        <result property="index" column="sort"/>
    </resultMap>

    <sql id="selectMapDataObstacleVo">
        select id,
               mapDataIndex,
               airspaceMapDataId,
               airspaceId,
               longitudeStart,
               longitudeEnd,
               latitudeStart,
               latitudeEnd,
               heightStart,
               heightEnd,
               pressureStart,
               pressureEnd,
               layer,
               longitudeIncrease,
               latitudeIncrease,
               statusCode,
               sort
        from map_data
    </sql>


    <insert id="insertMapDataObstacle">
        insert into map_data_obstacle(mapDataIndex,airspaceMapDataId,airspaceId,longitudeStart, longitudeEnd,
        latitudeStart, latitudeEnd,
        heightStart, heightEnd,
        pressureStart, pressureEnd, layer, longitudeIncrease, latitudeIncrease,statusCode,sort)
        values
        <foreach collection="mapDataObstacleList" item="item" index="index" separator=",">
            (#{item.mapDataIndex},#{item.airspaceMapDataId},#{item.airspaceId},#{item.longitudeStart},#{item.longitudeEnd},#{item.latitudeStart},#{item.latitudeEnd},#{item.heightStart},
            #{item.heightEnd},#{item.pressureStart},#{item.pressureEnd},#{item.layer},#{item.longitudeIncrease},
            #{item.latitudeIncrease},#{item.statusCode},#{item.index})
        </foreach>
    </insert>
    <delete id="deleteByAirspaceId">
        delete
        from map_data_obstacle
        where airspaceId = #{airspaceId}

    </delete>

    <select id="selectObstacleByIndex" resultType="java.lang.Integer">
        select sort
        from map_data_obstacle
        where mapDataIndex = #{index}
          AND airspaceId = #{airspaceId}
          AND heightStart &lt;= #{param.elevation}
          AND heightEnd > #{param.elevation}
          AND longitudeStart &lt;= #{param.longitude}
          AND longitudeEnd >= #{param.longitude}
          AND latitudeStart &lt;= #{param.latitude}
          AND latitudeEnd >= #{param.latitude}
          AND statusCode = 55 LIMIT 1
    </select>


</mapper>
