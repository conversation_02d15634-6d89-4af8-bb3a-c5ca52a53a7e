<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.RouteLongLatMapper">

    <resultMap type="RouteLongLat" id="RouteLongLatResult">
        <result property="id"    column="id"    />
        <result property="routeId"    column="routeId"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="height"    column="height"    />
        <result property="sortNumber"    column="sortNumber"    />
        <result property="doubleLongitude" column="doubleLongitude"/>
        <result property="doubleLatitude" column="doubleLatitude"/>
        <result property="coordinateName" column="coordinateName"/>
        <result property="pointType" column="pointType"/>
    </resultMap>

    <sql id="selectRouteLongLatVo">
        select id, routeId, longitude, latitude, height, sortNumber,doubleLongitude,doubleLatitude,coordinateName,pointType from route_long_lat
    </sql>

    <select id="selectRouteLongLatList" parameterType="RouteLongLat" resultMap="RouteLongLatResult">
        <include refid="selectRouteLongLatVo"/>
        <where>
            <if test="routeId != null "> and routeId = #{routeId}</if>
            <if test="longitude != null  and longitude != ''"> and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and latitude = #{latitude}</if>
            <if test="doubleLongitude != null  and doubleLongitude != ''"> and doubleLongitude = #{doubleLongitude}</if>
            <if test="doubleLatitude != null  and doubleLatitude != ''"> and doubleLatitude = #{doubleLatitude}</if>
            <if test="height != null "> and height = #{height}</if>
            <if test="coordinateName != null "> and coordinateName = #{coordinateName}</if>
            <if test="pointType != null "> and pointType = #{pointType}</if>
        </where>
        order by sortNumber ASC
    </select>

    <select id="selectRouteLongLatById" parameterType="Long" resultMap="RouteLongLatResult">
        <include refid="selectRouteLongLatVo"/>
        where id = #{id}
    </select>

    <select id="selectByRouteId" parameterType="Long" resultMap="RouteLongLatResult">
        <include refid="selectRouteLongLatVo"/>
        where routeId = #{routeId}
    </select>

    <insert id="insertRouteLongLat" parameterType="RouteLongLat" useGeneratedKeys="true" keyProperty="id">
        insert into route_long_lat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="routeId != null">routeId,</if>
            <if test="longitude != null and longitude != ''">longitude,</if>
            <if test="latitude != null and latitude != ''">latitude,</if>
            <if test="doubleLongitude != null and doubleLongitude != ''">doubleLongitude,</if>
            <if test="doubleLatitude != null and doubleLatitude != ''">doubleLatitude,</if>
            <if test="height != null">height,</if>
            <if test="sortNumber != null">sortNumber,</if>
            <if test="coordinateName != null">coordinateName,</if>
            <if test="pointType != null">pointType,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="routeId != null">#{routeId},</if>
            <if test="longitude != null and longitude != ''">#{longitude},</if>
            <if test="latitude != null and latitude != ''">#{latitude},</if>
            <if test="doubleLongitude != null and doubleLongitude != ''">doubleLongitude,</if>
            <if test="doubleLatitude != null and doubleLatitude != ''">doubleLatitude,</if>
            <if test="height != null">#{height},</if>
            <if test="sortNumber != null">#{sortNumber},</if>
            <if test="coordinateName != null">#{coordinateName},</if>
            <if test="pointType != null">#{pointType},</if>
         </trim>
    </insert>

    <insert id="bulkInsertRouteLongLats">
        insert into route_long_lat(routeId,longitude, latitude, doubleLongitude, doubleLatitude, height, sortNumber,coordinateName,pointType)
        values
        <foreach collection="routeLongLatList" item="routeLongLat" separator=",">
            (#{routeLongLat.routeId},#{routeLongLat.longitude}, #{routeLongLat.latitude}, #{routeLongLat.doubleLongitude}, #{routeLongLat.doubleLatitude}, #{routeLongLat.height}, #{routeLongLat.sortNumber},#{routeLongLat.coordinateName},#{routeLongLat.pointType})
        </foreach>
    </insert>


    <update id="updateRouteLongLat" parameterType="RouteLongLat">
        update route_long_lat
        <trim prefix="SET" suffixOverrides=",">
            <if test="routeId != null">routeId = #{routeId},</if>
            <if test="longitude != null and longitude != ''">longitude = #{longitude},</if>
            <if test="latitude != null and latitude != ''">latitude = #{latitude},</if>
            <if test="doubleLongitude != null and doubleLongitude != ''">doubleLongitude = #{doubleLongitude},</if>
            <if test="doubleLatitude != null and doubleLatitude != ''">doubleLatitude = #{doubleLatitude},</if>
            <if test="height != null">height = #{height},</if>
            <if test="sortNumber != null">sortNumber = #{sortNumber},</if>
            <if test="coordinateName != null">coordinateName = #{coordinateName},</if>
            <if test="pointType != null">pointType = #{pointType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRouteLongLatById" parameterType="Long">
        delete from route_long_lat where id = #{id}
    </delete>

    <delete id="deleteRouteLongLatByIds" parameterType="String">
        delete from route_long_lat where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteRouteLongLatByRouteId">
        delete from route_long_lat where routeId = #{id}
    </delete>
</mapper>
