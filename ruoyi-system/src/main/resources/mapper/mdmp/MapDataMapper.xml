<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.MapDataMapper">

    <resultMap type="MapData" id="MapDataResult">
        <result property="id" column="id"/>
        <result property="longitudeStart" column="longitudeStart"/>
        <result property="longitudeEnd" column="longitudeEnd"/>
        <result property="latitudeStart" column="latitudeStart"/>
        <result property="latitudeEnd" column="latitudeEnd"/>
        <result property="heightStart" column="heightStart"/>
        <result property="heightEnd" column="heightEnd"/>
        <result property="pressureStart" column="pressureStart"/>
        <result property="pressureEnd" column="pressureEnd"/>
        <result property="layer" column="layer"/>
        <result property="longitudeIncrease" column="longitudeIncrease"/>
        <result property="latitudeIncrease" column="latitudeIncrease"/>
        <result property="statusCode" column="statusCode"/>
        <result property="index" column="sort"/>
        <result property="airspaceName" column="airspaceName"/>
    </resultMap>

    <sql id="selectMapDataVo">
        select id,
               longitudeStart,
               longitudeEnd,
               latitudeStart,
               latitudeEnd,
               heightStart,
               heightEnd,
               pressureStart,
               pressureEnd,
               layer,
               longitudeIncrease,
               latitudeIncrease,
               statusCode,
               sort
        from map_data
    </sql>
    <delete id="deleteAll">
        delete
        from map_data
    </delete>


    <select id="selectMapDataList" resultMap="MapDataResult">
        <include refid="selectMapDataVo"/>
        where layer = 1
    </select>
    <select id="selectMapDataByHeight" resultMap="MapDataResult">
        <include refid="selectMapDataVo"/>
        WHERE
        (heightStart BETWEEN #{startHeight} AND #{endHeight}
        OR (heightEnd BETWEEN #{startHeight} AND #{endHeight})
        OR ( #{startHeight} BETWEEN heightStart AND heightEnd)
        OR ( #{endHeight} BETWEEN heightStart AND heightEnd))

    </select>
    <select id="selectGridsInBoundary" resultMap="MapDataResult">

        SELECT *
        FROM map_data
        WHERE
            longitudeEnd >= #{minLon}
        AND longitudeStart &lt;= #{maxLon}
        AND latitudeEnd >= #{minLat}
        AND latitudeStart&lt;= #{maxLat}
        AND heightStart >= #{startHeight}
        AND heightEnd &lt;= #{endHeight}
    </select>

    <select id="selectMapDataByLongLatAndHeight" resultMap="MapDataResult">
        <include refid="selectMapDataVo"/>
        where ((latitudeStart &gt;= #{mapALat} and latitudeEnd &lt;= #{mapBLat}) or
        (latitudeEnd &gt;= #{mapALat} and latitudeStart &lt;= #{mapBLat}))
        or ((longitudeStart &gt;= #{mapALong} and longitudeEnd &lt;= #{mapBLong}) or
        (longitudeEnd &gt;= #{mapALong} and longitudeStart &lt;= #{mapBLong}))
    </select>
    <!--    <select id="selectMapDataByHeightAndMaxAndMinAndLat" resultMap="MapDataResult">-->
    <!--        select *-->
    <!--        from map_data md-->
    <!--        where heightStart &gt;= #{startHeight}-->
    <!--          and heightEnd &lt;= #{endHeight}-->
    <!--          and latitudeStart = #{lat}-->
    <!--          and longitudeStart &gt;= #{longitudeStart}-->
    <!--          and longitudeStart &lt;= #{longitudeStart1}-->
    <!--        order by sort asc-->
    <!--    </select>-->
    <select id="selectMapDataByHeightAndMaxAndMinAndLat" resultMap="MapDataResult">
        select *
        from map_data md
        where latitudeStart = #{lat}
          and longitudeStart &gt;= #{longitudeStart}
          and longitudeStart &lt;= #{longitudeStart1}
          and (heightStart BETWEEN #{startHeight} AND #{endHeight}
            OR (heightEnd BETWEEN #{startHeight} AND #{endHeight})
            OR (#{startHeight} BETWEEN heightStart AND heightEnd)
            OR (#{endHeight} BETWEEN heightStart AND heightEnd))
        order by sort asc
    </select>

    <select id="mapData" resultMap="MapDataResult">
        select *
        from map_data
        where heightStart &lt;= #{param.height}
          and heightEnd &gt; #{param.height}
          and longitudeStart &lt;= #{param.longitude}
          and longitudeEnd &gt; #{param.longitude}
          and latitudeStart &lt;= #{param.latitude}
          and latitudeEnd &gt; #{param.latitude}
    </select>

    <select id="selectMapDataListById" resultMap="MapDataResult">
        SELECT b.id,
               b.longitudeStart,
               b.longitudeEnd,
               b.latitudeStart,
               b.latitudeEnd,
               b.heightStart,
               b.heightEnd,
               b.pressureStart,
               b.pressureEnd,
               b.layer,
               b.longitudeIncrease,
               b.latitudeIncrease,
               #{colorType} as 'statusCode',b.sort as 'index'
        FROM airspace_map_data a
                 JOIN map_data b on a.sort = b.sort
            and a.airspaceId = #{airspaceId}
        order by b.sort asc
    </select>

    <select id="queryMapDate" resultType="java.lang.Long">
        SELECT sort from map_data
        <where>
            <foreach collection="param" item="item" index="index" separator=" OR ">
                (
                longitudeStart = #{item.longitudeStart}
                AND longitudeEnd = #{item.longitudeEnd}
                AND latitudeStart = #{item.latitudeStart}
                AND latitudeEnd = #{item.latitudeEnd}
                AND heightStart &lt;= #{item.height}
                AND heightEnd > #{item.height}
                )
            </foreach>
        </where>
    </select>
    <select id="queryMapDateByOne" resultType="java.lang.Long">
        SELECT sort
        from map_data
        where longitudeStart = #{param.longitudeStart}
          AND longitudeEnd = #{param.longitudeEnd}
          AND latitudeStart = #{param.latitudeStart}
          AND latitudeEnd = #{param.latitudeEnd}
          AND heightStart &lt;= #{param.height}
          AND heightEnd > #{param.height}
    </select>
    <!--    <select id="selectMapDataByHeightAndMaxAndMinAndLat" resultMap="MapDataResult">-->
    <!--        select *-->
    <!--        from map_data md-->
    <!--        where heightStart &gt;= #{startHeight}-->
    <!--          and heightEnd &lt;= #{endHeight}-->
    <!--          and latitudeStart = #{lat}-->
    <!--          and longitudeStart &gt;= #{longitudeStart}-->
    <!--          and longitudeStart &lt;= #{longitudeStart1}-->
    <!--    </select>-->


    <!--    <select id="selectMapDataByLongLatAndHeight" resultMap="MapDataResult">-->
    <!--        select *-->
    <!--        from map_data md-->
    <!--        where heightStart &gt;= #{startHeight}-->
    <!--          and heightEnd &lt;= #{endHeight}-->
    <!--          and ((md.longitudeStart &gt;= #{voA.longitude} and-->
    <!--                md.longitudeEnd &lt;= #{voB.longitude} and md.latitudeStart &gt;= #{voA.latitude} and-->
    <!--                md.latitudeEnd &lt;= #{voB.longitude}) or (md.longitudeStart &gt;= #{voB.longitude} and-->
    <!--                                                           md.longitudeEnd &lt;= #{voA.longitude} and-->
    <!--                                                           md.latitudeStart &gt;= #{voB.latitude} and-->
    <!--                                                           md.latitudeEnd &lt;= #{voA.latitude}));-->
    <!--    </select>-->

    <insert id="insertMapData">
        insert into map_data(longitudeStart, longitudeEnd, latitudeStart, latitudeEnd, heightStart, heightEnd,
        pressureStart, pressureEnd, layer, longitudeIncrease, latitudeIncrease,statusCode,sort)
        values
        <foreach collection="mapDataList" item="item" index="index" separator=",">
            (#{item.longitudeStart},#{item.longitudeEnd},#{item.latitudeStart},#{item.latitudeEnd},#{item.heightStart},
            #{item.heightEnd},#{item.pressureStart},#{item.pressureEnd},#{item.layer},#{item.longitudeIncrease},
            #{item.latitudeIncrease},#{item.statusCode},#{item.index})
        </foreach>
    </insert>

    <insert id="insertTopographic">
        INSERT into topographic_map_data(statusCode,sort)
        values
        <foreach collection="indexList" item="item" index="index" separator=",">
            (35,#{item} )
        </foreach>
    </insert>

    <select id="getTopographicMapDate" resultMap="MapDataResult">
        SELECT t1.*, t2.statusCode as statusCodes
        FROM topographic_map_data t2
                 JOIN map_data t1 ON t2.sort = t1.sort
        where heightStart &lt;= #{param.elevation}
          and heightEnd > #{param.elevation}
          and longitudeStart &lt;= #{param.longitude}
          and longitudeEnd &gt; #{param.longitude}
          and latitudeStart &lt;= #{param.latitude}
          and latitudeEnd &gt; #{param.latitude}
    </select>

    <select id="mapDataList" resultMap="MapDataResult">
        select *
        from map_data
        where heightStart &lt;= #{param.height}
          and heightEnd &gt;= 0
          and longitudeStart &lt;= #{param.doubleLongitude}
          and longitudeEnd &gt;= #{param.doubleLongitude}
          and latitudeStart &lt;= #{param.doubleLatitude}
          and latitudeEnd &gt;= #{param.doubleLatitude}
    </select>

    <select id="getObstaclesList" resultMap="MapDataResult">
        SELECT md.*,
               a.airspaceName,
               amd.color as statusCodes
        FROM map_data md
                 JOIN
             airspace_map_data amd ON md.sort = amd.sort
                 JOIN
             airspace a ON amd.airspaceId = a.id
        WHERE a.airspaceType = 3
          AND md.heightStart &lt;= #{param.elevation}
          and md.heightEnd > #{param.elevation}
          and md.longitudeStart &lt;= #{param.longitude}
          and md.longitudeEnd &gt; #{param.longitude}
          and md.latitudeStart &lt;= #{param.latitude}
          and md.latitudeEnd &gt; #{param.latitude}
    </select>
    <select id="getControlZoneAlerts" resultMap="MapDataResult">
        SELECT DISTINCT md.*,
                        a.airspaceName,
                        amd.color AS statusCodes
        FROM map_data md
                 JOIN airspace_map_data amd ON md.sort = amd.sort
                 JOIN airspace a ON amd.airspaceId = a.id
                 LEFT JOIN aircraft_airspace aa ON aa.airspaceId = a.id
        WHERE a.airspaceType = 1
          AND NOT EXISTS(
                SELECT 1
                FROM aircraft_airspace aa_filter
                WHERE aa_filter.airspaceId = a.id
                  AND aa_filter.aircraftReg = #{param.aircraftReg}
            )
          AND md.heightStart &lt;= #{param.elevation}
          and md.heightEnd
            > #{param.elevation}
          and md.longitudeStart &lt;= #{param.longitude}
          and md.longitudeEnd &gt; #{param.longitude}
          and md.latitudeStart &lt;= #{param.latitude}
          and md.latitudeEnd &gt; #{param.latitude}
    </select>
    <select id="getMapData" resultMap="MapDataResult">
        select *
        from map_data
        where pressureStart = #{param.height}
          and longitudeStart = #{param.longitude}
          and latitudeStart = #{param.latitude}
    </select>
    <select id="getMeteInfo" resultType="com.ruoyi.system.domain.mdmp.vo.MeteInfo">
        WITH u_data AS (
            SELECT dataValue AS u_dataValue
            FROM (
                     SELECT dataValue,
                            ROW_NUMBER() OVER (
        ORDER BY
        ABS(fcstTimeSequence - UNIX_TIMESTAMP()),
        addTime DESC
        ) AS rn
                     FROM u_mp_data
                     WHERE latitude = #{param.latitude}
                       AND altitude = #{param.altitude}
                       AND longitudeMin &lt;= #{param.longitude}
                       AND longitudeMax >= #{param.longitude}
                 ) t
            WHERE rn = 1
        ),
             v_data AS (
                 SELECT dataValue AS v_dataValue
                 FROM (
                          SELECT dataValue,
                                 ROW_NUMBER() OVER (
        ORDER BY
        ABS(fcstTimeSequence - UNIX_TIMESTAMP()),
        addTime DESC
        ) AS rn
                          FROM v_mp_data
                          WHERE latitude = #{param.latitude}
                            AND altitude = #{param.altitude}
                            AND longitudeMin &lt;= #{param.longitude}
                            AND longitudeMax >= #{param.longitude}
                      ) t
                 WHERE rn = 1
             ),
             temp_data AS (
                 SELECT dataValue AS temp_dataValue
                 FROM (
                          SELECT dataValue,
                                 ROW_NUMBER() OVER (
        ORDER BY
        ABS(fcstTimeSequence - UNIX_TIMESTAMP()),
        addTime DESC
        ) AS rn
                          FROM temperature_mp_data
                          WHERE latitude = #{param.latitude}
                            AND altitude = #{param.altitude}
                            AND longitudeMin &lt;= #{param.longitude}
                            AND longitudeMax >= #{param.longitude}
                      ) t
                 WHERE rn = 1
             ),
             rain_data AS (
                 SELECT dataValue AS rain_dataValue
                 FROM (
                          SELECT dataValue,
                                 ROW_NUMBER() OVER (
        ORDER BY
        ABS(fcstTimeSequence - UNIX_TIMESTAMP()),
        addTime DESC
        ) AS rn
                          FROM rainfall_mp_data
                          WHERE latitude = #{param.latitude}
                            AND altitude = 0.0
                            AND longitudeMin &lt;= #{param.longitude}
                            AND longitudeMax >= #{param.longitude}
                      ) t
                 WHERE rn = 1
             ),
             vis_data AS (
                 SELECT dataValue AS vis_dataValue
                 FROM (
                          SELECT dataValue,
                                 ROW_NUMBER() OVER (
        ORDER BY
        ABS(fcstTimeSequence - UNIX_TIMESTAMP()),
        addTime DESC
        ) AS rn
                          FROM visibility_mp_data
                          WHERE latitude = #{param.latitude}
                            AND altitude = 0.0
                            AND longitudeMin &lt;= #{param.longitude}
                            AND longitudeMax >= #{param.longitude}
                      ) t
                 WHERE rn = 1
             ),
             storm_data AS (
                 SELECT dataValue AS storm_dataValue
                 FROM (
                          SELECT dataValue,
                                 ROW_NUMBER() OVER (
        ORDER BY
        ABS(reatimeTimeSequence - UNIX_TIMESTAMP()),
        addTime DESC
        ) AS rn
                          FROM thunderstorm_mp_data
                          WHERE latitude = #{param.latitude}
                            AND altitude = 0.0
                            AND longitude = #{param.longitude}
                      ) t
                 WHERE rn = 1
             ),
             map_index AS (
                 SELECT sort AS `index`
                 FROM map_data
                 WHERE pressureStart = #{param.altitude}
                   AND longitudeStart = #{param.longitude}
                   AND latitudeStart = #{param.latitude}
            LIMIT 1
            )
        SELECT u_dataValue     as u,
               v_dataValue     as v,
               temp_dataValue  as temperature,
               rain_dataValue  as rainfall,
               vis_dataValue   as visibility,
               storm_dataValue as strom,
               `index`
        FROM u_data,
             v_data,
             temp_data,
             rain_data,
             vis_data,
             storm_data,
             map_index;
    </select>

    <select id="findIntersectingMapDataByMBR" resultMap="MapDataResult">
        SELECT *
        FROM map_data
        WHERE longitudeStart &lt;= #{mbr.maxLongitude}
          AND longitudeEnd &gt;= #{mbr.minLongitude}
          AND latitudeStart &lt;= #{mbr.maxLatitude}
          AND latitudeEnd &gt;= #{mbr.minLatitude}
          AND heightStart &lt;= #{mbr.maxHeight}
          AND heightEnd &gt;= #{mbr.minHeight}
    </select>
    <!--    <select id="getUAndVMapData" resultType="com.ruoyi.system.domain.mdmp.vo.MapDataVo">-->
    <!--        SELECT MAX(addTime)-->
    <!--        INTO @u_latest_addTime-->
    <!--        FROM u_mp_data-->
    <!--        WHERE fcstTimeSequence = #{time};-->
    <!--        SELECT MAX(addTime)-->
    <!--        INTO @v_latest_addTime-->
    <!--        FROM v_mp_data-->
    <!--        WHERE fcstTimeSequence = #{time};-->
    <!--        SELECT m.sort AS 'index', SQRT(u_sq + v_sq) AS wind_speed,-->
    <!--        55 AS statusCodes-->
    <!--        FROM (-->
    <!--        SELECT u.latitude,-->
    <!--        u.longitudeMin,-->
    <!--        u.longitudeMax,-->
    <!--        u.dataValue,-->
    <!--        POW(u.dataValue, 2) AS u_sq-->
    <!--        FROM u_mp_data u-->
    <!--        WHERE u.fcstTimeSequence = #{time}-->
    <!--        AND u.addTime = @u_latest_addTime-->
    <!--        AND u.altitude = (SELECT pressureStart FROM map_data LIMIT 1) ) u-->
    <!--        STRAIGHT_JOIN (-->
    <!--        SELECT-->
    <!--        v.latitude,-->
    <!--        v.longitudeMin,-->
    <!--        v.longitudeMax,-->
    <!--        v.dataValue,-->
    <!--        POW(v.dataValue, 2) AS v_sq-->
    <!--        FROM v_mp_data v-->
    <!--        WHERE-->
    <!--        v.fcstTimeSequence = #{time}-->
    <!--        AND v.addTime = @v_latest_addTime-->
    <!--        AND v.altitude = (SELECT pressureStart FROM map_data LIMIT 1)-->
    <!--        ) v-->
    <!--        ON u.latitude = v.latitude-->
    <!--        STRAIGHT_JOIN map_data m-->
    <!--        ON m.latitudeStart = u.latitude-->
    <!--        AND m.longitudeStart BETWEEN u.longitudeMin AND u.longitudeMax-->
    <!--        AND m.longitudeStart BETWEEN v.longitudeMin AND v.longitudeMax-->
    <!--        WHERE-->
    <!--        1=1-->
    <!--        <if test="param.mpMin != null ">and u_sq + v_sq>= #{param.mpMin}*#{param.mpMin}</if>-->
    <!--        <if test="param.mpMax != null ">and u_sq + v_sq &lt;= #{param.mpMax}*#{param.mpMax}</if>-->
    <!--    </select>-->
    <select id="getUAndVMapData" resultType="com.ruoyi.system.domain.mdmp.vo.MapDataVo">
        SELECT
        m.sort AS 'index',
        SQRT(u.u_sq + v.v_sq) AS wind_speed,
        55 AS statusCodes
        FROM
        (SELECT
        distinct
        altitude,
        latitude,
        longitudeMin AS longitudeMin,
        longitudeMax AS longitudeMax,
        POW(dataValue, 2) AS u_sq
        FROM u_mp_data
        WHERE fcstTimeSequence = #{time}
        AND addTime = (SELECT MAX(addTime) FROM u_mp_data WHERE fcstTimeSequence = #{time})
        ) AS u
        STRAIGHT_JOIN
        ( SELECT
        distinct
        altitude,
        latitude,
        longitudeMin AS longitudeMin,
        longitudeMax AS longitudeMax,
        POW(dataValue, 2) AS v_sq
        FROM v_mp_data
        WHERE fcstTimeSequence = #{time}
        AND addTime = (SELECT MAX(addTime) FROM v_mp_data WHERE fcstTimeSequence = #{time})
        ) AS v
        ON u.latitude = v.latitude
        AND u.altitude = v.altitude
        and u.longitudeMin=v.longitudeMin
        and u.longitudeMax=v.longitudeMax
        STRAIGHT_JOIN map_data AS m
        ON m.latitudeStart = u.latitude
        AND m.longitudeStart BETWEEN u.longitudeMin AND u.longitudeMax
        AND m.pressureStart = u.altitude
        WHERE 1=1
        <if test="param.mpMin != null ">and SQRT(u_sq + v_sq)>= #{param.mpMin}</if>
        <if test="param.mpMax != null ">and SQRT(u_sq + v_sq) &lt;= #{param.mpMax}</if>
    </select>


    <select id="getAirspaceAlarm" resultType="com.ruoyi.system.domain.mdmp.vo.MapDataVo">
        SELECT amd.sort as 'index',55 as statusCodes
        from airspace a
                 join airspace_map_data amd
                      on a.id = amd.airspaceId
                 left join aircraft_airspace aa
                           on aa.airspaceId = a.id
        where a.airspaceType = 1
          and (aa.aircraftReg!=#{aircraftReg}
            or aa.aircraftReg is null)
    </select>

    <select id="getUAndVAlerts" resultType="com.ruoyi.system.domain.mdmp.vo.MapDataVo">
        SELECT m.sort AS 'index', SQRT(u.u_sq + v.v_sq) AS wind_speed,
        55 AS statusCodes
        FROM (SELECT distinct altitude,
        latitude,
        longitudeMin AS longitudeMin,
        longitudeMax AS longitudeMax,
        POW(dataValue, 2) AS u_sq
        FROM u_mp_data
        WHERE fcstTimeSequence = #{time}
        AND addTime = (SELECT MAX(addTime) FROM u_mp_data WHERE fcstTimeSequence = #{time})
        and longitudeMin &lt;= #{flightData.longitude}
        and longitudeMax >= #{flightData.longitude}
        and latitude = #{flightData.latitude}
        and altitude = (
        SELECT pressure
        FROM `height_pressure`
        where heightStart &lt; #{flightData.elevation}
        and heightEnd >= #{flightData.elevation})
        ) AS u
        STRAIGHT_JOIN
        ( SELECT
        distinct
        altitude,
        latitude,
        longitudeMin AS longitudeMin,
        longitudeMax AS longitudeMax,
        POW(dataValue, 2) AS v_sq
        FROM v_mp_data
        WHERE fcstTimeSequence = #{time}
        AND addTime = (SELECT MAX(addTime) FROM v_mp_data WHERE fcstTimeSequence = #{time})
        and longitudeMin &lt;= #{flightData.longitude}
        and longitudeMax >= #{flightData.longitude}
        and latitude = #{flightData.latitude}
        and altitude=(
        SELECT pressure FROM `height_pressure`
        where heightStart
        &lt; #{flightData.elevation}
        and heightEnd >= #{flightData.elevation})
        ) AS v
        ON u.latitude = v.latitude
        AND u.altitude = v.altitude
        and u.longitudeMin=v.longitudeMin
        and u.longitudeMax=v.longitudeMax
        STRAIGHT_JOIN map_data AS m
        ON m.latitudeStart = u.latitude
        AND m.longitudeStart BETWEEN u.longitudeMin AND u.longitudeMax
        AND m.pressureStart = u.altitude
        and m.longitudeStart=#{flightData.longitude}
        and m.latitudeStart= #{flightData.latitude}
        and m.pressureStart=(
        SELECT pressure FROM `height_pressure`
        where heightStart&lt;#{flightData.elevation}
        and heightEnd>=#{flightData.elevation})
        <if test="aircraftMp.mpMin != null ">and SQRT(u_sq + v_sq)>= #{aircraftMp.mpMin}</if>
        <if test="aircraftMp.mpMax != null ">and SQRT(u_sq + v_sq) &lt;= #{aircraftMp.mpMax}</if>
        LIMIT 1
    </select>
    <select id="getAlerts" resultType="com.ruoyi.system.domain.mdmp.MapData">
        SELECT DISTINCT md.*,
               a.airspaceName,
               amd.color AS statusCodes,
               '1'       AS type,
               md.sort as 'index', a.id as airspaceId
        FROM map_data md
                 JOIN airspace_map_data amd ON md.sort = amd.sort
                 JOIN airspace a ON amd.airspaceId = a.id
        WHERE a.airspaceType = 3
          AND md.heightStart &lt;= #{param.elevation}
          AND md.heightEnd > #{param.elevation}
          AND md.longitudeStart &lt;= #{param.longitude}
          AND md.longitudeEnd >= #{param.longitude}
          AND md.latitudeStart &lt;= #{param.latitude}
          AND md.latitudeEnd >= #{param.latitude}
        UNION ALL
        SELECT DISTINCT t1.*,
               NULL          AS airspaceName,
               t2.statusCode AS statusCodes,
               '0'           AS type,
               t1.sort as 'index', 0 as airspaceId
        FROM topographic_map_data t2
                 JOIN map_data t1 ON t2.sort = t1.sort
        WHERE t1.heightStart &lt;= #{param.elevation}
          AND t1.heightEnd > #{param.elevation}
          AND t1.longitudeStart &lt;= #{param.longitude}
          AND t1.longitudeEnd >= #{param.longitude}
          AND t1.latitudeStart &lt;= #{param.latitude}
          AND t1.latitudeEnd >= #{param.latitude}
        UNION ALL
        SELECT DISTINCT md.*,
                        a.airspaceName,
                        amd.color AS statusCodes,
                        '3'       AS type,
                        md.sort as 'index', 0 as airspaceId
        FROM map_data md
                 JOIN airspace_map_data amd ON md.sort = amd.sort
                 JOIN airspace a ON amd.airspaceId = a.id
                 JOIN aircraft_airspace aa ON aa.airspaceId = a.id
        WHERE a.airspaceType = 1
          AND NOT EXISTS(
                SELECT 1
                FROM aircraft_airspace aa_filter
                WHERE aa_filter.airspaceId = a.id
                  AND aa_filter.aircraftReg = #{param.aircraftReg}
            )
          AND md.heightStart &lt;= #{param.elevation}
          AND md.heightEnd > #{param.elevation}
          AND md.longitudeStart &lt;= #{param.longitude}
          AND md.longitudeEnd > #{param.longitude}
          AND md.latitudeStart &lt;= #{param.latitude}
          AND md.latitudeEnd > #{param.latitude}
        UNION ALL
        SELECT DISTINCT md.*,
                        a.airspaceName,
                        amd.color AS statusCodes,
                        '3'       AS type,
                        md.sort as 'index', 0 as airspaceId
        FROM map_data md
                 JOIN airspace_map_data amd ON md.sort = amd.sort
                 JOIN airspace a ON amd.airspaceId = a.id
        WHERE a.airspaceType = 1
          and amd.color = 55
          AND md.heightStart &lt;= #{param.elevation}
          AND md.heightEnd > #{param.elevation}
          AND md.longitudeStart &lt;= #{param.longitude}
          AND md.longitudeEnd > #{param.longitude}
          AND md.latitudeStart &lt;= #{param.latitude}
          AND md.latitudeEnd > #{param.latitude}
    </select>
    <select id="selectMapDataByIndex" resultType="com.ruoyi.system.domain.mdmp.MapData">
        select *
        from map_data
        where sort = #{index}
    </select>
</mapper>
