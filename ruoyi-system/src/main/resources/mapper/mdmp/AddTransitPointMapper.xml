<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.AddTransitPointMapper">
    <resultMap type="AddTransitPoint" id="AddTransitPointMap">
        <result property="id" column="id"/>
        <result property="enterLeaveId" column="enter_leave_id"/>
        <result property="positionName" column="position_name"/>
        <result property="predictArriveTime" column="predict_arrive_time"/>
        <result property="actualArriveTime" column="actual_arrive_time"/>
    </resultMap>

    <select id="queryByEnterLeaveId" resultMap="AddTransitPointMap">
        select
            id,enter_leave_id,position_name,predict_arrive_time,actual_arrive_time
        from add_transit_point
        where enter_leave_id = #{enterLeaveId}
    </select>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into add_transit_point(id,enter_leave_id,position_name,predict_arrive_time,actual_arrive_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.enterLeaveId},#{entity.positionName},#{entity.predictArriveTime},#{entity.actualArriveTime})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into add_transit_point(id,enter_leave_id,position_name,predict_arrive_time,actual_arrive_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.enterLeaveId},#{entity.positionName},#{entity.predictArriveTime},#{entity.actualArriveTime})
        </foreach>
        on duplicate key update
        id=values(id),
        enter_leave_id=values(enter_leave_id),
        position_name=values(position_name),
        predict_arrive_time=values(predict_arrive_time),
        actual_arrive_time=values(actual_arrive_time)
    </insert>

    <!--通过主键删除-->
    <delete id="deleteByEnterLeaveId">
        delete from add_transit_point where enter_leave_id = #{enterLeaveId}
    </delete>
</mapper>
