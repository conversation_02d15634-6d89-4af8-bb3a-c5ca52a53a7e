<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.VisibilityMpDataMapper">

    <resultMap type="VisibilityMpData" id="VisibilityMpDataResult">
        <result property="dataValue" column="dataValue"/>
        <result property="fcstTimeSequence" column="fcstTimeSequence"/>
        <result property="reatimeTimeSequence" column="reatimeTimeSequence"/>
        <result property="latitude" column="latitude"/>
        <result property="altitude" column="altitude"/>
    </resultMap>

    <sql id="selectVisibilityMpDataVo">
        select id,
               dataValue,
               fcstTimeSequence,
               reatimeTimeSequence,
               mpTimeListId,
               longitude,
               latitude,
               altitude
        from visibility_mp_data
    </sql>

    <select id="selectVisibilityMpDataList" parameterType="VisibilityMpData" resultMap="VisibilityMpDataResult">
        <include refid="selectVisibilityMpDataVo"/>
        <where>
            <if test="dataValue != null ">and dataValue = #{dataValue}</if>
            <if test="fcstTimeSequence != null ">and fcstTimeSequence = #{fcstTimeSequence}</if>
            <if test="reatimeTimeSequence != null ">and reatimeTimeSequence = #{reatimeTimeSequence}</if>
            <if test="mpTimeListId != null ">and mpTimeListId = #{mpTimeListId}</if>
            <if test="longitude != null ">and longitude = #{longitude}</if>
            <if test="latitude != null ">and latitude = #{latitude}</if>
            <if test="altitude != null ">and altitude = #{altitude}</if>
        </where>
    </select>

    <select id="selectVisibilityMpDataById" parameterType="Long" resultMap="VisibilityMpDataResult">
        <include refid="selectVisibilityMpDataVo"/>
        where id = #{id}
    </select>

    <select id="selectOldVMpDataList" resultType="java.lang.Long">
        SELECT id
        from visibility_mp_data
        WHERE fcstTimeSequence &lt; UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY));
    </select>
    <select id="selectVisibilityMpDataByMeteInfo" resultType="com.ruoyi.system.domain.mdmp.VisibilityMpData">
        <include refid="selectVisibilityMpDataVo"/>
        where latitude=#{param.latitude}
        ORDER BY ABS(TIMESTAMPDIFF(SECOND, fcstTimeSequence, NOW())) ASC
        LIMIT 1;
    </select>

    <select id="getList" resultType="java.lang.Double">
        SELECT a.dataValue
        FROM (
                 SELECT DISTINCT m.id, u.dataValue
                 FROM map_data m
                          JOIN visibility_mp_data u
                               ON m.latitudeStart = u.latitude
                                   AND m.longitudeStart BETWEEN u.longitudeMin AND u.longitudeMax
                          JOIN (
                     SELECT fcstTimeSequence,
                            MAX(addTime) AS latest_addTime
                     FROM visibility_mp_data
                     where altitude = 0.0
                       and fcstTimeSequence = #{param.time}
                 ) latest_fcst
                               ON u.fcstTimeSequence = latest_fcst.fcstTimeSequence
                                   AND u.addTime = latest_fcst.latest_addTime
                 WHERE m.pressureStart = 950
                   AND u.altitude = 0.0
             ) a
    </select>
    <select id="getVisibilityMpMapData" resultType="com.ruoyi.system.domain.mdmp.vo.MapDataVo">
        WITH latest_rainfall AS (
        SELECT rmd.*,
        MAX(addTime) OVER (PARTITION BY fcstTimeSequence) as max_addtime
        FROM visibility_mp_data rmd
        WHERE fcstTimeSequence = #{time}
        <if test="param.mpMin != null ">and dataValue >= #{param.mpMin}</if>
        <if test="param.mpMax != null ">and dataValue &lt;= #{param.mpMax}</if>
        )
        SELECT md.sort AS 'index', lr.dataValue,
        55 AS statusCodes
        FROM latest_rainfall lr
        JOIN map_data md
        ON md.latitudeStart = lr.latitude
        AND md.longitudeStart BETWEEN lr.longitudeMin AND lr.longitudeMax
        WHERE lr.addTime = lr.max_addtime
        ORDER BY md.sort
    </select>
    <select id="getVisibilityAlerts" resultType="com.ruoyi.system.domain.mdmp.vo.MapDataVo">
        SELECT m.sort as 'index',
        55 AS statusCodes
        FROM map_data m
        INNER JOIN
        visibility_mp_data r
        ON
        m.latitudeStart = r.latitude
        AND m.longitudeStart BETWEEN r.longitudeMin AND r.longitudeMax
        WHERE m.latitudeStart = #{flightData.latitude}
        AND m.longitudeStart = #{flightData.longitude}
        AND m.pressureStart = (
        SELECT pressure
        FROM height_pressure
        WHERE heightStart
        &lt; #{flightData.elevation}
        AND heightEnd >= #{flightData.elevation}
        )
        AND r.fcstTimeSequence = #{time}
        AND r.addTime = (
        SELECT MAX(addTime)
        FROM visibility_mp_data
        WHERE fcstTimeSequence = #{time}
        )
        <if test="aircraftMp.mpMin != null ">and dataValue>= #{aircraftMp.mpMin}</if>
        <if test="aircraftMp.mpMax != null ">and dataValue&lt;= #{aircraftMp.mpMax}</if>
        LIMIT 1
    </select>

    <insert id="insertVisibilityMpData" parameterType="VisibilityMpData" useGeneratedKeys="true" keyProperty="id">
        insert into visibility_mp_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">dataValue,</if>
            <if test="fcstTimeSequence != null">fcstTimeSequence,</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence,</if>
            <if test="mpTimeListId != null">mpTimeListId,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="altitude != null">altitude,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">#{dataValue},</if>
            <if test="fcstTimeSequence != null">#{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">#{reatimeTimeSequence},</if>
            <if test="mpTimeListId != null">#{mpTimeListId},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="altitude != null">#{altitude},</if>
        </trim>
    </insert>

    <insert id="insertVisibilityMpDataList">
        insert into visibility_mp_data
        (dataValue,fcstTimeSequence,reatimeTimeSequence,addTime,longitudeMin,longitudeMax,latitude,altitude)
        values
        <foreach collection="param" item="item" index="index" separator=",">
            (#{item.dataValue},#{item.fcstTimeSequence},#{item.reatimeTimeSequence},#{item.addTime},#{item.longitudeMin},#{item.longitudeMax},#{item.latitude},#{item.altitude})
        </foreach>
    </insert>

    <update id="updateVisibilityMpData" parameterType="VisibilityMpData">
        update visibility_mp_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="dataValue != null">dataValue = #{dataValue},</if>
            <if test="fcstTimeSequence != null">fcstTimeSequence = #{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence = #{reatimeTimeSequence},</if>
            <if test="mpTimeListId != null">mpTimeListId = #{mpTimeListId},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="altitude != null">altitude = #{altitude},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteVisibilityMpDataById" parameterType="Long">
        delete
        from visibility_mp_data
        where id = #{id}
    </delete>

    <delete id="deleteVisibilityMpDataByIds" parameterType="String">
        delete from visibility_mp_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <delete id="deleteVMpDataByIds">
        delete from visibility_mp_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteVisibilityMpDataByFcstTimeSequence">
        delete
        from visibility_mp_data
        where fcstTimeSequence = #{time}
    </delete>

    <delete id="deleteOldMpDataList" parameterType="Integer">
        delete
        from visibility_mp_data
        WHERE addTime &lt; NOW() - INTERVAL 1 DAYORDER BY fcstTimeSequence
            LIMIT 10000
    </delete>
</mapper>
