<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightLogMapper">
    <resultMap type="FlightLog" id="FlightLogMap">
        <result property="id" column="id"/>
        <result property="flightId" column="flight_id"/>
        <result property="callSign" column="call_sign"/>
        <result property="logMessage" column="log_message"/>
        <result property="createTime" column="create_time"/>
    </resultMap>

    <sql id="selectFlightVo">
        select id, flight_id, call_sign, log_message, create_time
        from flight_log
    </sql>

    <select id="queryAll" resultMap="FlightLogMap">
        <include refid="selectFlightVo"/>
        where DATE(create_time) = CURDATE()
        ORDER BY create_time ASC
    </select>

    <select id="queryByFlightId" resultMap="FlightLogMap">
        <include refid="selectFlightVo"/>
        where flight_id = #{flightId}
        <if test="deptCodeList != null and !deptCodeList.isEmpty()">
            and f.deptCode in
            <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
                #{deptCode}
            </foreach>
        </if>
        ORDER BY create_time ASC
    </select>

    <select id="selectFlightLogList" parameterType="FlightLog" resultMap="FlightLogMap">
        <include refid="selectFlightVo"/>
        <where>
            <if test="flightId != null ">and flight_id = #{flightId}</if>
            <if test="callSign != null ">and call_sign = #{callSign}</if>
            <if test="logMessage != null ">and log_message = #{logMessage}</if>
            <if test="createTime != null ">and DATE(create_time) = #{createTime}</if>
        </where>
        order by create_time desc
    </select>

    <select id="selectFlightLog" parameterType="com.ruoyi.system.domain.mdmp.dto.QueryFlightLogListDTO"
            resultMap="FlightLogMap">
        select fl.id,fl.flight_id,fl.call_sign,fl.log_message,fl.create_time
        from flight_log fl
        left join flight f on fl.flight_id = f.id
        <where>
            <if test="createTime != null ">and DATE(fl.create_time) = #{createTime}</if>
            <if test="callSign != null and callSign != ''">and f.call_sign = #{callSign}</if>
            <if test="flightStatus != null ">and f.flight_status = #{flightStatus}</if>
            <if test="deptCodeList != null and !deptCodeList.isEmpty()">
                and f.deptCode in
                <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
                    #{deptCode}
                </foreach>
            </if>
        </where>
        order by create_time desc
    </select>

    <insert id="insert" keyProperty="id" useGeneratedKeys="true">
        insert into flight_log(id, flight_id, call_sign, log_message, create_time)
        values (#{id}, #{flightId}, #{callSign}, #{logMessage}, #{createTime})
    </insert>

    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into flight_log(id,flight_id,call_sign,log_message,create_time)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.flightId},#{entity.callSign},#{entity.logMessage},#{entity.createTime})
        </foreach>
    </insert>

    <delete id="deleteByFlightId">
        delete
        from flight_log
        where flight_id = #{flightId}
    </delete>
</mapper>
