<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightPlanRouteLongLatMapper">

    <resultMap type="FlightPlanRouteLongLat" id="FlightPlanRouteLongLatResult">
        <result property="id" column="id"/>
        <result property="flightPlanRouteId" column="flightPlanRouteId"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="height" column="height"/>
        <result property="sortNumber" column="sortNumber"/>
        <result property="doubleLongitude" column="doubleLongitude"/>
        <result property="doubleLatitude" column="doubleLatitude"/>
        <result property="coordinateName" column="coordinateName"/>
        <result property="pointType" column="pointType"/>
    </resultMap>

    <sql id="selectRouteLongLatVo">
        select id,
               flightPlanRouteId,
               longitude,
               latitude,
               height,
               sortNumber,
               doubleLongitude,
               doubleLatitude,
               coordinateName,
               pointType
        from flight_plan_route_long_lat
    </sql>
    <insert id="insertAllFlightPlanRouteLongLat">
        insert into flight_plan_route_long_lat(flightPlanRouteId,
        longitude,latitude,height,sortNumber,doubleLongitude,doubleLatitude,coordinateName,pointType)
        values
        <foreach item="item" index="index" collection="flightPlanRouteLongLatList" separator=",">
            (#{item.flightPlanRouteId},#{item.longitude},#{item.latitude},#{item.height},#{item.sortNumber},#{item.doubleLongitude},#{item.doubleLatitude},#{item.coordinateName},#{item.pointType})
        </foreach>


    </insert>
    <delete id="deleteAllByFlightPlanRouteId">
        delete from flight_plan_route_long_lat where flightPlanRouteId = #{flightPlanRouteId}
    </delete>

    <select id="flightRouteLongLatList" parameterType="FlightPlanRouteLongLat" resultMap="FlightPlanRouteLongLatResult">
        <include refid="selectRouteLongLatVo"/>
        <where>
            <if test="routeId != null ">and routeId = #{routeId}</if>
            <if test="longitude != null  and longitude != ''">and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''">and latitude = #{latitude}</if>
            <if test="doubleLongitude != null  and doubleLongitude != ''">and doubleLongitude = #{doubleLongitude}</if>
            <if test="doubleLatitude != null  and doubleLatitude != ''">and doubleLatitude = #{doubleLatitude}</if>
            <if test="height != null ">and height = #{height}</if>
            <if test="coordinateName != null ">and coordinateName = #{coordinateName}</if>
        </where>
        order by sortNumber ASC
    </select>

    <select id="selectByFlightPlanRouteId" resultMap="FlightPlanRouteLongLatResult">
        <include refid="selectRouteLongLatVo"/>
        where flightPlanRouteId = #{flightPlanRouteId}
    </select>


</mapper>
