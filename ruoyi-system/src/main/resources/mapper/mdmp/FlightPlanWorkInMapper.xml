<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightPlanWorkInMapper">

    <resultMap type="FlightPlanWorkIn" id="FlightPlanWorkInResult">
        <result property="id" column="id"/>
        <result property="flightPlanWorkId" column="flightPlanWorkId"/>
        <result property="regionType" column="regionType"/>
        <result property="graphType" column="graphType"/>
        <result property="radius" column="radius"/>
        <result property="circleCenterLong" column="circleCenterLong"/>
        <result property="circleCenterLat" column="circleCenterLat"/>
        <result property="workInName" column="workInName"/>
        <result property="minHeight" column="minHeight"/>
        <result property="maxHeight" column="maxHeight"/>

        <!--        * 作业区内经纬度关联表-->
        <collection property="workinLongLatList"
                    select="com.ruoyi.system.mapper.mdmp.WorkinLongLatMapper.selectWorkinLongLatByFlightPlanWorkInId"
                    column="{flightPlanWorkInId=id}">
        </collection>
    </resultMap>

    <sql id="selectFlightPlanWorkInVo">
        select id,
               flightPlanWorkId,
               regionType,
               graphType,
               radius,
               circleCenterLong,
               circleCenterLat,
               workInName,
               minHeight,
               maxHeight
        from work_in
    </sql>

    <select id="selectFlightPlanWorkInList" parameterType="FlightPlanWorkIn" resultMap="FlightPlanWorkInResult">
        <include refid="selectFlightPlanWorkInVo"/>
        <where>
            <if test="flightPlanWorkId != null ">and flightPlanWorkId = #{flightPlanWorkId}</if>
            <if test="regionType != null ">and regionType = #{regionType}</if>
            <if test="graphType != null ">and graphType = #{graphType}</if>
            <if test="radius != null ">and radius = #{radius}</if>
            <if test="circleCenterLong != null  and circleCenterLong != ''">and circleCenterLong = #{circleCenterLong}
            </if>
            <if test="circleCenterLat != null  and circleCenterLat != ''">and circleCenterLat = #{circleCenterLat}</if>
            <if test="workInName != null  and workInName != ''">and workInName = #{workInName}</if>
        </where>
    </select>

    <select id="selectFlightPlanWorkInById" parameterType="Long" resultMap="FlightPlanWorkInResult">
        <include refid="selectFlightPlanWorkInVo"/>
        where id = #{id}
    </select>

    <select id="selectWorkInByFlightPlanWorkId"   resultMap="FlightPlanWorkInResult">
        <include refid="selectFlightPlanWorkInVo"/>
        where flightPlanWorkId = #{flightPlanWorkId}
    </select>

    <insert id="insertFlightPlanWorkIn" parameterType="FlightPlanWorkIn"  useGeneratedKeys="true" keyProperty="id">
        insert into work_in
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flightPlanWorkId != null">flightPlanWorkId,</if>
            <if test="regionType != null">regionType,</if>
            <if test="graphType != null">graphType,</if>
            <if test="radius != null">radius,</if>
            <if test="circleCenterLong != null">circleCenterLong,</if>
            <if test="circleCenterLat != null">circleCenterLat,</if>
            <if test="workInName != null">workInName,</if>
            <if test="minHeight != null">minHeight,</if>
            <if test="maxHeight != null">maxHeight,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flightPlanWorkId != null">#{flightPlanWorkId},</if>
            <if test="regionType != null">#{regionType},</if>
            <if test="graphType != null">#{graphType},</if>
            <if test="radius != null">#{radius},</if>
            <if test="circleCenterLong != null">#{circleCenterLong},</if>
            <if test="circleCenterLat != null">#{circleCenterLat},</if>
            <if test="workInName != null">#{workInName},</if>
            <if test="minHeight != null">#{minHeight},</if>
            <if test="maxHeight != null">#{maxHeight},</if>
        </trim>
    </insert>

    <update id="updateFlightPlanWorkIn" parameterType="FlightPlanWorkIn">
        update work_in
        <trim prefix="SET" suffixOverrides=",">
            <if test="flightPlanWorkId != null">flightPlanWorkId = #{flightPlanWorkId},</if>
            <if test="regionType != null">regionType = #{regionType},</if>
            <if test="graphType != null">graphType = #{graphType},</if>
            <if test="radius != null">radius = #{radius},</if>
            <if test="circleCenterLong != null">circleCenterLong = #{circleCenterLong},</if>
            <if test="circleCenterLat != null">circleCenterLat = #{circleCenterLat},</if>
            <if test="workInName != null">workInName = #{workInName},</if>
            <if test="minHeight != null">minHeight = #{minHeight},</if>
            <if test="maxHeight != null">maxHeight = #{maxHeight},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFlightPlanWorkInById" parameterType="Long">
        delete
        from work_in
        where id = #{id}
    </delete>

    <delete id="deleteFlightPlanWorkInByIds">
        delete from work_in where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByFlightPlanWorkId" resultType="com.ruoyi.system.domain.mdmp.FlightPlanWorkIn">
        <include refid="selectFlightPlanWorkInVo"/>
        where flightPlanWorkId = #{flightPlanWorkId}
    </select>
</mapper>
