<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightPlanAirportMapper">

    <resultMap type="FlightPlanAirport" id="FlightPlanAirportResult">
        <result property="id"    column="id"    />
        <result property="planType"    column="planType"    />
        <result property="flightPlanId"    column="flightPlanId"    />
        <result property="airportName"    column="airportName"    />
        <result property="threeCode"    column="threeCode"    />
        <result property="tetradCode"    column="tetradCode"    />
        <result property="longitude"    column="longitude"    />
        <result property="latitude"    column="latitude"    />
        <result property="airportType"    column="airportType"    />
        <result property="sortNumber"    column="sortNumber"    />
    </resultMap>

    <sql id="selectFlightPlanAirportVo">
        select id, planType, flightPlanId, airportName, threeCode, tetradCode, longitude, latitude, airportType, sortNumber from flight_plan_airport
    </sql>

    <select id="selectFlightPlanAirportList" parameterType="FlightPlanAirport" resultMap="FlightPlanAirportResult">
        <include refid="selectFlightPlanAirportVo"/>
        <where>
            <if test="planType != null "> and planType = #{planType}</if>
            <if test="flightPlanId != null "> and flightPlanId = #{flightPlanId}</if>
            <if test="airportName != null  and airportName != ''"> and airportName like concat('%', #{airportName}, '%')</if>
            <if test="threeCode != null  and threeCode != ''"> and threeCode = #{threeCode}</if>
            <if test="tetradCode != null  and tetradCode != ''"> and tetradCode = #{tetradCode}</if>
            <if test="longitude != null  and longitude != ''"> and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''"> and latitude = #{latitude}</if>
            <if test="airportType != null "> and airportType = #{airportType}</if>
            <if test="sortNumber != null "> and sortNumber = #{sortNumber}</if>
        </where>
    </select>

    <select id="selectFlightPlanAirportByID" parameterType="Long" resultMap="FlightPlanAirportResult">
        <include refid="selectFlightPlanAirportVo"/>
        where id = #{id}
    </select>
    <select id="selectAirportByFlightPlanId"    resultMap="FlightPlanAirportResult">
        <include refid="selectFlightPlanAirportVo"/>
        where flightPlanId = #{flightPlanId}
        and planType=#{planType}
    </select>

    <insert id="insertFlightPlanAirport" parameterType="FlightPlanAirport" useGeneratedKeys="true" keyProperty="ID">
        insert into flight_plan_airport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planType != null">planType,</if>
            <if test="flightPlanId != null">flightPlanId,</if>
            <if test="airportName != null and airportName != ''">airportName,</if>
            <if test="threeCode != null">threeCode,</if>
            <if test="tetradCode != null">tetradCode,</if>
            <if test="longitude != null and longitude != ''">longitude,</if>
            <if test="latitude != null and latitude != ''">latitude,</if>
            <if test="airportType != null">airportType,</if>
            <if test="sortNumber != null">sortNumber,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planType != null">#{planType},</if>
            <if test="flightPlanId != null">#{flightPlanId},</if>
            <if test="airportName != null and airportName != ''">#{airportName},</if>
            <if test="threeCode != null">#{threeCode},</if>
            <if test="tetradCode != null">#{tetradCode},</if>
            <if test="longitude != null and longitude != ''">#{longitude},</if>
            <if test="latitude != null and latitude != ''">#{latitude},</if>
            <if test="airportType != null">#{airportType},</if>
            <if test="sortNumber != null">#{sortNumber},</if>
         </trim>
    </insert>
    <insert id="insertAllFlightPlanAirport">
        insert into flight_plan_airport(planType,
        flightPlanId,airportName,threeCode,tetradCode,longitude,latitude,airportType,sortNumber)
        values
        <foreach item="item" index="index" collection="flightPlanAirportList" separator=",">
            (#{item.planType},#{item.flightPlanId},#{item.airportName},#{item.threeCode},#{item.tetradCode},#{item.longitude},
            #{item.latitude},#{item.airportType},#{item.sortNumber})
        </foreach>
    </insert>

    <update id="updateFlightPlanAirport" parameterType="FlightPlanAirport">
        update flight_plan_airport
        <trim prefix="SET" suffixOverrides=",">
            <if test="planType != null">planType = #{planType},</if>
            <if test="flightPlanId != null">flightPlanId = #{flightPlanId},</if>
            <if test="airportName != null and airportName != ''">airportName = #{airportName},</if>
            <if test="threeCode != null">threeCode = #{threeCode},</if>
            <if test="tetradCode != null">tetradCode = #{tetradCode},</if>
            <if test="longitude != null and longitude != ''">longitude = #{longitude},</if>
            <if test="latitude != null and latitude != ''">latitude = #{latitude},</if>
            <if test="airportType != null">airportType = #{airportType},</if>
            <if test="sortNumber != null">sortNumber = #{sortNumber},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFlightPlanAirportByID" parameterType="Long">
        delete from flight_plan_airport where id = #{id}
    </delete>

    <delete id="deleteFlightPlanAirportByIDs" parameterType="Long">
        delete from flight_plan_airport where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteAllByFlightPlanId">
        delete
        from flight_plan_airport
        where flightPlanId = #{flightPlanId}
          and planType = #{planType}
    </delete>
</mapper>
