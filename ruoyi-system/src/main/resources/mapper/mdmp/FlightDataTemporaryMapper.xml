<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightDataTemporaryMapper">

    <resultMap type="com.ruoyi.system.domain.mdmp.temporary.FlightDataTemporary"
               id="FlightDataTemporaryResult">
        <result property="id"    column="id"    />
        <result property="flightPlanId"    column="flight_plan_id"    />
        <result property="callSign"    column="call_sign"    />
        <result property="aircraftReg"    column="aircraft_reg"    />
        <result property="operationAgency"    column="operation_agency"    />
        <result property="deviceId"    column="deviceId"    />
        <result property="utcYear"    column="utc_year"    />
        <result property="utcMonth"    column="utc_month"    />
        <result property="utcDay"    column="utc_day"    />
        <result property="utcHour"    column="utc_hour"    />
        <result property="utcMinute"    column="utc_minute"    />
        <result property="utcSecond"    column="utc_second"    />
        <result property="flightDate"    column="flight_date"    />
        <result property="dynamicTime"    column="dynamic_time"    />
        <result property="gpsFlag"    column="gps_flag"    />
        <result property="latitude"    column="latitude"    />
        <result property="longitude"    column="longitude"    />
        <result property="elevation"    column="elevation"    />
        <result property="northSpeed"    column="north_speed"    />
        <result property="eastSpeed"    column="east_speed"    />
        <result property="verticalSpeed"    column="vertical_speed"    />
        <result property="headingAngle"    column="heading_angle"    />
        <result property="pitchAngle"    column="pitch_angle"    />
        <result property="rollAngle"    column="roll_angle"    />
        <result property="speed"    column="speed"    />
    </resultMap>

    <sql id="selectFlightDataVo">
        select id, flight_plan_id, call_sign, aircraft_reg, operation_agency, deviceId, utc_year, utc_month, utc_day, utc_hour, utc_minute,
               utc_second, flight_date, dynamic_time, gps_flag, latitude, longitude, elevation, north_speed, east_speed, vertical_speed,
               heading_angle, pitch_angle, roll_angle, speed from flight_data_temporary
    </sql>
    <insert id="insertFlightDataTemporary" parameterType="FlightData" useGeneratedKeys="true" keyProperty="id">
        insert into flight_data_temporary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flightPlanId != null">flight_plan_id,</if>
            <if test="callSign != null">call_sign,</if>
            <if test="aircraftReg != null">aircraft_reg,</if>
            <if test="operationAgency != null">operation_agency,</if>
            <if test="deviceId != null">deviceId,</if>
            <if test="utcYear != null">utc_year,</if>
            <if test="utcMonth != null">utc_month,</if>
            <if test="utcDay != null">utc_day,</if>
            <if test="utcHour != null">utc_hour,</if>
            <if test="utcMinute != null">utc_minute,</if>
            <if test="utcSecond != null">utc_second,</if>
            <if test="flightDate != null">flight_date,</if>
            <if test="dynamicTime != null">dynamic_time,</if>
            <if test="gpsFlag != null">gps_flag,</if>
            <if test="latitude != null">latitude,</if>
            <if test="longitude != null">longitude,</if>
            <if test="elevation != null">elevation,</if>
            <if test="northSpeed != null">north_speed,</if>
            <if test="eastSpeed != null">east_speed,</if>
            <if test="verticalSpeed != null">vertical_speed,</if>
            <if test="headingAngle != null">heading_angle,</if>
            <if test="pitchAngle != null">pitch_angle,</if>
            <if test="rollAngle != null">roll_angle,</if>
            <if test="speed != null">speed,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flightPlanId != null">#{flightPlanId},</if>
            <if test="callSign != null">#{callSign},</if>
            <if test="aircraftReg != null">#{aircraftReg},</if>
            <if test="operationAgency != null">#{operationAgency},</if>
            <if test="deviceId != null">#{deviceId},</if>
            <if test="utcYear != null">#{utcYear},</if>
            <if test="utcMonth != null">#{utcMonth},</if>
            <if test="utcDay != null">#{utcDay},</if>
            <if test="utcHour != null">#{utcHour},</if>
            <if test="utcMinute != null">#{utcMinute},</if>
            <if test="utcSecond != null">#{utcSecond},</if>
            <if test="flightDate != null">#{flightDate},</if>
            <if test="dynamicTime != null">#{dynamicTime},</if>
            <if test="gpsFlag != null">#{gpsFlag},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="elevation != null">#{elevation},</if>
            <if test="northSpeed != null">#{northSpeed},</if>
            <if test="eastSpeed != null">#{eastSpeed},</if>
            <if test="verticalSpeed != null">#{verticalSpeed},</if>
            <if test="headingAngle != null">#{headingAngle},</if>
            <if test="pitchAngle != null">#{pitchAngle},</if>
            <if test="rollAngle != null">#{rollAngle},</if>
            <if test="speed != null">#{speed},</if>
        </trim>
    </insert>

    <select id="selectList" parameterType="string" resultMap="FlightDataTemporaryResult">
        <include refid="selectFlightDataVo"/>
        where aircraft_reg = #{aircraftReg}
    </select>

    <select id="selectList3" parameterType="string" resultMap="FlightDataTemporaryResult">
        <include refid="selectFlightDataVo"/>
        where aircraft_reg = #{aircraftReg}
        and id between 11701 and 12000
    </select>

    <select id="selectList2" resultMap="FlightDataTemporaryResult">
        <include refid="selectFlightDataVo"/>
        where aircraft_reg = #{aircraftReg}
        and call_sign = #{callSign}
    </select>

</mapper>
