<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.SanddustMpDataMapper">

    <resultMap type="SanddustMpData" id="SanddustMpDataResult">
        <result property="dataValue" column="dataValue"/>
        <result property="fcstTimeSequence" column="fcstTimeSequence"/>
        <result property="reatimeTimeSequence" column="reatimeTimeSequence"/>
        <result property="longitudeMin" column="longitudeMin"/>
        <result property="latitude" column="latitude"/>
        <result property="altitude" column="altitude"/>
        <result property="addTime" column="addTime"/>
        <result property="longitudeMax" column="longitudeMax"/>
    </resultMap>

    <sql id="selectSanddustMpDataVo">
        select dataValue,
               fcstTimeSequence,
               reatimeTimeSequence,
               longitudeMin,
               latitude,
               altitude,
               addTime,
               longitudeMax
        from sanddust_mp_data
    </sql>

    <select id="selectSanddustMpDataList" parameterType="SanddustMpData" resultMap="SanddustMpDataResult">
        <include refid="selectSanddustMpDataVo"/>
        <where>
            <if test="dataValue != null ">and dataValue = #{dataValue}</if>
            <if test="fcstTimeSequence != null ">and fcstTimeSequence = #{fcstTimeSequence}</if>
            <if test="reatimeTimeSequence != null ">and reatimeTimeSequence = #{reatimeTimeSequence}</if>
            <if test="longitudeMin != null ">and longitudeMin = #{longitudeMin}</if>
            <if test="latitude != null ">and latitude = #{latitude}</if>
            <if test="altitude != null ">and altitude = #{altitude}</if>
            <if test="addTime != null ">and addTime = #{addTime}</if>
            <if test="longitudeMax != null ">and longitudeMax = #{longitudeMax}</if>
        </where>
    </select>

    <select id="selectSanddustMpDataByDataValue" parameterType="BigDecimal" resultMap="SanddustMpDataResult">
        <include refid="selectSanddustMpDataVo"/>
        where dataValue = #{dataValue}
    </select>

    <select id="getList" resultType="java.lang.Double">
        SELECT a.dataValue
        FROM (
                 SELECT DISTINCT m.id, u.dataValue
                 FROM map_data m
                          JOIN sanddust_mp_data u
                               ON m.latitudeStart = u.latitude
                                   AND m.longitudeStart BETWEEN u.longitudeMin AND u.longitudeMax
                          JOIN (
                     SELECT fcstTimeSequence,
                            MAX(addTime) AS latest_addTime
                     FROM sanddust_mp_data
                     where altitude = 0.0
                       and fcstTimeSequence = #{param.time}
                 ) latest_fcst
                               ON u.fcstTimeSequence = latest_fcst.fcstTimeSequence
                                   AND u.addTime = latest_fcst.latest_addTime
                 WHERE m.pressureStart = 950
                   AND u.altitude = 0.0
             ) a
    </select>

    <insert id="insertSanddustMpData" parameterType="SanddustMpData">
        insert into sanddust_mp_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">dataValue,</if>
            <if test="fcstTimeSequence != null">fcstTimeSequence,</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence,</if>
            <if test="longitudeMin != null">longitudeMin,</if>
            <if test="latitude != null">latitude,</if>
            <if test="altitude != null">altitude,</if>
            <if test="addTime != null">addTime,</if>
            <if test="longitudeMax != null">longitudeMax,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">#{dataValue},</if>
            <if test="fcstTimeSequence != null">#{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">#{reatimeTimeSequence},</if>
            <if test="longitudeMin != null">#{longitudeMin},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="altitude != null">#{altitude},</if>
            <if test="addTime != null">#{addTime},</if>
            <if test="longitudeMax != null">#{longitudeMax},</if>
        </trim>
    </insert>
    <insert id="insertsanddustMpDataList">
        insert into sanddust_mp_data
        (dataValue,fcstTimeSequence,reatimeTimeSequence,addTime,longitudeMin,longitudeMax,latitude,altitude)
        values
        <foreach collection="param" item="item" index="index" separator=",">
            (#{item.dataValue},#{item.fcstTimeSequence},#{item.reatimeTimeSequence},#{item.addTime},#{item.longitudeMin},#{item.longitudeMax},#{item.latitude},#{item.altitude})
        </foreach>
    </insert>

    <update id="updateSanddustMpData" parameterType="SanddustMpData">
        update sanddust_mp_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="fcstTimeSequence != null">fcstTimeSequence = #{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence = #{reatimeTimeSequence},</if>
            <if test="longitudeMin != null">longitudeMin = #{longitudeMin},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="altitude != null">altitude = #{altitude},</if>
            <if test="addTime != null">addTime = #{addTime},</if>
            <if test="longitudeMax != null">longitudeMax = #{longitudeMax},</if>
        </trim>
        where dataValue = #{dataValue}
    </update>

    <delete id="deleteSanddustMpDataByDataValue" parameterType="BigDecimal">
        delete
        from sanddust_mp_data
        where dataValue = #{dataValue}
    </delete>

    <delete id="deleteSanddustMpDataByDataValues" parameterType="String">
        delete from sanddust_mp_data where dataValue in
        <foreach item="dataValue" collection="array" open="(" separator="," close=")">
            #{dataValue}
        </foreach>
    </delete>

    <delete id="deleteAll">
        delete
        from sanddust_mp_data
        WHERE fcstTimeSequence &lt; UNIX_TIMESTAMP(DATE_SUB(NOW(), INTERVAL 1 DAY));
    </delete>
</mapper>
