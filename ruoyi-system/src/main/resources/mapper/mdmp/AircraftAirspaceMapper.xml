<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.AircraftAirspaceMapper">

    <resultMap type="AircraftAirspace" id="AircraftAirspaceResult">
        <result property="id"    column="id"    />
        <result property="aircraftId"    column="aircraftId"    />
        <result property="airspaceId"    column="airspaceId"    />
        <result property="aircraftReg"    column="aircraftReg"    />
    </resultMap>

    <sql id="selectAircraftAirspaceVo">
        select id, aircraftId, airspaceId, aircraftReg from aircraft_airspace
    </sql>

    <select id="selectAircraftAirspaceList" parameterType="AircraftAirspace" resultMap="AircraftAirspaceResult">
        <include refid="selectAircraftAirspaceVo"/>
        <where>
            <if test="aircraftId != null "> and aircraftId = #{aircraftId}</if>
            <if test="airspaceId != null "> and airspaceId = #{airspaceId}</if>
            <if test="aircraftReg != null  and aircraftReg != ''"> and aircraftReg = #{aircraftReg}</if>
        </where>
    </select>

    <select id="selectAircraftAirspaceById" parameterType="Long" resultMap="AircraftAirspaceResult">
        <include refid="selectAircraftAirspaceVo"/>
        where id = #{id}
    </select>

    <insert id="insertAircraftAirspace" parameterType="AircraftAirspace" useGeneratedKeys="true" keyProperty="id">
        insert into aircraft_airspace
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aircraftId != null">aircraftId,</if>
            <if test="airspaceId != null">airspaceId,</if>
            <if test="aircraftReg != null">aircraftReg,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aircraftId != null">#{aircraftId},</if>
            <if test="airspaceId != null">#{airspaceId},</if>
            <if test="aircraftReg != null">#{aircraftReg},</if>
         </trim>
    </insert>

    <insert id="insertAircraftAirspaceList">
        insert into aircraft_airspace(aircraftId,airspaceId,aircraftReg)
        values
        <foreach collection="param" item="item" index="index" separator=",">
            (#{item.aircraftId},#{item.airspaceId},#{item.aircraftReg})
        </foreach>
    </insert>

    <update id="updateAircraftAirspace" parameterType="AircraftAirspace">
        update aircraft_airspace
        <trim prefix="SET" suffixOverrides=",">
            <if test="aircraftId != null">aircraftId = #{aircraftId},</if>
            <if test="airspaceId != null">airspaceId = #{airspaceId},</if>
            <if test="aircraftReg != null">aircraftReg = #{aircraftReg},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAircraftAirspaceById" parameterType="Long">
        delete from aircraft_airspace where id = #{id}
    </delete>

    <delete id="deleteAircraftAirspaceByIds" parameterType="String">
        delete from aircraft_airspace where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteAircraftAirspaceByAirspaceId">
        delete
        from aircraft_airspace
        where airspaceId = #{airspaceId}
    </delete>
</mapper>
