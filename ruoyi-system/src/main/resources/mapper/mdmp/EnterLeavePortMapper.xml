<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.EnterLeavePortMapper">
    <resultMap type="EnterLeavePort" id="EnterLeavePortMap">
        <result property="id" column="id" />
        <result property="flightId" column="flight_id" />
        <result property="runway" column="runway" />
        <result property="runwayId" column="runway_id" />
        <result property="taxiInstruction" column="taxi_instruction" />
        <result property="direction" column="direction" />
        <result property="pushTime" column="push_time" />
        <result property="startUpTime" column="start_up_time" />
        <result property="vip" column="vip" />
        <result property="airConflicts" column="air_conflicts" />
        <result property="alternate" column="alternate" />
        <result property="turnBack" column="turn_back" />
        <result property="receivedMessage" column="received_message" />
        <result property="estReport" column="est_report" />
        <result property="airForceCoordination" column="air_force_coordination" />
        <result property="controllerName" column="controller_name" />
        <result property="flyDate" column="fly_date" />
        <result property="parkingGate" column="parking_gate" />
        <result property="approachCriteria" column="approach_criteria" />
        <collection property="heightList" javaType="java.util.List" ofType="com.ruoyi.system.domain.mdmp.AddHeight">
            <id property="id" column="height_id" />
            <result property="enterLeaveId" column="h_enter_leave_id"/>
            <result property="attribute" column="attribute"/>
            <result property="height" column="height"/>
            <result property="heightType" column="height_type"/>
            <result property="keep" column="keep"/>
        </collection>
        <collection property="commandList" javaType="java.util.List" ofType="com.ruoyi.system.domain.mdmp.AddCommand">
            <id property="id" column="command_id" />
            <result property="enterLeaveId" column="c_enter_leave_id"/>
            <result property="command" column="command"/>
            <result property="commandValue" column="command_value"/>
        </collection>
        <collection property="pointList" javaType="java.util.List" ofType="com.ruoyi.system.domain.mdmp.AddTransitPoint">
            <id property="id" column="point_id" />
            <result property="enterLeaveId" column="p_enter_leave_id"/>
            <result property="positionName" column="position_name"/>
            <result property="predictArriveTime" column="predict_arrive_time"/>
            <result property="actualArriveTime" column="actual_arrive_time"/>
        </collection>
    </resultMap>

    <sql id="selectEnterLeavePortVo">
        select id,flight_id,runway,runway_id,taxi_instruction,direction,push_time,start_up_time,vip,air_conflicts,alternate,turn_back,received_message,est_report,air_force_coordination,controller_name,fly_date,parking_gate,approach_criteria from enter_leave_port
    </sql>

    <select id="queryById" resultMap="EnterLeavePortMap">
        select el.id,el.flight_id,el.runway,el.runway_id,el.taxi_instruction,el.direction,el.push_time,el.start_up_time,el.vip,el.air_conflicts,el.alternate,el.turn_back,el.received_message,el.est_report,el.air_force_coordination,el.controller_name,el.fly_date,el.parking_gate,el.approach_criteria,
               h.id as height_id,h.enter_leave_id as h_enter_leave_id,h.attribute,h.height,h.height_type,h.keep,
               c.id as command_id,c.enter_leave_id as c_enter_leave_id,c.command,c.command_value,
               p.id as point_id,p.enter_leave_id as p_enter_leave_id,p.position_name,p.predict_arrive_time,p.actual_arrive_time
        from enter_leave_port el
                 left join add_height h on h.enter_leave_id = el.id
                 left join add_command c on c.enter_leave_id = el.id
                 left join add_transit_point p on p.enter_leave_id = el.id
        where el.id = #{id}
    </select>

    <select id="selectByFlightId" resultMap="EnterLeavePortMap">
        <include refid="selectEnterLeavePortVo"/>
        where flight_id = #{flightId}
    </select>

    <select id="queryByFlightId" resultMap="EnterLeavePortMap">
        select el.id,el.flight_id,el.runway,el.runway_id,el.taxi_instruction,el.direction,el.push_time,el.start_up_time,el.vip,el.air_conflicts,el.alternate,el.turn_back,el.received_message,el.est_report,el.air_force_coordination,el.controller_name,el.fly_date,el.parking_gate,el.approach_criteria,
               h.id as height_id,h.enter_leave_id as h_enter_leave_id,h.attribute,h.height,h.height_type,h.keep,
               c.id as command_id,c.enter_leave_id as c_enter_leave_id,c.command,c.command_value,
               p.id as point_id,p.enter_leave_id as p_enter_leave_id,p.position_name,p.predict_arrive_time,p.actual_arrive_time
        from enter_leave_port el
        left join add_height h on h.enter_leave_id = el.id
        left join add_command c on c.enter_leave_id = el.id
        left join add_transit_point p on p.enter_leave_id = el.id
        where flight_id = #{flightId}
    </select>

    <insert id="insert" parameterType="EnterLeavePort" useGeneratedKeys="true" keyProperty="id">
        insert into enter_leave_port
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flightId != null">flight_id,</if>
            <if test="runway != null">runway,</if>
            <if test="runwayId != null">runway_id,</if>
            <if test="taxiInstruction != null">taxi_instruction,</if>
            <if test="direction != null">direction,</if>
            <if test="pushTime != null">push_time,</if>
            <if test="startUpTime != null">start_up_time,</if>
            <if test="vip != null">vip,</if>
            <if test="airConflicts != null">air_conflicts,</if>
            <if test="alternate != null">alternate,</if>
            <if test="turnBack != null">turn_back,</if>
            <if test="receivedMessage != null">received_message,</if>
            <if test="estReport != null">est_report,</if>
            <if test="airForceCoordination != null">air_force_coordination,</if>
            <if test="controllerName != null">controller_name,</if>
            <if test="flyDate != null">fly_date,</if>
            <if test="parkingGate != null">parking_gate,</if>
            <if test="approachCriteria != null">approach_criteria,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flightId != null">#{flightId},</if>
            <if test="runway != null">#{runway},</if>
            <if test="runwayId != null">#{runwayId},</if>
            <if test="taxiInstruction != null">#{taxiInstruction},</if>
            <if test="direction != null">#{direction},</if>
            <if test="pushTime != null">#{pushTime},</if>
            <if test="startUpTime != null">#{startUpTime},</if>
            <if test="vip != null">#{vip},</if>
            <if test="airConflicts != null">#{airConflicts},</if>
            <if test="alternate != null">#{alternate},</if>
            <if test="turnBack != null">#{turnBack},</if>
            <if test="receivedMessage != null">#{receivedMessage},</if>
            <if test="estReport != null">#{estReport},</if>
            <if test="airForceCoordination != null">#{airForceCoordination},</if>
            <if test="controllerName != null">#{controllerName},</if>
            <if test="flyDate != null">#{flyDate},</if>
            <if test="parkingGate != null">#{parkingGate},</if>
            <if test="approachCriteria != null">#{approachCriteria},</if>
        </trim>
    </insert>

    <update id="update" parameterType="EnterLeavePort">
        update enter_leave_port
        <trim prefix="SET" suffixOverrides=",">
            <if test="flightId != null">flight_id = #{flightId},</if>
            <if test="runway != null">runway = #{runway},</if>
            <if test="runwayId != null">runway_id = #{runwayId},</if>
            <if test="taxiInstruction != null">taxi_instruction = #{taxiInstruction},</if>
            <if test="direction != null">direction = #{direction},</if>
            <if test="pushTime != null">push_time = #{pushTime},</if>
            <if test="startUpTime != null">start_up_time = #{startUpTime},</if>
            <if test="vip != null">vip = #{vip},</if>
            <if test="airConflicts != null">air_conflicts = #{airConflicts},</if>
            <if test="alternate != null">alternate = #{alternate},</if>
            <if test="turnBack != null">turn_back = #{turnBack},</if>
            <if test="receivedMessage != null">received_message = #{receivedMessage},</if>
            <if test="estReport != null">est_report = #{estReport},</if>
            <if test="airForceCoordination != null">air_force_coordination = #{airForceCoordination},</if>
            <if test="controllerName != null">controller_name = #{controllerName},</if>
            <if test="flyDate != null">fly_date = #{flyDate},</if>
            <if test="parkingGate != null">parking_gate = #{parkingGate},</if>
            <if test="approachCriteria != null">approach_criteria = #{approachCriteria},</if>
        </trim>
        where id = #{id}
    </update>
</mapper>
