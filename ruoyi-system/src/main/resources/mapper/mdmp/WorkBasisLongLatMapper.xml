<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.WorkBasisLongLatMapper">

    <resultMap type="WorkBasisLongLat" id="WorkBasisLongLatResult">
        <result property="id" column="id"/>
        <result property="workBasisId" column="workBasisId"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="height" column="height"/>
        <result property="sortNumber" column="sortNumber"/>
        <result property="doubleLongitude" column="doubleLongitude"/>
        <result property="doubleLatitude" column="doubleLatitude"/>
        <result property="groupNumber" column="groupNumber"/>
        <result property="coordinateName" column="coordinateName"/>
    </resultMap>

    <sql id="selectWorkBasisLongLatVo">
        select id,
               workBasisId,
               longitude,
               latitude,
               height,
               sortNumber,
               doubleLongitude,
               doubleLatitude,
               groupNumber,
               coordinateName
        from work_basis_long_lat
    </sql>

    <select id="selectWorkBasisLongLatList" parameterType="WorkBasisLongLat" resultMap="WorkBasisLongLatResult">
        <include refid="selectWorkBasisLongLatVo"/>
        <where>
            <if test="workBasisId != null ">and workBasisId = #{workBasisId}</if>
            <if test="longitude != null  and longitude != ''">and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''">and latitude = #{latitude}</if>
            <if test="height != null ">and height = #{height}</if>
            <if test="sortNumber != null ">and sortNumber = #{sortNumber}</if>
            <if test="doubleLongitude != null ">and doubleLongitude = #{doubleLongitude}</if>
            <if test="doubleLatitude != null ">and doubleLatitude = #{doubleLatitude}</if>
            <if test="groupNumber != null  and groupNumber != ''">and groupNumber = #{groupNumber}</if>
        </where>
    </select>

    <select id="selectWorkBasisLongLatById" parameterType="Long" resultMap="WorkBasisLongLatResult">
        <include refid="selectWorkBasisLongLatVo"/>
        where id = #{id}
    </select>

    <insert id="insertWorkBasisLongLat" parameterType="WorkBasisLongLat">
        insert into work_basis_long_lat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="workBasisId != null">workBasisId,</if>
            <if test="longitude != null">longitude,</if>
            <if test="latitude != null">latitude,</if>
            <if test="height != null">height,</if>
            <if test="sortNumber != null">sortNumber,</if>
            <if test="doubleLongitude != null">doubleLongitude,</if>
            <if test="doubleLatitude != null">doubleLatitude,</if>
            <if test="groupNumber != null">groupNumber,</if>
            <if test="coordinateName != null">coordinateName,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="workBasisId != null">#{workBasisId},</if>
            <if test="longitude != null">#{longitude},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="height != null">#{height},</if>
            <if test="sortNumber != null">#{sortNumber},</if>
            <if test="doubleLongitude != null">#{doubleLongitude},</if>
            <if test="doubleLatitude != null">#{doubleLatitude},</if>
            <if test="groupNumber != null">#{groupNumber},</if>
            <if test="coordinateName != null">#{groupNumber},</if>
        </trim>
    </insert>

    <insert id="insertWorkBasisLongLatList" parameterType="java.util.List">
        insert into work_basis_long_lat(workBasisId, longitude, latitude, height, sortNumber, doubleLongitude,
        doubleLatitude, groupNumber, coordinateName)
        values
        <foreach collection="workBasisLongLatList" item="item" index="index" separator=",">
            (#{item.workBasisId}, #{item.longitude}, #{item.latitude}, #{item.height}, #{item.sortNumber},
            #{item.doubleLongitude}, #{item.doubleLatitude}, #{item.groupNumber}, #{item.coordinateName})
        </foreach>
    </insert>

    <update id="updateWorkBasisLongLat" parameterType="WorkBasisLongLat">
        update work_basis_long_lat
        <trim prefix="SET" suffixOverrides=",">
            <if test="workBasisId != null">workBasisId = #{workBasisId},</if>
            <if test="longitude != null">longitude = #{longitude},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="height != null">height = #{height},</if>
            <if test="sortNumber != null">sortNumber = #{sortNumber},</if>
            <if test="doubleLongitude != null">doubleLongitude = #{doubleLongitude},</if>
            <if test="doubleLatitude != null">doubleLatitude = #{doubleLatitude},</if>
            <if test="groupNumber != null">groupNumber = #{groupNumber},</if>
            <if test="coordinateName != null">coordinateName = #{coordinateName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWorkBasisLongLatById" parameterType="Long">
        delete
        from work_basis_long_lat
        where id = #{id}
    </delete>

    <delete id="deleteWorkBasisLongLatByIds" parameterType="String">
        delete from work_basis_long_lat where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteWorkBasisLongLatByWorkBasisId">
        delete
        from work_basis_long_lat
        where workBasisId = #{id}
    </delete>
</mapper>