<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.RWindinforesMapper">

    <resultMap type="RWindinfores" id="RWindinforesResult">
        <result property="id" column="id"/>
        <result property="decodeReportId" column="decodeReportId"/>
        <result property="windDirection" column="windDirection"/>
        <result property="windSpeed" column="windSpeed"/>
        <result property="windDirectionBegin" column="windDirectionBegin"/>
        <result property="windDirectionEnd" column="windDirectionEnd"/>
        <result property="gust" column="gust"/>
        <result property="windDirectionCode" column="windDirectionCode"/>
        <result property="colourType" column="colourType"/>
    </resultMap>

    <sql id="selectRWindinforesVo">
        select id,
               colourType,
               decodeReportId,
               windDirection,
               windSpeed,
               windDirectionBegin,
               windDirectionEnd,
               gust,
               windDirectionCode
        from r_windinfores
    </sql>

    <select id="selectRWindinforesList" parameterType="RWindinfores" resultMap="RWindinforesResult">
        <include refid="selectRWindinforesVo"/>
        <where>
            <if test="decodeReportId != null ">and decodeReportId = #{decodeReportId}</if>
            <if test="windDirection != null ">and windDirection = #{windDirection}</if>
            <if test="windSpeed != null ">and windSpeed = #{windSpeed}</if>
            <if test="windDirectionBegin != null ">and windDirectionBegin = #{windDirectionBegin}</if>
            <if test="windDirectionEnd != null ">and windDirectionEnd = #{windDirectionEnd}</if>
            <if test="gust != null ">and gust = #{gust}</if>
            <if test="windDirectionCode != null  and windDirectionCode != ''">and windDirectionCode =
                #{windDirectionCode}
            </if>
            <if test="colourType != null ">and colourType = #{colourType}</if>
        </where>
    </select>

    <select id="selectRWindinforesById" parameterType="Long" resultMap="RWindinforesResult">
        <include refid="selectRWindinforesVo"/>
        where id = #{id}
    </select>

    <insert id="insertRWindinfores" parameterType="RWindinfores" useGeneratedKeys="true" keyProperty="id">
        insert into r_windinfores
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId,</if>
            <if test="windDirection != null">windDirection,</if>
            <if test="windSpeed != null">windSpeed,</if>
            <if test="windDirectionBegin != null">windDirectionBegin,</if>
            <if test="windDirectionEnd != null">windDirectionEnd,</if>
            <if test="gust != null">gust,</if>
            <if test="windDirectionCode != null">windDirectionCode,</if>
            <if test="colourType != null">colourType,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">#{decodeReportId},</if>
            <if test="windDirection != null">#{windDirection},</if>
            <if test="windSpeed != null">#{windSpeed},</if>
            <if test="windDirectionBegin != null">#{windDirectionBegin},</if>
            <if test="windDirectionEnd != null">#{windDirectionEnd},</if>
            <if test="gust != null">#{gust},</if>
            <if test="windDirectionCode != null">#{windDirectionCode},</if>
            <if test="colourType != null">#{colourType},</if>
        </trim>
    </insert>

    <update id="updateRWindinfores" parameterType="RWindinfores">
        update r_windinfores
        <trim prefix="SET" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId = #{decodeReportId},</if>
            <if test="windDirection != null">windDirection = #{windDirection},</if>
            <if test="windSpeed != null">windSpeed = #{windSpeed},</if>
            <if test="windDirectionBegin != null">windDirectionBegin = #{windDirectionBegin},</if>
            <if test="windDirectionEnd != null">windDirectionEnd = #{windDirectionEnd},</if>
            <if test="gust != null">gust = #{gust},</if>
            <if test="windDirectionCode != null">windDirectionCode = #{windDirectionCode},</if>
            <if test="colourType != null">colourType = #{colourType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRWindinforesById" parameterType="Long">
        delete
        from r_windinfores
        where id = #{id}
    </delete>

    <delete id="deleteRWindinforesByIds" parameterType="String">
        delete from r_windinfores where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
