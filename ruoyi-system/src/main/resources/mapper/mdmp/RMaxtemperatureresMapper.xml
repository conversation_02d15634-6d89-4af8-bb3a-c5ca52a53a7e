<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.RMaxtemperatureresMapper">

    <resultMap type="RMaxtemperatureres" id="RMaxtemperatureresResult">
        <result property="id" column="id"/>
        <result property="decodeReportId" column="decodeReportId"/>
        <result property="temperature" column="temperature"/>
        <result property="time" column="time"/>
        <result property="colourType" column="colourType"/>
    </resultMap>

    <sql id="selectRMaxtemperatureresVo">
        select id, decodeReportId, temperature, time, colourType
        from r_maxtemperatureres
    </sql>

    <select id="selectRMaxtemperatureresList" parameterType="RMaxtemperatureres" resultMap="RMaxtemperatureresResult">
        <include refid="selectRMaxtemperatureresVo"/>
        <where>
            <if test="decodeReportId != null ">and decodeReportId = #{decodeReportId}</if>
            <if test="temperature != null ">and temperature = #{temperature}</if>
            <if test="time != null ">and time = #{time}</if>
            <if test="colourType != null ">and colourType = #{colourType}</if>
        </where>
    </select>

    <select id="selectRMaxtemperatureresById" parameterType="Long" resultMap="RMaxtemperatureresResult">
        <include refid="selectRMaxtemperatureresVo"/>
        where id = #{id}
    </select>

    <insert id="insertRMaxtemperatureres" parameterType="RMaxtemperatureres" useGeneratedKeys="true" keyProperty="id">
        insert into r_maxtemperatureres
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId,</if>
            <if test="temperature != null">temperature,</if>
            <if test="time != null">time,</if>
            <if test="colourType != null">colourType,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">#{decodeReportId},</if>
            <if test="temperature != null">#{temperature},</if>
            <if test="time != null">#{time},</if>
            <if test="colourType != null">#{colourType},</if>
        </trim>
    </insert>

    <update id="updateRMaxtemperatureres" parameterType="RMaxtemperatureres">
        update r_maxtemperatureres
        <trim prefix="SET" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId = #{decodeReportId},</if>
            <if test="temperature != null">temperature = #{temperature},</if>
            <if test="time != null">time = #{time},</if>
            <if test="colourType != null">colourType = #{colourType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRMaxtemperatureresById" parameterType="Long">
        delete
        from r_maxtemperatureres
        where id = #{id}
    </delete>

    <delete id="deleteRMaxtemperatureresByIds" parameterType="String">
        delete from r_maxtemperatureres where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
