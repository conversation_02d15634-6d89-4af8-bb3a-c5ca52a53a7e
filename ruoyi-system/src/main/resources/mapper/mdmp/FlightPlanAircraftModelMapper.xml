<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightPlanAircraftModelMapper">

    <resultMap type="FlightPlanAircraftModel" id="FlightPlanAircraftModelResult">
        <result property="id" column="id"/>
        <result property="aircraftName" column="aircraftName"/>
        <result property="tailNumber" column="tailNumber"/>
        <result property="callsign" column="callsign"/>
        <result property="planType" column="planType"/>
        <result property="flightPlanId" column="flightPlanId"/>
    </resultMap>

    <sql id="selectFlightPlanAircraftModelVo">
        select id, aircraftName, tailNumber, callsign, planType, flightPlanId
        from flight_plan_aircraft_model
    </sql>

    <select id="selectFlightPlanAircraftModelList" parameterType="FlightPlanAircraftModel"
            resultMap="FlightPlanAircraftModelResult">
        <include refid="selectFlightPlanAircraftModelVo"/>
        <where>
            <if test="aircraftName != null  and aircraftName != ''">and aircraftName like concat('%', #{aircraftName},
                '%')
            </if>
            <if test="tailNumber != null  and tailNumber != ''">and tailNumber = #{tailNumber}</if>
            <if test="callsign != null  and callsign != ''">and callsign = #{callsign}</if>
            <if test="planType != null ">and planType = #{planType}</if>
            <if test="flightPlanId != null ">and flightPlanId = #{flightPlanId}</if>
        </where>
    </select>

    <select id="selectFlightPlanAircraftModelById" parameterType="Long" resultMap="FlightPlanAircraftModelResult">
        <include refid="selectFlightPlanAircraftModelVo"/>
        where id = #{id}
    </select>

    <select id="selectAircraftByFlightPlanId" resultMap="FlightPlanAircraftModelResult">
        <include refid="selectFlightPlanAircraftModelVo"/>
        where flightPlanId = #{flightPlanId}
        and planType = #{planType}
    </select>

    <select id="selectByFlightPlanIdAndOPlanType" resultMap="FlightPlanAircraftModelResult">
        <include refid="selectFlightPlanAircraftModelVo"/>
        where flightPlanId = #{flightPlanId}
        and planType = #{planType}
    </select>

    <select id="selectByFlightPlanIdAndTailNumber" resultMap="FlightPlanAircraftModelResult">
        <include refid="selectFlightPlanAircraftModelVo"/>
        where flightPlanId = #{flightPlanId}
        and tailNumber = #{aircraftReg}
    </select>

    <insert id="insertFlightPlanAircraftModel" parameterType="FlightPlanAircraftModel" useGeneratedKeys="true"
            keyProperty="id">
        insert into flight_plan_aircraft_model
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aircraftName != null and aircraftName != ''">aircraftName,</if>
            <if test="tailNumber != null">tailNumber,</if>
            <if test="callsign != null">callsign,</if>
            <if test="planType != null">planType,</if>
            <if test="flightPlanId != null">flightPlanId,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aircraftName != null and aircraftName != ''">#{aircraftName},</if>
            <if test="tailNumber != null">#{tailNumber},</if>
            <if test="callsign != null">#{callsign},</if>
            <if test="planType != null">#{planType},</if>
            <if test="flightPlanId != null">#{flightPlanId},</if>
        </trim>
    </insert>

    <insert id="insertAllFlightPlanAircraftModel">
        insert into flight_plan_aircraft_model(aircraftName,tailNumber,callsign,planType,flightPlanId)
        values
        <foreach item="item" index="index" collection="flightPlanAircraftModelList" separator=",">
            (#{item.aircraftName},#{item.tailNumber},#{item.callsign},#{item.planType},#{item.flightPlanId})
        </foreach>
    </insert>

    <update id="updateFlightPlanAircraftModel" parameterType="FlightPlanAircraftModel">
        update flight_plan_aircraft_model
        <trim prefix="SET" suffixOverrides=",">
            <if test="aircraftName != null and aircraftName != ''">aircraftName = #{aircraftName},</if>
            <if test="tailNumber != null">tailNumber = #{tailNumber},</if>
            <if test="callsign != null">callsign = #{callsign},</if>
            <if test="planType != null">planType = #{planType},</if>
            <if test="flightPlanId != null">flightPlanId = #{flightPlanId},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFlightPlanAircraftModelById" parameterType="Long">
        delete
        from flight_plan_aircraft_model
        where id = #{id}
    </delete>

    <delete id="deleteFlightPlanAircraftModelByIds" parameterType="String">
        delete from flight_plan_aircraft_model where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteAllByFlightPlanId">
        delete
        from flight_plan_aircraft_model
        where flightPlanId = #{flightPlanId}
          and planType = #{planType}
    </delete>
</mapper>
