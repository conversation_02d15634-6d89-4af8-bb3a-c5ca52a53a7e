<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.RVisinforesMapper">

    <resultMap type="RVisinfores" id="RVisinforesResult">
        <result property="id" column="id"/>
        <result property="decodeReportId" column="decodeReportId"/>
        <result property="vis" column="vis"/>
        <result property="visMin" column="visMin"/>
        <result property="visMinDirection" column="visMinDirection"/>
        <result property="colourType" column="colourType"/>
    </resultMap>

    <sql id="selectRVisinforesVo">
        select id, decodeReportId, vis, visMin, visMinDirection, colourType
        from r_visinfores
    </sql>

    <select id="selectRVisinforesList" parameterType="RVisinfores" resultMap="RVisinforesResult">
        <include refid="selectRVisinforesVo"/>
        <where>
            <if test="decodeReportId != null ">and decodeReportId = #{decodeReportId}</if>
            <if test="vis != null  and vis != ''">and vis = #{vis}</if>
            <if test="visMin != null ">and visMin = #{visMin}</if>
            <if test="visMinDirection != null  and visMinDirection != ''">and visMinDirection = #{visMinDirection}</if>
            <if test="colourType != null ">and colourType = #{colourType}</if>
        </where>
    </select>

    <select id="selectRVisinforesById" parameterType="Long" resultMap="RVisinforesResult">
        <include refid="selectRVisinforesVo"/>
        where id = #{id}
    </select>

    <insert id="insertRVisinfores" parameterType="RVisinfores" useGeneratedKeys="true" keyProperty="id">
        insert into r_visinfores
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId,</if>
            <if test="vis != null">vis,</if>
            <if test="visMin != null">visMin,</if>
            <if test="visMinDirection != null">visMinDirection,</if>
            <if test="colourType != null">colourType,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">#{decodeReportId},</if>
            <if test="vis != null">#{vis},</if>
            <if test="visMin != null">#{visMin},</if>
            <if test="visMinDirection != null">#{visMinDirection},</if>
            <if test="colourType != null">#{colourType},</if>
        </trim>
    </insert>

    <update id="updateRVisinfores" parameterType="RVisinfores">
        update r_visinfores
        <trim prefix="SET" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId = #{decodeReportId},</if>
            <if test="vis != null">vis = #{vis},</if>
            <if test="visMin != null">visMin = #{visMin},</if>
            <if test="visMinDirection != null">visMinDirection = #{visMinDirection},</if>
            <if test="colourType != null">colourType = #{colourType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRVisinforesById" parameterType="Long">
        delete
        from r_visinfores
        where id = #{id}
    </delete>

    <delete id="deleteRVisinforesByIds" parameterType="String">
        delete from r_visinfores where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
