<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.WorkinLongLatMapper">

    <resultMap type="WorkinLongLat" id="WorkinLongLatResult">
        <result property="id" column="id"/>
        <result property="workInId" column="workInId"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="height" column="height"/>
        <result property="sortNumber" column="sortNumber"/>
        <result property="doubleLongitude" column="doubleLongitude"/>
        <result property="doubleLatitude" column="doubleLatitude"/>
        <result property="coordinateName" column="coordinateName"/>
    </resultMap>

    <sql id="selectWorkinLongLatVo">
        select id,
               workInId,
               longitude,
               latitude,
               height,
               sortNumber,
               doubleLongitude,
               doubleLatitude,
               coordinateName
        from workin_long_lat
    </sql>

    <select id="selectWorkinLongLatList" parameterType="WorkinLongLat" resultMap="WorkinLongLatResult">
        <include refid="selectWorkinLongLatVo"/>
        <where>
            <if test="workInId != null ">and workInId = #{workInId}</if>
            <if test="longitude != null  and longitude != ''">and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''">and latitude = #{latitude}</if>
            <if test="height != null ">and height = #{height}</if>
            <if test="sortNumber != null  and sortNumber != ''">and sortNumber = #{sortNumber}</if>
            <if test="doubleLongitude != null ">and doubleLongitude = #{doubleLongitude}</if>
            <if test="doubleLatitude != null ">and doubleLatitude = #{doubleLatitude}</if>
            <if test="coordinateName != null ">and coordinateName = #{coordinateName}</if>
        </where>
    </select>

    <select id="selectWorkinLongLatById" parameterType="Long" resultMap="WorkinLongLatResult">
        <include refid="selectWorkinLongLatVo"/>
        where id = #{id}
    </select>

    <select id="selectWorkinLongLatByFlightPlanWorkInId" resultMap="WorkinLongLatResult">
        <include refid="selectWorkinLongLatVo"/>
        where workInId = #{flightPlanWorkInId}
    </select>

    <insert id="insertWorkinLongLat" parameterType="WorkinLongLat">
        insert into workin_long_lat
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="workInId != null">workInId,</if>
            <if test="longitude != null and longitude != ''">longitude,</if>
            <if test="latitude != null and latitude != ''">latitude,</if>
            <if test="height != null">height,</if>
            <if test="sortNumber != null and sortNumber != ''">sortNumber,</if>
            <if test="doubleLongitude != null">doubleLongitude,</if>
            <if test="doubleLatitude != null">doubleLatitude,</if>
            <if test="coordinateName != null">coordinateName,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},</if>
            <if test="workInId != null">#{workInId},</if>
            <if test="longitude != null and longitude != ''">#{longitude},</if>
            <if test="latitude != null and latitude != ''">#{latitude},</if>
            <if test="height != null">#{height},</if>
            <if test="sortNumber != null and sortNumber != ''">#{sortNumber},</if>
            <if test="doubleLongitude != null">#{doubleLongitude},</if>
            <if test="doubleLatitude != null">#{doubleLatitude},</if>
            <if test="coordinateName != null">#{coordinateName},</if>
        </trim>
    </insert>
    <insert id="insertAllWorkinLongLat">
        insert into workin_long_lat(workInId,
        longitude,latitude,height,sortNumber,doubleLongitude,doubleLatitude,coordinateName)
        values
        <foreach item="item" index="index" collection="workinLongLatList" separator=",">
            (#{item.workInId},#{item.longitude},#{item.latitude},#{item.height},#{item.sortNumber},#{item.doubleLongitude},#{item.doubleLatitude},#{item.coordinateName})
        </foreach>

    </insert>

    <update id="updateWorkinLongLat" parameterType="WorkinLongLat">
        update workin_long_lat
        <trim prefix="SET" suffixOverrides=",">
            <if test="workInId != null">workInId = #{workInId},</if>
            <if test="longitude != null and longitude != ''">longitude = #{longitude},</if>
            <if test="latitude != null and latitude != ''">latitude = #{latitude},</if>
            <if test="height != null">height = #{height},</if>
            <if test="sortNumber != null and sortNumber != ''">sortNumber = #{sortNumber},</if>
            <if test="doubleLongitude != null">doubleLongitude = #{doubleLongitude},</if>
            <if test="doubleLatitude != null">doubleLatitude = #{doubleLatitude},</if>
            <if test="coordinateName != null">coordinateName = #{coordinateName},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteWorkinLongLatById" parameterType="Long">
        delete
        from workin_long_lat
        where id = #{id}
    </delete>

    <delete id="deleteWorkinLongLatByIds" parameterType="Long">
        delete from workin_long_lat where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByFlightPlanWorkInId" parameterType="long" resultType="com.ruoyi.system.domain.mdmp.WorkinLongLat">
        <include refid="selectWorkinLongLatVo"/>
        where workInId = #{flightPlanWorkInId}
    </select>
</mapper>
