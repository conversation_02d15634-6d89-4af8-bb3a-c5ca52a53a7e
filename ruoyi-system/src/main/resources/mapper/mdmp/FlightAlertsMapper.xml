<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightAlertsMapper">

    <resultMap type="FlightAlerts" id="FlightAlertsResult">
        <result property="id" column="id"/>
        <result property="aircraftReg" column="aircraftReg"/>
        <result property="alarmName" column="alarmName"/>
        <result property="alertStatus" column="alertStatus"/>
        <result property="alertTime" column="alertTime"/>
        <result property="alertType" column="alertType"/>
        <result property="version" column="version"/>
        <result property="index" column="sort"/>
        <result property="flightDataId" column="flightDataId"/>
        <result property="statusCode" column="statusCode"/>
        <result property="color" column="color"/>
        <result property="targetIdentification" column="targetIdentification"/>
        <result property="isSent" column="isSent"/>
        <result property="subIndex" column="subIndex"/>
        <result property="relatedAircraftReg" column="relatedAircraftReg"/>
        <result property="uniqueId" column="uniqueId"/>
    </resultMap>

    <sql id="selectFlightAlertsVo">
        select id,
               aircraftReg,
               alarmName,
               alertStatus,
               alertTime,
               alertType,
               version,
               sort,
               flightDataId,
               statusCode,
               color,
               targetIdentification,
               isSent,
               subIndex,
               relatedAircraftReg,
               uniqueId
        from flight_alerts
    </sql>

    <select id="selectFlightAlertsList" parameterType="FlightAlerts" resultMap="FlightAlertsResult">
        <include refid="selectFlightAlertsVo"/>
        <where>
            <if test="aircraftReg != null  and aircraftReg != ''">and aircraftReg = #{aircraftReg}</if>
            <if test="alarmName != null  and alarmName != ''">and alarmName like concat('%', #{alarmName}, '%')</if>
            <if test="alertStatus != null ">and alertStatus = #{alertStatus}</if>
            <if test="alertTime != null ">and alertTime = #{alertTime}</if>
            <if test="alertType != null ">and alertType = #{alertType}</if>
            <if test="version != null ">and version = #{version}</if>
            <if test="sort != null ">and sort = #{index}</if>
        </where>
    </select>

    <select id="selectFlightAlertsById" parameterType="Long" resultMap="FlightAlertsResult">
        <include refid="selectFlightAlertsVo"/>
        where id = #{id}
    </select>

    <select id="selectFlightAlertsListByAircraftReg" resultMap="FlightAlertsResult">
        SELECT *
        FROM flight_alerts
        WHERE version = (SELECT MAX(version) FROM flight_alerts)
        and aircraftReg = #{aircraftReg}
        <if test="alertType != null and alertType != ''">and alertType = #{alertType}</if>
        and alertStatus = 1
    </select>
    <select id="selectMaxVersion" resultType="java.lang.Long">
        SELECT MAX(version)
        FROM flight_alerts
        where aircraftReg = #{aircraftReg}
    </select>

    <select id="selectFlightAlertsLists" resultMap="FlightAlertsResult">
        select *, sort as 'index'
        from flight_alerts
        where aircraftReg = #{aircraftReg}
          and alertStatus = 1
          and isSent = 0
          AND alertTime >= CAST(CURRENT_TIMESTAMP AS DATE)
        UNION
        SELECT *, sort AS 'index'
        FROM flight_alerts
        WHERE alertStatus = 1
          AND isSent = 0
          AND alertTime &lt;= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
          AND alertTime >= CURDATE()
    </select>
    <select id="selectConnection" resultMap="FlightAlertsResult">
        select *, sort as 'index'
        from flight_alerts
        where aircraftReg = #{aircraftReg}
          and alertStatus = 1
          and alertType = 8
          and isSent = 0
          AND alertTime >= CAST(CURRENT_TIMESTAMP AS DATE)
        UNION
        SELECT *, sort AS 'index'
        FROM flight_alerts
        WHERE alertStatus = 1
          AND alertType = 8
          AND isSent = 0
          AND alertTime &lt;= DATE_SUB(NOW(), INTERVAL 1 MINUTE)
          AND alertTime >= CURDATE()
    </select>



    <insert id="insertFlightAlerts" parameterType="FlightAlerts" useGeneratedKeys="true" keyProperty="id">
        insert into flight_alerts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="aircraftReg != null">aircraftReg,</if>
            <if test="alarmName != null">alarmName,</if>
            <if test="alertStatus != null">alertStatus,</if>
            <if test="alertTime != null">alertTime,</if>
            <if test="alertType != null">alertType,</if>
            <if test="version != null">version,</if>
            <if test="index != null">sort ,</if>
            <if test="flightDataId != null">flightDataId,</if>
            <if test="statusCode != null">statusCode,</if>
            <if test="color != null">color,</if>
            <if test="targetIdentification != null">targetIdentification,</if>
            <if test="isSent != null">isSent,</if>
            <if test="subIndex != null">subIndex,</if>
            <if test="relatedAircraftReg != null">relatedAircraftReg,</if>
            <if test="uniqueId != null">uniqueId,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="aircraftReg != null">#{aircraftReg},</if>
            <if test="alarmName != null">#{alarmName},</if>
            <if test="alertStatus != null">#{alertStatus},</if>
            <if test="alertTime != null">#{alertTime},</if>
            <if test="alertType != null">#{alertType},</if>
            <if test="version != null">#{version},</if>
            <if test="index != null">#{index},</if>
            <if test="flightDataId != null">#{flightDataId},</if>
            <if test="statusCode != null">#{statusCode},</if>
            <if test="color != null">#{color},</if>
            <if test="targetIdentification != null">#{targetIdentification},</if>
            <if test="isSent != null">#{isSent},</if>
            <if test="subIndex != null">#{subIndex},</if>
            <if test="relatedAircraftReg != null">#{relatedAircraftReg},</if>
            <if test="uniqueId != null">#{uniqueId},</if>
        </trim>
    </insert>
    <insert id="insertBatchFlightAlerts">
        insert into flight_alerts
        (aircraftReg, alarmName, alertStatus, alertTime, alertType, version, sort, flightDataId, statusCode, color,
        targetIdentification, isSent, subIndex, relatedAircraftReg, uniqueId)
        values
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.aircraftReg}, #{item.alarmName}, #{item.alertStatus}, #{item.alertTime}, #{item.alertType},
            #{item.version}, #{item.index}, #{item.flightDataId}, #{item.statusCode}, #{item.color},
            #{item.targetIdentification}, #{item.isSent}, #{item.subIndex}, #{item.relatedAircraftReg},
            #{item.uniqueId})
        </foreach>
    </insert>

    <update id="updateFlightAlerts" parameterType="FlightAlerts">
        update flight_alerts
        <trim prefix="SET" suffixOverrides=",">
            <if test="aircraftReg != null">aircraftReg = #{aircraftReg},</if>
            <if test="alarmName != null">alarmName = #{alarmName},</if>
            <if test="alertStatus != null">alertStatus = #{alertStatus},</if>
            <if test="alertTime != null">alertTime = #{alertTime},</if>
            <if test="alertType != null">alertType = #{alertType},</if>
            <if test="version != null">version = #{version},</if>
            <if test="sort != null">sort = #{index},</if>
            <if test="flightDataId != null">flightDataId = #{flightDataId},</if>
            <if test="statusCode != null">statusCodes = #{statusCode},</if>
            <if test="color != null">color = #{color},</if>
            <if test="targetIdentification != null">targetIdentification = #{targetIdentification},</if>
            <if test="subIndex != null">subIndex = #{subIndex},</if>
            <if test="relatedAircraftReg != null">relatedAircraftReg = #{relatedAircraftReg},</if>
            <if test="uniqueId != null">uniqueId = #{uniqueId},</if>
        </trim>
        where id = #{id}
    </update>
    <update id="batchUpdateSentStatus">
        UPDATE flight_alerts
        SET isSent = 1
        WHERE id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>
    <delete id="deleteFlightAlertsById" parameterType="Long">
        delete
        from flight_alerts
        where id = #{id}
    </delete>

    <delete id="deleteFlightAlertsByIds" parameterType="String">
        delete from flight_alerts where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
