<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.DailyFlightPlanMapper">

    <resultMap type="DailyFlightPlan" id="DailyFlightPlanResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="nextDayFlightPlanId" column="nextDayFlightPlanId"/>
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="taskType" column="taskType"/>
        <result property="flightDate" column="flightDate"/>
        <result property="temporary" column="temporary"/>
        <result property="remark" column="remark"/>
        <result property="refusalExplain" column="refusalExplain"/>
        <result property="isDelete" column="isDelete"/>
        <result property="auditor" column="auditor"/>
        <result property="auditTime" column="auditTime"/>
        <result property="creator" column="creator"/>
        <result property="creationTime" column="creationTime"/>
        <result property="contactName" column="contactName"/>
        <result property="contactPhone" column="contactPhone"/>
        <result property="serialNo" column="serialNo"/>
        <result property="status" column="status"/>
        <result property="answeringMachineCode" column="answeringMachineCode"/>
        <result property="explainDepartureTime" column="explainDepartureTime"/>
        <result property="explainArrivalTime" column="explainArrivalTime"/>
        <result property="radioFrequency" column="radioFrequency"/>
        <result property="actualDepartureTime" column="actualDepartureTime"/>
        <result property="actualArrivalTime" column="actualArrivalTime"/>
        <result property="isWork" column="isWork"/>
        <result property="agreementType" column="agreementType"/>
        <result property="agreementStartDate" column="agreementStartDate"/>
        <result property="agreementEndDate" column="agreementEndDate"/>
        <result property="planType" column="planType"/>
        <result property="deptCode" column="deptCode"/>
        <!--        * 计划 机型机号关联表-->
        <collection property="planModelAircraftList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanAircraftModelMapper.selectAircraftByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

        <!--        * 计划 机场关联表-->
        <collection property="flightPlanAirportList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanAirportMapper.selectAirportByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

        <!--        * 计划 航线关联表-->
        <collection property="flightPlanRouteList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanRouteMapper.selectRouteByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

        <!--        * 计划 附件关联表-->
        <collection property="flightPlanFileList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanFileMapper.selectFileByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>
        <!--        * 计划 作业区关联表-->
        <collection property="flightPlanWorkList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanWorkMapper.selectWorkByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>


    </resultMap>

    <!--    查询List-->
    <resultMap type="DailyFlightPlan" id="DailyFlightPlanListResult">
        <result property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="planType" column="planType"/>
        <result property="nextDayFlightPlanId" column="nextDayFlightPlanId"/>
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="taskType" column="taskType"/>
        <result property="flightDate" column="flightDate"/>
        <result property="temporary" column="temporary"/>
        <result property="remark" column="remark"/>
        <result property="refusalExplain" column="refusalExplain"/>
        <result property="isDelete" column="isDelete"/>
        <result property="auditor" column="auditor"/>
        <result property="auditTime" column="auditTime"/>
        <result property="creator" column="creator"/>
        <result property="creationTime" column="creationTime"/>
        <result property="contactName" column="contactName"/>
        <result property="contactPhone" column="contactPhone"/>
        <result property="serialNo" column="serialNo"/>
        <result property="status" column="status"/>
        <result property="answeringMachineCode" column="answeringMachineCode"/>
        <result property="explainDepartureTime" column="explainDepartureTime"/>
        <result property="explainArrivalTime" column="explainArrivalTime"/>
        <result property="radioFrequency" column="radioFrequency"/>
        <result property="actualDepartureTime" column="actualDepartureTime"/>
        <result property="actualArrivalTime" column="actualArrivalTime"/>
        <result property="isWork" column="isWork"/>
        <result property="agreementType" column="agreementType"/>
        <result property="agreementStartDate" column="agreementStartDate"/>
        <result property="agreementEndDate" column="agreementEndDate"/>
        <result property="deptCode" column="deptCode"/>
        <!--        * 计划 机型机号关联表-->
        <collection property="planModelAircraftList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanAircraftModelMapper.selectAircraftByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>
        <!--        * 计划 附件关联表-->
        <collection property="flightPlanFileList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanFileMapper.selectFileByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>
    </resultMap>

    <!--测试发送abs数据用的-->
    <resultMap type="DailyFlightPlan" id="DailyFlightPlanResult2">
        <result property="id" column="id"/>
        <result property="nextDayFlightPlanId" column="nextDayFlightPlanId"/>
        <result property="companyName" column="companyName"/>
        <result property="companyCode" column="companyCode"/>
        <result property="taskType" column="taskType"/>
        <result property="flightDate" column="flightDate"/>
        <result property="temporary" column="temporary"/>
        <result property="refusalExplain" column="refusalExplain"/>
        <result property="isDelete" column="isDelete"/>
        <result property="auditor" column="auditor"/>
        <result property="auditTime" column="auditTime"/>
        <result property="creator" column="creator"/>
        <result property="creationTime" column="creationTime"/>
        <result property="contactName" column="contactName"/>
        <result property="contactPhone" column="contactPhone"/>
        <result property="serialNo" column="serialNo"/>
        <result property="status" column="status"/>
        <result property="answeringMachineCode" column="answeringMachineCode"/>
        <result property="explainDepartureTime" column="explainDepartureTime"/>
        <result property="explainArrivalTime" column="explainArrivalTime"/>
        <result property="radioFrequency" column="radioFrequency"/>
        <result property="actualDepartureTime" column="actualDepartureTime"/>
        <result property="actualArrivalTime" column="actualArrivalTime"/>
        <result property="isWork" column="isWork"/>
        <result property="deptCode" column="deptCode"/>
    </resultMap>

    <sql id="selectDailyFlightPlanVo">
        select id,
               name,
               3 as planType,
               nextDayFlightPlanId,
               companyName,
               companyCode,
               taskType,
               flightDate, temporary, remark, refusalExplain, isDelete, auditor, auditTime, creator, creationTime, contactName, contactPhone, serialNo, status, answeringMachineCode, explainDepartureTime, explainArrivalTime, radioFrequency, actualDepartureTime, actualArrivalTime, isWork, agreementType, agreementStartDate, agreementEndDate, deptCode
        from daily_flight_plan
    </sql>

    <select id="selectDailyFlightPlanList" resultMap="DailyFlightPlanListResult">
        <include refid="selectDailyFlightPlanVo"/>
        <where>
            and isDelete =0
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="name != null  and name != ''">and name = #{name}</if>
            <if test="companyCode != null  and companyCode != ''">and companyCode = #{companyCode}</if>
            <if test="taskType != null  and taskType != ''">and taskType = #{taskType}</if>
            <if test="flightDate != null  and flightDate != ''">and flightDate = #{flightDate}</if>
            <if test="temporary != null ">and temporary = #{temporary}</if>
            <if test="contactName != null  and contactName != ''">and contactName like concat('%', #{contactName},
                '%')
            </if>
            <if test="contactPhone != null  and contactPhone != ''">and contactPhone = #{contactPhone}</if>
            <if test="serialNo != null  and serialNo != ''">and serialNo = #{serialNo}</if>
            <if test="status != null ">and status = #{status}</if>
            <if test="isWork != null ">and isWork = #{isWork}</if>
            <if test="deptCodeList != null and !deptCodeList.isEmpty()">
                and deptCode in
                <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
                    #{deptCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectDailyFlightPlanById" parameterType="Long" resultMap="DailyFlightPlanResult">
        <include refid="selectDailyFlightPlanVo"/>
        where id = #{id} and isDelete = 0
    </select>

    <select id="selectListByStatus" resultType="com.ruoyi.system.domain.mdmp.DailyFlightPlan">
        <include refid="selectDailyFlightPlanVo"/>
        where isDelete =0
        and status = #{status}
        and flightDate = #{flightDate}
    </select>

    <select id="selectByFlightDateAndTailNumber" resultMap="DailyFlightPlanResult2">
        select fp.id,
               fp.nextDayFlightPlanId,
               fp.companyName,
               fp.companyCode,
               fp.taskType,
               fp.flightDate,
               fp.temporary,
               fp.refusalExplain,
               fp.isDelete,
               fp.auditor,
               fp.auditTime,
               fp.creator,
               fp.creationTime,
               fp.contactName,
               fp.contactPhone,
               fp.serialNo,
               fp.status,
               fp.answeringMachineCode,
               fp.explainDepartureTime,
               fp.explainArrivalTime,
               fp.radioFrequency,
               fp.actualDepartureTime,
               fp.actualArrivalTime,
               fp.isWork
        from daily_flight_plan fp
                 left join flight_plan_aircraft_model air on air.flightPlanId = fp.id
        where fp.flightDate = #{flightDate}
          and air.tailNumber = #{aircraftReg}
          and fp.isDelete = 0
          and fp.status = 2
    </select>
    <select id="selectDailyFlightPlanByInterval" resultMap="DailyFlightPlanResult">

        <include refid="selectDailyFlightPlanVo"/>
        where isDelete = 0
        and status in (1,2,3)
        and flightDate = #{dailyFlightPlan.flightDate}
        and explainDepartureTime &lt;=#{dailyFlightPlan.explainDepartureTime}
        and explainArrivalTime>=#{dailyFlightPlan.explainArrivalTime}
    </select>

    <select id="selectDailyFlightByIdAndStatus" resultMap="DailyFlightPlanResult">
        <include refid="selectDailyFlightPlanVo"/>
        where id = #{id}
        -- and status = 1
        and isDelete = 0
    </select>

    <select id="getRouteAlerts" resultType="java.lang.Long">
        SELECT count(1)
        FROM daily_flight_plan dfp
                 JOIN flight_plan_route fpr ON dfp.id = fpr.flightPlanId AND fpr.planType = 3
                 JOIN route_map_data rmd on rmd.routeId = fpr.ID
                 JOIN flight_plan_aircraft_model fpam on dfp.id = fpam.flightPlanId and fpam.planType = 3
                 JOIN map_data md ON md.sort = rmd.sort
            and fpam.tailNumber = #{param.aircraftReg} and dfp.flightDate = #{param.flightDate}
            and dfp.isDelete = 0 and dfp.status = 2
            AND md.heightStart &lt;= #{param.elevation}
            and md.heightEnd > #{param.elevation}
            and md.longitudeStart &lt;= #{param.longitude}
            and md.longitudeEnd &gt; #{param.longitude}
            and md.latitudeStart &lt;= #{param.latitude}
            and md.latitudeEnd &gt; #{param.latitude}
    </select>

    <select id="getNoFlightPlanAlerts" resultMap="DailyFlightPlanResult">
        SELECT *
        FROM daily_flight_plan dfp
                 JOIN flight_plan_aircraft_model fpam on dfp.id = fpam.flightPlanId and fpam.planType = 3
            and fpam.tailNumber = #{param.aircraftReg} and dfp.flightDate = #{param.flightDate}
            and dfp.isDelete = 0 and dfp.status = 2 LIMIT 1
    </select>
    <select id="getWorAlerts" resultType="java.lang.Long">
        SELECT count(1)
        FROM daily_flight_plan dfp
                 JOIN flight_plan_work fpw ON dfp.id = fpw.flightPlanId AND fpw.planType = 3
                 JOIN work_map_data wmd on wmd.workId = fpw.ID
                 JOIN flight_plan_aircraft_model fpam on dfp.id = fpam.flightPlanId and fpam.planType = 3
                 JOIN map_data md ON md.sort = wmd.sort
            and fpam.tailNumber = #{param.aircraftReg} and dfp.flightDate = #{param.flightDate}
            and dfp.isDelete = 0 and dfp.status = 2
            and dfp.isDelete = 0 and dfp.status = 2
            AND md.heightStart &lt;= #{param.elevation}
            and md.heightEnd > #{param.elevation}
            and md.longitudeStart &lt;= #{param.longitude}
            and md.longitudeEnd &gt; #{param.longitude}
            and md.latitudeStart &lt;= #{param.latitude}
            and md.latitudeEnd &gt; #{param.latitude}
    </select>


    <insert id="insertDailyFlightPlan" parameterType="DailyFlightPlan" useGeneratedKeys="true" keyProperty="id">
        insert into daily_flight_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="nextDayFlightPlanId != null">nextDayFlightPlanId,</if>
            <if test="name != null">name,</if>
            <if test="companyName != null">companyName,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="taskType != null">taskType,</if>
            <if test="flightDate != null">flightDate,</if>
            <if test="temporary != null">temporary,</if>
            <if test="remark != null">remark,</if>
            <if test="creator != null">creator,</if>
            <if test="creationTime != null">creationTime,</if>
            <if test="contactName != null">contactName,</if>
            <if test="contactPhone != null">contactPhone,</if>
            <if test="serialNo != null">serialNo,</if>
            <if test="status != null">status,</if>
            <if test="answeringMachineCode != null">answeringMachineCode,</if>
            <if test="explainDepartureTime != null">explainDepartureTime,</if>
            <if test="explainArrivalTime != null">explainArrivalTime,</if>
            <if test="radioFrequency != null">radioFrequency,</if>
            <if test="actualDepartureTime != null">actualDepartureTime,</if>
            <if test="actualArrivalTime != null">actualArrivalTime,</if>
            <if test="isWork != null">isWork,</if>
            <if test="agreementType != null">agreementType,</if>
            <if test="agreementStartDate != null">agreementStartDate,</if>
            <if test="agreementEndDate != null">agreementEndDate,</if>
            <if test="deptCode != null">deptCode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="nextDayFlightPlanId != null">#{nextDayFlightPlanId},</if>
            <if test="name != null">#{name},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="taskType != null">#{taskType},</if>
            <if test="flightDate != null">#{flightDate},</if>
            <if test="temporary != null">#{temporary},</if>
            <if test="remark != null">#{remark},</if>
            <if test="creator != null">#{creator},</if>
            <if test="creationTime != null">#{creationTime},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="serialNo != null">#{serialNo},</if>
            <if test="status != null">#{status},</if>
            <if test="answeringMachineCode != null">#{answeringMachineCode},</if>
            <if test="explainDepartureTime != null">#{explainDepartureTime},</if>
            <if test="explainArrivalTime != null">#{explainArrivalTime},</if>
            <if test="radioFrequency != null">#{radioFrequency},</if>
            <if test="actualDepartureTime != null">#{actualDepartureTime},</if>
            <if test="actualArrivalTime != null">#{actualArrivalTime},</if>
            <if test="isWork != null">#{isWork},</if>
            <if test="agreementType != null">#{agreementType},</if>
            <if test="agreementStartDate != null">#{agreementStartDate},</if>
            <if test="agreementEndDate != null">#{agreementEndDate},</if>
            <if test="deptCode != null">#{deptCode},</if>
        </trim>
    </insert>

    <update id="updateDailyFlightPlan" parameterType="DailyFlightPlan">
        update daily_flight_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="nextDayFlightPlanId != null">nextDayFlightPlanId = #{nextDayFlightPlanId},</if>
            <if test="name != null">name = #{name},</if>
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="taskType != null">taskType = #{taskType},</if>
            <if test="flightDate != null">flightDate = #{flightDate},</if>
            <if test="temporary != null">temporary = #{temporary},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="refusalExplain != null">refusalExplain = #{refusalExplain},</if>
            <if test="isDelete != null">isDelete = #{isDelete},</if>
            <if test="auditor != null">auditor = #{auditor},</if>
            <if test="auditTime != null">auditTime = #{auditTime},</if>
            <if test="creator != null">creator = #{creator},</if>
            <if test="creationTime != null">creationTime = #{creationTime},</if>
            <if test="contactName != null">contactName = #{contactName},</if>
            <if test="contactPhone != null">contactPhone = #{contactPhone},</if>
            <if test="serialNo != null">serialNo = #{serialNo},</if>
            <if test="status != null">status = #{status},</if>
            <if test="answeringMachineCode != null">answeringMachineCode = #{answeringMachineCode},</if>
            <if test="explainDepartureTime != null">explainDepartureTime = #{explainDepartureTime},</if>
            <if test="explainArrivalTime != null">explainArrivalTime = #{explainArrivalTime},</if>
            <if test="radioFrequency != null">radioFrequency = #{radioFrequency},</if>
            <if test="actualDepartureTime != null">actualDepartureTime = #{actualDepartureTime},</if>
            <if test="actualArrivalTime != null">actualArrivalTime = #{actualArrivalTime},</if>
            <if test="isWork != null">isWork = #{isWork},</if>
            <if test="agreementType != null">agreementType = #{agreementType},</if>
            <if test="agreementStartDate != null">agreementStartDate = #{agreementStartDate},</if>
            <if test="agreementEndDate != null">agreementEndDate = #{agreementEndDate},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteDailyFlightPlanById" parameterType="Long">
        delete
        from daily_flight_plan
        where id = #{id}
    </delete>

    <update id="deleteDailyFlightPlanByIds">
        update daily_flight_plan set isDelete =1 where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>


</mapper>
