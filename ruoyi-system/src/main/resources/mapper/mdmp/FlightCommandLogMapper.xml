<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightCommandLogMapper">
    <resultMap type="FlightCommandLog" id="FlightCommandLogMap">
        <result property="id" column="id"/>
        <result property="flightId" column="flight_id"/>
        <result property="commandLog" column="command_log"/>
        <result property="callSign" column="call_sign"/>
        <result property="delivered" column="delivered"/>
        <result property="viewed" column="viewed"/>
        <result property="executed" column="executed"/>
        <result property="createDate" column="create_date"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectFlightCommandLogVo">
        select id, flight_id, command_log, call_sign, delivered, viewed, executed, create_date, create_time, update_time
        from flight_command_log
    </sql>
    <select id="queryById" resultMap="FlightCommandLogMap">
        <include refid="selectFlightCommandLogVo"/>
        where id = #{id}
    </select>
    <select id="queryByFlightId" resultMap="FlightCommandLogMap">
        <include refid="selectFlightCommandLogVo"/>
        where flight_id = #{flightId}
    </select>
    <select id="queryByDate" resultMap="FlightCommandLogMap">
        <include refid="selectFlightCommandLogVo"/>
        where create_date = #{date}
        order by update_time desc
    </select>

    <insert id="insert" parameterType="FlightCommandLog" useGeneratedKeys="true" keyProperty="id">
        insert into flight_command_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flightId != null">flight_id,</if>
            <if test="commandLog != null">command_log,</if>
            <if test="callSign != null">call_sign,</if>
            <if test="delivered != null">delivered,</if>
            <if test="viewed != null">viewed,</if>
            <if test="executed != null">executed,</if>
            <if test="createDate != null">create_date,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flightId != null">#{flightId},</if>
            <if test="commandLog != null">#{commandLog},</if>
            <if test="callSign != null">#{callSign},</if>
            <if test="delivered != null">#{delivered},</if>
            <if test="viewed != null">#{viewed},</if>
            <if test="executed != null">#{executed},</if>
            <if test="createDate != null">#{createDate},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
        </trim>
    </insert>

    <update id="update" parameterType="FlightCommandLog">
        update flight_command_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="flightId != null">flight_id = #{flightId},</if>
            <if test="commandLog != null">command_log = #{commandLog},</if>
            <if test="callSign != null">call_sign = #{callSign},</if>
            <if test="delivered != null">delivered = #{delivered},</if>
            <if test="viewed != null">viewed = #{viewed},</if>
            <if test="executed != null">executed = #{executed},</if>
            <if test="createDate != null">create_date = #{createDate},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
        </trim>
        where id = #{id}
    </update>



</mapper>
