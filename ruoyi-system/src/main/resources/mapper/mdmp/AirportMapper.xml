<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.AirportMapper">

    <resultMap type="Airport" id="AirportResult">
        <result property="id" column="id"/>
        <result property="airportName" column="airportName"/>
        <result property="threeCode" column="threeCode"/>
        <result property="tetradCode" column="tetradCode"/>
        <result property="longitude" column="longitude"/>
        <result property="latitude" column="latitude"/>
        <result property="remarks" column="remarks"/>
    </resultMap>

    <sql id="selectAirportVo">
        select id, airportName, threeCode, tetradCode, longitude, latitude, remarks,deptCode
        from airport
    </sql>

    <select id="selectAirportList" parameterType="Airport" resultMap="AirportResult">
        <include refid="selectAirportVo"/>
        <where>
            <if test="airportName != null  and airportName != ''">and airportName like concat('%', #{airportName},
                '%')
            </if>
            <if test="threeCode != null  and threeCode != ''">and threeCode = #{threeCode}</if>
            <if test="tetradCode != null  and tetradCode != ''">and tetradCode = #{tetradCode}</if>
            <if test="longitude != null  and longitude != ''">and longitude = #{longitude}</if>
            <if test="latitude != null  and latitude != ''">and latitude = #{latitude}</if>
            <if test="remarks != null  and remarks != ''">and remarks = #{remarks}</if>
            <if test="deptCodeList != null and !deptCodeList.isEmpty()">
                and deptCode in
                <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
                    #{deptCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectAirportById" parameterType="Long" resultMap="AirportResult">
        <include refid="selectAirportVo"/>
        where id = #{id}
    </select>

    <insert id="insertAirport" parameterType="Airport" useGeneratedKeys="true" keyProperty="id">
        insert into airport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="airportName != null and airportName != ''">airportName,</if>
            <if test="threeCode != null and threeCode != ''">threeCode,</if>
            <if test="tetradCode != null and tetradCode != ''">tetradCode,</if>
            <if test="longitude != null and longitude != ''">longitude,</if>
            <if test="latitude != null and latitude != ''">latitude,</if>
            <if test="remarks != null">remarks,</if>
            <if test="deptCode != null and deptCode != ''">deptCode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="airportName != null and airportName != ''">#{airportName},</if>
            <if test="threeCode != null and threeCode != ''">#{threeCode},</if>
            <if test="tetradCode != null and tetradCode != ''">#{tetradCode},</if>
            <if test="longitude != null and longitude != ''">#{longitude},</if>
            <if test="latitude != null and latitude != ''">#{latitude},</if>
            <if test="remarks != null">#{remarks},</if>
            <if test="deptCode != null and deptCode != ''">#{deptCode},</if>
        </trim>
    </insert>

    <update id="updateAirport" parameterType="Airport">
        update airport
        <trim prefix="SET" suffixOverrides=",">
            <if test="airportName != null and airportName != ''">airportName = #{airportName},</if>
            <if test="threeCode != null and threeCode != ''">threeCode = #{threeCode},</if>
            <if test="tetradCode != null and tetradCode != ''">tetradCode = #{tetradCode},</if>
            <if test="longitude != null and longitude != ''">longitude = #{longitude},</if>
            <if test="latitude != null and latitude != ''">latitude = #{latitude},</if>
            <if test="remarks != null">remarks = #{remarks},</if>
            <if test="deptCode != null and deptCode != ''">deptCode = #{deptCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAirportById" parameterType="Long">
        delete
        from airport
        where id = #{id}
    </delete>

    <delete id="deleteAirportByIds" parameterType="String">
        delete from airport where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
