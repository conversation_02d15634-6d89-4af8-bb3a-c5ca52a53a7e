<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.WorkInMapDataMapper">

    <resultMap type="WorkInMapData" id="WorkInMapDataResult">
        <result property="id" column="id"/>
        <result property="workInId" column="workInId"/>
        <result property="value" column="color"/>
        <result property="index" column="sort"/>
    </resultMap>

    <sql id="selectWorkInMapDataVo">
        select id, workInId, color as 'value',sort as 'index'
        from work_in_map_data
    </sql>
    <insert id="insertAllWorkInMapData">
        insert into work_in_map_data(workInId,color,sort)
        values
        <foreach collection="workInMapData" item="item" index="index" separator=",">
            (#{item.workInId},#{item.value},#{item.index})
        </foreach>
    </insert>
    <delete id="delByWorkInId">
        delete
        from work_in_map_data
        where workInId in
        <foreach item="id" collection="workId" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <select id="selectAllByWorkInId" resultType="com.ruoyi.system.domain.mdmp.WorkInMapData">
        <include refid="selectWorkInMapDataVo"/>
        where workInId = #{workInId}
    </select>


</mapper>
