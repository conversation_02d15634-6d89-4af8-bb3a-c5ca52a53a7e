<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.TopographicMapDataMapper">

    <resultMap type="TopographicMapData" id="TopographicMapDataResult">
        <result property="id" column="id"/>
        <result property="statusCode" column="statusCode"/>
        <result property="index" column="sort"/>
    </resultMap>

    <sql id="selectTopographicMapDataVo">
        select id, statusCode, sort
        from topographic_map_data
    </sql>

    <select id="selectTopographicMapDataList" parameterType="TopographicMapData" resultMap="TopographicMapDataResult">
        <include refid="selectTopographicMapDataVo"/>
        <where>
            <if test="statusCode != null  and statusCode != ''">and statusCode = #{statusCode}</if>
            <if test="index != null ">and sort = #{index}</if>
        </where>
    </select>

    <select id="selectTopographicMapDataById" parameterType="Long" resultMap="TopographicMapDataResult">
        <include refid="selectTopographicMapDataVo"/>
        where id = #{id}
    </select>
    <select id="selectTopographicMapDataByIndexList"
            resultMap="TopographicMapDataResult">
        <include refid="selectTopographicMapDataVo"/>
        WHERE sort in
        <foreach item="item" collection="param" open="(" separator="," close=")">
            #{item.index}
        </foreach>
    </select>

    <select id="selectTopographicMapData" resultMap="TopographicMapDataResult">
        SELECT t2.*
        FROM topographic_map_data t2
                 JOIN map_data t1 ON t2.sort = t1.sort
        where pressureStart = #{param.altitude}
          and longitudeStart &lt;= #{param.longitude}
          and longitudeEnd &gt;= #{param.longitude}
          and latitudeStart &lt;= #{param.latitude}
          and latitudeEnd &gt;= #{param.latitude} LIMIT 1
    </select>

    <insert id="insertTopographicMapData" parameterType="TopographicMapData" useGeneratedKeys="true" keyProperty="id">
        insert into topographic_map_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="statusCode != null">statusCode,</if>
            <if test="index != null">index,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="statusCode != null">#{statusCode},</if>
            <if test="index != null">#{index},</if>
        </trim>
    </insert>

    <update id="updateTopographicMapData" parameterType="TopographicMapData">
        update topographic_map_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="statusCode != null">statusCode = #{statusCode},</if>
            <if test="index != null">sort = #{index},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteTopographicMapDataById" parameterType="Long">
        delete
        from topographic_map_data
        where id = #{id}
    </delete>

    <delete id="deleteTopographicMapDataByIds" parameterType="String">
        delete from topographic_map_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
