<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.AirspaceMapper">

    <resultMap type="Airspace" id="AirspaceResult">
        <result property="id" column="id"/>
        <result property="airspaceName" column="airspaceName"/>
        <result property="airspaceType" column="airspaceType"/>
        <result property="startingHeight" column="startingHeight"/>
        <result property="terminationHeight" column="terminationHeight"/>
        <result property="distance" column="distance"/>
        <result property="benchmarkLong" column="benchmarkLong"/>
        <result property="magneticBearing" column="magneticBearing"/>
        <result property="graphicsType" column="graphicsType"/>
        <result property="effectiveStartDate" column="effectiveStartDate"/>
        <result property="effectiveEndDate" column="effectiveEndDate"/>
        <result property="circleCenterLong" column="circleCenterLong"/>
        <result property="radius" column="radius"/>
        <result property="angle" column="angle"/>
        <result property="centralAngle" column="centralAngle"/>
        <result property="longAxis" column="longAxis"/>
        <result property="halfShaft" column="halfShaft"/>
        <result property="flightStartTime" column="flightStartTime"/>
        <result property="flightEndTime" column="flightEndTime"/>
        <result property="benchmarkLat" column="benchmarkLat"/>
        <result property="circleCenterLat" column="circleCenterLat"/>
        <result property="airspaceOffset" column="airspaceOffset"/>
        <result property="positiveOrNegative" column="positiveOrNegative"/>
        <result property="colorType" column="colorType"/>
        <result property="pointType" column="pointType"/>
        <result property="deptCode" column="deptCode"/>
        <collection property="airspaceLongLatList"
                    javaType="java.util.List"
                    ofType="com.ruoyi.system.domain.mdmp.AirspaceLongLat"
        >
            <result property="airspaceId" column="airspaceId"/>
            <result property="sortNumber" column="sortNumber"/>
            <result property="longitude" column="longitude"/>
            <result property="latitude" column="latitude"/>
            <result property="height" column="height"/>
            <result property="doubleLongitude" column="doubleLongitude"/>
            <result property="doubleLatitude" column="doubleLatitude"/>
            <result property="groupNumber" column="groupNumber"/>
            <result property="coordinateName" column="coordinateName"/>
        </collection>
    </resultMap>

    <resultMap type="Airspace" id="AirspaceResultAndMapDate">
        <result property="id" column="id"/>
        <result property="airspaceName" column="airspaceName"/>
        <result property="airspaceType" column="airspaceType"/>
        <result property="startingHeight" column="startingHeight"/>
        <result property="terminationHeight" column="terminationHeight"/>
        <result property="distance" column="distance"/>
        <result property="benchmarkLong" column="benchmarkLong"/>
        <result property="magneticBearing" column="magneticBearing"/>
        <result property="graphicsType" column="graphicsType"/>
        <result property="effectiveStartDate" column="effectiveStartDate"/>
        <result property="effectiveEndDate" column="effectiveEndDate"/>
        <result property="circleCenterLong" column="circleCenterLong"/>
        <result property="radius" column="radius"/>
        <result property="angle" column="angle"/>
        <result property="centralAngle" column="centralAngle"/>
        <result property="longAxis" column="longAxis"/>
        <result property="halfShaft" column="halfShaft"/>
        <result property="flightStartTime" column="flightStartTime"/>
        <result property="flightEndTime" column="flightEndTime"/>
        <result property="benchmarkLat" column="benchmarkLat"/>
        <result property="circleCenterLat" column="circleCenterLat"/>
        <result property="airspaceOffset" column="airspaceOffset"/>
        <result property="positiveOrNegative" column="positiveOrNegative"/>
        <result property="pointType" column="pointType"/>
        <result property="colorType" column="colorType"/>
        <result property="deptCode" column="deptCode"/>
        <collection property="airspaceLongLatList"
                    select="com.ruoyi.system.mapper.mdmp.AirspaceLongLatMapper.selectAirspaceLongLatList"
                    column="{airspaceId=id}">
        </collection>
        <collection property="mapDataList" select="com.ruoyi.system.mapper.mdmp.MapDataMapper.selectMapDataListById"
                    column="{airspaceId=id,colorType=colorType}">
        </collection>
    </resultMap>


    <resultMap type="com.ruoyi.system.domain.mdmp.vo.AirspaceListVO" id="AirspaceResultAndMapDateS">
        <result property="id" column="id"/>
        <result property="airspaceName" column="airspaceName"/>
        <result property="colorType" column="colorType"/>
        <result property="airspaceType" column="airspaceType"/>
        <collection property="mapDataList"
                    javaType="java.util.List"
                    ofType="com.ruoyi.system.domain.mdmp.vo.AirspaceMapDataListVO">
            <result property="index" column="sort"/>
            <result property="statusCode" column="color"/>
        </collection>
    </resultMap>


    <resultMap id="obstacleResultMap" type="com.ruoyi.common.config.rule.Obstacle">
        <!-- 主键标识用于分组 -->
        <id property="index" column="index"/>
        <!-- 嵌套集合映射 -->
        <collection
                property="subset"
                ofType="com.ruoyi.common.config.rule.ObstacleSubset">
            <result property="index" column="obstacleIndex"/>
            <result property="color" column="obstacleColor"/>
        </collection>
    </resultMap>
    <sql id="selectAirspaceVo">
        select id,
               airspaceName,
               airspaceType,
               startingHeight,
               terminationHeight,
               distance,
               benchmarkLong,
               magneticBearing,
               graphicsType,
               effectiveStartDate,
               effectiveEndDate,
               circleCenterLong,
               radius,
               angle,
               centralAngle,
               longAxis,
               halfShaft,
               flightStartTime,
               flightEndTime,
               benchmarkLat,
               circleCenterLat,
               airspaceOffset,
               positiveOrNegative,
               pointType,
               colorType,
               deptCode
        from airspace
    </sql>

    <select id="selectAirspaceList" parameterType="Airspace" resultMap="AirspaceResult">
        <include refid="selectAirspaceVo"/>
        <where>
            <if test="airspaceName != null  and airspaceName != ''">and airspaceName like concat('%', #{airspaceName},
                '%')
            </if>
            <if test="airspaceType != null ">and airspaceType = #{airspaceType}</if>
            <if test="startingHeight != null ">and startingHeight = #{startingHeight}</if>
            <if test="terminationHeight != null ">and terminationHeight = #{terminationHeight}</if>
            <if test="distance != null ">and distance = #{distance}</if>
            <if test="benchmarkLong != null  and benchmarkLong != ''">and benchmarkLong = #{benchmarkLong}</if>
            <if test="magneticBearing != null ">and magneticBearing = #{magneticBearing}</if>
            <if test="graphicsType != null ">and graphicsType = #{graphicsType}</if>
            <if test="effectiveStartDate != null  and effectiveStartDate != ''">and effectiveStartDate &lt;=
                #{effectiveStartDate}
            </if>
            <if test="effectiveEndDate != null  and effectiveEndDate != ''">and effectiveEndDate >=
                #{effectiveEndDate}
            </if>
            <if test="circleCenterLong != null  and circleCenterLong != ''">and circleCenterLong = #{circleCenterLong}
            </if>
            <if test="radius != null ">and radius = #{radius}</if>
            <if test="angle != null ">and angle = #{angle}</if>
            <if test="centralAngle != null ">and centralAngle = #{centralAngle}</if>
            <if test="longAxis != null ">and longAxis = #{longAxis}</if>
            <if test="halfShaft != null ">and halfShaft = #{halfShaft}</if>
            <if test="flightStartTime != null  and flightStartTime != ''">and flightStartTime = #{flightStartTime}</if>
            <if test="flightEndTime != null  and flightEndTime != ''">and flightEndTime = #{flightEndTime}</if>
            <if test="benchmarkLat != null  and benchmarkLat != ''">and benchmarkLat = #{benchmarkLat}</if>
            <if test="circleCenterLat != null  and circleCenterLat != ''">and circleCenterLat = #{circleCenterLat}</if>
            <if test="deptCodeList != null and !deptCodeList.isEmpty()">
                and deptCode in
                <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
                    #{deptCode}
                </foreach>
            </if>
        </where>
    </select>

    <select id="selectAirspaceById" parameterType="Long" resultMap="AirspaceResultAndMapDate">
        <include refid="selectAirspaceVo"/>
        where id = #{id}
    </select>

    <select id="selectAirspaceByIds" resultMap="AirspaceResult">
        select * from airspace a
        inner join airspace_long_lat al on a.id = al.airspaceId
        where a.id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <select id="selectAll" resultMap="AirspaceResult">
        <include refid="selectAirspaceVo"/>
        where effectiveStartDate &lt;= #{date}
        and effectiveEndDate &gt;= #{date}
    </select>

    <select id="selectAirspaceListAndMap" resultMap="AirspaceResultAndMapDateS">
        SELECT a.id , a.airspaceName,a.colorType,a.airspaceType,amd.sort, amd.color FROM airspace a join
        airspace_map_data amd on a.id=amd.airspaceId
        where a.id IN
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
        and a.deptCode in
        <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
            #{deptCode}
        </foreach>

    </select>
    <select id="selectAirspaceListByCodeAndType" resultMap="obstacleResultMap">
        WITH FilteredAirspace AS (
        SELECT id
        FROM airspace
        WHERE airspaceType = #{airspaceType}
        AND deptCode IN
        <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
            #{deptCode}
        </foreach>
        )
        SELECT
        amd.sort AS 'index',
        mdo.sort AS 'obstacleIndex',
        mdo.statusCode AS 'obstacleColor'
        FROM FilteredAirspace a
        INNER JOIN airspace_map_data amd ON a.id = amd.airspaceId
        INNER JOIN map_data_obstacle mdo ON amd.id = mdo.airspaceMapDataId where mdo.statusCode =55
    </select>

    <insert id="insertAirspace" parameterType="Airspace" useGeneratedKeys="true" keyProperty="id">
        insert into airspace
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="airspaceName != null and airspaceName != ''">airspaceName,</if>
            <if test="airspaceType != null">airspaceType,</if>
            <if test="startingHeight != null">startingHeight,</if>
            <if test="terminationHeight != null">terminationHeight,</if>
            <if test="distance != null">distance,</if>
            <if test="benchmarkLong != null">benchmarkLong,</if>
            <if test="magneticBearing != null">magneticBearing,</if>
            <if test="graphicsType != null">graphicsType,</if>
            <if test="effectiveStartDate != null">effectiveStartDate,</if>
            <if test="effectiveEndDate != null">effectiveEndDate,</if>
            <if test="circleCenterLong != null">circleCenterLong,</if>
            <if test="radius != null">radius,</if>
            <if test="angle != null">angle,</if>
            <if test="centralAngle != null">centralAngle,</if>
            <if test="longAxis != null">longAxis,</if>
            <if test="halfShaft != null">halfShaft,</if>
            <if test="flightStartTime != null">flightStartTime,</if>
            <if test="flightEndTime != null">flightEndTime,</if>
            <if test="benchmarkLat != null">benchmarkLat,</if>
            <if test="circleCenterLat != null">circleCenterLat,</if>
            <if test="airspaceOffset != null">airspaceOffset,</if>
            <if test="positiveOrNegative != null">positiveOrNegative,</if>
            <if test="pointType != null">pointType,</if>
            <if test="colorType != null">colorType,</if>
            <if test="deptCode != null">deptCode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="airspaceName != null and airspaceName != ''">#{airspaceName},</if>
            <if test="airspaceType != null">#{airspaceType},</if>
            <if test="startingHeight != null">#{startingHeight},</if>
            <if test="terminationHeight != null">#{terminationHeight},</if>
            <if test="distance != null">#{distance},</if>
            <if test="benchmarkLong != null">#{benchmarkLong},</if>
            <if test="magneticBearing != null">#{magneticBearing},</if>
            <if test="graphicsType != null">#{graphicsType},</if>
            <if test="effectiveStartDate != null">#{effectiveStartDate},</if>
            <if test="effectiveEndDate != null">#{effectiveEndDate},</if>
            <if test="circleCenterLong != null">#{circleCenterLong},</if>
            <if test="radius != null">#{radius},</if>
            <if test="angle != null">#{angle},</if>
            <if test="centralAngle != null">#{centralAngle},</if>
            <if test="longAxis != null">#{longAxis},</if>
            <if test="halfShaft != null">#{halfShaft},</if>
            <if test="flightStartTime != null">#{flightStartTime},</if>
            <if test="flightEndTime != null">#{flightEndTime},</if>
            <if test="benchmarkLat != null">#{benchmarkLat},</if>
            <if test="circleCenterLat != null">#{circleCenterLat},</if>
            <if test="airspaceOffset != null">#{airspaceOffset},</if>
            <if test="positiveOrNegative != null">#{positiveOrNegative},</if>
            <if test="pointType != null">#{pointType},</if>
            <if test="colorType != null">#{colorType},</if>
            <if test="deptCode != null">#{deptCode},</if>
        </trim>
    </insert>

    <update id="updateAirspace" parameterType="Airspace">
        update airspace
        <trim prefix="SET" suffixOverrides=",">
            <if test="airspaceName != null and airspaceName != ''">airspaceName = #{airspaceName},</if>
            <if test="airspaceType != null">airspaceType = #{airspaceType},</if>
            <if test="startingHeight != null">startingHeight = #{startingHeight},</if>
            <if test="terminationHeight != null">terminationHeight = #{terminationHeight},</if>
            <if test="distance != null">distance = #{distance},</if>
            <if test="benchmarkLong != null">benchmarkLong = #{benchmarkLong},</if>
            <if test="magneticBearing != null">magneticBearing = #{magneticBearing},</if>
            <if test="graphicsType != null">graphicsType = #{graphicsType},</if>
            <if test="effectiveStartDate != null">effectiveStartDate = #{effectiveStartDate},</if>
            <if test="effectiveEndDate != null">effectiveEndDate = #{effectiveEndDate},</if>
            <if test="circleCenterLong != null">circleCenterLong = #{circleCenterLong},</if>
            <if test="radius != null">radius = #{radius},</if>
            <if test="angle != null">angle = #{angle},</if>
            <if test="centralAngle != null">centralAngle = #{centralAngle},</if>
            <if test="longAxis != null">longAxis = #{longAxis},</if>
            <if test="halfShaft != null">halfShaft = #{halfShaft},</if>
            <if test="flightStartTime != null">flightStartTime = #{flightStartTime},</if>
            <if test="flightEndTime != null">flightEndTime = #{flightEndTime},</if>
            <if test="benchmarkLat != null">benchmarkLat = #{benchmarkLat},</if>
            <if test="circleCenterLat != null">circleCenterLat = #{circleCenterLat},</if>
            <if test="airspaceOffset != null">airspaceOffset = #{airspaceOffset},</if>
            <if test="positiveOrNegative != null">positiveOrNegative = #{positiveOrNegative},</if>
            <if test="pointType != null">pointType = #{pointType},</if>
            <if test="colorType != null">colorType = #{colorType},</if>
            <if test="deptCode != null">deptCode = #{deptCode},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteAirspaceById" parameterType="Long">
        delete
        from airspace
        where id = #{id}
    </delete>

    <delete id="deleteAirspaceByIds" parameterType="String">
        delete from airspace where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
