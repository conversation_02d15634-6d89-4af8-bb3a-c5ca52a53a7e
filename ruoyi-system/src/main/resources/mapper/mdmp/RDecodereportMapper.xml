<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.RDecodereportMapper">

    <resultMap type="RDecodereport" id="RDecodereportResult">
        <result property="id" column="id"/>
        <result property="rawReportId" column="rawReportId"/>
        <result property="content" column="content"/>
        <result property="publishTime" column="publishTime"/>
        <result property="publishTimeType" column="publishTimeType"/>
        <result property="type" column="type"/>
        <result property="trendType" column="trendType"/>
        <result property="validTimeBegin" column="validTimeBegin"/>
        <result property="validTimeEnd" column="validTimeEnd"/>
        <result property="airportCode" column="airportCode"/>
        <result property="isAuto" column="isAuto"/>
        <result property="isCor" column="isCor"/>
        <result property="cavOk" column="cavOk"/>
        <result property="verticalVisibility" column="verticalVisibility"/>
        <result property="temperature" column="temperature"/>
        <result property="dpTemperature" column="dpTemperature"/>
        <result property="altimeter" column="altimeter"/>
        <result property="windShear" column="windShear"/>
        <result property="windShearContent" column="windShearContent"/>
        <result property="windShearRunway" column="windShearRunway"/>
        <result property="cloudCode" column="cloudCode"/>
        <result property="recentWeather" column="recentWeather"/>
        <result property="isCancel" column="isCancel"/>
        <result property="nsc" column="nsc"/>
        <result property="nsw" column="nsw"/>
        <result property="remark" column="remark"/>
        <result property="colourType" column="colourType"/>
    </resultMap>


    <resultMap type="DecodeReportVo" id="RDecodereportTime">
        <result property="id" column="id"/>
        <result property="content" column="content"/>
        <result property="type" column="type"/>
        <result property="colourType" column="colourType"/>
        <result property="hour" column="hour"/>
        <result property="validTimeBegin" column="validTimeBegin"/>
    </resultMap>
    <sql id="selectRDecodereportVo">
        select id,
               colourType,
               rawReportId,
               content,
               publishTime,
               publishTimeType,
               type,
               trendType,
               validTimeBegin,
               validTimeEnd,
               airportCode,
               isAuto,
               isCor,
               cavOk,
               verticalVisibility,
               temperature,
               dpTemperature,
               altimeter,
               windShear,
               windShearContent,
               windShearRunway,
               cloudCode,
               recentWeather,
               isCancel,
               nsc,
               nsw,
               remark
        from r_decodereport
    </sql>

    <select id="selectRDecodereportList" parameterType="RDecodereport" resultMap="RDecodereportResult">
        <include refid="selectRDecodereportVo"/>
        <where>
            <if test="rawReportId != null  and rawReportId != ''">and rawReportId = #{rawReportId}</if>
            <if test="content != null  and content != ''">and content = #{content}</if>
            <if test="publishTime != null ">and publishTime = #{publishTime}</if>
            <if test="publishTimeType != null ">and publishTimeType = #{publishTimeType}</if>
            <if test="type != null  and type != ''">and type = #{type}</if>
            <if test="trendType != null  and trendType != ''">and trendType = #{trendType}</if>
            <if test="validTimeBegin != null ">and validTimeBegin = #{validTimeBegin}</if>
            <if test="validTimeEnd != null ">and validTimeEnd = #{validTimeEnd}</if>
            <if test="airportCode != null  and airportCode != ''">and airportCode = #{airportCode}</if>
            <if test="isAuto != null  and isAuto != ''">and isAuto = #{isAuto}</if>
            <if test="isCor != null  and isCor != ''">and isCor = #{isCor}</if>
            <if test="cavOk != null ">and cavOk = #{cavOk}</if>
            <if test="verticalVisibility != null ">and verticalVisibility = #{verticalVisibility}</if>
            <if test="temperature != null ">and temperature = #{temperature}</if>
            <if test="dpTemperature != null ">and dpTemperature = #{dpTemperature}</if>
            <if test="altimeter != null ">and altimeter = #{altimeter}</if>
            <if test="windShear != null ">and windShear = #{windShear}</if>
            <if test="windShearContent != null  and windShearContent != ''">and windShearContent = #{windShearContent}
            </if>
            <if test="windShearRunway != null  and windShearRunway != ''">and windShearRunway = #{windShearRunway}</if>
            <if test="cloudCode != null  and cloudCode != ''">and cloudCode = #{cloudCode}</if>
            <if test="recentWeather != null  and recentWeather != ''">and recentWeather = #{recentWeather}</if>
            <if test="isCancel != null ">and isCancel = #{isCancel}</if>
            <if test="nsc != null ">and nsc = #{nsc}</if>
            <if test="nsw != null ">and nsw = #{nsw}</if>
            <if test="colourType != null ">and colourType = #{colourType}</if>
        </where>
    </select>

    <select id="selectRDecodereportById" parameterType="Long" resultMap="RDecodereportResult">
        <include refid="selectRDecodereportVo"/>
        where id = #{id}
    </select>

    <select id="selectRDecodereportListByTime" resultMap="RDecodereportTime">
        (
            SELECT id,
                   content,
                   type,
                   validTimeBegin,
                   colourType,
                   temperature,
                   DATE_FORMAT(FROM_UNIXTIME(validTimeBegin / 1000), '%H') AS hour
            FROM r_decodereport
            WHERE
                (UNIX_TIMESTAMP() * 1000 - 10800000) BETWEEN validTimeBegin
              AND validTimeEnd
              AND type ='SA'
            ORDER BY publishTime DESC, colourType DESC
                LIMIT 1
        )
        UNION ALL
        (
            SELECT id,
                   content,
                   type,
                   validTimeBegin,
                   colourType,
                   temperature,
                   DATE_FORMAT(FROM_UNIXTIME(validTimeBegin / 1000), '%H') AS hour
            FROM r_decodereport
            WHERE
                (UNIX_TIMESTAMP() * 1000 - 7200000) BETWEEN validTimeBegin
              AND validTimeEnd
              AND type ='SA'
            ORDER BY publishTime DESC, colourType DESC
                LIMIT 1
        )
        UNION ALL
        (
            SELECT id,
                   content,
                   type,
                   validTimeBegin,
                   colourType,
                   temperature,
                   DATE_FORMAT(FROM_UNIXTIME(validTimeBegin / 1000), '%H') AS hour
            FROM r_decodereport
            WHERE
                (UNIX_TIMESTAMP() * 1000 - 3600000) BETWEEN validTimeBegin
              AND validTimeEnd
              AND type ='SA'
            ORDER BY publishTime DESC, colourType DESC
                LIMIT 1
        )
        UNION ALL
        (
            SELECT id,
                   content,
                   type,
                   validTimeBegin,
                   colourType,
                   temperature,
                   DATE_FORMAT(FROM_UNIXTIME(validTimeBegin / 1000), '%H') AS hour
            FROM r_decodereport
            WHERE
                UNIX_TIMESTAMP() * 1000 BETWEEN validTimeBegin
              AND validTimeEnd
              AND type ='SA'
            ORDER BY publishTime DESC, colourType DESC
                LIMIT 1
        )
        UNION ALL
        (
            SELECT id,
                   content,
                   type,
                   validTimeBegin,
                   colourType,
                   temperature,
                   DATE_FORMAT(FROM_UNIXTIME(validTimeBegin / 1000), '%H') AS hour
            FROM (
                SELECT
                *,
                ROW_NUMBER() OVER (
                PARTITION BY validTimeBegin
                ORDER BY
                CASE WHEN trendType = 'MAIN' THEN 0 ELSE 1 END,
                colourType DESC,
                publishTime DESC
                ) AS rn
                FROM r_decodereport
                WHERE
                validTimeBegin > UNIX_TIMESTAMP() * 1000
                AND type = 'FC'
                ) AS ranked
            WHERE rn = 1
        )
        UNION ALL
        (
            SELECT id,
                   content,
                   type,
                   validTimeBegin,
                   colourType,
                   temperature,
                   DATE_FORMAT(FROM_UNIXTIME(validTimeBegin / 1000), '%H:%i') AS hour
            FROM (
                SELECT *,
                ROW_NUMBER() OVER (
                PARTITION BY validTimeBegin
                ORDER BY colourType DESC, publishTime DESC
                ) AS rn
                FROM r_decodereport
                WHERE
                validTimeBegin > UNIX_TIMESTAMP() * 1000
                AND type = 'SP'
                AND trendType='MAIN'
                ) AS ranked
            WHERE rn = 1
        )
    </select>

    <select id="selectNewContentList" resultType="java.lang.String">
        SELECT content
        FROM (
                 SELECT id,
                        type,
                        content,
                        ROW_NUMBER() OVER (PARTITION BY type ORDER BY publishTime DESC) AS rn
                 FROM r_decodereport
                 WHERE type IN ('FC', 'SA')
                   AND trendType = 'MAIN'
             ) AS tmp
        WHERE rn = 1
        ORDER BY id desc
    </select>

    <insert id="insertRDecodereport" parameterType="RDecodereport" useGeneratedKeys="true" keyProperty="id">
        insert into r_decodereport
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="rawReportId != null and rawReportId != ''">rawReportId,</if>
            <if test="content != null">content,</if>
            <if test="publishTime != null">publishTime,</if>
            <if test="publishTimeType != null">publishTimeType,</if>
            <if test="type != null">type,</if>
            <if test="trendType != null">trendType,</if>
            <if test="validTimeBegin != null">validTimeBegin,</if>
            <if test="validTimeEnd != null">validTimeEnd,</if>
            <if test="airportCode != null">airportCode,</if>
            <if test="isAuto != null">isAuto,</if>
            <if test="isCor != null">isCor,</if>
            <if test="cavOk != null">cavOk,</if>
            <if test="verticalVisibility != null">verticalVisibility,</if>
            <if test="temperature != null">temperature,</if>
            <if test="dpTemperature != null">dpTemperature,</if>
            <if test="altimeter != null">altimeter,</if>
            <if test="windShear != null">windShear,</if>
            <if test="windShearContent != null">windShearContent,</if>
            <if test="windShearRunway != null">windShearRunway,</if>
            <if test="cloudCode != null">cloudCode,</if>
            <if test="recentWeather != null">recentWeather,</if>
            <if test="isCancel != null">isCancel,</if>
            <if test="nsc != null">nsc,</if>
            <if test="nsw != null">nsw,</if>
            <if test="remark != null">remark,</if>
            <if test="colourType != null">colourType,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="rawReportId != null and rawReportId != ''">#{rawReportId},</if>
            <if test="content != null">#{content},</if>
            <if test="publishTime != null">#{publishTime},</if>
            <if test="publishTimeType != null">#{publishTimeType},</if>
            <if test="type != null">#{type},</if>
            <if test="trendType != null">#{trendType},</if>
            <if test="validTimeBegin != null">#{validTimeBegin},</if>
            <if test="validTimeEnd != null">#{validTimeEnd},</if>
            <if test="airportCode != null">#{airportCode},</if>
            <if test="isAuto != null">#{isAuto},</if>
            <if test="isCor != null">#{isCor},</if>
            <if test="cavOk != null">#{cavOk},</if>
            <if test="verticalVisibility != null">#{verticalVisibility},</if>
            <if test="temperature != null">#{temperature},</if>
            <if test="dpTemperature != null">#{dpTemperature},</if>
            <if test="altimeter != null">#{altimeter},</if>
            <if test="windShear != null">#{windShear},</if>
            <if test="windShearContent != null">#{windShearContent},</if>
            <if test="windShearRunway != null">#{windShearRunway},</if>
            <if test="cloudCode != null">#{cloudCode},</if>
            <if test="recentWeather != null">#{recentWeather},</if>
            <if test="isCancel != null">#{isCancel},</if>
            <if test="nsc != null">#{nsc},</if>
            <if test="nsw != null">#{nsw},</if>
            <if test="remark != null">#{remark},</if>
            <if test="colourType != null">#{colourType},</if>
        </trim>
    </insert>

    <update id="updateRDecodereport" parameterType="RDecodereport">
        update r_decodereport
        <trim prefix="SET" suffixOverrides=",">
            <if test="rawReportId != null and rawReportId != ''">rawReportId = #{rawReportId},</if>
            <if test="content != null">content = #{content},</if>
            <if test="publishTime != null">publishTime = #{publishTime},</if>
            <if test="publishTimeType != null">publishTimeType = #{publishTimeType},</if>
            <if test="type != null">type = #{type},</if>
            <if test="trendType != null">trendType = #{trendType},</if>
            <if test="validTimeBegin != null">validTimeBegin = #{validTimeBegin},</if>
            <if test="validTimeEnd != null">validTimeEnd = #{validTimeEnd},</if>
            <if test="airportCode != null">airportCode = #{airportCode},</if>
            <if test="isAuto != null">isAuto = #{isAuto},</if>
            <if test="isCor != null">isCor = #{isCor},</if>
            <if test="cavOk != null">cavOk = #{cavOk},</if>
            <if test="verticalVisibility != null">verticalVisibility = #{verticalVisibility},</if>
            <if test="temperature != null">temperature = #{temperature},</if>
            <if test="dpTemperature != null">dpTemperature = #{dpTemperature},</if>
            <if test="altimeter != null">altimeter = #{altimeter},</if>
            <if test="windShear != null">windShear = #{windShear},</if>
            <if test="windShearContent != null">windShearContent = #{windShearContent},</if>
            <if test="windShearRunway != null">windShearRunway = #{windShearRunway},</if>
            <if test="cloudCode != null">cloudCode = #{cloudCode},</if>
            <if test="recentWeather != null">recentWeather = #{recentWeather},</if>
            <if test="isCancel != null">isCancel = #{isCancel},</if>
            <if test="nsc != null">nsc = #{nsc},</if>
            <if test="nsw != null">nsw = #{nsw},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="colourType != null">colourType = #{colourType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRDecodereportById" parameterType="Long">
        delete
        from r_decodereport
        where id = #{id}
    </delete>

    <delete id="deleteRDecodereportByIds" parameterType="String">
        delete from r_decodereport where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
