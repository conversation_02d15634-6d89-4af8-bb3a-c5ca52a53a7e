<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.RCloudinforesMapper">

    <resultMap type="RCloudinfores" id="RCloudinforesResult">
        <result property="id" column="id"/>
        <result property="decodeReportId" column="decodeReportId"/>
        <result property="cloudQuantity" column="cloudQuantity"/>
        <result property="cloudHeight" column="cloudHeight"/>
        <result property="cloudShape" column="cloudShape"/>
        <result property="colourType" column="colourType"/>
    </resultMap>

    <sql id="selectRCloudinforesVo">
        select id, decodeReportId, cloudQuantity, cloudHeight, cloudShape, colourType
        from r_cloudinfores
    </sql>

    <select id="selectRCloudinforesList" parameterType="RCloudinfores" resultMap="RCloudinforesResult">
        <include refid="selectRCloudinforesVo"/>
        <where>
            <if test="decodeReportId != null ">and decodeReportId = #{decodeReportId}</if>
            <if test="cloudQuantity != null  and cloudQuantity != ''">and cloudQuantity = #{cloudQuantity}</if>
            <if test="cloudHeight != null  and cloudHeight != ''">and cloudHeight = #{cloudHeight}</if>
            <if test="cloudShape != null  and cloudShape != ''">and cloudShape = #{cloudShape}</if>
            <if test="colourType != null ">and colourType = #{colourType}</if>
        </where>
    </select>

    <select id="selectRCloudinforesById" parameterType="Long" resultMap="RCloudinforesResult">
        <include refid="selectRCloudinforesVo"/>
        where id = #{id}
    </select>

    <insert id="insertRCloudinfores" parameterType="RCloudinfores" useGeneratedKeys="true" keyProperty="id">
        insert into r_cloudinfores
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId,</if>
            <if test="cloudQuantity != null">cloudQuantity,</if>
            <if test="cloudHeight != null">cloudHeight,</if>
            <if test="cloudShape != null">cloudShape,</if>
            <if test="colourType != null">colourType,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="decodeReportId != null">#{decodeReportId},</if>
            <if test="cloudQuantity != null">#{cloudQuantity},</if>
            <if test="cloudHeight != null">#{cloudHeight},</if>
            <if test="cloudShape != null">#{cloudShape},</if>
            <if test="colourType != null">#{colourType},</if>
        </trim>
    </insert>

    <insert id="insertRCloudinforesList">
        insert into r_cloudinfores (decodeReportId, cloudQuantity, cloudHeight, cloudShape,colourType) values
        <foreach collection="param" item="item" index="index" separator=",">
            (#{item.decodeReportId}, #{item.cloudQuantity}, #{item.cloudHeight}, #{item.cloudShape},#{item.colourType})
        </foreach>
    </insert>

    <update id="updateRCloudinfores" parameterType="RCloudinfores">
        update r_cloudinfores
        <trim prefix="SET" suffixOverrides=",">
            <if test="decodeReportId != null">decodeReportId = #{decodeReportId},</if>
            <if test="cloudQuantity != null">cloudQuantity = #{cloudQuantity},</if>
            <if test="cloudHeight != null">cloudHeight = #{cloudHeight},</if>
            <if test="cloudShape != null">cloudShape = #{cloudShape},</if>
            <if test="colourType != null">colourType = #{colourType},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteRCloudinforesById" parameterType="Long">
        delete
        from r_cloudinfores
        where id = #{id}
    </delete>

    <delete id="deleteRCloudinforesByIds" parameterType="String">
        delete from r_cloudinfores where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
