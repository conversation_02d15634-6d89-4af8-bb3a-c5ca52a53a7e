<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.AddCommandMapper">
    <resultMap type="AddCommand" id="AddCommandMap">
        <result property="id" column="id"/>
        <result property="enterLeaveId" column="enter_leave_id"/>
        <result property="command" column="command"/>
        <result property="commandValue" column="command_value"/>
    </resultMap>

    <select id="queryByEnterLeaveId" resultMap="AddCommandMap">
        select
            id,enter_leave_id,command,command_value
        from add_command
        where enter_leave_id = #{enterLeaveId}
    </select>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into add_command(id,enter_leave_id,command,command_value)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.enterLeaveId},#{entity.command},#{entity.commandValue})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into add_command(id,enter_leave_id,command,command_value)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.enterLeaveId},#{entity.command},#{entity.commandValue})
        </foreach>
        on duplicate key update
        id=values(id),
        enter_leave_id=values(enter_leave_id),
        command=values(command),
        command_value=values(command_value)
    </insert>

    <!--通过主键删除-->
    <delete id="deleteByEnterLeaveId">
        delete from add_command where enter_leave_id = #{enterLeaveId}
    </delete>
</mapper>
