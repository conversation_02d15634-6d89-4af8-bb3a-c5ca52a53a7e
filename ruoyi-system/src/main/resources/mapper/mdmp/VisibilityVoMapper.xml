<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.VisibilityVoMapper">

    <resultMap type="VisibilityVo" id="VisibilityVoResult">
        <result property="dataValue" column="dataValue"/>
        <result property="fcstTimeSequence" column="fcstTimeSequence"/>
        <result property="reatimeTimeSequence" column="reatimeTimeSequence"/>
        <result property="longitudeMin" column="longitudeMin"/>
        <result property="latitude" column="latitude"/>
        <result property="altitude" column="altitude"/>
        <result property="addTime" column="addTime"/>
        <result property="longitudeMax" column="longitudeMax"/>
        <result property="latitudeMin" column="latitudeMin"/>
        <result property="latitudeMax" column="latitudeMax"/>
    </resultMap>

    <sql id="selectVisibilityVoVo">
        select dataValue,
               fcstTimeSequence,
               reatimeTimeSequence,
               longitudeMin,
               latitude,
               altitude,
               addTime,
               longitudeMax,
               latitudeMin,
               latitudeMax
        from visibility_vo
    </sql>

    <select id="selectVisibilityVoList" parameterType="VisibilityVo" resultMap="VisibilityVoResult">
        <include refid="selectVisibilityVoVo"/>
        <where>
            <if test="dataValue != null ">and dataValue = #{dataValue}</if>
            <if test="fcstTimeSequence != null ">and fcstTimeSequence = #{fcstTimeSequence}</if>
            <if test="reatimeTimeSequence != null ">and reatimeTimeSequence = #{reatimeTimeSequence}</if>
            <if test="longitudeMin != null ">and longitudeMin = #{longitudeMin}</if>
            <if test="latitude != null ">and latitude = #{latitude}</if>
            <if test="altitude != null ">and altitude = #{altitude}</if>
            <if test="addTime != null ">and addTime = #{addTime}</if>
            <if test="longitudeMax != null ">and longitudeMax = #{longitudeMax}</if>
            <if test="latitudeMin != null ">and latitudeMin = #{latitudeMin}</if>
            <if test="latitudeMax != null ">and latitudeMax = #{latitudeMax}</if>
        </where>
    </select>

    <select id="selectVisibilityVoByDataValue" parameterType="BigDecimal" resultMap="VisibilityVoResult">
        <include refid="selectVisibilityVoVo"/>
        where dataValue = #{dataValue}
    </select>
    <select id="getDoubleList" resultType="java.lang.Double">
        SELECT dataValue
        FROM `visibility_vo`
        WHERE fcstTimeSequence = 1743109200
          AND altitude = 900
          AND windType = 1
          and addTime = '2025-03-26 10:16:19'
        ORDER BY latitudeMin ASC,
                 longitudeMin ASC;
    </select>

    <insert id="insertVisibilityVo" parameterType="VisibilityVo">
        insert into visibility_vo
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">dataValue,</if>
            <if test="fcstTimeSequence != null">fcstTimeSequence,</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence,</if>
            <if test="longitudeMin != null">longitudeMin,</if>
            <if test="latitude != null">latitude,</if>
            <if test="altitude != null">altitude,</if>
            <if test="addTime != null">addTime,</if>
            <if test="longitudeMax != null">longitudeMax,</if>
            <if test="latitudeMin != null">latitudeMin,</if>
            <if test="latitudeMax != null">latitudeMax,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="dataValue != null">#{dataValue},</if>
            <if test="fcstTimeSequence != null">#{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">#{reatimeTimeSequence},</if>
            <if test="longitudeMin != null">#{longitudeMin},</if>
            <if test="latitude != null">#{latitude},</if>
            <if test="altitude != null">#{altitude},</if>
            <if test="addTime != null">#{addTime},</if>
            <if test="longitudeMax != null">#{longitudeMax},</if>
            <if test="latitudeMin != null">#{latitudeMin},</if>
            <if test="latitudeMax != null">#{latitudeMax},</if>
        </trim>
    </insert>

    <insert id="insertVisibilityMpDataList">
        insert into visibility_vo
        (windType,dataValue,fcstTimeSequence,reatimeTimeSequence,addTime,longitudeMin,longitudeMax,latitude,altitude,latitudeMin,latitudeMax)
        values
        <foreach collection="param" item="item" index="index" separator=",">
            (#{item.windType},#{item.dataValue},#{item.fcstTimeSequence},#{item.reatimeTimeSequence},#{item.addTime},#{item.longitudeMin},#{item.longitudeMax},#{item.latitude},#{item.altitude},#{item.latitudeMin},#{item.latitudeMax})
        </foreach>
    </insert>

    <update id="updateVisibilityVo" parameterType="VisibilityVo">
        update visibility_vo
        <trim prefix="SET" suffixOverrides=",">
            <if test="fcstTimeSequence != null">fcstTimeSequence = #{fcstTimeSequence},</if>
            <if test="reatimeTimeSequence != null">reatimeTimeSequence = #{reatimeTimeSequence},</if>
            <if test="longitudeMin != null">longitudeMin = #{longitudeMin},</if>
            <if test="latitude != null">latitude = #{latitude},</if>
            <if test="altitude != null">altitude = #{altitude},</if>
            <if test="addTime != null">addTime = #{addTime},</if>
            <if test="longitudeMax != null">longitudeMax = #{longitudeMax},</if>
            <if test="latitudeMin != null">latitudeMin = #{latitudeMin},</if>
            <if test="latitudeMax != null">latitudeMax = #{latitudeMax},</if>
        </trim>
        where dataValue = #{dataValue}
    </update>

    <delete id="deleteVisibilityVoByDataValue" parameterType="BigDecimal">
        delete
        from visibility_vo
        where dataValue = #{dataValue}
    </delete>

    <delete id="deleteVisibilityVoByDataValues" parameterType="String">
        delete from visibility_vo where dataValue in
        <foreach item="dataValue" collection="array" open="(" separator="," close=")">
            #{dataValue}
        </foreach>
    </delete>
</mapper>
