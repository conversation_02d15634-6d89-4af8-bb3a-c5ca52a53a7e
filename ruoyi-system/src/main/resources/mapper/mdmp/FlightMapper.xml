<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightMapper">

    <resultMap type="Flight" id="FlightMap">
        <result property="id" column="id"/>
        <result property="flightDate" column="flight_date"/>
        <result property="flightStatus" column="flight_status"/>
        <result property="callSign" column="call_sign"/>
        <result property="departCity" column="depart_city"/>
        <result property="departAirportCode" column="depart_airport_code"/>
        <result property="arriveCity" column="arrive_city"/>
        <result property="arriveAirportCode" column="arrive_airport_code"/>
        <result property="planDepartTime" column="plan_depart_time"/>
        <result property="planArriveTime" column="plan_arrive_time"/>
        <result property="aircraftType" column="aircraft_type"/>
        <result property="tailSign" column="tail_sign"/>
        <result property="radarTransponderMode" column="radar_transponder_mode"/>
        <result property="actualDepartTime" column="actual_depart_time"/>
        <result property="actualArriveTime" column="actual_arrive_time"/>
        <result property="ioSign" column="io_sign"/>
        <result property="progress" column="progress"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="heightAttribute" column="height_attribute"/>
        <result property="defaultHeight" column="default_height"/>
        <result property="releaseRemark" column="release_remark"/>
    </resultMap>

    <sql id="selectFlightVo">
        select id,
               flight_date,
               flight_status,
               call_sign,
               depart_city,
               depart_airport_code,
               arrive_city,
               arrive_airport_code,
               plan_depart_time,
               plan_arrive_time,
               aircraft_type,
               tail_sign,
               radar_transponder_mode,
               actual_depart_time,
               actual_arrive_time,
               io_sign,
               progress,
               create_time,
               update_time,
               height_attribute,
               default_height,
               release_remark,
               deptCode
        from flight
    </sql>

    <!-- 通过ID查询单条数据 -->
    <select id="queryById" resultMap="FlightMap">
        <include refid="selectFlightVo"/>
        where id = #{id}
    </select>

    <select id="queryAll" resultMap="FlightMap">
        <include refid="selectFlightVo"/>
        where DATE(flight_date) = CURDATE()
        and flight_status &lt;&gt; 4
        <if test="deptCodeList != null and !deptCodeList.isEmpty()">
            and deptCode in
            <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
                #{deptCode}
            </foreach>
        </if>
    </select>

    <select id="selectFlightList" parameterType="Flight" resultMap="FlightMap">
        <include refid="selectFlightVo"/>
        <where>
            <if test="flightDate != null">and flight_date = #{flightDate}</if>
            <if test="flightStatus != null">and flight_status = #{flightStatus}</if>
            <if test="callSign != null">and call_sign = #{callSign}</if>
            <if test="departCity != null">and depart_city = #{departCity}</if>
            <if test="departAirportCode != null">and depart_airport_code = #{departAirportCode}</if>
            <if test="arriveCity != null">and arrive_city = #{arriveCity}</if>
            <if test="arriveAirportCode != null">and arrive_airport_code = #{arriveAirportCode}</if>
            <if test="planDepartTime != null">and plan_depart_time = #{planDepartTime}</if>
            <if test="planArriveTime != null">and plan_arrive_time = #{planArriveTime}</if>
            <if test="aircraftType != null">and aircraft_type = #{aircraftType}</if>
            <if test="tailSign != null">and tail_sign = #{tailSign}</if>
            <if test="radarTransponderMode != null">and radar_transponder_mode = #{radarTransponderMode}</if>
            <if test="actualDepartTime != null">and actual_depart_time = #{actualDepartTime}</if>
            <if test="actualArriveTime != null">and actual_arrive_time = #{actualArriveTime}</if>
            <if test="ioSign != null ">and io_sign = #{ioSign}</if>
            <if test="progress != null ">and progress = #{progress}</if>
            <if test="deptCodeList != null and !deptCodeList.isEmpty()">
                and deptCode in
                <foreach collection="deptCodeList" item="deptCode" open="(" separator="," close=")">
                    #{deptCode}
                </foreach>
            </if>
        </where>
    </select>

    <insert id="insert" parameterType="Flight" useGeneratedKeys="true" keyProperty="id">
        insert into flight
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flightDate != null">flight_date,</if>
            <if test="flightStatus != null">flight_status,</if>
            <if test="callSign != null">call_sign,</if>
            <if test="departCity != null">depart_city,</if>
            <if test="departAirportCode != null">depart_airport_code,</if>
            <if test="arriveCity != null">arrive_city,</if>
            <if test="arriveAirportCode != null">arrive_airport_code,</if>
            <if test="planDepartTime != null">plan_depart_time,</if>
            <if test="planArriveTime != null">plan_arrive_time,</if>
            <if test="aircraftType != null">aircraft_type,</if>
            <if test="tailSign != null">tail_sign,</if>
            <if test="radarTransponderMode != null">radar_transponder_mode,</if>
            <if test="actualDepartTime != null">actual_depart_time,</if>
            <if test="actualArriveTime != null">actual_arrive_time,</if>
            <if test="ioSign != null">io_sign,</if>
            <if test="progress != null">progress,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="heightAttribute != null">height_attribute,</if>
            <if test="defaultHeight != null">default_height,</if>
            <if test="releaseRemark != null">release_remark,</if>
            <if test="deptCode != null">deptCode,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flightDate != null">#{flightDate},</if>
            <if test="flightStatus != null">#{flightStatus},</if>
            <if test="callSign != null">#{callSign},</if>
            <if test="departCity != null">#{departCity},</if>
            <if test="departAirportCode != null">#{departAirportCode},</if>
            <if test="arriveCity != null">#{arriveCity},</if>
            <if test="arriveAirportCode != null">#{arriveAirportCode},</if>
            <if test="planDepartTime != null">#{planDepartTime},</if>
            <if test="planArriveTime != null">#{planArriveTime},</if>
            <if test="aircraftType != null">#{aircraftType},</if>
            <if test="tailSign != null">#{tailSign},</if>
            <if test="radarTransponderMode != null">#{radarTransponderMode},</if>
            <if test="actualDepartTime != null">#{actualDepartTime},</if>
            <if test="actualArriveTime != null">#{actualArriveTime},</if>
            <if test="ioSign != null">#{ioSign},</if>
            <if test="progress != null">#{progress},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="heightAttribute != null">#{heightAttribute},</if>
            <if test="defaultHeight != null">#{defaultHeight},</if>
            <if test="releaseRemark != null">#{releaseRemark},</if>
            <if test="deptCode != null">#{deptCode},</if>
        </trim>
    </insert>

    <!-- 批量新增数据 -->
    <insert id="insertBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        flight(id,flight_date,flight_status,call_sign,depart_city,depart_airport_code,arrive_city,arrive_airport_code,plan_depart_time,plan_arrive_time,aircraft_type,tail_sign,radar_transponder_mode,actual_depart_time,actual_arrive_time,io_sign,progress,create_time,update_time,height_attribute,default_height,release_remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.flightDate},#{entity.flightStatus},#{entity.callSign},#{entity.departCity},#{entity.departAirportCode},#{entity.arriveCity},#{entity.arriveAirportCode},#{entity.planDepartTime},#{entity.planArriveTime},#{entity.aircraftType},#{entity.tailSign},#{entity.radarTransponderMode},#{entity.actualDepartTime},#{entity.actualArriveTime},#{entity.ioSign},#{entity.progress},#{entity.createTime},#{entity.updateTime},#{entity.heightAttribute},#{entity.defaultHeight},#{entity.releaseRemark})
        </foreach>
    </insert>

    <!-- 批量新增或按主键更新数据 -->
    <insert id="insertOrUpdateBatch" keyProperty="id" useGeneratedKeys="true">
        insert into
        flight(id,flight_date,flight_status,call_sign,depart_city,depart_airport_code,arrive_city,arrive_airport_code,plan_depart_time,plan_arrive_time,aircraft_type,tail_sign,radar_transponder_mode,actual_depart_time,actual_arrive_time,io_sign,progress,create_time,update_time,height_attribute,default_height,release_remark)
        values
        <foreach collection="entities" item="entity" separator=",">
            (#{entity.id},#{entity.flightDate},#{entity.flightStatus},#{entity.callSign},#{entity.departCity},#{entity.departAirportCode},#{entity.arriveCity},#{entity.arriveAirportCode},#{entity.planDepartTime},#{entity.planArriveTime},#{entity.aircraftType},#{entity.tailSign},#{entity.radarTransponderMode},#{entity.actualDepartTime},#{entity.actualArriveTime},#{entity.ioSign},#{entity.progress},#{entity.createTime},#{entity.updateTime},#{entity.heightAttribute},#{entity.defaultHeight},#{entity.releaseRemark})
        </foreach>
        on duplicate key update
        id=values(id),
        flight_date=values(flight_date),
        flight_status=values(flight_status),
        call_sign=values(call_sign),
        depart_city=values(depart_city),
        depart_airport_code=values(depart_airport_code),
        arrive_city=values(arrive_city),
        arrive_airport_code=values(arrive_airport_code),
        plan_depart_time=values(plan_depart_time),
        plan_arrive_time=values(plan_arrive_time),
        aircraft_type=values(aircraft_type),
        tail_sign=values(tail_sign),
        radar_transponder_mode=values(radar_transponder_mode),
        actual_depart_time=values(actual_depart_time),
        actual_arrive_time=values(actual_arrive_time),
        io_sign=values(io_sign),
        progress=values(progress),
        create_time=values(create_time),
        update_time=values(update_time),
        height_attribute=values(height_attribute),
        default_height=values(default_height),
        release_remark=values(release_remark)
    </insert>

    <update id="update" parameterType="Flight">
        update flight
        <trim prefix="SET" suffixOverrides=",">
            <if test="flightDate != null">flight_date = #{flightDate},</if>
            <if test="flightStatus != null">flight_status = #{flightStatus},</if>
            <if test="callSign != null">call_sign = #{callSign},</if>
            <if test="departCity != null">depart_city = #{departCity},</if>
            <if test="departAirportCode != null">depart_airport_code = #{departAirportCode},</if>
            <if test="arriveCity != null">arrive_city = #{arriveCity},</if>
            <if test="arriveAirportCode != null">arrive_airport_code = #{arriveAirportCode},</if>
            <if test="planDepartTime != null">plan_depart_time = #{planDepartTime},</if>
            <if test="planArriveTime != null">plan_arrive_time = #{planArriveTime},</if>
            <if test="aircraftType != null">aircraft_type = #{aircraftType},</if>
            <if test="tailSign != null">tail_sign = #{tailSign},</if>
            <if test="radarTransponderMode != null">radar_transponder_mode = #{radarTransponderMode},</if>
            <if test="actualDepartTime != null">actual_depart_time = #{actualDepartTime},</if>
            <if test="actualArriveTime != null">actual_arrive_time = #{actualArriveTime},</if>
            <if test="ioSign != null">io_sign = #{ioSign},</if>
            <if test="progress != null">progress = #{progress},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="heightAttribute != null">height_attribute = #{heightAttribute},</if>
            <if test="defaultHeight != null">default_height = #{defaultHeight},</if>
            <if test="releaseRemark != null">release_remark = #{releaseRemark},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="delete" parameterType="integer">
        delete
        from flight
        where id = #{id}
    </delete>
</mapper>
