<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.RunwayMapper">
    <resultMap type="Runway" id="RunwayMap">
        <result property="id" column="id"/>
        <result property="runwayName" column="runway_name"/>
        <result property="planeOccupy" column="plane_occupy"/>
        <result property="personOccupy" column="person_occupy"/>
    </resultMap>

    <sql id="selectRunwayVo">
        select id, runway_name, plane_occupy, person_occupy from runway
    </sql>

    <!-- 通过ID查询单条数据 -->
    <select id="queryById" resultMap="RunwayMap">
        <include refid="selectRunwayVo"/>
        where id = #{id}
    </select>

    <select id="queryAll" resultMap="RunwayMap">
        <include refid="selectRunwayVo"/>
    </select>

    <update id="update" parameterType="Runway">
        update runway
        <trim prefix="SET" suffixOverrides=",">
            <if test="runwayName != null">runway_name = #{runwayName},</if>
            <if test="planeOccupy != null">plane_occupy = #{planeOccupy},</if>
            <if test="personOccupy != null">person_occupy = #{personOccupy},</if>
        </trim>
        where id = #{id}
    </update>

</mapper>
