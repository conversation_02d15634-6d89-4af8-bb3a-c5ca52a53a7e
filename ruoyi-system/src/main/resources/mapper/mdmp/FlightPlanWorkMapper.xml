<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.FlightPlanWorkMapper">

    <resultMap type="FlightPlanWork" id="FlightPlanWorkResult">
        <result property="id" column="id"/>
        <result property="flightPlanId" column="flightPlanId"/>
        <result property="workId" column="workId"/>
        <result property="planType" column="planType"/>
        <result property="graphType" column="graphType"/>
        <result property="workType" column="workType"/>
        <result property="workOffset" column="workOffset"/>
        <result property="positiveOrNegative" column="positiveOrNegative"/>
        <result property="circleCenterLong" column="circleCenterLong"/>
        <result property="circleCenterLat" column="circleCenterLat"/>
        <result property="radius" column="radius"/>
        <result property="workName" column="workName"/>
        <result property="minHeight" column="minHeight"/>
        <result property="maxHeight" column="maxHeight"/>

        <!--        * 作业区经纬度关联表-->
        <collection property="flightPlanWorkLongLatList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanWorkLongLatMapper.selectWorkLongLatByFlightPlanWorkId"
                    column="{flightPlanWorkId=id}">
        </collection>
        <!--        * 作业区内表-->
        <collection property="flightPlanWorkInList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanWorkInMapper.selectWorkInByFlightPlanWorkId"
                    column="{flightPlanWorkId=id}">
        </collection>

        <collection property="workMapDataList" select="com.ruoyi.system.mapper.mdmp.WorkMapDataMapper.selectAllByWorkId"
                    column="{workId=id}">
        </collection>


    </resultMap>

    <sql id="selectFlightPlanWorkVo">
        select id,
               flightPlanId,
               planType,
               workType,
               graphType,
               workOffset,
               positiveOrNegative,
               circleCenterLong,
               circleCenterLat,
               radius,
               workName,
               minHeight,
               maxHeight,
               workId
        from flight_plan_work
    </sql>

    <select id="selectFlightPlanWorkList" parameterType="FlightPlanWork" resultMap="FlightPlanWorkResult">
        <include refid="selectFlightPlanWorkVo"/>
        <where>
            <if test="flightPlanId != null ">and flightPlanId = #{flightPlanId}</if>
            <if test="planType != null ">and planType = #{planType}</if>
            <if test="workType != null ">and workType = #{workType}</if>
            <if test="graphType != null ">and graphType = #{graphType}</if>
            <if test="workOffset != null ">and workOffset = #{workOffset}</if>
            <if test="circleCenterLong != null  and circleCenterLong != ''">and circleCenterLong = #{circleCenterLong}</if>
            <if test="circleCenterLat != null  and circleCenterLat != ''">and circleCenterLat = #{circleCenterLat}</if>
            <if test="radius != null  and radius != ''">and radius = #{radius}</if>
            <if test="workName != null  and workName != ''">and workName like concat('%', #{workName}, '%')</if>
        </where>
    </select>

    <select id="selectFlightPlanWorkById" parameterType="Long" resultMap="FlightPlanWorkResult">
        <include refid="selectFlightPlanWorkVo"/>
        where id = #{id}
    </select>

    <select id="selectWorkByFlightPlanId"  resultMap="FlightPlanWorkResult">
        <include refid="selectFlightPlanWorkVo"/>
        where flightPlanId = #{flightPlanId}
        and planType=#{planType}
    </select>

    <insert id="insertFlightPlanWork" parameterType="FlightPlanWork"  useGeneratedKeys="true" keyProperty="id">
        insert into flight_plan_work
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="flightPlanId != null">flightPlanId,</if>
            <if test="workId != null">workId,</if>
            <if test="planType != null">planType,</if>
            <if test="graphType != null">graphType,</if>
            <if test="workType != null">workType,</if>
            <if test="workOffset != null">workOffset,</if>
            <if test="positiveOrNegative != null">positiveOrNegative,</if>
            <if test="circleCenterLong != null">circleCenterLong,</if>
            <if test="circleCenterLat != null">circleCenterLat,</if>
            <if test="radius != null">radius,</if>
            <if test="workName != null">workName,</if>
            <if test="minHeight != null">minHeight,</if>
            <if test="maxHeight != null">maxHeight,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="flightPlanId != null">#{flightPlanId},</if>
            <if test="workId != null">#{workId},</if>
            <if test="planType != null">#{planType},</if>
            <if test="graphType != null">#{graphType},</if>
            <if test="workType != null">#{workType},</if>
            <if test="workOffset != null">#{workOffset},</if>
            <if test="positiveOrNegative != null">#{positiveOrNegative},</if>
            <if test="circleCenterLong != null">#{circleCenterLong},</if>
            <if test="circleCenterLat != null">#{circleCenterLat},</if>
            <if test="radius != null">#{radius},</if>
            <if test="workName != null">#{workName},</if>
            <if test="minHeight != null">#{minHeight},</if>
            <if test="maxHeight != null">#{maxHeight},</if>
        </trim>
    </insert>

    <update id="updateFlightPlanWork" parameterType="FlightPlanWork">
        update flight_plan_work
        <trim prefix="SET" suffixOverrides=",">
            <if test="flightPlanId != null">flightPlanId = #{flightPlanId},</if>
            <if test="planType != null">planType = #{planType},</if>
            <if test="graphType != null">graphType = #{graphType},</if>
            <if test="workType != null">workType = #{workType},</if>
            <if test="workOffset != null">workOffset = #{workOffset},</if>
            <if test="positiveOrNegative != null">positiveOrNegative = #{positiveOrNegative},</if>
            <if test="circleCenterLong != null">circleCenterLong = #{circleCenterLong},</if>
            <if test="circleCenterLat != null">circleCenterLat = #{circleCenterLat},</if>
            <if test="radius != null">radius = #{radius},</if>
            <if test="workName != null">workName = #{workName},</if>
            <if test="minHeight != null">minHeight = #{minHeight},</if>
            <if test="maxHeight != null">maxHeight = #{maxHeight},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteFlightPlanWorkById" parameterType="Long">
        delete
        from flight_plan_work
        where id = #{id}
    </delete>

    <delete id="deleteFlightPlanWorkByIds" parameterType="Long">
        delete from flight_plan_work where planType = #{planType} and id in
        <foreach item="id" collection="ids" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByFlightPlanId"  resultMap="FlightPlanWorkResult">
        <include refid="selectFlightPlanWorkVo"/>
        where flightPlanId = #{flightPlanId} and planType=#{planType}
    </select>
    <select id="selectByIndex" resultMap="FlightPlanWorkResult">
        <include refid="selectFlightPlanWorkVo"/>
        where sort = #{index}

        LIMIT 1
    </select>
</mapper>
