<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.mdmp.SingleFlightTaskMapper">

    <resultMap type="SingleFlightTask" id="SingleFlightTaskResult">
        <result property="id" column="id"/>
        <result property="planName" column="planName"/>
        <result property="planDate" column="planDate"/>
        <result property="planDepTime" column="planDepTime"/>
        <result property="planArrTime" column="planArrTime"/>
        <result property="companyCode" column="companyCode"/>
        <result property="companyName" column="companyName"/>
        <result property="contactName" column="contactName"/>
        <result property="contactPhone" column="contactPhone"/>
        <result property="remark" column="remark"/>
        <result property="planStatus" column="planStatus"/>
        <result property="planType" column="planType"/>

        <!--        * 计划 机型机号关联表-->
        <collection property="planModelAircraftList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanAircraftModelMapper.selectAircraftByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

        <!--        * 计划 航线关联表-->
        <collection property="flightPlanRouteList"
                    select="com.ruoyi.system.mapper.mdmp.FlightPlanRouteMapper.selectRouteByFlightPlanId"
                    column="{flightPlanId=id,planType=planType}">
        </collection>

    </resultMap>

    <sql id="selectSingleFlightTaskVo">
        select id,
               planName,
               planDate,
               planDepTime,
               planArrTime,
               companyCode,
               companyName,
               contactName,
               contactPhone,
               remark,
               planStatus,
               planType
        from single_flight_task
    </sql>

    <select id="selectSingleFlightTaskList" resultMap="SingleFlightTaskResult">
        <include refid="selectSingleFlightTaskVo"/>
        <where>
            <if test="planName != null  and planName != ''">and planName like concat('%', #{planName}, '%')</if>
            <if test="planDate != null  and planDate != ''">and planDate = #{planDate}</if>
            <if test="companyCode != null  and companyCode != ''">and companyCode = #{companyCode}</if>
            <if test="companyName != null  and companyName != ''">and companyName like concat('%', #{companyName},
                '%')
            </if>
            <if test="contactName != null  and contactName != ''">and contactName like concat('%', #{contactName},
                '%')
            </if>
            <if test="contactPhone != null  and contactPhone != ''">and contactPhone = #{contactPhone}</if>
            <if test="planStatus != null ">and planStatus = #{planStatus}</if>
        </where>
    </select>

    <select id="selectSingleFlightTaskById" parameterType="Long" resultMap="SingleFlightTaskResult">
        <include refid="selectSingleFlightTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insertSingleFlightTask" parameterType="SingleFlightTask" useGeneratedKeys="true" keyProperty="id">
        insert into single_flight_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planName != null">planName,</if>
            <if test="planDate != null">planDate,</if>
            <if test="planDepTime != null">planDepTime,</if>
            <if test="planArrTime != null">planArrTime,</if>
            <if test="companyCode != null">companyCode,</if>
            <if test="companyName != null">companyName,</if>
            <if test="contactName != null">contactName,</if>
            <if test="contactPhone != null">contactPhone,</if>
            <if test="remark != null">remark,</if>
            <if test="planStatus != null">planStatus,</if>
            <if test="planType != null">planType,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planName != null">#{planName},</if>
            <if test="planDate != null">#{planDate},</if>
            <if test="planDepTime != null">#{planDepTime},</if>
            <if test="planArrTime != null">#{planArrTime},</if>
            <if test="companyCode != null">#{companyCode},</if>
            <if test="companyName != null">#{companyName},</if>
            <if test="contactName != null">#{contactName},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="remark != null">#{remark},</if>
            <if test="planStatus != null">#{planStatus},</if>
            <if test="planType != null">#{planType},</if>
        </trim>
    </insert>

    <update id="updateSingleFlightTask" parameterType="SingleFlightTask">
        update single_flight_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="planName != null">planName = #{planName},</if>
            <if test="planDate != null">planDate = #{planDate},</if>
            <if test="planDepTime != null">planDepTime = #{planDepTime},</if>
            <if test="planArrTime != null">planArrTime = #{planArrTime},</if>
            <if test="companyCode != null">companyCode = #{companyCode},</if>
            <if test="companyName != null">companyName = #{companyName},</if>
            <if test="contactName != null">contactName = #{contactName},</if>
            <if test="contactPhone != null">contactPhone = #{contactPhone},</if>
            <if test="remark != null">remark = #{remark},</if>
            <if test="planStatus != null">planStatus = #{planStatus},</if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSingleFlightTaskById" parameterType="Long">
        delete
        from single_flight_task
        where id = #{id}
    </delete>

    <delete id="deleteSingleFlightTaskByIds" parameterType="String">
        delete from single_flight_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
