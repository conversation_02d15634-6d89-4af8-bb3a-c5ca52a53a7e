package com.ruoyi.system.util;

import org.locationtech.jts.geom.*;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * 航路规划
 */
public class FlightPathPlanner {
    private static final double DEGREE_TO_METERS = 111320.0;
    private static final double SAFETY_GAP = 500; // 安全间隙距离（米）

    public static void main(String[] args) {
        // 输入参数
        double[] start = {85.59, 44.64, 1000};
        double[] end =   {86.03, 44.62, 1000};
        double bufferDistance = 500; // 米

        // 创建多个空域
        List<Polygon> noFlyZones = new ArrayList<>();

        // 空域1
        List<double[]> zone1 = Arrays.asList(
                new double[]{85.75, 44.53},
                new double[]{85.91, 44.53},
                new double[]{85.94, 44.76},
                new double[]{85.68, 44.75},
                new double[]{85.75, 44.53}
        );
        noFlyZones.add(createBufferedPolygon(zone1, bufferDistance));

        // 空域2 (在空域1右侧)
        List<double[]> zone2 = Arrays.asList(
                new double[]{85.97, 44.60},
                new double[]{86.05, 44.60},
                new double[]{86.05, 44.70},
                new double[]{85.97, 44.70},
                new double[]{85.97, 44.60}
        );
//        noFlyZones.add(createBufferedPolygon(zone2, bufferDistance));

        // 计算路径
        List<double[]> flightPath = calculateOptimalPath(start, end, noFlyZones, bufferDistance);

        // 输出结果
        System.out.println("优化后的飞行路径:");
        for (int i = 0; i < flightPath.size(); i++) {
            double[] p = flightPath.get(i);
            System.out.printf("拐点 %d: (%.5f, %.5f, %.0f)%n",
                    i, p[0], p[1], p[2]);
        }
    }

    // 创建带缓冲区的多边形
    public static Polygon createBufferedPolygon(List<double[]> coords, double bufferMeters) {
        GeometryFactory factory = new GeometryFactory();
        double bufferDegrees = bufferMeters / DEGREE_TO_METERS;

        Coordinate[] coordinates = new Coordinate[coords.size()];
        for (int i = 0; i < coords.size(); i++) {
            double[] coord = coords.get(i);
            coordinates[i] = new Coordinate(coord[0], coord[1]);
        }

        LinearRing ring = factory.createLinearRing(coordinates);
        Polygon polygon = factory.createPolygon(ring);

        // 创建带缓冲区
        return (Polygon) polygon.buffer(bufferDegrees);
    }

    public static List<double[]> calculateOptimalPath(double[] start, double[] end,
                                                      List<Polygon> noFlyZones,
                                                      double bufferMeters) {
        // 将起点终点加入路径点列表
        List<double[]> path = new ArrayList<>();
        path.add(start);

        // 直接连接起点和终点的线段
        GeometryFactory factory = new GeometryFactory();
        LineString directRoute = factory.createLineString(new Coordinate[]{
                new Coordinate(start[0], start[1]),
                new Coordinate(end[0], end[1])
        });

        // 检查是否有交点
        if (!intersectsAnyZone(directRoute, noFlyZones)) {
            path.add(end);
            return path;
        }

        // 计算所有空域的最小边界矩形(MBR)
        List<Envelope> mbrs = new ArrayList<>();
        for (Polygon zone : noFlyZones) {
            mbrs.add(zone.getEnvelopeInternal());
        }

        // 寻找安全的穿越通道
        double safeGapDegrees = SAFETY_GAP / DEGREE_TO_METERS;
        List<double[]> safePassages = findSafePassages(mbrs, safeGapDegrees, start[1], end[1]);

        // 如果有安全通道，直接从通道穿过
        if (!safePassages.isEmpty()) {
            path.addAll(safePassages);
            path.add(end);
            return path;
        }

        // 没有安全通道，则绕行
        return calculateDetourPath(start, end, noFlyZones, bufferMeters);
    }

    // 检查线段是否与任何空域相交
    private static boolean intersectsAnyZone(LineString line, List<Polygon> zones) {
        for (Polygon zone : zones) {
            if (line.intersects(zone)) {
                return true;
            }
        }
        return false;
    }

    // 寻找安全通道
    private static List<double[]> findSafePassages(List<Envelope> mbrs,
                                                   double safeGap,
                                                   double startLat,
                                                   double endLat) {
        // 按经度排序
        Collections.sort(mbrs, (a, b) -> Double.compare(a.getMinX(), b.getMinX()));

        List<double[]> passages = new ArrayList<>();

        // 1. 在空域之间寻找水平通道
        for (int i = 0; i < mbrs.size() - 1; i++) {
            Envelope current = mbrs.get(i);
            Envelope next = mbrs.get(i + 1);

            // 计算间隙大小
            double gap = next.getMinX() - current.getMaxX();

            if (gap >= safeGap * 2) { // 需要两侧都有安全距离
                // 计算通道中间点
                double midLon = (current.getMaxX() + next.getMinX()) / 2.0;
                double avgLat = (startLat + endLat) / 2.0;

                // 计算安全的入口点
                double entryLon = current.getMaxX() + safeGap;
                double exitLon = next.getMinX() - safeGap;

                // 添加两个绕行点，形成穿越通道
                passages.add(new double[]{entryLon, avgLat, 1000});
                passages.add(new double[]{exitLon, avgLat, 1000});

                // 只需要找到第一个可用通道
                break;
            }
        }

        // 2. 如果水平方向没有通道，在垂直方向寻找
        if (passages.isEmpty()) {
            // 按纬度排序
            Collections.sort(mbrs, (a, b) -> Double.compare(a.getMinY(), b.getMinY()));

            for (int i = 0; i < mbrs.size() - 1; i++) {
                Envelope current = mbrs.get(i);
                Envelope next = mbrs.get(i + 1);

                // 计算间隙大小
                double gap = next.getMinY() - current.getMaxY();

                if (gap >= safeGap * 2) { // 需要两侧都有安全距离
                    // 计算通道中间点
                    double midLat = (current.getMaxY() + next.getMinY()) / 2.0;
                    double avgLon = (mbrs.get(0).getMinX() + mbrs.get(mbrs.size()-1).getMaxX()) / 2.0;

                    // 计算安全的入口点
                    double entryLat = current.getMaxY() + safeGap;
                    double exitLat = next.getMinY() - safeGap;

                    // 添加两个绕行点，形成穿越通道
                    passages.add(new double[]{avgLon, entryLat, 1000});
                    passages.add(new double[]{avgLon, exitLat, 1000});

                    // 只需要找到第一个可用通道
                    break;
                }
            }
        }

        return passages;
    }

    // 计算绕行路径
    private static List<double[]> calculateDetourPath(double[] start, double[] end,
                                                      List<Polygon> noFlyZones,
                                                      double bufferMeters) {
        // 计算整体空域边界
        double minLon = Double.MAX_VALUE;
        double maxLon = Double.MIN_VALUE;
        double minLat = Double.MAX_VALUE;
        double maxLat = Double.MIN_VALUE;

        for (Polygon zone : noFlyZones) {
            Envelope env = zone.getEnvelopeInternal();
            minLon = Math.min(minLon, env.getMinX());
            maxLon = Math.max(maxLon, env.getMaxX());
            minLat = Math.min(minLat, env.getMinY());
            maxLat = Math.max(maxLat, env.getMaxY());
        }

        // 添加安全距离
        double bufferDeg = bufferMeters / DEGREE_TO_METERS;
        minLon -= bufferDeg;
        maxLon += bufferDeg;
        minLat -= bufferDeg;
        maxLat += bufferDeg;

        // 判断绕行方向（上方或下方）
        double routeMidLat = (start[1] + end[1]) / 2.0;
        double detourLat = (routeMidLat > maxLat) ? maxLat : minLat;

        // 选择更短的绕行路径方案
        List<double[]> path1 = new ArrayList<>();
        List<double[]> path2 = new ArrayList<>();

        // 方案1：左绕行
        path1.add(start);
        path1.add(new double[]{minLon, detourLat, start[2]});
        path1.add(new double[]{maxLon, detourLat, start[2]});
        path1.add(end);

        // 方案2：右绕行
        path2.add(start);
        path2.add(new double[]{maxLon, detourLat, start[2]});
        path2.add(new double[]{minLon, detourLat, start[2]});
        path2.add(end);

        // 选择更短的路径
        return calculatePathLength(path1) < calculatePathLength(path2) ? path1 : path2;
    }

    // 计算路径长度（简化版）
    private static double calculatePathLength(List<double[]> path) {
        double length = 0;
        for (int i = 0; i < path.size() - 1; i++) {
            double[] p1 = path.get(i);
            double[] p2 = path.get(i+1);
            length += Math.sqrt(Math.pow(p2[0] - p1[0], 2) + Math.pow(p2[1] - p1[1], 2));
        }
        return length;
    }
}
