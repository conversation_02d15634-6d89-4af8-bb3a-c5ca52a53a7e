package com.ruoyi.system.util;

/**
 * <AUTHOR>
 * @date ：Created in 2024/4/17 9:53
 * @description：
 * @modified By：
 * @version: $
 */
public class GeoCalculatorUtil {

    // 地球半径，单位：公里
    private static final double EARTH_RADIUS = 6371.0;

    // 将角度转换为弧度
    private static double toRadians(double angle) {
        return Math.toRadians(angle);
    }

    // 将弧度转换为角度
    private static double toDegrees(double radians) {
        return Math.toDegrees(radians);
    }

    /**
     * 根据起始点的经纬度、磁方位角和距离计算目标点的经纬度。
     *
     * @param lat1     起始点纬度（度）
     * @param lon1     起始点经度（度）
     * @param bearing  磁方位角（度）
     * @param distance 距离（公里）
     * @return 包含目标点经纬度的double数组 {lat2, lon2}
     */
    public static double[] calculateDestinationPoint(double lat1, double lon1, double bearing, double distance) {
        double lat1Rad = toRadians(lat1);
        double lon1Rad = toRadians(lon1);
        double bearingRad = toRadians(bearing);

        // 将距离转换为弧度（使用地球半径）
        double angularDistance = distance / EARTH_RADIUS;

        // 计算目标点的纬度
        double lat2Rad = Math.asin(Math.sin(lat1Rad) * Math.cos(angularDistance) +
                Math.cos(lat1Rad) * Math.sin(angularDistance) * Math.cos(bearingRad));

        // 计算目标点的经度
        double lon2Rad = lon1Rad + Math.atan2(Math.sin(bearingRad) * Math.sin(angularDistance) * Math.cos(lat1Rad),
                Math.cos(angularDistance) - Math.sin(lat1Rad) * Math.sin(lat2Rad));

        // 将弧度转换回角度
        double lat2 = toDegrees(lat2Rad);
        double lon2 = toDegrees(lon2Rad);

        // 确保经度在-180到180之间
        lon2 = (lon2 + 180) % 360 - 180;

        return new double[]{lat2, lon2};
    }

}
