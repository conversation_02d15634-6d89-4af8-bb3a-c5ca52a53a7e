package com.ruoyi.system.util;

import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.vo.MeteVo;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2024/11/27 9:56
 * @description：
 * @modified By：
 * @version: $
 */
public class MeteUtil {
    private double MIN_LONGITUDE_A;
    private double MAX_LONGITUDE_A;
    private double MIN_LATITUDE_A;
    private double MAX_LATITUDE_A;
    private double STEP_A;

    public static final double MIN_LONGITUDE_B = 84.73;
    public static final double MAX_LONGITUDE_B = 87.17;
    public static final double MIN_LATITUDE_B = 43.60;
    public static final double MAX_LATITUDE_B = 46.16;
    public static final BigDecimal STEP_B = new BigDecimal(0.01);

    //    private static final double MIN_LONGITUDE_B = 106.308347;
//    private static final double MAX_LONGITUDE_B = 106.328347;
//    private static final double MIN_LATITUDE_B = 29.508872;
//    private static final double MAX_LATITUDE_B = 29.528872;
//    private static final BigDecimal STEP_B = new BigDecimal(0.0005);

    // 假设data是矩形A的数据，以List<List<Double>>格式存储
    private List<List<Double>> data;

    public MeteUtil(List<List<Double>> data, Double MIN_LONGITUDE_A, double MAX_LONGITUDE_A, double MIN_LATITUDE_A, double MAX_LATITUDE_A, double STEP_A) {
        this.data = data;
        this.MIN_LONGITUDE_A = MIN_LONGITUDE_A;
        this.MAX_LONGITUDE_A = MAX_LONGITUDE_A;
        this.MIN_LATITUDE_A = MIN_LATITUDE_A;
        this.MAX_LATITUDE_A = MAX_LATITUDE_A;
        this.STEP_A = STEP_A;
    }

    // 将经纬度转换为矩形A中的索引
    private int getIndexLon(double value, double min, double max, double step) {
        if (value < min) return 0;
        if (value > max) return (int) ((max - min) / step);
        return (int) Math.round((value - min) / step);
    }

    private int getIndexLat(double value, double min, double max, double step) {
        if (value < min) return 0;
        if (value > max) return (int) ((max - min) / step);
        return (int) Math.round(Math.abs(value - max) / step);
    }

    // 获取矩形A中对应格子的数据值，如果索引超出范围，则找到最近的格子
    private Double getDataValueForA(double lon, double lat) {
        int latIndex = getIndexLat(lat, MIN_LATITUDE_A, MAX_LATITUDE_A, STEP_A);
        int lonIndex = getIndexLon(lon, MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);
        Double value = data.get(latIndex).get(lonIndex);
        return value;
    }


    // 将矩形A的数据映射到矩形B
    public List<Double> mapDataToB() {
        List<Double> mappedData = new ArrayList<>();
        for (BigDecimal lat = BigDecimal.valueOf(MIN_LATITUDE_B); lat.compareTo(BigDecimal.valueOf(MAX_LATITUDE_B)) <= 0; lat = lat.add(STEP_B)) {
            for (BigDecimal lon = BigDecimal.valueOf(MIN_LONGITUDE_B); lon.compareTo(BigDecimal.valueOf(MAX_LONGITUDE_B)) <= 0; lon = lon.add(STEP_B)) {
                Double value = getDataValueForA(lon.doubleValue(), lat.doubleValue());
                mappedData.add(value);
            }
        }
        return mappedData;
    }

    public List<MpData> mapDataToMpData(Long time, Double altitude, Date addTime) {
        List<MpData> mappedData = new ArrayList<>();
        BigDecimal minLat = BigDecimal.valueOf(MIN_LATITUDE_B);
        BigDecimal maxLat = BigDecimal.valueOf(MAX_LATITUDE_B);
        BigDecimal stepLat = STEP_B;

        // 遍历纬度
        for (BigDecimal lat = minLat; lat.compareTo(maxLat) <= 0; lat = lat.add(stepLat)) {
            // int latIndex = getIndexLat(lat.doubleValue(), MIN_LATITUDE_A, MAX_LATITUDE_A, STEP_A);
            BigDecimal minLon = BigDecimal.valueOf(MIN_LONGITUDE_B);
            BigDecimal maxLon = BigDecimal.valueOf(MAX_LONGITUDE_B);
            BigDecimal stepLon = STEP_B;

            // 遍历经度（合并连续相同索引的经度范围）
            BigDecimal currentLon = minLon;
            while (currentLon.compareTo(maxLon) <= 0) {
                // 计算当前经度的索引
                int currentLonIndex = getIndexLon(currentLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);

                // 记录起始经度
                BigDecimal startLon = currentLon;
                BigDecimal endLon = currentLon;

                // 向后查找连续相同索引的经度
                while (true) {
                    BigDecimal nextLon = currentLon.add(stepLon);
                    if (nextLon.compareTo(maxLon) > 0) break;

                    // 计算下一个经度的索引
                    int nextLonIndex = getIndexLon(nextLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);
                    if (nextLonIndex != currentLonIndex) break; // 索引变化则停止

                    // 索引相同，扩展结束经度
                    endLon = nextLon;
                    currentLon = nextLon;
                }

                // 创建合并后的数据对象
                MpData mpData = new MpData();
                mpData.setLatitude(lat.doubleValue());
                mpData.setLongitudeMin(startLon.doubleValue());
                mpData.setLongitudeMax(endLon.doubleValue());

                // 获取数据值（根据起始点计算，或按需调整）
                Double value = getDataValueForA(startLon.doubleValue(), lat.doubleValue());
                mpData.setDataValue(value);

                // 设置其他字段
                mpData.setFcstTimeSequence(time / 1000);
                mpData.setAltitude(altitude);
                mpData.setAddTime(addTime);

                mappedData.add(mpData);

                // 移动到下一个不同索引的经度
                currentLon = currentLon.add(stepLon);
            }
        }
        return mappedData;
    }

    public List<VMpData> vMapDataToMpData(Long time, Double altitude, Date addTime) {
        List<VMpData> mappedData = new ArrayList<>();
        BigDecimal minLat = BigDecimal.valueOf(MIN_LATITUDE_B);
        BigDecimal maxLat = BigDecimal.valueOf(MAX_LATITUDE_B);
        BigDecimal stepLat = STEP_B;

        // 遍历纬度
        for (BigDecimal lat = minLat; lat.compareTo(maxLat) <= 0; lat = lat.add(stepLat)) {
            // int latIndex = getIndexLat(lat.doubleValue(), MIN_LATITUDE_A, MAX_LATITUDE_A, STEP_A);
            BigDecimal minLon = BigDecimal.valueOf(MIN_LONGITUDE_B);
            BigDecimal maxLon = BigDecimal.valueOf(MAX_LONGITUDE_B);
            BigDecimal stepLon = STEP_B;

            // 遍历经度（合并连续相同索引的经度范围）
            BigDecimal currentLon = minLon;
            while (currentLon.compareTo(maxLon) <= 0) {
                // 计算当前经度的索引
                int currentLonIndex = getIndexLon(currentLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);

                // 记录起始经度
                BigDecimal startLon = currentLon;
                BigDecimal endLon = currentLon;

                // 向后查找连续相同索引的经度
                while (true) {
                    BigDecimal nextLon = currentLon.add(stepLon);
                    if (nextLon.compareTo(maxLon) > 0) break;

                    // 计算下一个经度的索引
                    int nextLonIndex = getIndexLon(nextLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);
                    if (nextLonIndex != currentLonIndex) break; // 索引变化则停止

                    // 索引相同，扩展结束经度
                    endLon = nextLon;
                    currentLon = nextLon;
                }

                // 创建合并后的数据对象
                VMpData mpData = new VMpData();
                mpData.setLatitude(lat.doubleValue());
                mpData.setLongitudeMin(startLon.doubleValue());
                mpData.setLongitudeMax(endLon.doubleValue());

                // 获取数据值（根据起始点计算，或按需调整）
                Double value = getDataValueForA(startLon.doubleValue(), lat.doubleValue());
                mpData.setDataValue(value);

                // 设置其他字段
                mpData.setFcstTimeSequence(time / 1000);
                mpData.setAltitude(altitude);
                mpData.setAddTime(addTime);

                mappedData.add(mpData);

                // 移动到下一个不同索引的经度
                currentLon = currentLon.add(stepLon);
            }
        }
        return mappedData;
    }

    public List<VisibilityMpData> visibilityMpData(Long time, Double altitude, Date addTime) {
        List<VisibilityMpData> mappedData = new ArrayList<>();
        BigDecimal minLat = BigDecimal.valueOf(MIN_LATITUDE_B);
        BigDecimal maxLat = BigDecimal.valueOf(MAX_LATITUDE_B);
        BigDecimal stepLat = STEP_B;

        // 遍历纬度
        for (BigDecimal lat = minLat; lat.compareTo(maxLat) <= 0; lat = lat.add(stepLat)) {
            // int latIndex = getIndexLat(lat.doubleValue(), MIN_LATITUDE_A, MAX_LATITUDE_A, STEP_A);
            BigDecimal minLon = BigDecimal.valueOf(MIN_LONGITUDE_B);
            BigDecimal maxLon = BigDecimal.valueOf(MAX_LONGITUDE_B);
            BigDecimal stepLon = STEP_B;

            // 遍历经度（合并连续相同索引的经度范围）
            BigDecimal currentLon = minLon;
            while (currentLon.compareTo(maxLon) <= 0) {
                // 计算当前经度的索引
                int currentLonIndex = getIndexLon(currentLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);

                // 记录起始经度
                BigDecimal startLon = currentLon;
                BigDecimal endLon = currentLon;

                // 向后查找连续相同索引的经度
                while (true) {
                    BigDecimal nextLon = currentLon.add(stepLon);
                    if (nextLon.compareTo(maxLon) > 0) break;

                    // 计算下一个经度的索引
                    int nextLonIndex = getIndexLon(nextLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);
                    if (nextLonIndex != currentLonIndex) break; // 索引变化则停止

                    // 索引相同，扩展结束经度
                    endLon = nextLon;
                    currentLon = nextLon;
                }

                // 创建合并后的数据对象
                VisibilityMpData mpData = new VisibilityMpData();
                mpData.setLatitude(lat.doubleValue());
                mpData.setLongitudeMin(startLon.doubleValue());
                mpData.setLongitudeMax(endLon.doubleValue());

                // 获取数据值（根据起始点计算，或按需调整）
                Double value = getDataValueForA(startLon.doubleValue(), lat.doubleValue());
                mpData.setDataValue(value);

                // 设置其他字段
                mpData.setFcstTimeSequence(time / 1000);
                mpData.setAltitude(altitude);
                mpData.setAddTime(addTime);

                mappedData.add(mpData);

                // 移动到下一个不同索引的经度
                currentLon = currentLon.add(stepLon);
            }
        }
        return mappedData;
    }

//    public List<VisibilityMpData> visibilityMpData(Long time, Double altitude, Date addTime) {
//        List<VisibilityMpData> mappedData = new ArrayList<>();
//        BigDecimal minLat = BigDecimal.valueOf(MIN_LATITUDE_B);
//        BigDecimal maxLat = BigDecimal.valueOf(MAX_LATITUDE_B);
//        BigDecimal stepLat = STEP_B;
//
//        BigDecimal minLon = BigDecimal.valueOf(MIN_LONGITUDE_B);
//        BigDecimal maxLon = BigDecimal.valueOf(MAX_LONGITUDE_B);
//        BigDecimal stepLon = STEP_B;
//
//        // 合并纬度块
//        BigDecimal currentLat = minLat;
//        while (currentLat.compareTo(maxLat) <= 0) {
//            // 获取当前纬度索引
//            int currentLatIndex = getIndexLat(currentLat.doubleValue(), MIN_LATITUDE_A, MAX_LATITUDE_A, STEP_A);
//            BigDecimal startLat = currentLat;
//            BigDecimal endLat = currentLat;
//
//            // 扩展纬度块
//            while (true) {
//                BigDecimal nextLat = endLat.add(stepLat);
//                if (nextLat.compareTo(maxLat) > 0) break;
//                int nextLatIndex = getIndexLat(nextLat.doubleValue(), MIN_LATITUDE_A, MAX_LATITUDE_A, STEP_A);
//                if (nextLatIndex != currentLatIndex) break;
//                endLat = nextLat;
//            }
//
//            // 合并经度块
//            BigDecimal currentLon = minLon;
//            while (currentLon.compareTo(maxLon) <= 0) {
//                int currentLonIndex = getIndexLon(currentLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);
//                BigDecimal startLon = currentLon;
//                BigDecimal endLon = currentLon;
//
//                // 扩展经度块
//                while (true) {
//                    BigDecimal nextLon = endLon.add(stepLon);
//                    if (nextLon.compareTo(maxLon) > 0) break;
//                    int nextLonIndex = getIndexLon(nextLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);
//                    if (nextLonIndex != currentLonIndex) break;
//                    endLon = nextLon;
//                }
//
//                // 创建合并后的数据对象
//                VisibilityMpData mpData = new VisibilityMpData();
//                mpData.setLatitudeMin(startLat.doubleValue());
//                mpData.setLatitudeMax(endLat.doubleValue());
//                mpData.setLongitudeMin(startLon.doubleValue());
//                mpData.setLongitudeMax(endLon.doubleValue());
//
//                // 获取数据值（以起始点为例，可根据需求调整）
//                Double value = getDataValueForA(startLon.doubleValue(), startLat.doubleValue());
//                mpData.setDataValue(value);
//
//                // 设置其他字段
//                mpData.setFcstTimeSequence(time / 1000);
//                mpData.setAltitude(altitude);
//                mpData.setAddTime(addTime);
//
//                mappedData.add(mpData);
//
//                // 移动到下一个经度块
//                currentLon = endLon.add(stepLon);
//            }
//
//            // 移动到下一个纬度块
//            currentLat = endLat.add(stepLat);
//        }
//        return mappedData;
//    }

    public List<TemperatureMpData> temperatureMpData(Long time, Double altitude, Date addTime) {
        List<TemperatureMpData> mappedData = new ArrayList<>();
        BigDecimal minLat = BigDecimal.valueOf(MIN_LATITUDE_B);
        BigDecimal maxLat = BigDecimal.valueOf(MAX_LATITUDE_B);
        BigDecimal stepLat = STEP_B;

        // 遍历纬度
        for (BigDecimal lat = minLat; lat.compareTo(maxLat) <= 0; lat = lat.add(stepLat)) {
            // int latIndex = getIndexLat(lat.doubleValue(), MIN_LATITUDE_A, MAX_LATITUDE_A, STEP_A);
            BigDecimal minLon = BigDecimal.valueOf(MIN_LONGITUDE_B);
            BigDecimal maxLon = BigDecimal.valueOf(MAX_LONGITUDE_B);
            BigDecimal stepLon = STEP_B;

            // 遍历经度（合并连续相同索引的经度范围）
            BigDecimal currentLon = minLon;
            while (currentLon.compareTo(maxLon) <= 0) {
                // 计算当前经度的索引
                int currentLonIndex = getIndexLon(currentLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);

                // 记录起始经度
                BigDecimal startLon = currentLon;
                BigDecimal endLon = currentLon;

                // 向后查找连续相同索引的经度
                while (true) {
                    BigDecimal nextLon = currentLon.add(stepLon);
                    if (nextLon.compareTo(maxLon) > 0) break;

                    // 计算下一个经度的索引
                    int nextLonIndex = getIndexLon(nextLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);
                    if (nextLonIndex != currentLonIndex) break; // 索引变化则停止

                    // 索引相同，扩展结束经度
                    endLon = nextLon;
                    currentLon = nextLon;
                }

                // 创建合并后的数据对象
                TemperatureMpData mpData = new TemperatureMpData();
                mpData.setLatitude(lat.doubleValue());
                mpData.setLongitudeMin(startLon.doubleValue());
                mpData.setLongitudeMax(endLon.doubleValue());

                // 获取数据值（根据起始点计算，或按需调整）
                Double value = getDataValueForA(startLon.doubleValue(), lat.doubleValue());
                mpData.setDataValue(value);

                // 设置其他字段
                mpData.setFcstTimeSequence(time / 1000);
                mpData.setAltitude(altitude);
                mpData.setAddTime(addTime);

                mappedData.add(mpData);

                // 移动到下一个不同索引的经度
                currentLon = currentLon.add(stepLon);
            }
        }
        return mappedData;
    }

    public static Map<String, Double> convertListToMap(MeteVo meteVo, List<List<Double>> array) {
        Map<String, Double> map = new HashMap<>();

        // 遍历外层列表（纬度数据）
        for (int row = 0; row < array.size(); row++) {
            List<Double> innerList = array.get(row);
            // 每一行代表一个纬度，row对应纬度的索引
            double currentLat = meteVo.getBboxLatMin() + row * meteVo.getLatRes();  // 纬度增加

            // 遍历内层列表（该纬度的所有经度数据）
            for (int col = 0; col < innerList.size(); col++) {
                double currentLon = meteVo.getBboxLonMin() + col * meteVo.getLonRes();  // 经度增加
                String key = currentLon + "_" + currentLat;

                // 存储列表中的值
                map.put(key, innerList.get(col));
            }
        }

        return map;
    }

    public List<RainfallMpData> rainfallMpData(Long time, Double altitude, Date addTime) {
        List<RainfallMpData> mappedData = new ArrayList<>();
        BigDecimal minLat = BigDecimal.valueOf(MIN_LATITUDE_B);
        BigDecimal maxLat = BigDecimal.valueOf(MAX_LATITUDE_B);
        BigDecimal stepLat = STEP_B;

        // 遍历纬度
        for (BigDecimal lat = minLat; lat.compareTo(maxLat) <= 0; lat = lat.add(stepLat)) {
            // int latIndex = getIndexLat(lat.doubleValue(), MIN_LATITUDE_A, MAX_LATITUDE_A, STEP_A);
            BigDecimal minLon = BigDecimal.valueOf(MIN_LONGITUDE_B);
            BigDecimal maxLon = BigDecimal.valueOf(MAX_LONGITUDE_B);
            BigDecimal stepLon = STEP_B;

            // 遍历经度（合并连续相同索引的经度范围）
            BigDecimal currentLon = minLon;
            while (currentLon.compareTo(maxLon) <= 0) {
                // 计算当前经度的索引
                int currentLonIndex = getIndexLon(currentLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);

                // 记录起始经度
                BigDecimal startLon = currentLon;
                BigDecimal endLon = currentLon;

                // 向后查找连续相同索引的经度
                while (true) {
                    BigDecimal nextLon = currentLon.add(stepLon);
                    if (nextLon.compareTo(maxLon) > 0) break;

                    // 计算下一个经度的索引
                    int nextLonIndex = getIndexLon(nextLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);
                    if (nextLonIndex != currentLonIndex) break; // 索引变化则停止

                    // 索引相同，扩展结束经度
                    endLon = nextLon;
                    currentLon = nextLon;
                }

                // 创建合并后的数据对象
                RainfallMpData mpData = new RainfallMpData();
                mpData.setLatitude(lat.doubleValue());
                mpData.setLongitudeMin(startLon.doubleValue());
                mpData.setLongitudeMax(endLon.doubleValue());

                // 获取数据值（根据起始点计算，或按需调整）
                Double value = getDataValueForA(startLon.doubleValue(), lat.doubleValue());
                mpData.setDataValue(value);

                // 设置其他字段
                mpData.setFcstTimeSequence(time / 1000);
                mpData.setAltitude(altitude);
                mpData.setAddTime(addTime);

                mappedData.add(mpData);

                // 移动到下一个不同索引的经度
                currentLon = currentLon.add(stepLon);
            }
        }
        return mappedData;
    }

    public static double getDateValue(String date, double lon) {
        int index = (int) Math.round(Math.abs(lon - MIN_LONGITUDE_B) / 0.01);
        String[] parts = date.split(",");
        if (index < 0 || index >= parts.length) {
            throw new IllegalArgumentException("Index out of bounds: " + index);
        }
        String doubleStr = parts[index];
        try {
            return Double.parseDouble(doubleStr);
        } catch (NumberFormatException e) {
            throw new IllegalArgumentException("Invalid double value: " + doubleStr, e);
        }
    }

    public List<SanddustMpData> sanddustMpData(Long time, Double altitude, Date addTime) {
        List<SanddustMpData> mappedData = new ArrayList<>();
        BigDecimal minLat = BigDecimal.valueOf(MIN_LATITUDE_B);
        BigDecimal maxLat = BigDecimal.valueOf(MAX_LATITUDE_B);
        BigDecimal stepLat = STEP_B;

        // 遍历纬度
        for (BigDecimal lat = minLat; lat.compareTo(maxLat) <= 0; lat = lat.add(stepLat)) {
            // int latIndex = getIndexLat(lat.doubleValue(), MIN_LATITUDE_A, MAX_LATITUDE_A, STEP_A);
            BigDecimal minLon = BigDecimal.valueOf(MIN_LONGITUDE_B);
            BigDecimal maxLon = BigDecimal.valueOf(MAX_LONGITUDE_B);
            BigDecimal stepLon = STEP_B;

            // 遍历经度（合并连续相同索引的经度范围）
            BigDecimal currentLon = minLon;
            while (currentLon.compareTo(maxLon) <= 0) {
                // 计算当前经度的索引
                int currentLonIndex = getIndexLon(currentLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);

                // 记录起始经度
                BigDecimal startLon = currentLon;
                BigDecimal endLon = currentLon;

                // 向后查找连续相同索引的经度
                while (true) {
                    BigDecimal nextLon = currentLon.add(stepLon);
                    if (nextLon.compareTo(maxLon) > 0) break;

                    // 计算下一个经度的索引
                    int nextLonIndex = getIndexLon(nextLon.doubleValue(), MIN_LONGITUDE_A, MAX_LONGITUDE_A, STEP_A);
                    if (nextLonIndex != currentLonIndex) break; // 索引变化则停止

                    // 索引相同，扩展结束经度
                    endLon = nextLon;
                    currentLon = nextLon;
                }

                // 创建合并后的数据对象
                SanddustMpData mpData = new SanddustMpData();
                mpData.setLatitude(lat.doubleValue());
                mpData.setLongitudeMin(startLon.doubleValue());
                mpData.setLongitudeMax(endLon.doubleValue());

                // 获取数据值（根据起始点计算，或按需调整）
                Double value = getDataValueForA(startLon.doubleValue(), lat.doubleValue());
                mpData.setDataValue(value);

                // 设置其他字段
                mpData.setFcstTimeSequence(time / 1000);
                mpData.setAltitude(altitude);
                mpData.setAddTime(addTime);

                mappedData.add(mpData);

                // 移动到下一个不同索引的经度
                currentLon = currentLon.add(stepLon);
            }
        }
        return mappedData;
    }

//    public static List<LocationValue> convertMapToList(Map<String, Double> map) {
//        List<LocationValue> locationValues = new ArrayList<>();
//
//        for (Map.Entry<String, Double> entry : map.entrySet()) {
//            String key = entry.getKey();
//            Double value = entry.getValue();
//
//            // 根据 "_" 拆分经度和纬度
//            String[] coordinates = key.split("_");
//            double longitude = Double.parseDouble(coordinates[0]);
//            double latitude = Double.parseDouble(coordinates[1]);
//
//            // 创建新的 LocationValue 对象并添加到列表
//            locationValues.add(new LocationValue(longitude, latitude, value));
//        }
//
//        return locationValues;
//    }
}
