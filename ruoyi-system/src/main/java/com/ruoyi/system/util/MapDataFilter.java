package com.ruoyi.system.util;

import com.ruoyi.system.domain.mdmp.FlightPlanRouteLongLat;
import com.ruoyi.system.domain.mdmp.MapData;
import com.ruoyi.system.domain.mdmp.vo.MapDataLongLatVo;

import java.util.ArrayList;
import java.util.List;

public class MapDataFilter {

    public Point3D convertTo3DPointFlightPlanRouteLongLat(FlightPlanRouteLongLat start) {

        return new Point3D(start.getDoubleLongitude().doubleValue(), start.getDoubleLatitude().doubleValue(), start.getHeight().doubleValue());
    }

    public Point3D convertTo3DPointMapDataLongLatVo(MapDataLongLatVo mapDataLongLatVo) {
        return new Point3D(mapDataLongLatVo.getLongitude().doubleValue(), mapDataLongLatVo.getLatitude().doubleValue(), mapDataLongLatVo.getHeight().doubleValue());
    }

    public List<MapData> filterMapDataWithin500Meters(List<MapData> mapDataList, Point3D a, Point3D b) {
        // 坐标系转换参数
        double midLat = (a.y + b.y) / 2;
        double metersPerDegreeLat = 111194.9;
        double metersPerDegreeLon = 111194.9 * Math.cos(Math.toRadians(midLat));

        // 转换A和B到平面坐标
        Point2D aPlane = new Point2D(a.x * metersPerDegreeLon, a.y * metersPerDegreeLat);
        Point2D bPlane = new Point2D(b.x * metersPerDegreeLon, b.y * metersPerDegreeLat);
        LineSegment abSegment = new LineSegment(aPlane, bPlane);

        List<MapData> result = new ArrayList<>();

        for (MapData data : mapDataList) {
            // 1. 检查A或B是否在矩形内
            if (isPointInside(a, data) || isPointInside(b, data)) {
                result.add(data);
                continue;
            }

            // 2. 获取矩形顶点和边
            List<Point2D> vertices = getVertices(data, metersPerDegreeLon, metersPerDegreeLat);
            List<LineSegment> edges = getEdges(vertices);

            // 3. 检查AB是否与矩形边相交
            boolean intersects = false;
            for (LineSegment edge : edges) {
                if (segmentsIntersect(abSegment, edge)) {
                    intersects = true;
                    break;
                }
            }
            if (intersects) {
                result.add(data);
                continue;
            }

            // 4. 计算顶点和边到AB的最小距离
            double minDistance = calculateMinDistance(vertices, edges, abSegment);

            // 5. 判断是否在500米范围内
            if (minDistance <= 500) {
                result.add(data);
            }
        }

        return result;
    }

    // 计算最小距离（顶点到线段 + 边到线段）
    private double calculateMinDistance(List<Point2D> vertices, List<LineSegment> edges, LineSegment abSegment) {
        double minDistance = Double.MAX_VALUE;

        // 顶点到AB的距离
        for (Point2D vertex : vertices) {
            double dist = distancePointToLineSegment(vertex, abSegment);
            if (dist < minDistance) {
                minDistance = dist;
            }
        }

        // 边到AB的距离
        for (LineSegment edge : edges) {
            double dist = distanceBetweenLineSegments(edge, abSegment);
            if (dist < minDistance) {
                minDistance = dist;
            }
        }

        return minDistance;
    }


    // 判断点是否在MapData的矩形区域内（经纬度）
    public static boolean isPointInside(Point3D point, MapData data) {
        double minLon = Math.min(data.getLongitudeStart().doubleValue(), data.getLongitudeEnd().doubleValue());
        double maxLon = Math.max(data.getLongitudeStart().doubleValue(), data.getLongitudeEnd().doubleValue());
        double minLat = Math.min(data.getLatitudeStart().doubleValue(), data.getLatitudeEnd().doubleValue());
        double maxLat = Math.max(data.getLatitudeStart().doubleValue(), data.getLatitudeEnd().doubleValue());

        return (point.x >= minLon && point.x <= maxLon) &&
                (point.y >= minLat && point.y <= maxLat);
    }


    // 辅助方法：获取MapData的四个顶点
    private List<Point2D> getVertices(MapData data, double metersPerDegreeLon, double metersPerDegreeLat) {
        List<Point2D> vertices = new ArrayList<>();
        double lonStart = data.getLongitudeStart().doubleValue();
        double lonEnd = data.getLongitudeEnd().doubleValue();
        double latStart = data.getLatitudeStart().doubleValue();
        double latEnd = data.getLatitudeEnd().doubleValue();

        vertices.add(new Point2D(lonStart * metersPerDegreeLon, latStart * metersPerDegreeLat));
        vertices.add(new Point2D(lonEnd * metersPerDegreeLon, latStart * metersPerDegreeLat));
        vertices.add(new Point2D(lonEnd * metersPerDegreeLon, latEnd * metersPerDegreeLat));
        vertices.add(new Point2D(lonStart * metersPerDegreeLon, latEnd * metersPerDegreeLat));
        return vertices;
    }

    // 辅助方法：生成矩形的四条边
    private List<LineSegment> getEdges(List<Point2D> vertices) {
        List<LineSegment> edges = new ArrayList<>();
        for (int i = 0; i < vertices.size(); i++) {
            Point2D current = vertices.get(i);
            Point2D next = vertices.get((i + 1) % vertices.size());
            edges.add(new LineSegment(current, next));
        }
        return edges;
    }

    // 计算点到线段的最短距离
    private double distancePointToLineSegment(Point2D p, LineSegment segment) {
        Point2D a = segment.start;
        Point2D b = segment.end;

        double ax = a.x, ay = a.y;
        double bx = b.x, by = b.y;
        double px = p.x, py = p.y;

        double dx = bx - ax;
        double dy = by - ay;
        if (dx == 0 && dy == 0) { // 线段退化为点
            return Math.hypot(px - ax, py - ay);
        }

        double t = ((px - ax) * dx + (py - ay) * dy) / (dx * dx + dy * dy);
        if (t < 0) {
            return Math.hypot(px - ax, py - ay);
        } else if (t > 1) {
            return Math.hypot(px - bx, py - by);
        } else {
            double projX = ax + t * dx;
            double projY = ay + t * dy;
            return Math.hypot(px - projX, py - projY);
        }
    }

    // 计算两条线段之间的最短距离
    private double distanceBetweenLineSegments(LineSegment seg1, LineSegment seg2) {
        // 检查两条线段是否相交，若相交则距离为0
        if (segmentsIntersect(seg1, seg2)) {
            return 0;
        }

        // 否则计算各端点之间的最小距离
        double d1 = distancePointToLineSegment(seg1.start, seg2);
        double d2 = distancePointToLineSegment(seg1.end, seg2);
        double d3 = distancePointToLineSegment(seg2.start, seg1);
        double d4 = distancePointToLineSegment(seg2.end, seg1);
        return Math.min(Math.min(d1, d2), Math.min(d3, d4));
    }

    // 判断两条线段是否相交
    private boolean segmentsIntersect(LineSegment seg1, LineSegment seg2) {
        Point2D p1 = seg1.start;
        Point2D q1 = seg1.end;
        Point2D p2 = seg2.start;
        Point2D q2 = seg2.end;

        int o1 = orientation(p1, q1, p2);
        int o2 = orientation(p1, q1, q2);
        int o3 = orientation(p2, q2, p1);
        int o4 = orientation(p2, q2, q1);

        // 一般情况
        if (o1 != o2 && o3 != o4) return true;

        // 特殊情况：共线且有重叠
        if (o1 == 0 && onSegment(p1, p2, q1)) return true;
        if (o2 == 0 && onSegment(p1, q2, q1)) return true;
        if (o3 == 0 && onSegment(p2, p1, q2)) return true;
        if (o4 == 0 && onSegment(p2, q1, q2)) return true;

        return false;
    }

    // 计算点r相对于线段pq的方向
    private int orientation(Point2D p, Point2D q, Point2D r) {
        double val = (q.y - p.y) * (r.x - q.x) - (q.x - p.x) * (r.y - q.y);
        if (val == 0) return 0; // 共线
        return (val > 0) ? 1 : 2; // 顺时针或逆时针
    }

    // 判断点r是否在线段pq上
    private boolean onSegment(Point2D p, Point2D r, Point2D q) {
        return r.x <= Math.max(p.x, q.x) && r.x >= Math.min(p.x, q.x) &&
                r.y <= Math.max(p.y, q.y) && r.y >= Math.min(p.y, q.y);
    }

    // 辅助类定义
    static class Point2D {
        double x, y;

        Point2D(double x, double y) {
            this.x = x;
            this.y = y;
        }
    }

    static class LineSegment {
        Point2D start, end;

        LineSegment(Point2D start, Point2D end) {
            this.start = start;
            this.end = end;
        }
    }
}
