package com.ruoyi.system.util.word;

import com.ruoyi.common.utils.PdfConverUtils;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 动态信息PDF文档处理工具类
 */
@Slf4j
public class DynamicInfoPdfUtils {

    /**
     * 生成动态信息PDF文档
     *
     * @param templatePath  模板文件路径
     * @param data          动态信息数据
     * @param sealImagePath 公章图片路径
     * @return PDF文档字节数组
     * @throws Exception 处理异常
     */
    public static byte[] generateDynamicInfoPdf(String templatePath, WordMeteorologicalVo data, String sealImagePath) throws Exception {
        log.info("开始生成动态信息PDF文档，模板路径: {}", templatePath);
        
        try {
            // 1. 先生成Word文档
            byte[] wordBytes = DynamicInfoWordUtils.generateDynamicInfoDocument(templatePath, data, sealImagePath);
            
            // 2. 将Word转换为PDF
            ByteArrayInputStream wordInputStream = new ByteArrayInputStream(wordBytes);
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
            
            boolean success = PdfConverUtils.wordToPdfByAspose(wordInputStream, pdfOutputStream);
            if (!success) {
                throw new Exception("Word转PDF失败");
            }
            
            byte[] pdfBytes = pdfOutputStream.toByteArray();
            log.info("动态信息PDF文档生成完成，大小: {} 字节", pdfBytes.length);
            return pdfBytes;
            
        } catch (Exception e) {
            log.error("生成动态信息PDF文档失败", e);
            throw new Exception("生成PDF文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量生成动态信息PDF文档并压缩
     *
     * @param templatePath    模板文件路径
     * @param dataList        动态信息数据列表
     * @param sealImagePath   公章图片路径
     * @param fileNamePrefix  文件名前缀
     * @return ZIP压缩包字节数组
     * @throws Exception 处理异常
     */
    public static byte[] generateDynamicInfoPdfBatch(String templatePath, List<WordMeteorologicalVo> dataList, 
                                                     String sealImagePath, String fileNamePrefix) throws Exception {
        log.info("开始批量生成动态信息PDF文档，数量: {}", dataList.size());
        
        try (ByteArrayOutputStream zipOutputStream = new ByteArrayOutputStream();
             ZipOutputStream zip = new ZipOutputStream(zipOutputStream)) {
            
            for (int i = 0; i < dataList.size(); i++) {
                WordMeteorologicalVo data = dataList.get(i);
                
                try {
                    // 生成单个PDF
                    byte[] pdfBytes = generateDynamicInfoPdf(templatePath, data, sealImagePath);
                    
                    // 构建文件名
                    String fileName = buildPdfFileName(data, fileNamePrefix, i + 1);
                    
                    // 添加到压缩包
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zip.putNextEntry(zipEntry);
                    zip.write(pdfBytes);
                    zip.closeEntry();
                    
                    log.info("已添加PDF文件到压缩包: {}", fileName);
                    
                } catch (Exception e) {
                    log.error("生成第{}个PDF文档失败: {}", i + 1, e.getMessage());
                    // 继续处理其他文档，不中断整个批量操作
                }
            }
            
            zip.finish();
            byte[] zipBytes = zipOutputStream.toByteArray();
            log.info("批量动态信息PDF文档生成完成，压缩包大小: {} 字节", zipBytes.length);
            return zipBytes;
            
        } catch (Exception e) {
            log.error("批量生成动态信息PDF文档失败", e);
            throw new Exception("批量生成PDF文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建PDF文件名
     */
    private static String buildPdfFileName(WordMeteorologicalVo data, String prefix, int index) {
        StringBuilder fileName = new StringBuilder();
        
        fileName.append(prefix);
        
        // 添加机型和注册号信息
        if (data.getAircraftType() != null && !data.getAircraftType().isEmpty()) {
            fileName.append("_").append(data.getAircraftType());
        }
        if (data.getRegistrationNumber() != null && !data.getRegistrationNumber().isEmpty()) {
            fileName.append("_").append(data.getRegistrationNumber());
        }
        
        // 添加飞行日期
        if (data.getFlightDate() != null && !data.getFlightDate().isEmpty()) {
            fileName.append("_").append(data.getFlightDate());
        }
        
        // 添加序号
        fileName.append("_").append(String.format("%03d", index));
        
        // 添加时间戳
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("HHmmss"));
        fileName.append("_").append(timestamp);
        
        fileName.append(".pdf");
        
        return fileName.toString();
    }

    /**
     * 生成文件名
     */
    public static String generateFileName(String prefix, String extension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return prefix + "_" + timestamp + "." + extension;
    }

    /**
     * 设置PDF响应头
     */
    public static void setPdfResponseHeaders(HttpServletResponse response, String fileName) {
        try {
            response.setContentType("application/pdf");
            response.setCharacterEncoding("UTF-8");
            
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
            
            log.info("设置PDF响应头完成，文件名: {}", fileName);
        } catch (Exception e) {
            log.error("设置PDF响应头失败", e);
        }
    }

    /**
     * 设置ZIP响应头
     */
    public static void setZipResponseHeaders(HttpServletResponse response, String fileName) {
        try {
            response.setContentType("application/zip");
            response.setCharacterEncoding("UTF-8");
            
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
            
            log.info("设置ZIP响应头完成，文件名: {}", fileName);
        } catch (Exception e) {
            log.error("设置ZIP响应头失败", e);
        }
    }

    /**
     * 输出PDF文档到响应流
     */
    public static void streamPdfDocumentToResponse(HttpServletResponse response, byte[] pdfBytes) {
        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(pdfBytes);
            outputStream.flush();
            log.info("PDF文档输出到响应流完成，大小: {} 字节", pdfBytes.length);
        } catch (Exception e) {
            log.error("输出PDF文档到响应流失败", e);
        }
    }

    /**
     * 输出ZIP文档到响应流
     */
    public static void streamZipDocumentToResponse(HttpServletResponse response, byte[] zipBytes) {
        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(zipBytes);
            outputStream.flush();
            log.info("ZIP文档输出到响应流完成，大小: {} 字节", zipBytes.length);
        } catch (Exception e) {
            log.error("输出ZIP文档到响应流失败", e);
        }
    }
}
