package com.ruoyi.system.util;

import com.ruoyi.system.domain.mdmp.VisibilityVo;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2025/3/25 13:31
 * @description：
 * @modified By：
 * @version: $
 */
public class DataExtractorUtil {
    // 用户定义的经纬度范围
    public static final double MIN_LONGITUDE_B = 84.80;
    public static final double MAX_LONGITUDE_B = 86.45;
    public static final double MIN_LATITUDE_B = 43.70;
    public static final double MAX_LATITUDE_B = 44.90;

    // 数据源属性（需初始化）
    public double bboxLatMin;
    public double bboxLonMin;
    public double bboxLatMax;  // 纬度最大值（数据存储方向为降序）
    public double bboxLonMax;
    public double lonRes;      // 经度步长
    public double latRes;      // 纬度步长
    public List<List<Double>> data; // 数据矩阵



    // 提取目标范围内的数据为 List<Visibility>
    public List<VisibilityVo> extractVisibilityData(int windType,Long fcstTimeSequence, Double altitude, Date addTime) {
        List<VisibilityVo> result = new ArrayList<>();

        // 计算目标范围的索引边界
        int iStart = getIndexLat(MAX_LATITUDE_B, bboxLatMin, bboxLatMax, latRes);
        int iEnd = getIndexLat(MIN_LATITUDE_B, bboxLatMin, bboxLatMax, latRes);
        int jStart = getIndexLon(MIN_LONGITUDE_B, bboxLonMin, bboxLonMax, lonRes);
        int jEnd = getIndexLon(MAX_LONGITUDE_B, bboxLonMin, bboxLonMax, lonRes);

        // 遍历索引范围
        for (int i = iStart; i <= iEnd; i++) {
            for (int j = jStart; j <= jEnd; j++) {
                // 计算实际经纬度边界
                double minLon = bboxLonMin + j * lonRes;
                double maxLon = minLon + lonRes;
                double maxLat = bboxLatMax - i * latRes; // 纬度降序排列
                double minLat = maxLat - latRes;
                double value = data.get(i).get(j);

                // 创建对象并添加到结果
                result.add(new VisibilityVo(minLon, maxLon, minLat, maxLat, value,fcstTimeSequence,altitude,addTime,windType));
            }
        }
        return result;
    }

    private int getIndexLon(double value, double min, double max, double step) {
        if (value < min) return 0;
        if (value > max) return (int) ((max - min) / step);
        return (int) Math.round((value - min) / step);
    }

    private int getIndexLat(double value, double min, double max, double step) {
        if (value < min) return 0;
        if (value > max) return (int) ((max - min) / step);
        return (int) Math.round(Math.abs(value - max) / step);
    }


    public static void main(String[] args) {
        // 原始数据参数
        double originalLonStart = 84.750;
        double originalLonEnd = 86.500;
        double originalLonStep = 0.125;
        double originalLatStart = 43.625;
        double originalLatEnd = 44.750;
        double originalLatStep = 0.125;

        // 生成原始经纬度列表
        List<Double> originalLons = generateValues(originalLonStart, originalLonEnd, originalLonStep);
        List<Double> originalLats = generateValues(originalLatStart, originalLatEnd, originalLatStep);

        // 假设原始数据已加载，这里用示例数据填充
        int lonCount = originalLons.size();
        int latCount = originalLats.size();
        double[][] originalData = new double[lonCount][latCount];
        for (int i = 0; i < lonCount; i++) {
            for (int j = 0; j < latCount; j++) {
                // 示例数据，实际应替换为真实数据
                originalData[i][j] = originalLons.get(i) + originalLats.get(j);
            }
        }

        // 新网格参数
        double newLonStart = 84.80;
        double newLonEnd = 86.45;
        double newLatStart = 43.70;
        double newLatEnd = 44.90;
        double newStep = 0.001;

        // 生成新经纬度列表
        List<Double> newLons = generateValues(newLonStart, newLonEnd, newStep);
        List<Double> newLats = generateValues(newLatStart, newLatEnd, newStep);

        // 存储插值结果
        double[][] newData = new double[newLons.size()][newLats.size()];

        // 对每个新点进行插值
        for (int i = 0; i < newLons.size(); i++) {
            double lon = newLons.get(i);
            for (int j = 0; j < newLats.size(); j++) {
                double lat = newLats.get(j);

                // 计算原始经度的左索引
                int iLon = (int) ((lon - originalLonStart) / originalLonStep);
                iLon = Math.max(0, Math.min(iLon, originalLons.size() - 2));

                // 计算原始纬度的左索引
                int iLat = (int) ((lat - originalLatStart) / originalLatStep);
                iLat = Math.max(0, Math.min(iLat, originalLats.size() - 2));

                // 获取周围四个点的值
                double v00 = originalData[iLon][iLat];
                double v10 = originalData[iLon + 1][iLat];
                double v01 = originalData[iLon][iLat + 1];
                double v11 = originalData[iLon + 1][iLat + 1];

                // 计算dx和dy
                double dx = (lon - originalLons.get(iLon)) / originalLonStep;
                double dy = (lat - originalLats.get(iLat)) / originalLatStep;

                // 双线性插值计算
                double v0 = v00 + dx * (v10 - v00);
                double v1 = v01 + dx * (v11 - v01);
                double interpolatedValue = v0 + dy * (v1 - v0);

                newData[i][j] = interpolatedValue;
            }
        }

        // 示例输出某个点的插值结果
        System.out.println("插值示例: newData[0][0] = " + newData[0][0]);
    }

    // 生成指定范围的等步长列表
    private static List<Double> generateValues(double start, double end, double step) {
        List<Double> values = new ArrayList<>();
        for (double v = start; v <= end + 1e-6; v += step) {
            values.add(v);
        }
        return values;
    }
}
