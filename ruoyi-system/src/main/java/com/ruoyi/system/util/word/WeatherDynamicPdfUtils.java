package com.ruoyi.system.util.word;

import com.ruoyi.common.utils.PdfConverUtils;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import lombok.extern.slf4j.Slf4j;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.zip.ZipEntry;
import java.util.zip.ZipOutputStream;

/**
 * 气象信息记录页PDF文档处理工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class WeatherDynamicPdfUtils {

    /**
     * 生成气象信息记录页PDF文档
     *
     * @param templatePath  模板文件路径
     * @param data          气象信息数据
     * @param sealImagePath 公章图片路径
     * @return PDF文档字节数组
     * @throws Exception 处理异常
     */
    public static byte[] generateWeatherRecordPdf(String templatePath, WordMeteorologicalVo data, String sealImagePath) throws Exception {
        log.info("开始生成气象信息记录页PDF文档，模板路径: {}", templatePath);
        
        try {
            // 1. 先生成Word文档
            byte[] wordBytes = WeatherRecordWordUtils.generateWeatherRecordDocument(templatePath, data, sealImagePath);
            
            // 2. 将Word转换为PDF
            ByteArrayInputStream wordInputStream = new ByteArrayInputStream(wordBytes);
            ByteArrayOutputStream pdfOutputStream = new ByteArrayOutputStream();
            
            boolean success = PdfConverUtils.wordToPdfByAspose(wordInputStream, pdfOutputStream);
            if (!success) {
                throw new Exception("Word转PDF失败");
            }
            
            byte[] pdfBytes = pdfOutputStream.toByteArray();
            log.info("气象信息记录页PDF文档生成完成，大小: {} 字节", pdfBytes.length);
            return pdfBytes;
            
        } catch (Exception e) {
            log.error("生成气象信息记录页PDF文档失败", e);
            throw new Exception("生成PDF文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 批量生成气象信息记录页PDF文档并压缩
     *
     * @param templatePath  模板文件路径
     * @param dataList      气象信息数据列表
     * @param sealImagePath 公章图片路径
     * @param fileNamePrefix 文件名前缀
     * @return 压缩包字节数组
     * @throws Exception 处理异常
     */
    public static byte[] generateWeatherRecordPdfBatch(String templatePath, List<WordMeteorologicalVo> dataList, 
                                                      String sealImagePath, String fileNamePrefix) throws Exception {
        log.info("开始批量生成气象信息记录页PDF文档，数量: {}", dataList.size());
        
        try (ByteArrayOutputStream zipOutputStream = new ByteArrayOutputStream();
             ZipOutputStream zip = new ZipOutputStream(zipOutputStream)) {
            
            for (int i = 0; i < dataList.size(); i++) {
                WordMeteorologicalVo data = dataList.get(i);
                
                try {
                    // 生成单个PDF
                    byte[] pdfBytes = generateWeatherRecordPdf(templatePath, data, sealImagePath);
                    
                    // 构建文件名
                    String fileName = buildPdfFileName(data, fileNamePrefix, i + 1);
                    
                    // 添加到压缩包
                    ZipEntry zipEntry = new ZipEntry(fileName);
                    zip.putNextEntry(zipEntry);
                    zip.write(pdfBytes);
                    zip.closeEntry();
                    
                    log.info("已添加PDF文件到压缩包: {}", fileName);
                    
                } catch (Exception e) {
                    log.error("生成第{}个PDF文档失败: {}", i + 1, e.getMessage());
                    // 继续处理其他文档，不中断整个批量操作
                }
            }
            
            zip.finish();
            byte[] zipBytes = zipOutputStream.toByteArray();
            log.info("批量PDF文档压缩包生成完成，大小: {} 字节", zipBytes.length);
            return zipBytes;
            
        } catch (Exception e) {
            log.error("批量生成气象信息记录页PDF文档失败", e);
            throw new Exception("批量生成PDF文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建PDF文件名
     *
     * @param data 气象数据
     * @param prefix 文件名前缀
     * @param index 序号
     * @return 文件名
     */
    private static String buildPdfFileName(WordMeteorologicalVo data, String prefix, int index) {
        StringBuilder fileName = new StringBuilder();
        
        if (prefix != null && !prefix.trim().isEmpty()) {
            fileName.append(prefix).append("_");
        }
        
        // 添加机型和注册号
        if (data.getAircraftType() != null && !data.getAircraftType().trim().isEmpty()) {
            fileName.append(data.getAircraftType()).append("_");
        }
        if (data.getRegistrationNumber() != null && !data.getRegistrationNumber().trim().isEmpty()) {
            fileName.append(data.getRegistrationNumber()).append("_");
        }
        
        // 添加飞行日期
        if (data.getFlightDate() != null && !data.getFlightDate().trim().isEmpty()) {
            fileName.append(data.getFlightDate().replace("-", "")).append("_");
        }
        
        // 添加序号
        fileName.append(String.format("%03d", index));
        
        // 添加扩展名
        fileName.append(".pdf");
        
        return fileName.toString();
    }

    /**
     * 设置PDF响应头
     *
     * @param response HTTP响应对象
     * @param fileName 文件名
     */
    public static void setPdfResponseHeaders(HttpServletResponse response, String fileName) {
        try {
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            response.setContentType("application/pdf");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
        } catch (UnsupportedEncodingException e) {
            log.error("设置PDF响应头失败", e);
            response.setContentType("application/pdf");
            response.setHeader("Content-Disposition", "attachment; filename=\"weather_record.pdf\"");
        }
    }

    /**
     * 设置ZIP响应头
     *
     * @param response HTTP响应对象
     * @param fileName 文件名
     */
    public static void setZipResponseHeaders(HttpServletResponse response, String fileName) {
        try {
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString());
            response.setContentType("application/zip");
            response.setCharacterEncoding("UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
        } catch (UnsupportedEncodingException e) {
            log.error("设置ZIP响应头失败", e);
            response.setContentType("application/zip");
            response.setHeader("Content-Disposition", "attachment; filename=\"weather_records.zip\"");
        }
    }

    /**
     * 生成默认文件名
     *
     * @param prefix 前缀
     * @param extension 扩展名
     * @return 文件名
     */
    public static String generateDefaultFileName(String prefix, String extension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return prefix + "_" + timestamp + "." + extension;
    }

    /**
     * 将字节数组写入响应流
     *
     * @param response HTTP响应对象
     * @param data 数据字节数组
     * @throws IOException IO异常
     */
    public static void writeToResponse(HttpServletResponse response, byte[] data) throws IOException {
        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(data);
            outputStream.flush();
        }
    }
}
