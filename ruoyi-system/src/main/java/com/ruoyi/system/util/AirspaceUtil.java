package com.ruoyi.system.util;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.mdmp.Airspace;
import com.ruoyi.system.domain.type.AirspaceType;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2024/4/10 10:40
 * @description：
 * @modified By：
 * @version: $
 */
public class AirspaceUtil {

    /**
     * 清除障碍物数据
     *
     * @param airspace
     */
    public static void clearObstacleData(Airspace airspace) {
        airspace.setDistance(null);
        airspace.setBenchmarkLat(null);
        airspace.setBenchmarkLong(null);
        airspace.setMagneticBearing(null);
    }

    /**
     * 清除扇形数据
     *
     * @param airspace
     */
    public static void clearFanShapedData(Airspace airspace) {
        airspace.setAngle(null);
        airspace.setCentralAngle(null);

    }

    /**
     * 清除椭圆数据
     *
     * @param airspace
     */
    public static void clearEllipseData(Airspace airspace) {
        airspace.setLongAxis(null);
        airspace.setHalfShaft(null);

    }

    /**
     * 清除圆数据
     *
     * @param airspace
     */
    public static void clearCircularData(Airspace airspace, Integer airspaceType) {
        airspace.setCircleCenterLong(null);
        airspace.setCircleCenterLat(null);
        if (airspaceType != AirspaceType.OBSTACLES) {
            airspace.setRadius(null);
        }
    }
}
