package com.ruoyi.system.util;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2024/11/25 10:39
 * @description：
 * @modified By：
 * @version: $
 */

public class DataExtractor {

    public static double lonMin = 85.25;
    public static double lonMax = 86.44;
    public static double latMin = 43.70;
    public static double latMax = 44.74;

    public static Map<String, Double> extractSmallBoxData(Map<String, Double> bigBoxData) {
        Map<String, Double> smallBoxData = new HashMap<>(); // 小框数据
        Set<String> seenKeys = new HashSet<>(); // 使用集合来跟踪已添加的数据

        // 设置步长，使用BigDecimal进行精确的步进
        BigDecimal lonStep = new BigDecimal("0.01");
        BigDecimal latStep = new BigDecimal("0.01");

        // 提取小框数据
        for (BigDecimal lon = BigDecimal.valueOf(lonMin); lon.compareTo(BigDecimal.valueOf(lonMax)) <= 0; lon = lon.add(lonStep)) {
            for (BigDecimal lat = BigDecimal.valueOf(latMin); lat.compareTo(BigDecimal.valueOf(latMax)) <= 0; lat = lat.add(latStep)) {
                // 寻找对应的大框数据
                double bigLon = Math.round(lon.doubleValue() * 4) / 4.0; // 四舍五入到0.25步长
                double bigLat = Math.round(lat.doubleValue() * 4) / 4.0; // 四舍五入到0.25步长

                String key = bigLon + "_" + bigLat;

                // 检查大框数据是否存在
                if (bigBoxData.containsKey(key)) {
                    String smallBoxKey = lon.setScale(2, RoundingMode.HALF_UP) + "_" + lat.setScale(2, RoundingMode.HALF_UP);
                    if (!seenKeys.contains(smallBoxKey)) {
                        seenKeys.add(smallBoxKey); // 记录已添加的条目
                        smallBoxData.put(smallBoxKey, bigBoxData.get(key)); // 将结果加入Map
                    }
                }
            }
        }

        return smallBoxData;
    }

    public static Double getValueByCoordinates(Map<String, Double> smallBoxData, double longitude, double latitude) {
        String key = BigDecimal.valueOf(longitude).setScale(2, RoundingMode.HALF_UP) + "_" +
                BigDecimal.valueOf(latitude).setScale(2, RoundingMode.HALF_UP);
        return smallBoxData.get(key); // 返回对应的值，或返回null
    }


}
