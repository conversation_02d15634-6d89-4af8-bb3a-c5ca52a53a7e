package com.ruoyi.system.util;

import java.util.Arrays;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2025/4/7 10:47
 * @description：
 * @modified By：
 * @version: $
 */
public class IDWInterpolation {

    public static double[][] idwInterpolation(double[][] inputGrid, int xScale, int yScale) {
        return idwInterpolation(inputGrid, xScale, yScale, 7, Math.max(xScale, yScale));
    }

    public static double[][] idwInterpolation(double[][] inputGrid, int xScale, int yScale, int power, double radius) {
        int rows = inputGrid.length;
        int cols = inputGrid[0].length;
        int newRows = rows * xScale;
        int newCols = cols * yScale;
        double[][] outputGrid = new double[newRows][newCols];

        for (int i = 0; i < newRows; i++) {
            for (int j = 0; j < newCols; j++) {
                double newPointValue = 0.0;
                double[][] invDistanceGrid = new double[rows][cols];
                double invDistanceSum = 0.0;

                // 计算反距离和总和
                for (int r = 0; r < rows; r++) {
                    for (int c = 0; c < cols; c++) {
                        double x = r * xScale + xScale / 2.0 - 1;
                        double y = c * yScale + yScale / 2.0 - 1;
                        double dx = Math.abs(x - i);
                        double dy = Math.abs(y - j);
                        double distance = Math.max(Math.max(dx, dy), 1.0);
                        double invDistance = (distance > radius) ? 0.0 : (1.0 / Math.pow(distance, power));
                        invDistanceGrid[r][c] = invDistance;
                        invDistanceSum += invDistance;
                    }
                }

                // 计算权重并累加值
                for (int r = 0; r < rows; r++) {
                    for (int c = 0; c < cols; c++) {
                        double weight = (invDistanceSum == 0) ? 0.0 : (invDistanceGrid[r][c] / invDistanceSum);
                        newPointValue += weight * inputGrid[r][c];
                    }
                }

                outputGrid[i][j] = newPointValue;
            }
        }

        return outputGrid;
    }

    // 示例数据准备和调用方法
    public static void main(String[] args) {
        // 示例中的索引数据（需要根据实际情况初始化）
        int[] colIndex = {0, 13, 25, 38, 50, 63, 75, 88, 100, 113, 125, 138, 150, 163, 164};
        int[] rowIndex = {0, 13, 25, 38, 50, 63, 75, 88, 100, 113, 119};
        // 假设 intensities 是一个已初始化的数组
        double[] intensities = new double[20000];

        double[] lngRange={84.8, 86.45};
        // 计算 validIndex 和 validValue
        int rowSize = (int) Math.round((lngRange[1] - lngRange[0]) * 100);
        int[][] validIndex = new int[rowIndex.length][colIndex.length];
        for (int r = 0; r < rowIndex.length; r++) {
            for (int c = 0; c < colIndex.length; c++) {
                validIndex[r][c] = rowSize * rowIndex[r] + colIndex[c];
            }
        }

        double[][] validValue = new double[rowIndex.length][colIndex.length];
        for (int r = 0; r < rowIndex.length; r++) {
            for (int c = 0; c < colIndex.length; c++) {
                int j = validIndex[r][c];
                int index = (int) Math.round(j / 1.8);
                validValue[r][c] = intensities[index];
            }
        }
        System.out.println(new Date());
        // 执行插值
        double[][] handledValue = idwInterpolation(validValue, 200, 200);
        System.out.println(handledValue.length);
        System.out.println(handledValue[0].length);
        System.out.println(new Date());
       // System.out.println(Arrays.deepToString(handledValue));

    }

}

