package com.ruoyi.system.util.word;

import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.xwpf.usermodel.*;

import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.net.URLEncoder;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 气象信息Word文档处理工具类
 */
@Slf4j
public class WeatherInfoWordUtils {

    /**
     * 生成气象信息Word文档
     *
     * @param templatePath  模板文件路径
     * @param data          气象信息数据
     * @param sealImagePath 公章图片路径
     * @return Word文档字节数组
     * @throws Exception 处理异常
     */
    public static byte[] generateWeatherInfoDocument(String templatePath, WordMeteorologicalVo data, String sealImagePath) throws Exception {
        log.info("开始生成气象信息Word文档，模板路径: {}", templatePath);

        try (InputStream templateStream = getTemplateInputStream(templatePath)) {
            XWPFDocument document = new XWPFDocument(templateStream);

            // 1. 构建占位符参数
            Map<String, String> params = buildPlaceholderParams(data);

            // 2. 替换文档中的占位符
            replaceInDocumentBody(document, params);
            processParagraphsAndTables(document, params);

            // 3. 填充表格数据（仅气象信息）
            fillWeatherTableData(document, data);

            // 4. 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            document.write(outputStream);
            log.info("气象信息Word文档生成完成");
            return outputStream.toByteArray();

        } catch (Exception e) {
            log.error("生成气象信息Word文档失败", e);
            throw new Exception("生成Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 获取模板输入流
     */
    private static InputStream getTemplateInputStream(String templatePath) throws Exception {
        try {
            // 尝试从类路径加载
            InputStream stream = WeatherInfoWordUtils.class.getClassLoader().getResourceAsStream(templatePath);
            if (stream != null) {
                log.info("从类路径加载模板文件: {}", templatePath);
                return stream;
            }

            // 尝试从文件系统加载
            if (Files.exists(Paths.get(templatePath))) {
                log.info("从文件系统加载模板文件: {}", templatePath);
                return Files.newInputStream(Paths.get(templatePath));
            }

            throw new FileNotFoundException("模板文件未找到: " + templatePath);
        } catch (Exception e) {
            log.error("加载模板文件失败: {}", templatePath, e);
            throw new Exception("加载模板文件失败: " + e.getMessage(), e);
        }
    }

    /**
     * 构建占位符参数
     */
    private static Map<String, String> buildPlaceholderParams(WordMeteorologicalVo data) {
        Map<String, String> params = new HashMap<>();
        
        // 基本信息
        params.put("${aircraftType}", data.getAircraftType() != null ? data.getAircraftType() : "");
        params.put("${registrationNumber}", data.getRegistrationNumber() != null ? data.getRegistrationNumber() : "");
        params.put("${flightDate}", data.getFlightDate() != null ? data.getFlightDate() : "");
        params.put("${fuel}", data.getFuel() != null ? data.getFuel() : "");
        
        // 统计信息
        params.put("${groundTimeMinTotal}", data.getGroundTimeMinTotal() != null ? data.getGroundTimeMinTotal() : "");
        params.put("${airTimeMinTotal}", data.getAirTimeMinTotal() != null ? data.getAirTimeMinTotal() : "");
        params.put("${totalTimeMinTotal}", data.getTotalTimeMinTotal() != null ? data.getTotalTimeMinTotal() : "");
        params.put("${sortieCountTotal}", data.getSortieCountTotal() != null ? data.getSortieCountTotal() : "");

        log.info("构建占位符参数完成，参数数量: {}", params.size());
        return params;
    }

    /**
     * 替换文档正文中的占位符
     */
    private static void replaceInDocumentBody(XWPFDocument document, Map<String, String> params) {
        log.info("开始替换文档正文中的占位符");
        
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceParagraphText(paragraph, params);
        }
        
        log.info("文档正文占位符替换完成");
    }

    /**
     * 处理段落和表格中的占位符
     */
    private static void processParagraphsAndTables(XWPFDocument document, Map<String, String> params) {
        log.info("开始处理段落和表格中的占位符");
        
        // 处理表格中的占位符
        for (XWPFTable table : document.getTables()) {
            for (XWPFTableRow row : table.getRows()) {
                for (XWPFTableCell cell : row.getTableCells()) {
                    for (XWPFParagraph paragraph : cell.getParagraphs()) {
                        replaceParagraphText(paragraph, params);
                    }
                }
            }
        }
        
        log.info("段落和表格占位符处理完成");
    }

    /**
     * 替换段落文本
     */
    private static void replaceParagraphText(XWPFParagraph paragraph, Map<String, String> params) {
        String text = paragraph.getText();
        if (text != null) {
            for (Map.Entry<String, String> entry : params.entrySet()) {
                if (text.contains(entry.getKey())) {
                    text = text.replace(entry.getKey(), entry.getValue());
                }
            }
            
            // 如果文本发生了变化，重新设置段落内容
            if (!text.equals(paragraph.getText())) {
                // 清除原有内容
                for (int i = paragraph.getRuns().size() - 1; i >= 0; i--) {
                    paragraph.removeRun(i);
                }
                // 添加新内容
                XWPFRun run = paragraph.createRun();
                run.setText(text);
            }
        }
    }

    /**
     * 填充气象信息表格数据
     */
    private static void fillWeatherTableData(XWPFDocument document, WordMeteorologicalVo data) {
        log.info("开始填充气象信息表格数据");

        List<XWPFTable> tables = document.getTables();
        if (tables.isEmpty()) {
            log.warn("文档中没有找到表格");
            return;
        }

        // 处理第一个表格（主表格）
        XWPFTable mainTable = tables.get(0);

        // 分析表格结构，找到气象信息表头的位置
        WeatherTableHeaderInfo headerInfo = analyzeWeatherTableHeaders(mainTable);

        // 根据表头位置填充气象信息数据
        fillWeatherDataByHeaders(mainTable, headerInfo, data);

        log.info("气象信息表格数据填充完成");
    }

    /**
     * 气象信息表头信息结构
     */
    private static class WeatherTableHeaderInfo {
        int departureWeatherHeaderRow = -1;  // 始发地气象信息表头行
        int arrivalWeatherHeaderRow = -1;    // 目的地气象信息表头行
    }

    /**
     * 分析气象信息表格表头
     */
    private static WeatherTableHeaderInfo analyzeWeatherTableHeaders(XWPFTable table) {
        log.info("开始分析气象信息表格表头");

        WeatherTableHeaderInfo headerInfo = new WeatherTableHeaderInfo();
        List<XWPFTableRow> rows = table.getRows();

        for (int i = 0; i < rows.size(); i++) {
            XWPFTableRow row = rows.get(i);
            String rowText = getRowText(row);
            log.debug("第{}行内容: {}", i, rowText);

            // 查找始发地气象信息表头 - 更精确的匹配
            if ((rowText.contains("批次") && rowText.contains("始发地") && rowText.contains("天气")) ||
                (rowText.contains("批次") && rowText.contains("始发地") && (rowText.contains("云高") || rowText.contains("温度")))) {
                headerInfo.departureWeatherHeaderRow = i;
                log.info("找到始发地气象信息表头，行号: {}", i);
            }

            // 查找目的地气象信息表头 - 更精确的匹配
            if ((rowText.contains("批次") && rowText.contains("目的地") && rowText.contains("天气")) ||
                (rowText.contains("批次") && rowText.contains("目的地") && (rowText.contains("云高") || rowText.contains("温度")))) {
                headerInfo.arrivalWeatherHeaderRow = i;
                log.info("找到目的地气象信息表头，行号: {}", i);
            }

            // 如果只有一个气象信息表头（不区分始发地和目的地）
            if (headerInfo.departureWeatherHeaderRow == -1 && headerInfo.arrivalWeatherHeaderRow == -1) {
                if (rowText.contains("批次") && rowText.contains("天气") &&
                    (rowText.contains("云高") || rowText.contains("温度") || rowText.contains("风向"))) {
                    headerInfo.departureWeatherHeaderRow = i;
                    log.info("找到通用气象信息表头，设为始发地表头，行号: {}", i);
                }
            }
        }

        log.info("气象信息表格表头分析完成，始发地表头行: {}, 目的地表头行: {}",
                headerInfo.departureWeatherHeaderRow, headerInfo.arrivalWeatherHeaderRow);
        return headerInfo;
    }

    /**
     * 获取行文本
     */
    private static String getRowText(XWPFTableRow row) {
        StringBuilder text = new StringBuilder();
        for (XWPFTableCell cell : row.getTableCells()) {
            text.append(cell.getText()).append(" ");
        }
        return text.toString();
    }

    /**
     * 根据表头位置填充气象信息数据
     */
    private static void fillWeatherDataByHeaders(XWPFTable table, WeatherTableHeaderInfo headerInfo, WordMeteorologicalVo data) {
        log.info("开始根据表头位置填充气象信息数据");

        // 填充始发地气象信息
        if (headerInfo.departureWeatherHeaderRow != -1 && data.getDepartureWeatherInfoList() != null && !data.getDepartureWeatherInfoList().isEmpty()) {
            log.info("开始填充始发地气象信息，数据条数: {}", data.getDepartureWeatherInfoList().size());

            int insertPosition = headerInfo.departureWeatherHeaderRow + 1;

            // 检查是否已有数据行，如果有则先清除
            clearExistingDataRows(table, insertPosition, headerInfo.arrivalWeatherHeaderRow);

            for (int i = 0; i < data.getDepartureWeatherInfoList().size(); i++) {
                WordFlightWeatherInfoVo weather = data.getDepartureWeatherInfoList().get(i);
                log.info("处理始发地数据第{}条: 批次={}, 位置={}, 天气={}",
                        i + 1, weather.getBatch(), weather.getLocationName(), weather.getWeather());

                String[] rowData = buildWeatherRowData(weather);
                insertRowAtPosition(table, insertPosition + i, rowData, true);
            }
        } else {
            log.warn("始发地气象信息数据为空或表头未找到");
        }

        // 重新分析表头位置（因为插入了新行）
        headerInfo = analyzeWeatherTableHeaders(table);

        // 填充目的地气象信息
        if (headerInfo.arrivalWeatherHeaderRow != -1 && data.getArrivalWeatherInfoList() != null && !data.getArrivalWeatherInfoList().isEmpty()) {
            log.info("开始填充目的地气象信息，数据条数: {}", data.getArrivalWeatherInfoList().size());

            int insertPosition = headerInfo.arrivalWeatherHeaderRow + 1;

            // 检查是否已有数据行，如果有则先清除
            clearExistingDataRows(table, insertPosition, -1);

            for (int i = 0; i < data.getArrivalWeatherInfoList().size(); i++) {
                WordFlightWeatherInfoVo weather = data.getArrivalWeatherInfoList().get(i);
                log.info("处理目的地数据第{}条: 批次={}, 位置={}, 天气={}",
                        i + 1, weather.getBatch(), weather.getLocationName(), weather.getWeather());

                String[] rowData = buildWeatherRowData(weather);
                insertRowAtPosition(table, insertPosition + i, rowData, true);
            }
        } else {
            log.warn("目的地气象信息数据为空或表头未找到");
        }

        log.info("根据表头位置填充气象信息数据完成");
    }

    /**
     * 清除已存在的数据行
     */
    private static void clearExistingDataRows(XWPFTable table, int startRow, int endRow) {
        List<XWPFTableRow> rows = table.getRows();
        int actualEndRow = endRow == -1 ? rows.size() : endRow;

        // 从后往前删除，避免索引变化问题
        for (int i = actualEndRow - 1; i >= startRow; i--) {
            if (i < rows.size()) {
                XWPFTableRow row = rows.get(i);
                String rowText = getRowText(row);

                // 如果是数据行（不包含表头关键字），则删除
                if (!rowText.contains("批次") && !rowText.contains("天气") &&
                    !rowText.contains("云高") && !rowText.contains("温度") &&
                    !rowText.contains("合计") && rowText.trim().length() > 0) {
                    table.removeRow(i);
                    log.debug("删除第{}行数据: {}", i, rowText);
                }
            }
        }
    }

    /**
     * 构建气象信息行数据
     */
    private static String[] buildWeatherRowData(WordFlightWeatherInfoVo weather) {
        log.debug("构建气象信息行数据: 批次={}, 位置={}", weather.getBatch(), weather.getLocationName());

        return new String[]{
            weather.getBatch() != null ? weather.getBatch() : "",
            weather.getLocationName() != null ? weather.getLocationName() : "",
            weather.getWeather() != null ? weather.getWeather() : "",
            weather.getCloudHeight() != null ? weather.getCloudHeight() : "",
            weather.getTemperature() != null ? weather.getTemperature() : "",
            weather.getWindDirection() != null ? weather.getWindDirection() : "",
            weather.getWindSpeed() != null ? weather.getWindSpeed() : "",
            weather.getVisibility() != null ? weather.getVisibility() : "",
            weather.getQnh() != null ? weather.getQnh() : ""
        };
    }

    /**
     * 在指定位置插入行
     */
    private static void insertRowAtPosition(XWPFTable table, int position, String[] data, boolean needMerge) {
        log.info("在位置{}插入行，数据长度: {}", position, data.length);

        try {
            XWPFTableRow newRow;

            // 如果位置超出表格范围，则在末尾添加
            if (position >= table.getRows().size()) {
                newRow = table.createRow();
                log.debug("在表格末尾创建新行");
            } else {
                newRow = table.insertNewTableRow(position);
                log.debug("在位置{}插入新行", position);
            }

            List<XWPFTableCell> cells = newRow.getTableCells();

            // 确保有足够的单元格
            while (cells.size() < data.length) {
                newRow.addNewTableCell();
                cells = newRow.getTableCells();
            }

            // 填充数据
            for (int i = 0; i < data.length && i < cells.size(); i++) {
                XWPFTableCell cell = cells.get(i);

                // 清除单元格原有内容
                cell.removeParagraph(0);
                XWPFParagraph paragraph = cell.addParagraph();
                XWPFRun run = paragraph.createRun();
                run.setText(data[i]);

                log.debug("填充第{}列数据: {}", i, data[i]);
            }

            log.info("数据插入完成，共填充{}列", Math.min(data.length, cells.size()));

        } catch (Exception e) {
            log.error("插入行数据失败，位置: {}", position, e);
            throw new RuntimeException("插入行数据失败", e);
        }
    }

    /**
     * 生成文件名
     */
    public static String generateFileName(String prefix, String extension) {
        String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
        return prefix + "_" + timestamp + "." + extension;
    }

    /**
     * 设置Word响应头
     */
    public static void setWordResponseHeaders(HttpServletResponse response, String fileName) {
        try {
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
            response.setCharacterEncoding("UTF-8");
            
            String encodedFileName = URLEncoder.encode(fileName, "UTF-8");
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodedFileName + "\"");
            response.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.setHeader("Pragma", "no-cache");
            response.setHeader("Expires", "0");
            
            log.info("设置Word响应头完成，文件名: {}", fileName);
        } catch (Exception e) {
            log.error("设置Word响应头失败", e);
        }
    }

    /**
     * 输出Word文档到响应流
     */
    public static void streamWordDocumentToResponse(HttpServletResponse response, byte[] docBytes) {
        try (OutputStream outputStream = response.getOutputStream()) {
            outputStream.write(docBytes);
            outputStream.flush();
            log.info("Word文档输出到响应流完成，大小: {} 字节", docBytes.length);
        } catch (Exception e) {
            log.error("输出Word文档到响应流失败", e);
        }
    }
}
