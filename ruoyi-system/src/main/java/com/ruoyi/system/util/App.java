package com.ruoyi.system.util;

import com.ruoyi.system.domain.mdmp.MapData;
import org.locationtech.jts.geom.*;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/4/15 11:15
 * @description：
 * @modified By：
 * @version: $
 */
public class App {
    public static void main(String[] args) {
//        App app = new App();
        // 拓扑关系 contains
//        app.doContains();

        // 叠加分析 intersection
//        app.doIntersection();
//    * 经度范围 85.25～86.45
//    * 纬度范围 43.70～44.75

    }

    private JTSGeometryUtil jtsGeometryUtil = new JTSGeometryUtil();

    /**
     * Contains 包含
     */
    public boolean doContains(Double lon, Double lat) {
        // 点
        Point point = jtsGeometryUtil.getPoint(lon, lat);
        // 面
//        Coordinate[] coordinates = new Coordinate[5];
//        coordinates[0] = new Coordinate(116.361725, 39.95676); // 首尾坐标相同
//        coordinates[1] = new Coordinate(116.364887, 39.908515);
//        coordinates[2] = new Coordinate(116.442501, 39.909622);
//        coordinates[3] = new Coordinate(116.440488, 39.955654);
//        coordinates[4] = new Coordinate(116.361725, 39.95676); // 首尾坐标相同
//        Polygon polygon = jtsGeometryUtil.getPolygon(coordinates);

        Coordinate[] coordinates2 = new Coordinate[5];
        coordinates2[0] = new Coordinate(85.5772904, 44.3447870); // 首尾坐标相同
        coordinates2[1] = new Coordinate(86.2219068, 44.3179356);
        coordinates2[2] = new Coordinate(86.2064475, 44.1382887);
        coordinates2[3] = new Coordinate(85.5637836, 44.1650594);
        coordinates2[4] = new Coordinate(85.5772904, 44.3447870); // 首尾坐标相同
        Polygon polygon1 = jtsGeometryUtil.getPolygon(coordinates2);


        // 操作
        Geometry geometry = point.intersection(polygon1);
        Coordinate[] coordinate = geometry.getCoordinates();
        if (coordinate.length > 0) {
            //在判断航线的最低点>=作业区的最低高度，且航线最高点<=作业区的最高高度，如果同时满足则证明航线与作业区有交集

            for (Coordinate coordinate1 : coordinate) {
                System.out.println(coordinate1.x + "," + coordinate1.y);
            }
            return true;
        }
        return false;
    }


    /**
     * Contains 包含
     * 判断点是否在多边形内
     */
    public boolean judgeContains(Double lon, Double lat, Coordinate[] coordinates) {
        // 点
        Point point = jtsGeometryUtil.getPoint(lon, lat);
        // 面
        Polygon polygon1 = jtsGeometryUtil.getPolygon(coordinates);
        // 操作
        Geometry geometry = point.intersection(polygon1);
        Coordinate[] coordinate = geometry.getCoordinates();
        if (coordinate.length > 0) {
            //在判断航线的最低点>=作业区的最低高度，且航线最高点<=作业区的最高高度，如果同时满足则证明航线与作业区有交集
            for (Coordinate coordinate1 : coordinate) {
                System.out.println(coordinate1.x + "," + coordinate1.y);
            }
            return true;
        }
        return false;
    }


    /**
     * Intersection 交叉分析
     */
    public void doIntersection() {
        // 线一
        Coordinate[] coordinates1 = new Coordinate[2];
        coordinates1[0] = new Coordinate(116.362209, 40.51499435375046);
        coordinates1[1] = new Coordinate(116.422897, 39.878002);
        LineString lineString1 = jtsGeometryUtil.getLineString(coordinates1);

        // 线二
        Coordinate[] coordinates2 = new Coordinate[2];
        coordinates2[0] = new Coordinate(116.32873535156249, 40.01499435375046);
        coordinates2[1] = new Coordinate(116.52099609375, 40.019201307686785);
        LineString lineString2 = jtsGeometryUtil.getLineString(coordinates2);
        // 操作
        Geometry geometry = lineString1.intersection(lineString2);
        System.out.println(geometry);
    }

}
