package com.ruoyi.system.util;

import com.ruoyi.system.domain.mdmp.AirspaceLongLat;
import com.ruoyi.system.domain.mdmp.WorkBasisLongLat;

import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/4/1 15:01
 * @description：
 * @modified By：
 * @version: $
 */
public class CircularUtil {
    /**
     * 以坐标点为中心，简单粗略的创建一个指定半径的圆，半径单位米，pointCount为构建圆的坐标点数（比如24个点，点越多越圆，最少3个点），返回构成圆的坐标点数组
     **/
//    static public double[][] createSimpleCircle(double lng, double lat, double radius, int pointCount) {
//        //球面坐标不会算，转换成三角坐标简单点，经度代表值大约：0.01≈1km 0.1≈10km 1≈100km 10≈1000km
//        double km = radius / 1000;
//        double a = km < 5 ? 0.01 : km < 50 ? 0.1 : km < 500 ? 1 : 10;
//        double b = distance(lng, lat, lng + a, lat);
//        double c = distance(lng, lat, lng, lat + a);
//        double rb = radius / b * a;
//        double rc = radius / c * a;
//        double[][] arr = new double[pointCount + 1][];
//        double n = 0, step = 360.0 / pointCount, N = 360 - step / 2; //注意浮点数±0.000000001的差异
//        for (int i = 0; n < N; i++, n += step) {
//            double x = lng + rb * Math.cos(n * Math.PI / 180);
//            double y = lat + rc * Math.sin(n * Math.PI / 180);
//            arr[i] = new double[]{x, y};
//        }
//        arr[pointCount] = new double[]{arr[0][0], arr[0][1]}; //闭环
//        return arr;
//    }

    static public double[][] createSimpleCircle(double lng, double lat, double radius, int pointCount) {
        final double R = 6371000; // 地球半径 (米)
        double latRad = Math.toRadians(lat);
        double lngRad = Math.toRadians(lng);
        double radDist = radius / R; // 圆心角 (弧度)

        double[][] points = new double[pointCount + 1][2];
        for (int i = 0; i < pointCount; i++) {
            double angle = 2 * Math.PI * i / pointCount; // 方位角 (0 = 正北)
            // 计算新点纬度和经度增量
            double latRadNew = Math.asin(
                    Math.sin(latRad) * Math.cos(radDist) +
                            Math.cos(latRad) * Math.sin(radDist) * Math.cos(angle)
            );
            double dLon = Math.atan2(
                    Math.sin(angle) * Math.sin(radDist) * Math.cos(latRad),
                    Math.cos(radDist) - Math.sin(latRad) * Math.sin(latRadNew)
            );
            double lngRadNew = lngRad + dLon;

            // 转换为度并存储
            points[i][0] = Math.toDegrees(lngRadNew);
            points[i][1] = Math.toDegrees(latRadNew);
        }
        points[pointCount] = points[0].clone(); // 闭合圆
        return points;
    }
    /**
     * 计算两个坐标的距离，单位米
     **/
    static public double distance(double lng1, double lat1, double lng2, double lat2) {
        //采用Haversine formula算法，高德地图的js计算代码，比较简洁 https://www.cnblogs.com/ggz19/p/7551088.html
        double d = Math.PI / 180;
        double f = lat1 * d, h = lat2 * d;
        double i = lng2 * d - lng1 * d;
        double e = (1 - Math.cos(h - f) + (1 - Math.cos(i)) * Math.cos(f) * Math.cos(h)) / 2;
        return 2 * 6378137 * Math.asin(Math.sqrt(e));
    }

    /**
     * 检查给定点是否在指定距离范围内与任意其他四个点邻近
     * @param lng1     目标点经度
     * @param lat1     目标点纬度
     * @param others   包含四个点的二维数组，每个点为[经度, 纬度]
     * @param range    判断邻近的距离范围（米）
     * @return true 如果至少一个点在范围内，否则 false
     */
    static public boolean isNearbyAnyPoint(
            double lng1,
            double lat1,
            double[][] others,
            double range
    ) {
        // 验证参数有效性
        if (others == null || others.length < 4) {
            throw new IllegalArgumentException("必须提供4个点");
        }

        // 检查每个点是否在范围内
        for (double[] point : others) {
            // 跳过无效点
            if (point == null || point.length < 2) continue;

            // 计算当前点与目标点的距离
            double distance = distance(lng1, lat1, point[0], point[1]);

            // 发现一个点在范围内即返回true
            if (distance <= range) {
                return true;
            }
        }
        return false;
    }

    public static void main(String[] args) {
        System.out.println(distance(85.111946,44.63877,86.240336,44.605621 ));

    }

    // 辅助方法，用于创建AirspaceLongLat对象并设置共享属性
    public static AirspaceLongLat createAirspaceLongLat(long id, double longitude, double latitude, int sortNumber, int groupNumber, int height) {
        AirspaceLongLat airspaceLongLat = new AirspaceLongLat();
        airspaceLongLat.setAirspaceId(id);
        airspaceLongLat.setDoubleLongitude(Math.round(longitude * 1e6) / 1e6);
        airspaceLongLat.setDoubleLatitude(Math.round(latitude * 1e6) / 1e6);
        airspaceLongLat.setSortNumber(sortNumber);
        airspaceLongLat.setGroupNumber(groupNumber);
        airspaceLongLat.setHeight(height);
        return airspaceLongLat;
    }

    public static List<AirspaceLongLat> createSimpleCircleWKT(long id, double lng, double lat, double radius, double offset, int pointCount,int height) {
        List<AirspaceLongLat> airspaceLongLatList = new ArrayList<>();
        double[][] points = createSimpleCircle(lng, lat, radius, pointCount);
        double[][] offsetPoints = createSimpleCircle(lng, lat, radius - offset, pointCount);
        // 遍历外部圆的点并添加到列表
        for (int i = 0; i < points.length; i++) {
            airspaceLongLatList.add(createAirspaceLongLat(id, points[i][0], points[i][1], i, 0,height));
        }
        // 遍历内部圆的点并添加到列表
        for (int i = 0; i < offsetPoints.length; i++) {
            airspaceLongLatList.add(createAirspaceLongLat(id, offsetPoints[i][0], offsetPoints[i][1], i, 1,height));
        }
        return airspaceLongLatList;
    }

    public static List<AirspaceLongLat> createSimpleCircleWKT(long id, double lng, double lat, double radius, int pointCount,int height) {
        List<AirspaceLongLat> airspaceLongLatList = new ArrayList<>();
        double[][] points = createSimpleCircle(lng, lat, radius, pointCount);
        // 遍历外部圆的点并添加到列表
        for (int i = 0; i < points.length; i++) {
            airspaceLongLatList.add(createAirspaceLongLat(id, points[i][0], points[i][1], i, 0,height));
        }
        return airspaceLongLatList;
    }

}
