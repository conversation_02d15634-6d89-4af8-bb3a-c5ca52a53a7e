package com.ruoyi.system.util;

/**
 * <AUTHOR>
 * @date ：Created in 2024/10/10 10:43
 * @description：
 * @modified By：
 * @version: $
 */
public class GeoDistanceCalculator {


    // 地球半径（单位：米）
    private static final double EARTH_RADIUS = 6371e3;

    // 将角度转换为弧度
    private static double toRadians(double degrees) {
        return degrees * Math.PI / 180;
    }

    // 使用Haversine公式计算两点在地球表面的大圆距离
    private static double haversineDistance(double lat1, double lon1, double lat2, double lon2) {
        double dLat = toRadians(lat2 - lat1);
        double dLon = toRadians(lon2 - lon1);
        double a = Math.sin(dLat / 2) * Math.sin(dLat / 2) +
                Math.cos(toRadians(lat1)) * Math.cos(toRadians(lat2)) *
                        Math.sin(dLon / 2) * Math.sin(dLon / 2);
        double c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1 - a));
        return EARTH_RADIUS * c;
    }

    // 计算三维空间中的直线距离
    private static double euclideanDistance(double x1, double y1, double z1, double x2, double y2, double z2) {
        return Math.sqrt(Math.pow(x2 - x1, 2) + Math.pow(y2 - y1, 2) + Math.pow(z2 - z1, 2));
    }

    // 计算两个点之间的总距离（考虑地球表面距离和高度差）
    public static double calculateTotalDistance(double lat1, double lon1, double height1,
                                                double lat2, double lon2, double height2) {
        double surfaceDistance = haversineDistance(lat1, lon1, lat2, lon2);
        double heightDifference = Math.abs(height1 - height2);

        // 使用勾股定理计算总距离
        return Math.sqrt(Math.pow(surfaceDistance, 2) + Math.pow(heightDifference, 2));
    }


    public static void main(String[] args) {
        double lat1 = 44.274417; // 北京的纬度
        double lon1 = 85.879813; // 北京的经度
        double alt1 = 5000; // 北京的高度（假设）

        double lat2 = 44.337368; // 上海的纬度
        double lon2 = 86.20496; // 上海的经度
        double alt2 = 5000; // 上海的高度（假设）

        double distance = calculateTotalDistance(lat1, lon1, alt1, lat2, lon2, alt2);
        System.out.println("两个点之间的距离为: " + distance + " 米");
    }
}
