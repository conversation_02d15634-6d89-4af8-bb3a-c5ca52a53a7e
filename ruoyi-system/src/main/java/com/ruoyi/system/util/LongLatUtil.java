package com.ruoyi.system.util;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.mdmp.FlightPlanRouteLongLat;
import com.ruoyi.system.domain.mdmp.MapData;
import com.ruoyi.system.domain.mdmp.RouteLongLat;
import com.ruoyi.system.domain.mdmp.vo.MapDataLongLatVo;
import org.locationtech.jts.geom.Coordinate;
import org.locationtech.jts.geom.Geometry;
import org.locationtech.jts.geom.LineString;
import org.locationtech.jts.geom.Polygon;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;

/**
 * <AUTHOR>
 * @date ：Created in 2024/4/1 10:24
 * @description：经纬度工具
 * @modified By：
 * @version: $
 */
public class LongLatUtil {


    // 向量的运算
    private static Point3D subtract(Point3D a, Point3D b) {
        return new Point3D(a.x - b.x, a.y - b.y, a.z - b.z);
    }

    private static Point3D cross(Point3D a, Point3D b) {
        return new Point3D(
                a.y * b.z - a.z * b.y,
                a.z * b.x - a.x * b.z,
                a.x * b.y - a.y * b.x
        );
    }

    private static double dot(Point3D a, Point3D b) {
        return a.x * b.x + a.y * b.y + a.z * b.z;
    }


    /**
     * 求解两个线段的交点
     *
     * @param p1
     * @param p2
     * @param p3
     * @param p4
     * @return
     */
    public static Point3D findIntersection(Point3D p1, Point3D p2, Point3D p3, Point3D p4) {
        if (p1.equals(p3)) {
            return p3;
        }
        if (p2.equals(p4)) {
            return p4;
        }
        Point3D d1 = subtract(p2, p1);
        Point3D d2 = subtract(p4, p3);
        Point3D e = subtract(p3, p1);
        Point3D h = cross(d1, d2);
        Point3D g = cross(e, d2);
        double t = dot(g, h) / dot(h, h);
        // 如果t不在0和1之间，则线段不相交
        if (t < 0 || t > 1) {
            return null;
        }
        Point3D i = cross(e, d1);
        double u = dot(i, h) / dot(h, h);
        // 如果u不在0和1之间，则线段不相交
        if (u < 0 || u > 1) {
            return null;
        }
        // 如果t和u都在0和1之间，则线段可能相交
        Point3D intersection1 = new Point3D(p1.x + t * d1.x, p1.y + t * d1.y, p1.z + t * d1.z);
        Point3D intersection2 = new Point3D(p3.x + u * d2.x, p3.y + u * d2.y, p3.z + u * d2.z);
        // 检查两点是否相同（在计算精度范围内）
        if (Math.abs(intersection1.x - intersection2.x) <= 1E-6 &&
                Math.abs(intersection1.y - intersection2.y) <= 1E-6 &&
                Math.abs(intersection1.z - intersection2.z) <= 1E-6) {
            return intersection1;
        }
        return null;
    }


    public static boolean findInSurface(Point3D a, Point3D b, List<Point3D> face, double workMin, double workMax) {
        if (face.isEmpty()) {
            return false;
        }

        // 判断线段与面的投影是否相交
        Coordinate[] coordinates = new Coordinate[face.size()];
        for (int i = 0; i < face.size(); i++) {
            coordinates[i] = new Coordinate(face.get(i).x, face.get(i).y);
        }
        JTSGeometryUtil jtsGeometryUtil = new JTSGeometryUtil();
        Polygon polygon = jtsGeometryUtil.getPolygon(coordinates);

        Coordinate[] lineCoordinates = new Coordinate[2];
        lineCoordinates[0] = new Coordinate(a.x, a.y);
        lineCoordinates[1] = new Coordinate(b.x, b.y);
        LineString lineString = jtsGeometryUtil.getLineString(lineCoordinates);

        Geometry intersection = polygon.intersection(lineString);
        if (intersection.isEmpty()) {
            return false; // 投影无交点
        }

        // 判断高度范围是否有重叠
        double routeMin = Math.min(a.z, b.z);
        double routeMax = Math.max(a.z, b.z);
        if (routeMax >= workMin && routeMin <= workMax) {
            return true; // 高度范围有重叠
        }

        return false;
    }


    /**
     * 求解面和面是否有交点
     *
     * @return
     */
    public static boolean findSurfaceAndSurfacePoint(List<Point3D> listA, List<Point3D> listB,
                                                     double workMinA, double workMaxA, double workMinB, double workMaxB) {
        if (listA.isEmpty() || listB.isEmpty()) {
            return false;
        }
        if (workMinA >= workMaxB || workMaxA <= workMinA) {
            //在判断航线的最低点>=作业区的最低高度，且航线最高点<=作业区的最高高度，如果同时满足则证明航线与作业区有交集
            return false;
        }
        Coordinate[] coordinates = new Coordinate[listA.size()];
        for (int i = 0; i < listA.size(); i++) {
            coordinates[i] = new Coordinate(listA.get(i).x, listA.get(i).y);
        }
        JTSGeometryUtil jtsGeometryUtil = new JTSGeometryUtil();
        Polygon polygon = jtsGeometryUtil.getPolygon(coordinates);
        Coordinate[] coordinatesB = new Coordinate[listB.size()];
        for (int i = 0; i < listB.size(); i++) {
            coordinatesB[i] = new Coordinate(listB.get(i).x, listB.get(i).y);
        }
        Polygon polygonB = jtsGeometryUtil.getPolygon(coordinatesB);
        // 操作
        Geometry geometry = polygon.intersection(polygonB);
        Coordinate[] coordinate = geometry.getCoordinates();
        if (coordinate.length > 0) {
            //在判断航线的最低点>=作业区的最低高度，且航线最高点<=作业区的最高高度，如果同时满足则证明航线与作业区有交集
            return true;
        } else {
            return false;
        }
    }


    /**
     * 度分秒转换成经纬度
     *
     * @param dms
     * @return
     */
    public static double convertDMSToDD(String dms) {
        try {
            String[] parts = dms.split("[°']"); // 使用正则表达式一步分割
            double degrees = Double.parseDouble(parts[0]);
            double minutes = parts.length > 1 ? Double.parseDouble(parts[1]) : 0;
            double seconds = parts.length > 2 ? Double.parseDouble(parts[2]) : 0;
            // 直接进行计算，无需BigDecimal除非需要精确控制舍入模式
            double decimalDegrees = degrees + (minutes / 60) + (seconds / 3600);
            return Math.round(decimalDegrees * 1e6) / 1e6; // 提供六位小数的精度控制
        } catch (NumberFormatException | ArrayIndexOutOfBoundsException e) {
            // 异常处理，可以根据实际需求调整处理逻辑
            // 这里简单返回0或错误标志值，或者抛出一个自定义异常
            throw new ServiceException("无效的经纬度格式：" + dms);
        }
    }


}
