package com.ruoyi.system.util;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 * @date ：Created in 2025/6/3 14:01
 * @description：
 * @modified By：
 * @version: $
 */
public class ThreadPoolManager {

    // 固定数量的线程组 (建议2*CPU核心数)
    private static final int POOL_SIZE = Runtime.getRuntime().availableProcessors() * 2;
    // 每个线程组关联一个单线程执行器
    private static final ExecutorService[] executors = new ExecutorService[POOL_SIZE];

    static {
        for (int i = 0; i < POOL_SIZE; i++) {
            // 每个执行器使用单线程+无界队列
            executors[i] = Executors.newSingleThreadExecutor();
        }
    }

    // 根据Key路由提交任务
    public static void submitByKey(String key, Runnable task) {
        int index = Math.abs(key.hashCode()) % POOL_SIZE;
        executors[index].submit(task);
    }

    // 关闭线程池 (应用退出时调用)
    public static void shutdown() {
        for (ExecutorService executor : executors) {
            executor.shutdown();
        }
    }
}
