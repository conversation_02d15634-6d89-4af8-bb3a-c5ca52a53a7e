package com.ruoyi.system.util;

import com.ruoyi.system.domain.mdmp.GeoPoint;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.TreeMap;

/**
 * <AUTHOR>
 * @date ：Created in 2025/3/31 9:54
 * @description：
 * @modified By：
 * @version: $
 */
public class InterpolationService {
    // 双线性插值实现
    private double bilinearInterpolation(double x, double y,
                                         double x0, double x1,
                                         double y0, double y1,
                                         double q00, double q01,
                                         double q10, double q11) {
        return ((q00 * (x1 - x) * (y1 - y) +
                q10 * (x - x0) * (y1 - y) +
                q01 * (x1 - x) * (y - y0) +
                q11 * (x - x0) * (y - y0)) /
                ((x1 - x0) * (y1 - y0)));
    }

    // 主处理方法
    public List<Double> generateGrid(List<GeoPoint> rawData,
                                     double startLon, double endLon,
                                     double startLat, double endLat,
                                     double step) {
        // 构建数据矩阵
        Map<Double, Map<Double, Double>> dataMap = new TreeMap<>();
        for (GeoPoint point : rawData) {
            dataMap.computeIfAbsent(point.getLatitude(), k -> new TreeMap<>())
                    .put(point.getLongitude(), point.getValue());
        }

        // 生成目标网格序列
        List<Double> lonList = generateSequence(startLon, endLon, step);
        List<Double> latList = generateSequence(startLat, endLat, step);

        // 存储结果
        List<Double> result = new ArrayList<>(lonList.size() * latList.size());

        // 逐点插值
        for (double lat : latList) {
            for (double lon : lonList) {
                // 获取四个邻近点
                double[] neighbors = findNeighbors(lon, lat, dataMap);

                // 执行插值计算
                double value = bilinearInterpolation(
                        lon, lat,
                        neighbors[0], neighbors[1], // x0, x1
                        neighbors[2], neighbors[3], // y0, y1
                        neighbors[4], neighbors[5], // q00, q01
                        neighbors[6], neighbors[7]  // q10, q11
                );

                result.add(value);
            }
        }
        return result;
    }

    // 生成步长序列
    private List<Double> generateSequence(double start, double end, double step) {
        int size = (int) ((end - start) / step) + 1;
        List<Double> list = new ArrayList<>(size);
        for (double d = start; d <= end; d += step) {
            list.add(Double.parseDouble(String.format("%.2f", d)));
        }
        return list;
    }

    // 查找最近的四个点
    private double[] findNeighbors(double lon, double lat,
                                   Map<Double, Map<Double, Double>> dataMap) {
        // 实现找邻近点逻辑（需处理边界情况）
        // 返回数组格式：[x0, x1, y0, y1, q00, q01, q10, q11]
        // 具体实现需要处理数据缺失的情况
        return null;
    }
}
