package com.ruoyi.system.util;

import lombok.Data;

import java.math.BigDecimal;
import java.util.HashMap;
import java.util.Map;

/**
 *
 */
@Data
public class LineSegment3D {
    private BigDecimal longitudeA;
    private BigDecimal latitudeA;
    private Integer heightA;
    private BigDecimal longitudeB;
    private BigDecimal latitudeB;
    private Integer heightB;

    public Map<String, Object> getMBR() {
        BigDecimal minLongitude = longitudeA.compareTo(longitudeB) < 0 ? longitudeA : longitudeB;
        BigDecimal maxLongitude = longitudeA.compareTo(longitudeB) > 0 ? longitudeA : longitudeB;
        BigDecimal minLatitude = latitudeA.compareTo(latitudeB) < 0 ? latitudeA : latitudeB;
        BigDecimal maxLatitude = latitudeA.compareTo(latitudeB) > 0 ? latitudeA : latitudeB;
        Integer minHeight = Math.min(heightA, heightB);
        Integer maxHeight = Math.max(heightA, heightB);

        Map<String, Object> mbr = new HashMap<>();
        mbr.put("minLongitude", minLongitude);
        mbr.put("maxLongitude", maxLongitude);
        mbr.put("minLatitude", minLatitude);
        mbr.put("maxLatitude", maxLatitude);
        mbr.put("minHeight", minHeight);
        mbr.put("maxHeight", maxHeight);
        return mbr;
    }
}
