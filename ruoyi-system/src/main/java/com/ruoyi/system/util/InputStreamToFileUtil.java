package com.ruoyi.system.util;

import java.io.*;

/**
 * <AUTHOR>
 * @date ：Created in 2024/11/26 10:11
 * @description：
 * @modified By：
 * @version: $
 */
public class InputStreamToFileUtil {

    public static void saveInputStreamToFile(InputStream inputStream, String filePath) {
        FileOutputStream outputStream = null;

        try {
            File outputFile = new File(filePath);
            outputStream = new FileOutputStream(outputFile);

            byte[] buffer = new byte[4096]; // 设置缓冲区大小
            int bytesRead;

            // 从InputStream读取数据到缓冲区，并写入到文件中
            while ((bytesRead = inputStream.read(buffer)) != -1) {
                outputStream.write(buffer, 0, bytesRead);
            }

            System.out.println("文件已成功保存到: " + filePath);
        } catch (IOException e) {
            System.err.println("保存文件时出错: " + e.getMessage());
        } finally {
            // 确保流被关闭，避免内存泄漏
            try {
                if (outputStream != null) {
                    outputStream.close();
                }
                inputStream.close();
            } catch (IOException e) {
                System.err.println("关闭流时出错: " + e.getMessage());
            }
        }
    }

}
