package com.ruoyi.system.util;

import java.time.*;
import java.util.*;
import java.util.stream.*;
import java.util.NoSuchElementException;

/**
 * <AUTHOR>
 * @date ：Created in 2025/3/17 15:33
 * @description：
 * @modified By：
 * @version: $
 */
public class NearestAnyTimestamp {

    /**
     * 获取距离指定小时列表最近的时间戳（秒级）
     * @return 距离当前时间最近的秒级时间戳
     */
    public static long getNearestTimestamp() {
        List<Integer> hours = Arrays.asList(2, 5, 8, 11,14, 17, 20, 23);
        return getNearestTimestamp(hours, LocalDateTime.now());
    }

    /**
     * 获取距离指定小时列表最近的时间戳（秒级）
     * @param targetHours 目标小时集合(0-23)
     * @param baseTime 基准时间
     * @return 距离基准时间最近的秒级时间戳
     */
    public static long getNearestTimestamp(List<Integer> targetHours, LocalDateTime baseTime) {
        // 参数校验
        validateHours(targetHours);
        Objects.requireNonNull(baseTime, "基准时间不能为null");

        // 生成所有候选时间
        List<LocalDateTime> candidates = targetHours.stream()
                .flatMap(hour -> Stream.of(
                        getPreviousOccurrence(baseTime, hour),
                        getNextOccurrence(baseTime, hour)
                ))
                .collect(Collectors.toList());

        // 找出绝对最近的时间点
        LocalDateTime nearest = candidates.stream()
                .min(Comparator.comparingLong(
                        time -> Math.abs(Duration.between(baseTime, time).toMillis())
                ))
                .orElseThrow(() -> new NoSuchElementException("无法找到最近时间点"));

        // 转换为秒级时间戳
        return nearest.atZone(ZoneId.systemDefault()).toEpochSecond();
    }

    // 校验小时参数合法性
    private static void validateHours(List<Integer> hours) {
        if (hours == null || hours.isEmpty()) {
            throw new IllegalArgumentException("目标小时列表不能为空");
        }

        for (Integer hour : hours) {
            if (hour == null || hour < 0 || hour > 23) {
                throw new IllegalArgumentException("非法的小时值: " + hour + "，必须为0-23的整数");
            }
        }
    }

    // 获取前一个出现时间
    private static LocalDateTime getPreviousOccurrence(LocalDateTime base, int hour) {
        LocalDateTime time = base.withHour(hour).withMinute(0).withSecond(0).withNano(0);
        return time.isAfter(base) ? time.minusDays(1) : time;
    }

    // 获取后一个出现时间
    private static LocalDateTime getNextOccurrence(LocalDateTime base, int hour) {
        LocalDateTime time = base.withHour(hour).withMinute(0).withSecond(0).withNano(0);
        return time.isBefore(base) ? time.plusDays(1) : time;
    }

    // 使用示例
    public static void main(String[] args) {

        // 获取当前时间的最近时间戳
        long timestamp1 = getNearestTimestamp();
        System.out.println("当前时间最近点秒级时间戳: " + timestamp1);


    }
}
