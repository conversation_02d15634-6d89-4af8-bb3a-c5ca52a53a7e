package com.ruoyi.system.util;

/**
 * <AUTHOR>
 * @date ：Created in 2025/3/25 16:01
 * @description：
 * @modified By：
 * @version: $
 */
public class GeoInterpolator {
    // 双线性插值方法
    public static double[][] bilinearInterpolation(double[] origLons, double[] origLats,
                                                   double[][] origData, double newStep) {
        // 计算新网格的经纬度范围
        double minLon = origLons[0];
        double maxLon = origLons[origLons.length - 1];
        double minLat = origLats[0];
        double maxLat = origLats[origLats.length - 1];

        // 生成新经纬度数组
        int newLonCount = (int) Math.round((maxLon - minLon) / newStep) + 1;
        int newLatCount = (int) Math.round((maxLat - minLat) / newStep) + 1;
        double[][] newData = new double[newLatCount][newLonCount];

        // 遍历新网格每个点进行插值
        for (int latIdx = 0; latIdx < newLatCount; latIdx++) {
            double targetLat = minLat + latIdx * newStep;
            for (int lonIdx = 0; lonIdx < newLonCount; lonIdx++) {
                double targetLon = minLon + lonIdx * newStep;

                // 查找最近的四个原始网格点
                int i = findLowerIndex(origLons, targetLon);
                int j = findLowerIndex(origLats, targetLat);

                // 边界检查
                if (i < 0 || j < 0 || i >= origLons.length - 1 || j >= origLats.length - 1) {
                    newData[latIdx][lonIdx] = Double.NaN;
                    continue;
                }

                // 获取四个相邻点的值
                double q11 = origData[j][i];
                double q12 = origData[j + 1][i];
                double q21 = origData[j][i + 1];
                double q22 = origData[j + 1][i + 1];

                // 计算插值权重
                double x = (targetLon - origLons[i]) / 0.125;
                double y = (targetLat - origLats[j]) / 0.125;

                // 执行双线性插值计算
                newData[latIdx][lonIdx] = interpolate(q11, q12, q21, q22, x, y);
            }
        }
        return newData;
    }

    // 辅助方法：查找小于等于目标值的最大索引
    private static int findLowerIndex(double[] array, double target) {
        int index = (int) ((target - array[0]) / 0.125);
        if (index < 0) return -1;
        if (index >= array.length - 1) return array.length - 2;
        return index;
    }

    // 双线性插值核心计算
    private static double interpolate(double q11, double q12,
                                      double q21, double q22,
                                      double x, double y) {
        // 经度方向插值
        double r1 = q11 * (1 - x) + q21 * x;
        double r2 = q12 * (1 - x) + q22 * x;

        // 纬度方向插值
        return r1 * (1 - y) + r2 * y;
    }

    // 示例用法
    public static void main(String[] args) {
        // 示例原始数据（0.125步长）
        double[] origLons = generateRange(85.250, 0.125, 15);  // [0.0, 0.125, 0.25]
        double[] origLats = generateRange(44.500, 0.125, 2);

        double[][] origData = {
                {8204.00, 8026.00
                        , 8130.00
                        , 9008.00
                        , 8514.00
                        , 10274.00
                        , 13390.00
                        , 14022.00
                        , 16028.00
                        , 17030.00
                        , 16734.00
                        , 17386.00
                        , 18536.00
                        , 19664.00
                        , 20958.00},
                {14958.00
                        , 11666.00
                        , 10028.00
                        , 9810.00
                        , 6434.00
                        , 4334.00
                        , 3836.00
                        , 6340.00
                        , 9356.00
                        , 9944.00
                        , 10326.00
                        , 11108.00
                        , 12322.00
                        , 13990.00
                        , 15464.00}
        };

        // 执行插值
        double[][] newData = bilinearInterpolation(origLons, origLats, origData, 0.01);

        // 输出结果示例
        System.out.println("插值结果矩阵:" + newData.length);
        for (double[] row : newData) {
            for (double val : row) {
                System.out.printf("%.5f ", val);
            }
            System.out.println();
        }
    }

    // 辅助方法：生成等距数组
    private static double[] generateRange(double start, double step, int count) {
        double[] arr = new double[count];
        for (int i = 0; i < count; i++) {
            arr[i] = start + i * step;
        }
        return arr;
    }
}
