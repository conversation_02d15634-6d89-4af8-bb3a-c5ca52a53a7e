package com.ruoyi.system.util;

import com.ruoyi.system.domain.mdmp.FlightPlanWorkLongLat;
import com.ruoyi.system.domain.mdmp.WorkBasisLongLat;
import com.ruoyi.system.domain.mdmp.WorkInBasisLongLat;
import com.ruoyi.system.domain.mdmp.WorkinLongLat;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/4/8 9:44
 * @description：
 * @modified By：
 * @version: $
 */
public class WorkBasisUtil {

    public static WorkBasisLongLat createWorkBasisLongLat(Long id, Double longitude, Double latitude, Integer sortNumber, Integer groupNumber) {
        WorkBasisLongLat workBasisLongLat = new WorkBasisLongLat();
        workBasisLongLat.setWorkBasisId(id);
        workBasisLongLat.setDoubleLongitude(Math.round(longitude * 1e6) / 1e6);
        workBasisLongLat.setDoubleLatitude(Math.round(latitude * 1e6) / 1e6);
        workBasisLongLat.setHeight(new BigDecimal(0));
        workBasisLongLat.setSortNumber(sortNumber);
        workBasisLongLat.setGroupNumber(groupNumber);
        return workBasisLongLat;
    }

    public static FlightPlanWorkLongLat createFlightPlanWorkLongLat(Long id, Double longitude, Double latitude, Integer sortNumber, Integer groupNumber) {
        FlightPlanWorkLongLat flightPlanWorkLongLat = new FlightPlanWorkLongLat();
        flightPlanWorkLongLat.setFlightPlanWorkId(id);
        flightPlanWorkLongLat.setDoubleLongitude(BigDecimal.valueOf(Math.round(longitude * 1e6) / 1e6));
        flightPlanWorkLongLat.setDoubleLatitude(BigDecimal.valueOf(Math.round(latitude * 1e6) / 1e6));
        flightPlanWorkLongLat.setHeight(new BigDecimal(0));
        flightPlanWorkLongLat.setSortNumber(sortNumber);
        flightPlanWorkLongLat.setGroupNumber(groupNumber);
        return flightPlanWorkLongLat;
    }


    public static WorkinLongLat createFlightPlanWorkInLongLat(Long id, Double longitude, Double latitude, Integer sortNumber, Integer groupNumber) {
        WorkinLongLat workinLongLat = new WorkinLongLat();
        workinLongLat.setWorkInId(id);
        workinLongLat.setDoubleLongitude(BigDecimal.valueOf(Math.round(longitude * 1e6) / 1e6));
        workinLongLat.setDoubleLatitude(BigDecimal.valueOf(Math.round(latitude * 1e6) / 1e6));
        workinLongLat.setHeight(new BigDecimal(0));
        workinLongLat.setSortNumber(sortNumber);
        return workinLongLat;
    }

    public static List<WorkBasisLongLat> getWorkBasisLongLat(Long id, Double lng, Double lat, Double radius, Double offset, Integer pointCount) {
        List<WorkBasisLongLat> workBasisLongLatList = new ArrayList<>();
        double[][] points = CircularUtil.createSimpleCircle(lng, lat, radius, pointCount);
        double[][] offsetPoints = CircularUtil.createSimpleCircle(lng, lat, radius - offset, pointCount);
        // 遍历外部圆的点并添加到列表
        for (int i = 0; i < points.length; i++) {
            workBasisLongLatList.add(createWorkBasisLongLat(id, points[i][0], points[i][1], i, 0));
        }
        // 遍历内部圆的点并添加到列表
        for (int i = 0; i < offsetPoints.length; i++) {
            workBasisLongLatList.add(createWorkBasisLongLat(id, offsetPoints[i][0], offsetPoints[i][1], i, 1));
        }

        return workBasisLongLatList;
    }

    public static List<WorkinLongLat> getWorkInLongLat(Long id, Double lng, Double lat, Double radius, Double offset, Integer pointCount) {
        List<WorkinLongLat> workinLongLatList = new ArrayList<>();
        double[][] points = CircularUtil.createSimpleCircle(lng, lat, radius, pointCount);
        // 遍历外部圆的点并添加到列表
        for (int i = 0; i < points.length; i++) {
            workinLongLatList.add(createFlightPlanWorkInLongLat(id, points[i][0], points[i][1], i, 0));
        }
        return workinLongLatList;
    }


    public static List<FlightPlanWorkLongLat> getFlightPlanWorkLongLat(Long id, Double lng, Double lat, Double radius, Double offset, Integer pointCount) {
        List<FlightPlanWorkLongLat> flightPlanWorkLongLatList = new ArrayList<>();
        double[][] points = CircularUtil.createSimpleCircle(lng, lat, radius, pointCount);
        double[][] offsetPoints = CircularUtil.createSimpleCircle(lng, lat, radius - offset, pointCount);
        // 遍历外部圆的点并添加到列表
        for (int i = 0; i < points.length; i++) {
            flightPlanWorkLongLatList.add(createFlightPlanWorkLongLat(id, points[i][0], points[i][1], i, 0));
        }
        if (offset != 0) {
            // 遍历内部圆的点并添加到列表
            for (int i = 0; i < offsetPoints.length; i++) {
                flightPlanWorkLongLatList.add(createFlightPlanWorkLongLat(id, offsetPoints[i][0], offsetPoints[i][1], i, 1));
            }
        }


        return flightPlanWorkLongLatList;
    }

    public static WorkInBasisLongLat createWorkBasisLongLat(Long id, Double longitude, Double latitude, Integer sortNumber) {
        WorkInBasisLongLat workInBasisLongLat = new WorkInBasisLongLat();
        workInBasisLongLat.setWorkInBasisId(id);
        workInBasisLongLat.setDoubleLongitude(Math.round(longitude * 1e6) / 1e6);
        workInBasisLongLat.setDoubleLatitude(Math.round(latitude * 1e6) / 1e6);
        workInBasisLongLat.setHeight(new BigDecimal(0));
        workInBasisLongLat.setSortNumber(sortNumber);
        return workInBasisLongLat;
    }

    public static List<WorkInBasisLongLat> getWorkInBasisLongLat(Long id, Double lng, Double lat, Double radius, Integer pointCount) {
        List<WorkInBasisLongLat> workInBasisLongLatList = new ArrayList<>();
        double[][] points = CircularUtil.createSimpleCircle(lng, lat, radius, pointCount);
        // 遍历外部圆的点并添加到列表
        for (int i = 0; i < points.length; i++) {
            workInBasisLongLatList.add(createWorkBasisLongLat(id, points[i][0], points[i][1], i));
        }
        return workInBasisLongLatList;
    }
}
