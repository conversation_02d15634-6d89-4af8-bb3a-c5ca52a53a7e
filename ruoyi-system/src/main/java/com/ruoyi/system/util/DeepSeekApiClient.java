package com.ruoyi.system.util;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.system.domain.vo.DeepSeekRequest;
import com.ruoyi.system.domain.vo.DeepSeekResponse;
import com.ruoyi.system.domain.vo.MessageAi;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.stereotype.Component;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.zip.GZIPInputStream;


@Component
public class DeepSeekApiClient {

    private static final String API_URL = "https://api.deepseek.com/v1/chat/completions";
    private static final String API_KEY = "sk-bb23e4b9f0984a37b6455fe892201a30"; // 替换为你的API Key


    public static String soloChat(String userInput) throws Exception {
        if (userInput == null || userInput.isEmpty()) {
            throw new Exception("用户输入不能为空");
        }
        List<MessageAi> messages = new ArrayList<>();

        MessageAi msg1 = new MessageAi();
        msg1.setRole("system");
        msg1.setContent("你是一个专业且精准的AI助手，回答需简洁、准确且逻辑清晰、中文回答");
        messages.add(msg1);
        // 添加用户消息
        MessageAi msg = new MessageAi();
        msg.setRole("user");
        msg.setContent(userInput);
        messages.add(msg);
        messages.add(msg);

        // 获取AI回复
        String response = DeepSeekApiClient.chatCompletion(messages);
        System.out.println("AI回复：" + response);
        // 返回 AI 的回答
        return response;
    }


    public static String chatCompletion(List<MessageAi> messages) throws Exception {
        CloseableHttpClient httpClient = HttpClients.createDefault();
        HttpPost httpPost = new HttpPost(API_URL);

        // 设置请求头
        httpPost.setHeader("Content-Type", "application/json");
        httpPost.setHeader("Authorization", "Bearer " + API_KEY);
        httpPost.setHeader("Accept-Encoding", "gzip"); // 声明支持压缩

        // 构造请求体
        DeepSeekRequest request = new DeepSeekRequest();
        request.setMessages(messages);
        httpPost.setEntity(new StringEntity(JSON.toJSONString(request), StandardCharsets.UTF_8));

        // 发送请求
        try (CloseableHttpResponse response = httpClient.execute(httpPost)) {
            // 检查状态码
            if (response.getStatusLine().getStatusCode() != 200) {
                String errorBody = EntityUtils.toString(response.getEntity(), StandardCharsets.UTF_8);
                throw new RuntimeException("API 返回错误: " + errorBody);
            }

            // 处理可能的 Gzip 压缩
            InputStream contentStream = response.getEntity().getContent();
            if (response.getFirstHeader("Content-Encoding") != null
                    && response.getFirstHeader("Content-Encoding").getValue().contains("gzip")) {
                contentStream = new GZIPInputStream(contentStream);
            }

            // 读取响应
            String rawResponse = new BufferedReader(new InputStreamReader(contentStream, StandardCharsets.UTF_8))
                    .lines()
                    .collect(Collectors.joining("\n"));

            System.out.println("原始响应：" + rawResponse); // 调试用

            // 解析 JSON
            DeepSeekResponse apiResponse = JSONObject.parseObject(rawResponse, DeepSeekResponse.class);
            return apiResponse.getChoices().get(0).getMessage().getContent();
        }
    }


}
