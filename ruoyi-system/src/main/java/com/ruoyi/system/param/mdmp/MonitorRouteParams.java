package com.ruoyi.system.param.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "监控航线请求参数")
public class MonitorRouteParams {

    /**
     * 航班计划id
     */
    @ApiModelProperty(value = "航班计划id", example = "1", required = true)
    private Long flightPlanId;

    @ApiModelProperty(value = "飞机注册号", example = "BD-100", required = true)
    private String aircraftReg;
}
