package com.ruoyi.system.param.mdmp;


import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(value = "查询次日飞行计划列表请求参数")
public class NexDayFlightPlanListParam {

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    private String companyName;

    /**
     * 计划名称
     */
    @Excel(name = "计划名称")
    private String name;

    /**
     * 公司代码
     */
    @Excel(name = "公司代码")
    private String companyCode;

    /**
     * 任务类型;枚举
     */
    @Excel(name = "任务类型;枚举")
    private String taskType;

    /**
     * 航班日期
     */
    @Excel(name = "航班日期", readConverterExp = "航班日期")
    private String flightDate;


    /**
     * 联系人姓名
     */
    @Excel(name = "联系人姓名")
    private String contactName;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话")
    private String contactPhone;

    /**
     * 编号
     */
    @Excel(name = "编号")
    private String serialNo;

    /**
     * 状态;0草稿/1 已提交/ 2 已通过/ 3审核中/ 4拒绝
     */
    @Excel(name = "状态;0草稿/1 已提交/ 2 已通过/ 3审核中/ 4拒绝")
    @ApiModelProperty(value = "状态( 0,草稿;1, 已提交;2, 已通过;3, 审核中;4, 拒绝)", required = true, allowableValues = "0,1,2,3,4")
    private Integer status;

    /**
     * 应答机编码（次日计划）
     */
    @Excel(name = "应答机编码", readConverterExp = "次=日计划")
    private String answeringMachineCode;

    @Excel(name = "是否临时计划;0 非临时计划 1 临时计划")
    @ApiModelProperty(value = "是否临时计划( 0,非临时计划;1, 临时计划)", required = true, allowableValues = "0,1")
    private Integer temporary;


    /**
     * 无线电频率(次日计划)
     */
    @Excel(name = "无线电频率(次日计划)")
    private String radioFrequency;

    /**
     * 是否作业;1作业 0 航线（ 不作业）
     */
    @Excel(name = "是否作业;1作业 0 航线", readConverterExp = "不=作业")
    private Integer isWork;

    private String deptCode;

    private List<String> deptCodeList;
}
