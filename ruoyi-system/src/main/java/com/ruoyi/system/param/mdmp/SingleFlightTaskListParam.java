package com.ruoyi.system.param.mdmp;


import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

@Data
@ApiModel(value = "查询单一飞行任务列表请求参数")
public class SingleFlightTaskListParam {

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    private String companyName;

    /**
     * 计划名称
     */
    @Excel(name = "计划名称")
    private String planName;

    /**
     * 公司代码
     */
    @Excel(name = "公司代码")
    private String companyCode;


    /**
     * 航班日期
     */
    @Excel(name = "航班日期", readConverterExp = "航班日期")
    private String planDate;



    /**
     * 联系人姓名
     */
    @Excel(name = "联系人姓名")
    private String contactName;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话")
    private String contactPhone;



    /**
     * 状态;0草稿/1 已提交/ 2 已通过/ 3审核中/ 4拒绝
     */
    @Excel(name = "状态;0草稿/1 已提交/ 2 已通过/ 3审核中/ 4拒绝")
    @ApiModelProperty(value = "状态( 0,草稿;1, 已提交;2, 已通过;3, 审核中;4, 拒绝)", required = true, allowableValues = "0,1,2,3,4")
    private Integer planStatus;



}
