package com.ruoyi.system.param.mdmp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2024/12/30 14:37
 * @description：
 * @modified By：
 * @version: $
 */

@Data
public class MeteInfoParam {

    @ApiModelProperty(value = "格子index", example = "0", dataType = "Long")
    private Long index;

    @ApiModelProperty(value = "经度", example = "0", dataType = "Double")
    private Double longitude;

    @ApiModelProperty(value = "纬度", example = "0", dataType = "Double")
    private Double latitude;

    @ApiModelProperty(value = "海拔高度", example = "0", dataType = "Double")
    private Double altitude;


    // 设置经度并直接保留两位小数（不四舍五入）
    public void setLongitude(Double longitude) {
        if (longitude != null) {
            this.longitude = truncateToTwoDecimals(longitude); // 直接保留两位小数
        } else {
            this.longitude = null;
        }
    }

    // 设置纬度并直接保留两位小数（不四舍五入）
    public void setLatitude(Double latitude) {
        if (latitude != null) {
            this.latitude = truncateToTwoDecimals(latitude); // 直接保留两位小数
        } else {
            this.latitude = null;
        }
    }

    // 工具方法：直接截断到两位小数
    private Double truncateToTwoDecimals(Double value) {
        if (value == null) {
            return null;
        }
        return (double) ((int) (value * 100)) / 100;
    }

}
