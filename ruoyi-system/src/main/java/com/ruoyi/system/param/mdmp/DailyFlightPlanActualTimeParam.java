package com.ruoyi.system.param.mdmp;


import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.time.LocalTime;

@Data
@ApiModel(value = "修改当日飞行计划 实际起飞到达 请求参数")
public class DailyFlightPlanActualTimeParam {


    /**
     * id
     */
    @ApiModelProperty(value = "id", example = "id", dataType = "Long")
    @NotNull(message = "id不能为空")
    private Long id;

    /**
     * 实际起飞时间
     */
    @Excel(name = "实际起飞时间")
    @ApiModelProperty(value = "实际起飞时间", example = "13:00", dataType = "String")
    @NotNull(message = "实际起飞时间不能为空")
    private LocalTime actualDepartureTime;

    /**
     * 实际到达时间
     */
    @Excel(name = "实际到达时间")
    @ApiModelProperty(value = "实际到达时间", example = "13:00", dataType = "String")
    @NotNull(message = "实际到达时间不能为空")
    private LocalTime actualArrivalTime;

}
