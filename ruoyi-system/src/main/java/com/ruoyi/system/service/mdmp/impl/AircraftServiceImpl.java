package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.mdmp.Aircraft;
import com.ruoyi.system.domain.mdmp.AircraftMp;
import com.ruoyi.system.mapper.mdmp.AircraftMapper;
import com.ruoyi.system.mapper.mdmp.AircraftMpMapper;
import com.ruoyi.system.service.mdmp.IAircraftService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
@Service
public class AircraftServiceImpl implements IAircraftService {

    @Resource
    private AircraftMapper aircraftMapper;

    @Resource
    private AircraftMpMapper aircraftMpMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public Aircraft selectAircraftById(Long id) {
        AircraftMp aircraftMp = new AircraftMp();
        aircraftMp.setAircraftId(id);
        Aircraft aircraft = aircraftMapper.selectAircraftById(id);
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        //判断deptCodeList是否包含aircraft.getDeptCode()
        if (deptCodeList.contains(aircraft.getDeptCode())) {
            aircraft.setAircraftMpList(aircraftMpMapper.selectAircraftMpList(aircraftMp));
            return aircraft;
        } else {
            throw new ServiceException("无权限访问该数据");
        }
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param aircraft 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<Aircraft> selectAircraftList(Aircraft aircraft) {
        return aircraftMapper.selectAircraftList(aircraft);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param aircraft 【请填写功能名称】
     * @return 结果
     */
    @Transactional
    public int insertAircraft(Aircraft aircraft) {
        if (aircraft == null) {
            throw new IllegalArgumentException("Aircraft must not be null");
        }

        int mainAffectedRows = aircraftMapper.insertAircraft(aircraft);
        if (mainAffectedRows == 0) {
            return 0; // 主表插入失败直接返回
        }

        List<AircraftMp> mpList = aircraft.getAircraftMpList();
        if (mpList == null || mpList.isEmpty()) {
            return mainAffectedRows; // 无子表数据直接返回主表结果
        }

        // 批量设置关联ID
        Long aircraftId = aircraft.getId();
        mpList.forEach(mp -> mp.setAircraftId(aircraftId));

        // 批量插入子表数据
        int subAffectedRows = aircraftMpMapper.insertAircraftMpList(mpList);
        return mainAffectedRows + subAffectedRows; // 返回总影响行数
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param aircraft 【请填写功能名称】
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAircraft(Aircraft aircraft) {
        if (aircraft == null || aircraft.getId() == null) {
            throw new IllegalArgumentException("Invalid aircraft data");
        }
        selectAircraftById(aircraft.getId());
        //删除机型
        aircraftMapper.deleteAircraftById(aircraft.getId());
        //删除关联阈值数据
        aircraftMpMapper.deleteByAircraftId(aircraft.getId());
        return insertAircraft(aircraft);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteAircraftByIds(Long[] ids) {
        return aircraftMapper.deleteAircraftByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteAircraftById(Long id) {
        selectAircraftById(id);
        return aircraftMapper.deleteAircraftById(id);
    }

    @Override
    public boolean checkAircraftUnique(Aircraft aircraft) {
        Long aircraftId = StringUtils.isNull(aircraft.getId()) ? -1L : aircraft.getId();
        Aircraft info = aircraftMapper.checkAircraftUnique(aircraft);
        if (StringUtils.isNotNull(info) && info.getId().longValue() != aircraftId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    @Override
    public boolean checkAircraftOverlap(Aircraft aircraft) {
        List<Aircraft> overlap = aircraftMapper.checkAircraftOverlap(aircraft);
        return !CollectionUtils.isEmpty(overlap);
    }

    @Override
    public boolean checkAircraftOverlapWhenUpdate(Aircraft aircraft) {
        List<Aircraft> overlap = aircraftMapper.checkAircraftOverlap(aircraft);
        if (CollectionUtils.isEmpty(overlap)) {
            return false;
        }
        if (overlap.size() == 1) {
//            return overlap.get(0).getId() != aircraft.getId();
            return false;
        }
        return true;
    }
}
