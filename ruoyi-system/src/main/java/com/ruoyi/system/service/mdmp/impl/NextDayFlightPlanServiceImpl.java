package com.ruoyi.system.service.mdmp.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import com.ruoyi.common.core.domain.entity.SysMenu;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.PlanNoUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.type.WorkType;
import com.ruoyi.system.mapper.mdmp.*;
import com.ruoyi.system.param.mdmp.NexDayFlightPlanListParam;
import com.ruoyi.system.service.mdmp.ILongTermFlightPlanService;
import com.ruoyi.system.service.mdmp.INextDayFlightPlanService;
import com.ruoyi.system.util.LongLatUtil;
import com.ruoyi.system.util.Point3D;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 次日计划飞行Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Service
public class NextDayFlightPlanServiceImpl implements INextDayFlightPlanService {
    @Resource
    private NextDayFlightPlanMapper nextDayFlightPlanMapper;


    @Resource
    private ILongTermFlightPlanService longTermFlightPlanService;

    @Resource
    private FlightPlanAircraftModelMapper flightPlanAircraftModelMapper;

    @Resource
    private FlightPlanAirportMapper flightPlanAirportMapper;

    @Resource
    private FlightPlanRouteMapper flightPlanRouteMapper;

    @Resource
    private FlightPlanFileMapper flightPlanFileMapper;


    @Resource
    private FlightPlanWorkMapper flightPlanWorkMapper;

    @Resource
    private FlightPlanWorkLongLatMapper flightPlanWorkLongLatMapper;
    @Resource
    private FlightPlanWorkInMapper flightPlanWorkInMapper;

    @Resource
    private WorkinLongLatMapper workinLongLatMapper;


    /**
     * 查询次日计划飞行
     *
     * @param id 次日计划飞行主键
     * @return 次日计划飞行
     */
    @Override
    public NextDayFlightPlan selectNextDayFlightPlanById(Long id) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        //判断deptCodeList是否包含aircraft.getDeptCode()
        NextDayFlightPlan nextDayFlightPlan = nextDayFlightPlanMapper.selectNextDayFlightPlanById(id);
        if (deptCodeList.contains(nextDayFlightPlan.getDeptCode())) {
            return nextDayFlightPlan;
        } else {
            throw new ServiceException("无权限访问该数据");
        }
    }

    /**
     * 查询次日计划飞行列表
     *
     * @param param 次日计划飞行
     * @return 次日计划飞行
     */
    @Override
    public List<NextDayFlightPlan> selectNextDayFlightPlanList(NexDayFlightPlanListParam param) {
        return nextDayFlightPlanMapper.selectNextDayFlightPlanList(param);
    }

    /**
     * 新增次日计划飞行
     *
     * @param nextDayFlightPlan 次日计划飞行
     * @return 结果
     */
    @Override
    public int insertNextDayFlightPlan(NextDayFlightPlan nextDayFlightPlan) {


        /**
         *  生成计划编号
         */
        nextDayFlightPlan.setSerialNo(PlanNoUtil.generatePlanNo(nextDayFlightPlan.getCompanyCode()));

        /**
         * 保存次日计划
         */
        int n = nextDayFlightPlanMapper.insertNextDayFlightPlan(nextDayFlightPlan);

        /**
         * 保存关联表
         */
        longTermFlightPlanService.addAssociatedEntitiesLongTermFlightPlan(2, nextDayFlightPlan.getId(), nextDayFlightPlan.getPlanModelAircraftList(),
                nextDayFlightPlan.getFlightPlanAirportList(),
                nextDayFlightPlan.getFlightPlanRouteList(),
                nextDayFlightPlan.getFlightPlanFileList(),
                nextDayFlightPlan.getFlightPlanWorkList());

        return n;
    }

    /**
     * 参数验证 次日飞行计划新增
     *
     * @param nextDayFlightPlan
     * @param type              0 新增 1 修改
     */
    private void paramVerificationNextDayFlightPlan(int type, NextDayFlightPlan nextDayFlightPlan) {

    }


    /**
     * 修改次日计划飞行
     *
     * @param nextDayFlightPlan 次日计划飞行
     * @return 结果
     */
    @Override
    public int updateNextDayFlightPlan(NextDayFlightPlan nextDayFlightPlan) {
        selectNextDayFlightPlanById(nextDayFlightPlan.getId());
        //删除原有关联表
        longTermFlightPlanService.deleteExistingAssociated(nextDayFlightPlan.getId(), 2);
        //更新航班计划
        int count = nextDayFlightPlanMapper.updateNextDayFlightPlan(nextDayFlightPlan);
        //新增关联实体
        longTermFlightPlanService.addAssociatedEntitiesLongTermFlightPlan(2, nextDayFlightPlan.getId(), nextDayFlightPlan.getPlanModelAircraftList(),
                nextDayFlightPlan.getFlightPlanAirportList(),
                nextDayFlightPlan.getFlightPlanRouteList(),
                nextDayFlightPlan.getFlightPlanFileList(),
                nextDayFlightPlan.getFlightPlanWorkList());
        return count;
    }


    //冲突格式化
    private void appendConflictInfo(StringBuilder conflictMessage, Point3D p1,
                                    Point3D p2) {

        conflictMessage.append("冲突线段经纬度为：(").append(p1.x).append(",").append(p1.y).append(",").
                append(p1.z).append("-")
                .append(p2.x).append(",").append(p2.y).append(",").append(p2.z).append(")。");
        // Here, you can format the message with specific details as needed.
    }

    //作业区冲突格式化
//    private void appendConflictInfoWork(StringBuilder conflictMessage, FlightPlanRoute routeA, FlightPlanRouteLongLat p1,
//                                    FlightPlanRouteLongLat p2) {
//        conflictMessage.append("航线代号：").append(routeA.getRouteCode()).append("与其他航线存在冲突。")
//                .append("冲突线段经纬度为：(").append(p1.getLongitude()).append(",").append(p1.getLatitude()).append(",").
//                append(p1.getHeight()).append("-")
//                .append(p2.getLongitude()).append(",").append(p2.getLatitude()).append(",").append(p2.getHeight()).append(")。");
//        // Here, you can format the message with specific details as needed.
//    }

    /**
     * 批量删除次日计划飞行
     *
     * @param ids 需要删除的次日计划飞行主键
     * @return 结果
     */
    @Override
    public int deleteNextDayFlightPlanByIds(Long[] ids) {
        for (Long id : ids) {
            selectNextDayFlightPlanById(id);
        }
        return nextDayFlightPlanMapper.deleteNextDayFlightPlanByIds(ids);
    }

    /**
     * 删除次日计划飞行信息
     *
     * @param id 次日计划飞行主键
     * @return 结果
     */
    @Override
    public int deleteNextDayFlightPlanById(Long id) {
        return nextDayFlightPlanMapper.deleteNextDayFlightPlanById(id);
    }

    @Override
    public NextDayFlightPlan verifyNextDayFlightPlan(NextDayFlightPlan nextDayFlightPlan) {
        return (NextDayFlightPlan) longTermFlightPlanService.verifyLongTermFlightPlan(nextDayFlightPlan);
    }

    @Override
    public int auditing(NextDayFlightPlan nextDayFlightPlan) {
        selectNextDayFlightPlanById(nextDayFlightPlan.getId());
        //根据id查询非草稿状态下的飞行计划
        NextDayFlightPlan selectNextDayFlightPlan = nextDayFlightPlanMapper.selectNextDayFlightPlanByIdAndStatus(nextDayFlightPlan.getId());
        if (selectNextDayFlightPlan != null) {
            int i = updateNextDayFlightPlan(nextDayFlightPlan);
            return i; // 如果更新成功，返回结果
        } else {
            // 处理未找到的情况，例如返回特定的错误代码或抛出异常
            throw new ServiceException("未查询到飞行计划");
        }
    }
}
