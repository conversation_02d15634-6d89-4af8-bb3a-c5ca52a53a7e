package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.MpData;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
public interface IUMpDataService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public MpData selectUMpDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param uMpData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<MpData> selectUMpDataList(MpData uMpData);

    /**
     * 新增【请填写功能名称】
     *
     * @param uMpData 【请填写功能名称】
     * @return 结果
     */
    public int insertUMpData(MpData uMpData);

    /**
     * 修改【请填写功能名称】
     *
     * @param uMpData 【请填写功能名称】
     * @return 结果
     */
    public int updateUMpData(MpData uMpData);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteUMpDataByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteUMpDataById(Long id);

    double selectUMpDataByMeteInfo(MeteInfoParam meteInfoParam);

    public List<MpData> getList(MeteParam meteParam);
}
