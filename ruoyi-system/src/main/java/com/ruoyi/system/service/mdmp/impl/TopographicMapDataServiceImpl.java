package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.system.domain.mdmp.TopographicMapData;
import com.ruoyi.system.mapper.mdmp.TopographicMapDataMapper;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.service.mdmp.ITopographicMapDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
@Service
public class TopographicMapDataServiceImpl implements ITopographicMapDataService {
    @Autowired
    private TopographicMapDataMapper topographicMapDataMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public TopographicMapData selectTopographicMapDataById(Long id) {
        return topographicMapDataMapper.selectTopographicMapDataById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param topographicMapData 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<TopographicMapData> selectTopographicMapDataList(TopographicMapData topographicMapData) {
        return topographicMapDataMapper.selectTopographicMapDataList(topographicMapData);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param topographicMapData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertTopographicMapData(TopographicMapData topographicMapData) {
        return topographicMapDataMapper.insertTopographicMapData(topographicMapData);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param topographicMapData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateTopographicMapData(TopographicMapData topographicMapData) {
        return topographicMapDataMapper.updateTopographicMapData(topographicMapData);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTopographicMapDataByIds(Long[] ids) {
        return topographicMapDataMapper.deleteTopographicMapDataByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTopographicMapDataById(Long id) {
        return topographicMapDataMapper.deleteTopographicMapDataById(id);
    }

    @Override
    public int editTopographic(List<TopographicMapData> param) {
        List<TopographicMapData> list = topographicMapDataMapper.selectTopographicMapDataByIndexList(param);
        for (TopographicMapData topographicMapData : list) {
            topographicMapData.setIndex(topographicMapData.getIndex() + 19800);
            updateTopographicMapData(topographicMapData);
        }
        return 1;
    }

    @Override
    public int selectTopographicMapData(MeteInfoParam meteInfoParam) {
        TopographicMapData topographicMapData = topographicMapDataMapper.selectTopographicMapData(meteInfoParam);
        if (topographicMapData != null) {
            return topographicMapData.getStatusCode().intValue();
        } else {
            return 0;
        }
    }
}
