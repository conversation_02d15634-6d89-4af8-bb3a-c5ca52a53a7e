package com.ruoyi.system.service.oc;

import com.ruoyi.system.domain.oc.FlightTaskBook;
import com.ruoyi.system.domain.oc.dto.FlightTaskAddDTO;
import com.ruoyi.system.domain.oc.dto.FlightTaskQueryDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 飞行任务书
 * @date 2025/7/18 14:42:24
 */
public interface FlightTaskService {
    boolean add(String companyCode,FlightTaskAddDTO dto);

    List<FlightTaskBook> queryFlightTaskInfo(String companyCode, FlightTaskQueryDTO dto);

    boolean update(String companyCode,FlightTaskAddDTO dto);

    FlightTaskAddDTO getInfo(String companyCode, Long id);

    boolean delete(String companyCode, Long id);

}
