package com.ruoyi.system.service.mdmp;

import com.ruoyi.system.domain.mdmp.AirspaceMapData;
import com.ruoyi.system.param.mdmp.MeteInfoParam;

import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface IAirspaceMapDataService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public AirspaceMapData selectAirspaceMapDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param airspaceMapData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<AirspaceMapData> selectAirspaceMapDataList(AirspaceMapData airspaceMapData);

    /**
     * 新增【请填写功能名称】
     *
     * @param airspaceMapData 【请填写功能名称】
     * @return 结果
     */
    public int insertAirspaceMapData(AirspaceMapData airspaceMapData);

    /**
     * 修改【请填写功能名称】
     *
     * @param airspaceMapData 【请填写功能名称】
     * @return 结果
     */
    public int updateAirspaceMapData(AirspaceMapData airspaceMapData);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteAirspaceMapDataByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteAirspaceMapDataById(Long id);

    int selectAirspaceMapData(MeteInfoParam meteInfoParam);
}
