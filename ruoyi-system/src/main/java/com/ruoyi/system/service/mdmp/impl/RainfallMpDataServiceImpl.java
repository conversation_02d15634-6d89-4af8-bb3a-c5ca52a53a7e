package com.ruoyi.system.service.mdmp.impl;

import java.util.ArrayList;
import java.util.List;

import com.ruoyi.system.domain.mdmp.RainfallMpData;
import com.ruoyi.system.domain.mdmp.TemperatureMpData;
import com.ruoyi.system.domain.mdmp.VisibilityMpData;
import com.ruoyi.system.mapper.mdmp.RainfallMpDataMapper;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;
import com.ruoyi.system.service.mdmp.IRainfallMpDataService;
import com.ruoyi.system.util.MeteUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class RainfallMpDataServiceImpl implements IRainfallMpDataService {
    @Autowired
    private RainfallMpDataMapper rainfallMpDataMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public RainfallMpData selectRainfallMpDataById(Long id) {
        return rainfallMpDataMapper.selectRainfallMpDataById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param rainfallMpData 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<RainfallMpData> selectRainfallMpDataList(RainfallMpData rainfallMpData) {
        return rainfallMpDataMapper.selectRainfallMpDataList(rainfallMpData);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param rainfallMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertRainfallMpData(RainfallMpData rainfallMpData) {
        return rainfallMpDataMapper.insertRainfallMpData(rainfallMpData);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param rainfallMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateRainfallMpData(RainfallMpData rainfallMpData) {
        return rainfallMpDataMapper.updateRainfallMpData(rainfallMpData);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteRainfallMpDataByIds(Long[] ids) {
        return rainfallMpDataMapper.deleteRainfallMpDataByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteRainfallMpDataById(Long id) {
        return rainfallMpDataMapper.deleteRainfallMpDataById(id);
    }

    @Override
    public double selectRainfallMpDataByMeteInfo(MeteInfoParam meteInfoParam) {
        RainfallMpData rainfallMpData = rainfallMpDataMapper.selectRainfallMpDataByMeteInfo(meteInfoParam);
        if (rainfallMpData != null) {
            return rainfallMpData.getDataValue();
        }
        return 0;
    }

    @Override
    public List<Double> getList(MeteParam meteParam) {
        List<Double> rainfallMpDataList = rainfallMpDataMapper.getList(meteParam);
        return rainfallMpDataList;
    }


}
