package com.ruoyi.system.service.mdmp;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.mdmp.FlightCommandLog;
import com.ruoyi.system.domain.mdmp.dto.FlightCommandDTO;
import com.ruoyi.system.domain.mdmp.dto.PushToDroneSystemDTO;
import com.ruoyi.system.domain.mdmp.dto.QueryFlightCommandLogDTO;

import java.util.List;

public interface FlightCommandService {

    CommonResult<String> executionResult(FlightCommandDTO dto);

    CommonResult<List<FlightCommandLog>> query(QueryFlightCommandLogDTO dto);

    CommonResult<String> pushToDroneSystem(PushToDroneSystemDTO dto);
}
