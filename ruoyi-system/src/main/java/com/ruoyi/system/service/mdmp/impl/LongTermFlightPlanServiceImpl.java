package com.ruoyi.system.service.mdmp.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.ArrayList;
import java.util.function.Function;
import java.util.stream.Collectors;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PlanNoUtil;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.vo.ConflictRoute;
import com.ruoyi.system.domain.mdmp.vo.ConflictWorkVo;
import com.ruoyi.system.domain.mdmp.vo.MapDataLongLatVo;
import com.ruoyi.system.domain.mdmp.vo.QueryMapDataVo;
import com.ruoyi.system.domain.type.FlightPlanType;
import com.ruoyi.system.domain.type.GraphicsType;
import com.ruoyi.system.domain.type.PositiveOrNegativeType;
import com.ruoyi.system.domain.type.WorkType;
import com.ruoyi.system.mapper.mdmp.*;
import com.ruoyi.system.param.mdmp.LongTermFlightPlanListParam;
import com.ruoyi.system.service.mdmp.ILongTermFlightPlanService;
import com.ruoyi.system.service.mdmp.IMapDataService;
import com.ruoyi.system.util.*;
import org.apache.logging.log4j.util.Strings;
import org.apache.poi.ss.formula.functions.T;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 长期飞行计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Service
@EnableAsync
public class LongTermFlightPlanServiceImpl implements ILongTermFlightPlanService {
    @Resource
    private LongTermFlightPlanMapper longTermFlightPlanMapper;
    @Resource
    private NextDayFlightPlanMapper nextDayFlightPlanMapper;
    @Resource
    private DailyFlightPlanMapper dailyFlightPlanMapper;

    @Resource
    private FlightPlanAircraftModelMapper flightPlanAircraftModelMapper;

    @Resource
    private FlightPlanAirportMapper flightPlanAirportMapper;

    @Resource
    private FlightPlanRouteMapper flightPlanRouteMapper;

    @Resource
    private FlightPlanRouteLongLatMapper flightPlanRouteLongLatMapper;

    @Resource
    private FlightPlanFileMapper flightPlanFileMapper;


    @Resource
    private FlightPlanWorkMapper flightPlanWorkMapper;

    @Resource
    private FlightPlanWorkLongLatMapper flightPlanWorkLongLatMapper;
    @Resource
    private FlightPlanWorkInMapper flightPlanWorkInMapper;

    @Resource
    private WorkinLongLatMapper workinLongLatMapper;

    @Resource
    private MapDataMapper mapDataMapper;
    @Resource
    private IMapDataService mapDataService;
    @Resource
    private RouteMapDataMapper routeMapDataMapper;
    @Resource
    private WorkMapDataMapper workMapDataMapper;


    Logger logger = LoggerFactory.getLogger(LongTermFlightPlanServiceImpl.class);


    /**
     * 查询长期飞行计划
     *
     * @param id 长期飞行计划主键
     * @return 长期飞行计划
     */
    @Override
    public LongTermFlightPlan selectLongTermFlightPlanById(Long id) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        //判断deptCodeList是否包含aircraft.getDeptCode()
        LongTermFlightPlan longTermFlightPlan = longTermFlightPlanMapper.selectLongTermFlightPlanById(id);
        if (deptCodeList.contains(longTermFlightPlan.getDeptCode())) {
            return longTermFlightPlan;
        } else {
            throw new ServiceException("无权限访问该数据");
        }

    }

    /**
     * 查询长期飞行计划列表
     *
     * @param longTermFlightPlan 长期飞行计划
     * @return 长期飞行计划
     */
    @Override
    public List<LongTermFlightPlan> selectLongTermFlightPlanList(LongTermFlightPlanListParam longTermFlightPlan) {
        return longTermFlightPlanMapper.selectLongTermFlightPlanList(longTermFlightPlan);
    }

    /**
     * 新增长期飞行计划
     *
     * @param longTermFlightPlan 长期飞行计划
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertLongTermFlightPlan(LongTermFlightPlan longTermFlightPlan) {


        //设置航班计划编号

        longTermFlightPlan.setSerialNo(PlanNoUtil.generatePlanNo(longTermFlightPlan.getCompanyCode()));

        /**
         * 新增计划实体
         */
        int longTermFlightPlanCount = longTermFlightPlanMapper.insertLongTermFlightPlan(longTermFlightPlan);


        /**
         * 新增关联实体
         *
         */

        addAssociatedEntitiesLongTermFlightPlan(1, longTermFlightPlan.getId(), longTermFlightPlan.getPlanModelAircraftList(),
                longTermFlightPlan.getFlightPlanAirportList(),
                longTermFlightPlan.getFlightPlanRouteList(),
                longTermFlightPlan.getFlightPlanFileList(),
                longTermFlightPlan.getFlightPlanWorkList());


        return longTermFlightPlanCount;
    }


    /**
     * 计算候选立方体中到线段距离小于500米的立方体
     *
     * @param candidateCubes 候选立方体列表
     * @param pointA         线段端点A
     * @param pointB         线段端点B
     * @return 符合距离条件的立方体列表
     */
    public List<MapData> filterCubesByDistance(List<MapData> candidateCubes, FlightPlanRouteLongLat pointA, FlightPlanRouteLongLat pointB, double distances) {
        // 转换线段端点为三维坐标
        Point3D a = convertTo3DPoint(pointA);
        Point3D b = convertTo3DPoint(pointB);

        // 计算线段向量和长度平方
        double abx = b.x - a.x;
        double aby = b.y - a.y;
        double abz = b.z - a.z;
        double abLengthSquared = abx * abx + aby * aby + abz * abz;

        List<MapData> result = new ArrayList<>();

        for (MapData cube : candidateCubes) {
            List<Point3D> vertices = generateCubeVertices(cube, pointA, pointB);
            double minDistance = Double.MAX_VALUE;

            for (Point3D vertex : vertices) {
                double distance = calculateDistanceToSegment(vertex, a, b, abx, aby, abz, abLengthSquared);
                if (distance < minDistance) {
                    minDistance = distance;
                }
            }

            if (minDistance <= distances) {
                result.add(cube);
            }
        }

        return result;
    }

    // 将航线点转换为三维坐标
    private Point3D convertTo3DPoint(FlightPlanRouteLongLat point) {
        double refLon = point.getDoubleLongitude().doubleValue();
        double refLat = point.getDoubleLatitude().doubleValue();
        double height = point.getHeight().doubleValue();

        // 使用该点作为参考点计算转换因子
        double metersPerDegreeLat = 111319.9;
        double metersPerDegreeLon = 111319.9 * Math.cos(Math.toRadians(refLat));

        // 转换为相对于该点的坐标（假设参考点为自身，简化计算）
        double x = 0; // (lon - refLon) * ... 但因为参考点是自身，此处为0
        double y = 0;
        double z = height;

        return new Point3D(x, y, z);
    }

    // 生成立方体的8个顶点
    private List<Point3D> generateCubeVertices(MapData cube, FlightPlanRouteLongLat pointA, FlightPlanRouteLongLat pointB) {
        // 获取线段中点的参考经纬度
        double refLon = (pointA.getDoubleLongitude().doubleValue() + pointB.getDoubleLongitude().doubleValue()) / 2;
        double refLat = (pointA.getDoubleLatitude().doubleValue() + pointB.getDoubleLatitude().doubleValue()) / 2;

        double metersPerDegreeLat = 111319.9;
        double metersPerDegreeLon = 111319.9 * Math.cos(Math.toRadians(refLat));

        List<Point3D> vertices = new ArrayList<>();

        // 解析立方体边界值
        BigDecimal[] lons = {cube.getLongitudeStart(), cube.getLongitudeEnd()};
        BigDecimal[] lats = {cube.getLatitudeStart(), cube.getLatitudeEnd()};
        Integer[] heights = {cube.getHeightStart(), cube.getHeightEnd()};

        // 生成所有顶点组合
        for (BigDecimal lon : lons) {
            for (BigDecimal lat : lats) {
                for (Integer height : heights) {
                    double lonD = lon.doubleValue();
                    double latD = lat.doubleValue();
                    double x = (lonD - refLon) * metersPerDegreeLon;
                    double y = (latD - refLat) * metersPerDegreeLat;
                    double z = height.doubleValue();

                    vertices.add(new Point3D(x, y, z));
                }
            }
        }

        return vertices;
    }

    // 计算点到线段的最短距离
    private double calculateDistanceToSegment(Point3D p, Point3D a, Point3D b,
                                              double abx, double aby, double abz,
                                              double abLengthSquared) {
        double apx = p.x - a.x;
        double apy = p.y - a.y;
        double apz = p.z - a.z;

        double dotProduct = apx * abx + apy * aby + apz * abz;

        if (abLengthSquared < 1e-6) { // 线段退化为点
            return Math.sqrt(apx * apx + apy * apy + apz * apz);
        }

        double t = dotProduct / abLengthSquared;

        if (t <= 0.0) {
            return Math.sqrt(apx * apx + apy * apy + apz * apz);
        } else if (t >= 1.0) {
            double bpx = p.x - b.x;
            double bpy = p.y - b.y;
            double bpz = p.z - b.z;
            return Math.sqrt(bpx * bpx + bpy * bpy + bpz * bpz);
        } else {
            double projX = a.x + t * abx;
            double projY = a.y + t * aby;
            double projZ = a.z + t * abz;
            double dx = p.x - projX;
            double dy = p.y - projY;
            double dz = p.z - projZ;
            return Math.sqrt(dx * dx + dy * dy + dz * dz);
        }
    }


    private boolean isLineIntersectingAABB(
            BigDecimal[] lineStart, BigDecimal[] lineEnd,
            BigDecimal[] boxMin, BigDecimal[] boxMax
    ) {
        BigDecimal tMin = BigDecimal.ZERO;
        BigDecimal tMax = BigDecimal.ONE;

        for (int i = 0; i < 3; i++) {
            BigDecimal lineDir = lineEnd[i].subtract(lineStart[i]);
            if (lineDir.compareTo(BigDecimal.ZERO) == 0) {
                // 线段平行于坐标轴
                if (lineStart[i].compareTo(boxMin[i]) < 0 || lineStart[i].compareTo(boxMax[i]) > 0) {
                    return false; // 线段在轴外，不相交
                }
                // 线段在轴内，设置 tMin 和 tMax 为全范围 [0,1]
                tMin = tMin.max(BigDecimal.ZERO);
                tMax = tMax.min(BigDecimal.ONE);
                continue;
            }

            BigDecimal invDir = BigDecimal.ONE.divide(lineDir, 10, RoundingMode.HALF_UP);
            BigDecimal t1 = boxMin[i].subtract(lineStart[i]).multiply(invDir);
            BigDecimal t2 = boxMax[i].subtract(lineStart[i]).multiply(invDir);

            BigDecimal tNear = t1.min(t2);
            BigDecimal tFar = t1.max(t2);

            tMin = tMin.max(tNear);
            tMax = tMax.min(tFar);

            if (tMin.compareTo(tMax) > 0) {
                return false;
            }
        }

        return true;
    }


    /**
     * 新增关联实体
     */
    public void addAssociatedEntitiesLongTermFlightPlan(Integer planType, Long id, List<FlightPlanAircraftModel> planModelAircraftList,
                                                        List<FlightPlanAirport> flightPlanAirportList,
                                                        List<FlightPlanRoute> flightPlanRouteList,
                                                        List<FlightPlanFile> flightPlanFileList,
                                                        List<FlightPlanWork> flightPlanWorkList) {

        //flightPlanRouteList 和flightPlanWorkList 不能全部为空
        if ((flightPlanRouteList == null || flightPlanRouteList.isEmpty()) && (flightPlanWorkList == null || flightPlanWorkList.isEmpty())) {
            throw new ServiceException("航班计划航线和航班计划作业区不能全部为空");
        }
        /**
         *  新增关联实体 航班计划关`89联机型表
         *  批量设置航班计划id
         */


        if (planModelAircraftList != null && !planModelAircraftList.isEmpty()) {
            planModelAircraftList.forEach(e -> {
                e.setFlightPlanId(id);
                e.setPlanType(planType);
            });
            int flightPlanAircraftModelCount = flightPlanAircraftModelMapper.insertAllFlightPlanAircraftModel(planModelAircraftList);
        }


        /**
         *  新增关联实体 航班计划关联机场表
         *  批量设置航班计划id
         */
        if (flightPlanAirportList != null && !flightPlanAirportList.isEmpty()) {
            for (int i = 0; i < flightPlanAirportList.size(); i++) {
                flightPlanAirportList.get(i).setFlightPlanId(id);
                flightPlanAirportList.get(i).setSortNumber(i);
                flightPlanAirportList.get(i).setPlanType(planType);
            }
            int flightPlanAirportCount = flightPlanAirportMapper.insertAllFlightPlanAirport(flightPlanAirportList);
        }


        /**
         *  新增关联实体 航班计划关联航线表
         *  批量设置航班计划id
         */
        flightPlanRouteList.forEach(e -> {
            e.setFlightPlanId(id);
            e.setPlanType(planType);
            int flightPlanRouteCount = flightPlanRouteMapper.insertFlightPlanRoute(e);
            /**
             *  新增关联实体 航班航线关联航班航线经纬度表
             */
            for (int i = 0; i < e.getFlightPlanRouteLongLatList().size(); i++) {
                e.getFlightPlanRouteLongLatList().get(i).setFlightPlanRouteId(e.getId());
                e.getFlightPlanRouteLongLatList().get(i).setSortNumber(i);
                if (e.getFlightPlanRouteLongLatList().get(i).getPointType().equals(0)) {
                    e.getFlightPlanRouteLongLatList().get(i).setDoubleLatitude(BigDecimal.valueOf(LongLatUtil.convertDMSToDD(e.getFlightPlanRouteLongLatList().get(i).getLatitude())));
                    e.getFlightPlanRouteLongLatList().get(i).setDoubleLongitude(BigDecimal.valueOf(LongLatUtil.convertDMSToDD(e.getFlightPlanRouteLongLatList().get(i).getLongitude())));
                }
                e.getFlightPlanRouteLongLatList().get(i).setHeight(e.getFlightPlanRouteLongLatList().get(i).getHeight());
            }

            int routeLongLatCount = flightPlanRouteLongLatMapper.insertAllFlightPlanRouteLongLat(e.getFlightPlanRouteLongLatList());
            //保存航线地图数据
            if (planType.equals(FlightPlanType.DAY_RELEASE_PLAN)) {
                //只有当日计划才保存
                logger.info("保存航线地图数据");
                mapDataService.saveRouteMapData(e);
            }


        });

        /**
         *  新增关联实体 航班计划关联文件表
         *  批量设置航班计划id
         */

        if (flightPlanFileList != null && !flightPlanFileList.isEmpty()) {
            flightPlanFileList.forEach(e -> {
                e.setFlightPlanId(id);
                e.setPlanType(planType);
            });
            int flightPlanFileCount = flightPlanFileMapper.insertAllFlightPlanFile(flightPlanFileList);
        }

        /**
         *  新增关联实体 航班计划关联作业区表
         *  批量设置航班计划id
         */
        if (flightPlanWorkList != null && !flightPlanWorkList.isEmpty()) {
            flightPlanWorkList.forEach(e -> {
                e.setFlightPlanId(id);
                e.setPlanType(planType);
            });
            insertAllFlightPlanWork(planType, flightPlanWorkList);
        }
    }

    /**
     * 航班计划关联作业区表
     *
     * @param flightPlanWorkList
     * @return
     */
    private void insertAllFlightPlanWork(Integer planType, List<FlightPlanWork> flightPlanWorkList) {

        verifyWorkParam(flightPlanWorkList);
        /**
         * 1批量保存作业区表
         * 2批量保存作业区经纬度关联表
         * 3批量保存作业区内关联表
         * 4批量保存作业区内表经纬度关联表
         */

        flightPlanWorkList.forEach(e -> {

            //验证作业区


            int flightPlanWorkCount = flightPlanWorkMapper.insertFlightPlanWork(e);
            /**
             * 2批量保存作业区经纬度关联表
             */
            if (e.getGraphType().equals(GraphicsType.CIRCULAR)) {
                //根据圆心半径算出50个点
                double circleCenterLong = LongLatUtil.convertDMSToDD(e.getCircleCenterLong());
                double circleCenterLat = LongLatUtil.convertDMSToDD(e.getCircleCenterLat());
                double radius = e.getRadius().doubleValue();
                double offset = e.getWorkOffset().doubleValue();
                if (e.getPositiveOrNegative() == PositiveOrNegativeType.NEGATIVE) {
                    offset = -e.getWorkOffset().doubleValue();
                }
                e.setFlightPlanWorkLongLatList(WorkBasisUtil.getFlightPlanWorkLongLat(e.getId(), circleCenterLong, circleCenterLat, radius, offset, 50));
            } else {
                /**
                 * 自定义排序
                 */
                for (int i = 0; i < e.getFlightPlanWorkLongLatList().size(); i++) {
                    e.getFlightPlanWorkLongLatList().get(i).setSortNumber(i);
                    e.getFlightPlanWorkLongLatList().get(i).setGroupNumber(0);
                    e.getFlightPlanWorkLongLatList().get(i).setFlightPlanWorkId(e.getId());
                    e.getFlightPlanWorkLongLatList().get(i).setDoubleLatitude(BigDecimal.valueOf(LongLatUtil.convertDMSToDD(e.getFlightPlanWorkLongLatList().get(i).getLatitude())));
                    e.getFlightPlanWorkLongLatList().get(i).setDoubleLongitude(BigDecimal.valueOf(LongLatUtil.convertDMSToDD(e.getFlightPlanWorkLongLatList().get(i).getLongitude())));
                    e.getFlightPlanWorkLongLatList().get(i).setHeight(e.getFlightPlanWorkLongLatList().get(i).getHeight());
                }

                if (e.getWorkType().equals(WorkType.REGION) && e.getGraphType().equals(GraphicsType.POLYGON)) {
                    // 将第一个坐标添加到列表末尾，形成闭合多边形
                    List<FlightPlanWorkLongLat> flightPlanWorkLongLatList = e.getFlightPlanWorkLongLatList();
                    FlightPlanWorkLongLat f = new FlightPlanWorkLongLat();
                    BeanUtils.copyProperties(flightPlanWorkLongLatList.get(0), f);
                    // 设置最后一个元素的groupNumber
                    int listSize = flightPlanWorkLongLatList.size();
                    f.setSortNumber(listSize);
                    flightPlanWorkLongLatList.add(f);
//                    flightPlanWorkLongLatList.get(listSize - 1).setSortNumber(listSize);
                    e.setFlightPlanWorkLongLatList(flightPlanWorkLongLatList);
                }


            }


            if (!e.getFlightPlanWorkLongLatList().isEmpty()) {
                int flightPlanWorkLongLatCount = flightPlanWorkLongLatMapper.insertAllFlightPlanWorkLongLat(e.getFlightPlanWorkLongLatList());

                //保存作业区地图数据
                if (planType.equals(FlightPlanType.DAY_RELEASE_PLAN)) {
                    //只有当日计划才保存
                    logger.info("保存作业区地图数据" + new Date());
                    mapDataService.saveWorkMapData(e);
                }

            }
            /**
             *批量保存作业区内关联表
             */
            if (e.getFlightPlanWorkInList() != null) {
                e.getFlightPlanWorkInList().forEach(in -> {
                    in.setFlightPlanWorkId(e.getId());
                    int flightPlanWorkInCount = flightPlanWorkInMapper.insertFlightPlanWorkIn(in);
                    /**
                     * 设置作业区内关联经纬度表id
                     */

                    if (in.getGraphType().equals(GraphicsType.CIRCULAR)) {
                        //根据圆心半径算出50个点
                        double circleCenterLong = LongLatUtil.convertDMSToDD(in.getCircleCenterLong());
                        double circleCenterLat = LongLatUtil.convertDMSToDD(in.getCircleCenterLat());
                        double radius = in.getRadius().doubleValue();
                        double offset = e.getWorkOffset().doubleValue();
                        in.setWorkinLongLatList(WorkBasisUtil.getWorkInLongLat(in.getId(), circleCenterLong, circleCenterLat, radius, offset, 50));
                    } else {
                        /**
                         * 自定义排序
                         */
                        for (int i = 0; i < in.getWorkinLongLatList().size(); i++) {
                            in.getWorkinLongLatList().get(i).setSortNumber(i);
                            in.getWorkinLongLatList().get(i).setWorkInId(in.getId());
                            in.getWorkinLongLatList().get(i).setDoubleLatitude(BigDecimal.valueOf(LongLatUtil.convertDMSToDD(in.getWorkinLongLatList().get(i).getLatitude())));
                            in.getWorkinLongLatList().get(i).setDoubleLongitude(BigDecimal.valueOf(LongLatUtil.convertDMSToDD(in.getWorkinLongLatList().get(i).getLongitude())));
                            in.getWorkinLongLatList().get(i).setHeight(in.getWorkinLongLatList().get(i).getHeight());
                        }
                        if (e.getWorkType().equals(WorkType.REGION) && in.getGraphType().equals(GraphicsType.POLYGON)) {
                            // 将第一个坐标添加到列表末尾，形成闭合多边形
                            List<WorkinLongLat> workinLongLatList = in.getWorkinLongLatList();
                            WorkinLongLat f = new WorkinLongLat();
                            BeanUtils.copyProperties(workinLongLatList.get(0), f);
                            // 设置最后一个元素的groupNumber
                            int listSize = workinLongLatList.size();
                            f.setSortNumber(listSize);
                            workinLongLatList.add(f);
                            in.setWorkinLongLatList(workinLongLatList);
                        }
                    }
//

                    /**
                     *批量保存作业区内关联经纬度表
                     */
                    if (!in.getWorkinLongLatList().isEmpty()) {
                        int wllCount = workinLongLatMapper.insertAllWorkinLongLat(in.getWorkinLongLatList());
                    }
                });
            }


        });
    }

    private void verifyWorkParam(List<FlightPlanWork> flightPlanWorkList) {

        flightPlanWorkList.forEach(e -> {
            if (Strings.isBlank(e.getWorkName())) {
                throw new RuntimeException("作业区名称不能为空");
            }
            if (e.getWorkType().equals(WorkType.REGION)) {
                //区域内作业 圆
                if (e.getGraphType().equals(GraphicsType.CIRCULAR)) {
                    if (e.getMinHeight() == null) {
                        throw new RuntimeException("作业区最小高度不能为空");
                    }
                    if (e.getMaxHeight() == null) {
                        throw new RuntimeException("作业区最大高度不能为空");
                    }
                    if (e.getWorkOffset() == null) {
                        throw new RuntimeException("作业区偏移量不能为空");
                    }
                    if (e.getRadius() == null) {
                        throw new RuntimeException("半径不能为空");
                    }
                    if (Strings.isBlank(e.getCircleCenterLong())) {
                        throw new RuntimeException("圆心经度不能为空");
                    }

                    if (Strings.isBlank(e.getCircleCenterLat())) {
                        throw new RuntimeException("圆心纬度不能为空");
                    }

                    if (e.getPositiveOrNegative() == null) {
                        throw new RuntimeException("作业区偏移量类型不能为空");
                    }
                    //如果还有子作业区
                    if (e.getFlightPlanWorkInList() != null && !e.getFlightPlanWorkInList().isEmpty()) {
                        for (int i = 0; i < e.getFlightPlanWorkInList().size(); i++) {
                            FlightPlanWorkIn workIn = e.getFlightPlanWorkInList().get(i);
                            if (Strings.isBlank(workIn.getWorkInName())) {
                                throw new RuntimeException("子作业区名称不能为空");
                            }
                            //如果是圆
                            if (workIn.getGraphType().equals(GraphicsType.CIRCULAR)) {
                                if (workIn.getMinHeight() == null) {
                                    throw new RuntimeException("子作业区最小高度不能为空");
                                }
                                if (workIn.getMaxHeight() == null) {
                                    throw new RuntimeException("子作业区最大高度不能为空");
                                }
                                if (workIn.getRadius() == null) {
                                    throw new RuntimeException("子作业区半径不能为空");
                                }
                                if (Strings.isBlank(workIn.getCircleCenterLong())) {
                                    throw new RuntimeException("子作业区圆心经度不能为空");
                                }
                                if (Strings.isBlank(workIn.getCircleCenterLat())) {
                                    throw new RuntimeException("子作业区圆心纬度不能为空");
                                }
                            } else {
                                if (workIn.getWorkinLongLatList() == null || workIn.getWorkinLongLatList().isEmpty()) {
                                    throw new RuntimeException("子作业区经纬度不能为空");
                                }
                                if (workIn.getWorkinLongLatList().size() < 2) {
                                    throw new RuntimeException("子作业区经纬度点不能小于2");
                                }
                                for (int j = 0; j < workIn.getWorkinLongLatList().size(); j++) {
                                    if (Strings.isBlank(workIn.getWorkinLongLatList().get(j).getLatitude())) {
                                        throw new RuntimeException("子作业区纬度不能为空");
                                    }
                                    if (Strings.isBlank(workIn.getWorkinLongLatList().get(j).getLongitude())) {
                                        throw new RuntimeException("子作业区经度不能为空");
                                    }
                                    if (Strings.isBlank(workIn.getWorkinLongLatList().get(j).getCoordinateName())) {
                                        throw new RuntimeException("子作业区坐标名称不能为空");
                                    }
                                }
                            }
                        }
                    }


                } else {
                    if (e.getFlightPlanWorkLongLatList() == null || e.getFlightPlanWorkLongLatList().isEmpty()) {
                        throw new RuntimeException("作业区经纬度不能为空");
                    }
                    if (e.getFlightPlanWorkLongLatList().size() < 2) {
                        throw new RuntimeException("作业区经纬度点不能小于2");
                    }
                    for (int i = 0; i < e.getFlightPlanWorkLongLatList().size(); i++) {
                        if (Strings.isBlank(e.getFlightPlanWorkLongLatList().get(i).getLatitude())) {
                            throw new RuntimeException("作业区纬度不能为空");
                        }
                        if (Strings.isBlank(e.getFlightPlanWorkLongLatList().get(i).getLongitude())) {
                            throw new RuntimeException("作业区经度不能为空");
                        }
                        if (Strings.isBlank(e.getFlightPlanWorkLongLatList().get(i).getCoordinateName())) {
                            throw new RuntimeException("作业区坐标名称不能为空");
                        }
                    }
                }


            } else if (e.getWorkType().equals(WorkType.ALONG) || e.getWorkType().equals(WorkType.NOT_ALONG)) {
                if (e.getFlightPlanWorkLongLatList() == null || e.getFlightPlanWorkLongLatList().isEmpty()) {
                    throw new RuntimeException("作业区经纬度不能为空");
                }
                if (e.getFlightPlanWorkLongLatList().size() < 2) {
                    throw new RuntimeException("作业区经纬度点不能小于2");
                }
                for (int i = 0; i < e.getFlightPlanWorkLongLatList().size(); i++) {
                    if (Strings.isBlank(e.getFlightPlanWorkLongLatList().get(i).getLatitude())) {
                        throw new RuntimeException("作业区纬度不能为空");
                    }
                    if (Strings.isBlank(e.getFlightPlanWorkLongLatList().get(i).getLongitude())) {
                        throw new RuntimeException("作业区经度不能为空");
                    }
                    if (Strings.isBlank(e.getFlightPlanWorkLongLatList().get(i).getCoordinateName())) {
                        throw new RuntimeException("作业区坐标名称不能为空");
                    }
                    if (e.getFlightPlanWorkLongLatList().get(i).getHeight() == null) {
                        throw new RuntimeException("作业区高度不能为空");
                    }
                }
            }
        });
    }


    /**
     * 修改长期飞行计划
     *
     * @param longTermFlightPlan 长期飞行计划
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateLongTermFlightPlan(LongTermFlightPlan longTermFlightPlan) {
        selectLongTermFlightPlanById(longTermFlightPlan.getId());
        //删除原有关联表
        deleteExistingAssociated(longTermFlightPlan.getId(), 1);


        //更新航班计划
        int count = longTermFlightPlanMapper.updateLongTermFlightPlan(longTermFlightPlan);
        //新增关联实体
        addAssociatedEntitiesLongTermFlightPlan(1, longTermFlightPlan.getId(), longTermFlightPlan.getPlanModelAircraftList(),
                longTermFlightPlan.getFlightPlanAirportList(),
                longTermFlightPlan.getFlightPlanRouteList(),
                longTermFlightPlan.getFlightPlanFileList(),
                longTermFlightPlan.getFlightPlanWorkList());
        return count;
    }

    /**
     * 删除原有关联表
     *
     * @param id 航班计划id
     * @param i  航班计划类型 1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划
     */
    public void deleteExistingAssociated(Long id, int i) {
        //删除机型表
        flightPlanAircraftModelMapper.deleteAllByFlightPlanId(id,i);
        //删除机场表
        flightPlanAirportMapper.deleteAllByFlightPlanId(id,i);

        List<FlightPlanRoute> flightPlanRouteList = flightPlanRouteMapper.selectFlightPlanRouteByFlightPlanId(id,i);

        flightPlanRouteList.forEach(e -> {
            //删除航线关联经纬度表
            flightPlanRouteLongLatMapper.deleteAllByFlightPlanRouteId(e.getId());
            //删除航线地图数据关联表
            routeMapDataMapper.delByRouteId(e.getId());
        });
        //删除航线表
        flightPlanRouteMapper.deleteAllByFlightPlanId(id,i);


        // TODO 删除文件服务器上的相关文件
        //删除文件表
        flightPlanFileMapper.deleteAllByFlightPlanId(id,i);

        //删除作业区
        FlightPlanWork flightPlanWork = new FlightPlanWork();
        flightPlanWork.setFlightPlanId(id);
        flightPlanWork.setPlanType(i);
        List<FlightPlanWork> flightPlanWorkList = flightPlanWorkMapper.selectWorkByFlightPlanId(flightPlanWork);
        flightPlanWorkList.forEach(e -> {
            //取出一个集合对象里面的id 放在一个集合里面
            if (!e.getFlightPlanWorkInList().isEmpty()) {
                //删除作业区内关联经纬度
                workinLongLatMapper.deleteWorkinLongLatByIds(e.getFlightPlanWorkInList().stream()
                        .map(FlightPlanWorkIn::getId)
                        .toArray(Long[]::new));
                //删除作业区内表
                flightPlanWorkInMapper.deleteFlightPlanWorkInByIds(e.getFlightPlanWorkInList().stream()
                        .map(FlightPlanWorkIn::getId)
                        .toArray(Long[]::new));

                //删除作业区地图数据关联表
                routeMapDataMapper.delByRouteId(e.getId());

            }

            //删除作业区 地图数据表
            workMapDataMapper.delByWorkId(e.getId());

            if (!e.getFlightPlanWorkLongLatList().isEmpty()) {
                //删除作业区 经纬度表
                flightPlanWorkLongLatMapper.deleteFlightPlanWorkLongLatByIds(e.getFlightPlanWorkLongLatList().stream()
                        .map(FlightPlanWorkLongLat::getId)
                        .toArray(Long[]::new));
            }
        });
        //删除作业区表
        if (!flightPlanWorkList.isEmpty()) {
            flightPlanWorkMapper.deleteFlightPlanWorkByIds(flightPlanWorkList.stream()
                    .map(FlightPlanWork::getId)
                    .toArray(Long[]::new),i);
        }

    }

    // 度分秒转换为经纬度并设置经纬度
    private void convertDMSToDDAndSetLatLong(List<? extends FlightPlanRoute> flightPlanRouteList) {
        flightPlanRouteList.parallelStream().forEach(route -> {
            route.getFlightPlanRouteLongLatList().parallelStream().forEach(routes -> {
                if (routes.getDoubleLongitude() == null) {
                    routes.setDoubleLongitude(BigDecimal.valueOf(LongLatUtil.convertDMSToDD(routes.getLongitude())));
                }
                if (routes.getDoubleLatitude() == null) {
                    routes.setDoubleLatitude(BigDecimal.valueOf(LongLatUtil.convertDMSToDD(routes.getLatitude())));
                }
            });
        });
    }

    private void convertDMSToDDAndSetLatLongWork(List<? extends FlightPlanWork> flightPlanRouteList) {
        flightPlanRouteList.parallelStream().forEach(route -> {
            if (route.getGraphType().equals(GraphicsType.POLYGON)) {
                route.getFlightPlanWorkLongLatList().parallelStream().forEach(routes -> {
                    if (routes.getDoubleLongitude() == null) {
                        routes.setDoubleLongitude(BigDecimal.valueOf(LongLatUtil.convertDMSToDD(routes.getLongitude())));
                    }
                    if (routes.getDoubleLatitude() == null) {
                        routes.setDoubleLatitude(BigDecimal.valueOf(LongLatUtil.convertDMSToDD(routes.getLatitude())));
                    }
                });
            }
        });
    }

    // 设置圆形作业区的50个点
    private void setCircularLongLat(List<? extends FlightPlanWork> flightPlanWorkList) {
        flightPlanWorkList.parallelStream().forEach(work -> {
            if (work.getGraphType().equals(GraphicsType.CIRCULAR)) {
                double circleCenterLong = LongLatUtil.convertDMSToDD(work.getCircleCenterLong());
                double circleCenterLat = LongLatUtil.convertDMSToDD(work.getCircleCenterLat());
                double radius = work.getRadius().doubleValue();
                double offset = work.getWorkOffset().doubleValue();
                work.setFlightPlanWorkLongLatList(WorkBasisUtil.getFlightPlanWorkLongLat(work.getId(), circleCenterLong, circleCenterLat, radius, offset, 50));
            }
            if (work.getFlightPlanWorkInList() != null && !work.getFlightPlanWorkInList().isEmpty()) {
                work.getFlightPlanWorkInList().parallelStream().forEach(workIn -> {
                    if (workIn.getGraphType().equals(GraphicsType.CIRCULAR)) {
                        double circleCenterLong = LongLatUtil.convertDMSToDD(workIn.getCircleCenterLong());
                        double circleCenterLat = LongLatUtil.convertDMSToDD(workIn.getCircleCenterLat());
                        double radius = workIn.getRadius().doubleValue();
                        double offset = work.getWorkOffset().doubleValue();
                        workIn.setWorkinLongLatList(WorkBasisUtil.getWorkInLongLat(workIn.getId(), circleCenterLong, circleCenterLat, radius, offset, 50));
                    }
                    if (workIn.getGraphType().equals(GraphicsType.POLYGON)) {
                        if (workIn.getWorkinLongLatList() != null && workIn.getWorkinLongLatList().size() > 0) {
                            // 将第一个坐标添加到列表末尾，形成闭合多边形
                            List<WorkinLongLat> workinLongLatList = workIn.getWorkinLongLatList();
                            workinLongLatList.add(workinLongLatList.get(0));
                            // 设置最后一个元素的groupNumber
                            int listSize = workinLongLatList.size();
                            workinLongLatList.get(listSize - 1).setSortNumber(listSize);
                            workIn.setWorkinLongLatList(workinLongLatList);
                        }
                    }
                });
            }

            if (work.getGraphType().equals(GraphicsType.POLYGON)) {
                if (work.getFlightPlanWorkLongLatList() != null && work.getFlightPlanWorkLongLatList().size() > 0) {
                    // 将第一个坐标添加到列表末尾，形成闭合多边形
                    List<FlightPlanWorkLongLat> flightPlanWorkLongLatList = work.getFlightPlanWorkLongLatList();
                    flightPlanWorkLongLatList.add(flightPlanWorkLongLatList.get(0));
                    // 设置最后一个元素的groupNumber
                    int listSize = flightPlanWorkLongLatList.size();
                    flightPlanWorkLongLatList.get(listSize - 1).setSortNumber(listSize);
                    work.setFlightPlanWorkLongLatList(flightPlanWorkLongLatList);

                    work.getFlightPlanWorkLongLatList().forEach(e -> {
                        e.setGroupNumber(0);
                    });
                }
            }

        });
    }

    /**
     * 验证飞行计划
     *
     * @param flightPlan 范型 LongTermFlightPlan  NextDayFlightPlan DailyFlightPlan
     * @return
     */
    @Override
    public Object verifyLongTermFlightPlan(Object flightPlan) {

        if (flightPlan instanceof LongTermFlightPlan) {
            //验证长期计划
            LongTermFlightPlan longTermFlightPlan = (LongTermFlightPlan) flightPlan;
            //验证开始日期大于今日
            if (!longTermFlightPlan.getStartDate().equals(longTermFlightPlan.getEndDate()) && !DateUtils.getDate().equals(longTermFlightPlan.getStartDate()) && DateUtils.isBefore(longTermFlightPlan.getStartDate(), new Date())) {
                throw new ServiceException("开始日期不能小于今日");
            }

            //验证开始日期小于截止日期
            if (DateUtils.isBefore(longTermFlightPlan.getEndDate(), DateUtils.parseDate(longTermFlightPlan.getStartDate()))) {
                throw new ServiceException("截止日期不能小于开始日期");
            }
            verifyWorkParam(longTermFlightPlan.getFlightPlanWorkList());
            //度分秒转换为经纬度
            convertDMSToDDAndSetLatLong(longTermFlightPlan.getFlightPlanRouteList());

            //度分秒转换为经纬度
            convertDMSToDDAndSetLatLongWork(longTermFlightPlan.getFlightPlanWorkList());
            //修改验证
            if (longTermFlightPlan.getPlanModelAircraftList() == null || longTermFlightPlan.getPlanModelAircraftList().size() == 0) {
                throw new ServiceException("机型不能为空");
            }
            if (longTermFlightPlan.getFlightPlanAirportList() == null || longTermFlightPlan.getFlightPlanAirportList().size() == 0) {
                throw new ServiceException("机场不能为空");
            }
            //查询次日航班计划有相同区间的航班计划
            List<LongTermFlightPlan> longTermFlightPlanList = longTermFlightPlanMapper.selectLongTermFlightPlanByInterval(longTermFlightPlan);
            if (longTermFlightPlan.getId() != null) {
                longTermFlightPlanList = longTermFlightPlanList.stream().filter(column -> !column.getId().equals(longTermFlightPlan.getId())).collect(Collectors.toList());
            }
            if (longTermFlightPlanList != null && longTermFlightPlanList.size() > 0) {
                //验证计划名称 不能重复
                boolean isNameRepeat = longTermFlightPlanList.stream()
                        .anyMatch(r -> r.getName().equals(longTermFlightPlan.getName()));
                if (isNameRepeat) {
                    throw new ServiceException("计划名称已存在");
                }

                //把所有航线放入集合
                List<FlightPlanRoute> consolidatedFlightPlanRouteList = longTermFlightPlanList.stream()
                        .map(LongTermFlightPlan::getFlightPlanRouteList)
                        .filter(Objects::nonNull)  // 过滤出非空列表，避免NullPointerException
                        .filter(list -> !list.isEmpty())  // 过滤出非空列表，避免将空列表添加到结果中
                        .flatMap(List::stream)  // 展平操作，将多个列表合并到一个流中
                        .collect(Collectors.toList());  // 收集结果到新的列表中
                //把所有工作区放入集合
                List<FlightPlanWork> consolidatedFlightPlanWorkList = longTermFlightPlanList.stream()
                        .map(LongTermFlightPlan::getFlightPlanWorkList)
                        .filter(Objects::nonNull)  // 过滤出非空列表，避免NullPointerException
                        .filter(list -> !list.isEmpty())  // 过滤出非空列表，避免将空列表添加到结果中
                        .flatMap(List::stream)  // 展平操作，将多个列表合并到一个流中
                        .collect(Collectors.toList());  // 收集结果到新的列表中
                // 设置原圆形作业区的50个点
                setCircularLongLat(longTermFlightPlan.getFlightPlanWorkList());
                //验证航线冲突
                verifyRoute(longTermFlightPlan.getFlightPlanRouteList(), consolidatedFlightPlanRouteList, consolidatedFlightPlanWorkList);
                //验证作业区冲突
                verifyWork(longTermFlightPlan.getFlightPlanWorkList(), consolidatedFlightPlanRouteList, consolidatedFlightPlanWorkList);
                String routeConflictDescription = longTermFlightPlan.getFlightPlanRouteList().stream()
                        .filter(e -> !Strings.isBlank(e.getRouteConflictDescription()))
                        .map(e -> e.getRouteConflictDescription())
                        .collect(Collectors.joining());

                if (!Strings.isBlank(routeConflictDescription)) {
                    longTermFlightPlan.setRouteConflictDescription(routeConflictDescription);
                }
                String workConflictDescription = longTermFlightPlan.getFlightPlanWorkList().stream()
                        .filter(e -> !Strings.isBlank(e.getWorkConflictDescription()))
                        .map(e -> e.getWorkConflictDescription())
                        .collect(Collectors.joining());
                if (!Strings.isBlank(workConflictDescription)) {
                    longTermFlightPlan.setWorkConflictDescription(workConflictDescription);
                }
                if (Strings.isBlank(routeConflictDescription) && Strings.isBlank(workConflictDescription)) {
                    longTermFlightPlan.setVerify(true);
                }
            } else {
                longTermFlightPlan.setVerify(true);
            }
            return longTermFlightPlan;
        } else if (flightPlan instanceof NextDayFlightPlan) {
            //验证次日计划
            NextDayFlightPlan nextDayFlightPlan = (NextDayFlightPlan) flightPlan;

            //验证日期只能是明天
            if (!nextDayFlightPlan.getFlightDate().equals(DateUtils.parseDateToStr(DateUtils.YYYY_MM_DD, DateUtils.addDays(DateUtils.getNowDate(), 1)))) {
                throw new ServiceException("次日计划日期只能是明天");
            }

            verifyWorkParam(nextDayFlightPlan.getFlightPlanWorkList());
            //度分秒转换为经纬度
            convertDMSToDDAndSetLatLong(nextDayFlightPlan.getFlightPlanRouteList());


            convertDMSToDDAndSetLatLong(nextDayFlightPlan.getFlightPlanRouteList());


            if (nextDayFlightPlan.getPlanModelAircraftList() == null || nextDayFlightPlan.getPlanModelAircraftList().size() == 0) {
                throw new ServiceException("机型不能为空");
            }
            if (nextDayFlightPlan.getFlightPlanAirportList() == null || nextDayFlightPlan.getFlightPlanAirportList().size() == 0) {
                throw new ServiceException("机场不能为空");
            }
            //查询次日航班计划有相同区间的航班计划
            List<NextDayFlightPlan> nextDayFlightPlanList = nextDayFlightPlanMapper.selectNextDayFlightPlanByInterval(nextDayFlightPlan);
            if (nextDayFlightPlan.getId() != null) {
                nextDayFlightPlanList = nextDayFlightPlanList.stream().filter(column -> !column.getId().equals(nextDayFlightPlan.getId())).collect(Collectors.toList());
            }
            if (nextDayFlightPlanList != null && nextDayFlightPlanList.size() > 0) {

                //验证应答机编号
                //验证应答机编号
                //TODO 当日计划才有
//                boolean isAnsweringMachineCodeConflict = nextDayFlightPlanList.stream()
//                        .anyMatch(r -> r.getAnsweringMachineCode().equals(nextDayFlightPlan.getAnsweringMachineCode()));
//                if (isAnsweringMachineCodeConflict) {
//                    throw new ServiceException("应答机编号冲突");
//                }

                //验证无线电频率
//                boolean isRadioFrequencyConflict = nextDayFlightPlanList.stream()
//                        .anyMatch(r -> r.getRadioFrequency().equals(nextDayFlightPlan.getRadioFrequency()));
//                if (isRadioFrequencyConflict) {
//                    throw new ServiceException("无线电频率冲突");
//                }


                //把所有航线放入集合
                List<FlightPlanRoute> consolidatedFlightPlanRouteList = nextDayFlightPlanList.stream()
                        .map(NextDayFlightPlan::getFlightPlanRouteList)
                        .filter(Objects::nonNull)  // 过滤出非空列表，避免NullPointerException
                        .filter(list -> !list.isEmpty())  // 过滤出非空列表，避免将空列表添加到结果中
                        .flatMap(List::stream)  // 展平操作，将多个列表合并到一个流中
                        .collect(Collectors.toList());  // 收集结果到新的列表中
                //把所有工作区放入集合
                List<FlightPlanWork> consolidatedFlightPlanWorkList = nextDayFlightPlanList.stream()
                        .map(NextDayFlightPlan::getFlightPlanWorkList)
                        .filter(Objects::nonNull)  // 过滤出非空列表，避免NullPointerException
                        .filter(list -> !list.isEmpty())  // 过滤出非空列表，避免将空列表添加到结果中
                        .flatMap(List::stream)  // 展平操作，将多个列表合并到一个流中
                        .collect(Collectors.toList());  // 收集结果到新的列表中


                // 设置圆形作业区的50个点
                setCircularLongLat(nextDayFlightPlan.getFlightPlanWorkList());
                //验证航线冲突
                verifyRoute(nextDayFlightPlan.getFlightPlanRouteList(), consolidatedFlightPlanRouteList, consolidatedFlightPlanWorkList);
                //验证作业区冲突
                verifyWork(nextDayFlightPlan.getFlightPlanWorkList(), consolidatedFlightPlanRouteList, consolidatedFlightPlanWorkList);

                String routeConflictDescription = nextDayFlightPlan.getFlightPlanRouteList().stream()
                        .filter(e -> !Strings.isBlank(e.getRouteConflictDescription()))
                        .map(e -> e.getRouteConflictDescription())
                        .collect(Collectors.joining());

                if (!Strings.isBlank(routeConflictDescription)) {
                    nextDayFlightPlan.setRouteConflictDescription(routeConflictDescription);
                }

                String workConflictDescription = nextDayFlightPlan.getFlightPlanWorkList().stream()
                        .filter(e -> !Strings.isBlank(e.getWorkConflictDescription()))
                        .map(e -> e.getWorkConflictDescription())
                        .collect(Collectors.joining());
                if (!Strings.isBlank(workConflictDescription)) {
                    nextDayFlightPlan.setWorkConflictDescription(workConflictDescription);
                }

                if (Strings.isBlank(routeConflictDescription) && Strings.isBlank(workConflictDescription)) {
                    nextDayFlightPlan.setVerify(true);
                }
            } else {
                nextDayFlightPlan.setVerify(true);
            }
            return nextDayFlightPlan;
        } else if (flightPlan instanceof DailyFlightPlan) {
            //验证当日计划
            DailyFlightPlan dailyFlightPlan = (DailyFlightPlan) flightPlan;

            //验证日期只能是当天
            if (!DateUtils.getDate().equals(dailyFlightPlan.getFlightDate())) {
                throw new ServiceException("当日计划日期只能是当天");
            }


            verifyWorkParam(dailyFlightPlan.getFlightPlanWorkList());
            //度分秒转换为经纬度
            convertDMSToDDAndSetLatLong(dailyFlightPlan.getFlightPlanRouteList());

            convertDMSToDDAndSetLatLongWork(dailyFlightPlan.getFlightPlanWorkList());
            //修改验证
            if (dailyFlightPlan.getPlanModelAircraftList() == null || dailyFlightPlan.getPlanModelAircraftList().size() == 0) {
                throw new ServiceException("机型不能为空");
            }
            if (dailyFlightPlan.getFlightPlanAirportList() == null || dailyFlightPlan.getFlightPlanAirportList().size() == 0) {
                throw new ServiceException("机场不能为空");
            }
            //查询次日航班计划有相同区间的航班计划
            List<DailyFlightPlan> dailyFlightPlanList = dailyFlightPlanMapper.selectDailyFlightPlanByInterval(dailyFlightPlan);
            if (dailyFlightPlan.getId() != null) {
                dailyFlightPlanList = dailyFlightPlanList.stream().filter(column -> !column.getId().equals(dailyFlightPlan.getId())).collect(Collectors.toList());
            }
            if (dailyFlightPlanList != null && dailyFlightPlanList.size() > 0) {

                //验证应答机编号
                boolean isAnsweringMachineCodeConflict = dailyFlightPlanList.stream()
                        .anyMatch(r -> r.getAnsweringMachineCode().equals(dailyFlightPlan.getAnsweringMachineCode()));
                if (isAnsweringMachineCodeConflict) {
                    throw new ServiceException("应答机编号冲突");
                }

                //验证无线电频率
//                boolean isRadioFrequencyConflict = dailyFlightPlanList.stream()
//                        .anyMatch(r -> r.getRadioFrequency().equals(dailyFlightPlan.getRadioFrequency()));
//                if (isRadioFrequencyConflict) {
//                    throw new ServiceException("无线电频率冲突");
//                }

                //把所有航线放入集合
                List<FlightPlanRoute> consolidatedFlightPlanRouteList = dailyFlightPlanList.stream()
                        .map(DailyFlightPlan::getFlightPlanRouteList)
                        .filter(Objects::nonNull)  // 过滤出非空列表，避免NullPointerException
                        .filter(list -> !list.isEmpty())  // 过滤出非空列表，避免将空列表添加到结果中
                        .flatMap(List::stream)  // 展平操作，将多个列表合并到一个流中
                        .collect(Collectors.toList());  // 收集结果到新的列表中
                //把所有工作区放入集合
                List<FlightPlanWork> consolidatedFlightPlanWorkList = dailyFlightPlanList.stream()
                        .map(DailyFlightPlan::getFlightPlanWorkList)
                        .filter(Objects::nonNull)  // 过滤出非空列表，避免NullPointerException
                        .filter(list -> !list.isEmpty())  // 过滤出非空列表，避免将空列表添加到结果中
                        .flatMap(List::stream)  // 展平操作，将多个列表合并到一个流中
                        .collect(Collectors.toList());  // 收集结果到新的列表中

                // 设置圆形作业区的50个点
                setCircularLongLat(dailyFlightPlan.getFlightPlanWorkList());

                //验证航线冲突
                verifyRoute(dailyFlightPlan.getFlightPlanRouteList(), consolidatedFlightPlanRouteList, consolidatedFlightPlanWorkList);
                //验证作业区冲突
                verifyWork(dailyFlightPlan.getFlightPlanWorkList(), consolidatedFlightPlanRouteList, consolidatedFlightPlanWorkList);
                String routeConflictDescription = dailyFlightPlan.getFlightPlanRouteList().stream()
                        .filter(e -> !Strings.isBlank(e.getRouteConflictDescription()))
                        .map(e -> e.getRouteConflictDescription())
                        .collect(Collectors.joining());
                if (!Strings.isBlank(routeConflictDescription)) {
                    dailyFlightPlan.setRouteConflictDescription(routeConflictDescription);
                }
                String workConflictDescription = dailyFlightPlan.getFlightPlanWorkList().stream()
                        .filter(e -> !Strings.isBlank(e.getWorkConflictDescription()))
                        .map(e -> e.getWorkConflictDescription())
                        .collect(Collectors.joining());
                if (!Strings.isBlank(workConflictDescription)) {
                    dailyFlightPlan.setWorkConflictDescription(workConflictDescription);
                }
                if (Strings.isBlank(routeConflictDescription) && Strings.isBlank(workConflictDescription)) {
                    dailyFlightPlan.setVerify(true);
                }
            } else {
                dailyFlightPlan.setVerify(true);
            }
            return dailyFlightPlan;
        } else {
            throw new ServiceException("验证飞行计划失败 ，参数类型错误");
        }
    }

    @Override
    public int auditing(LongTermFlightPlan longTermFlightPlan) {
        selectLongTermFlightPlanById(longTermFlightPlan.getId());
        //根据id查询非草稿状态下的飞行计划
        LongTermFlightPlan selectLongTermFlightPlan = longTermFlightPlanMapper.selectLongTermFlightPlanByIdAndStatus(longTermFlightPlan.getId());
        if (selectLongTermFlightPlan != null) {
            int i = updateLongTermFlightPlan(longTermFlightPlan);
            return i; // 如果更新成功，返回结果
        } else {
            // 处理未找到的情况，例如返回特定的错误代码或抛出异常
            throw new ServiceException("未查询到飞行计划");
        }
    }

    //验证作业区冲突
    private void verifyWork
    (List<FlightPlanWork> nextDayFlightPlanWorkList, List<FlightPlanRoute> consolidatedFlightPlanRouteList, List<FlightPlanWork> consolidatedFlightPlanWorkList) {

        for (FlightPlanWork flightPlanWork : nextDayFlightPlanWorkList) {
            //作业区与航线对比
            for (FlightPlanRoute compRoute : consolidatedFlightPlanRouteList) {
                findWorkAndRouteConflicts(flightPlanWork, compRoute);
            }
            //作业区与作业区对比
            for (FlightPlanWork work : consolidatedFlightPlanWorkList) {
                findWorkAndWorkConflicts(work, flightPlanWork);
            }
        }
    }

    //作业区与工作区对比
    private void findWorkAndWorkConflicts(FlightPlanWork workA, FlightPlanWork workB) {
        List<FlightPlanWorkLongLat> flightPlanWorkLongLatListA = workA.getFlightPlanWorkLongLatList().stream().filter(r -> r.getGroupNumber() == 0).collect(Collectors.toList());
        List<FlightPlanWorkLongLat> flightPlanWorkLongLatListB = workB.getFlightPlanWorkLongLatList().stream().filter(r -> r.getGroupNumber() == 0).collect(Collectors.toList());

        //工作区A为线段 与 工作区B为线段
        if (!workA.getWorkType().equals(WorkType.REGION) && !workB.getWorkType().equals(WorkType.REGION)) {
            for (int i = 0; i < flightPlanWorkLongLatListA.size() - 1; i++) {
                Point3D l1p1 = longLatToPoint3D(flightPlanWorkLongLatListA.get(i));
                Point3D l1p2 = longLatToPoint3D(flightPlanWorkLongLatListA.get(i + 1));
                for (int j = 0; j < flightPlanWorkLongLatListB.size() - 1; j++) {
                    Point3D l2p1 = longLatToPoint3D(flightPlanWorkLongLatListB.get(j));
                    Point3D l2p2 = longLatToPoint3D(flightPlanWorkLongLatListB.get(j + 1));
                    Point3D intersection = LongLatUtil.findIntersection(l1p1, l1p2, l2p1, l2p2);
                    if (intersection != null) {
                        appendWorkAndWorkConflictInfo(workA, workB, l2p1, l2p2, intersection);
//                    return; // 找到冲突即可返回，避免重复检测
                    }
                }
            }
        }


        //工作区A为线段 与 工作区B为面
        if (!workA.getWorkType().equals(WorkType.REGION) && workB.getWorkType().equals(WorkType.REGION)) {
            for (int i = 0; i < flightPlanWorkLongLatListA.size() - 1; i++) {
                Point3D l1p1 = longLatToPoint3D(flightPlanWorkLongLatListA.get(i));
                Point3D l1p2 = longLatToPoint3D(flightPlanWorkLongLatListA.get(i + 1));
                //作业区航线与作业区面对比
                List<Point3D> point3DList = flightPlanWorkLongLatListB.stream()
                        .map(e -> new Point3D(e.getDoubleLongitude().doubleValue(),
                                e.getDoubleLatitude().doubleValue(),
                                e.getHeight() == null ? 0 : e.getHeight().doubleValue()))
                        .collect(Collectors.toList());
                boolean flag = LongLatUtil.findInSurface(l1p1, l1p2, point3DList, workB.getMinHeight().doubleValue(), workB.getMaxHeight().doubleValue());
                if (flag) {
                    appendWorkAndWorkConflictInfo(workA, workB, l1p1, l1p2, null);
                }
            }
        }

        //工作区A为面 与 工作区B为线段
        if (workA.getWorkType().equals(WorkType.REGION) && workB.getWorkType().equals(WorkType.ALONG)) {

            for (int i = 0; i < flightPlanWorkLongLatListB.size() - 1; i++) {
                Point3D l1p1 = longLatToPoint3D(flightPlanWorkLongLatListB.get(i));
                Point3D l1p2 = longLatToPoint3D(flightPlanWorkLongLatListB.get(i + 1));
                //作业区航线与作业区面对比
                List<Point3D> point3DList = flightPlanWorkLongLatListA.stream()
                        .map(e -> new Point3D(e.getDoubleLongitude().doubleValue(),
                                e.getDoubleLatitude().doubleValue(),
                                e.getHeight() == null ? 0 : e.getHeight().doubleValue()))
                        .collect(Collectors.toList());
                boolean flag = LongLatUtil.findInSurface(l1p1, l1p2, point3DList, flightPlanWorkLongLatListB.get(i).getHeight().doubleValue(), flightPlanWorkLongLatListB.get(i + 1).getHeight().doubleValue());
                if (flag) {
                    appendWorkAndWorkConflictInfo(workA, workB, l1p1, l1p2, null);
                }
            }


        }

        //工作区A为面 与 工作区B为面
        if (workA.getWorkType().equals(WorkType.REGION) && workB.getWorkType().equals(WorkType.REGION)) {

            //作业区航线与作业区面对比
            List<Point3D> point3DListA = flightPlanWorkLongLatListA.stream()
                    .map(e -> new Point3D(e.getDoubleLongitude().doubleValue(),
                            e.getDoubleLatitude().doubleValue(),
                            e.getHeight() == null ? 0 : e.getHeight().doubleValue()))
                    .collect(Collectors.toList());

            List<Point3D> point3DListB = flightPlanWorkLongLatListB.stream()
                    .map(e -> new Point3D(e.getDoubleLongitude().doubleValue(),
                            e.getDoubleLatitude().doubleValue(),
                            e.getHeight() == null ? 0 : e.getHeight().doubleValue()))
                    .collect(Collectors.toList());

            boolean flag = LongLatUtil.findSurfaceAndSurfacePoint(point3DListA, point3DListB, workA.getMinHeight().doubleValue(), workA.getMaxHeight().doubleValue(), workB.getMinHeight().doubleValue(), workB.getMaxHeight().doubleValue());
            if (flag) {
                appendWorkAndWorkConflictInfo(workA, workB, null, null, null);
            }
        }


    }


    // 验证航线冲突
    private void verifyRoute(List<FlightPlanRoute> nextDayFlightPlanRouteList,
                             List<FlightPlanRoute> consolidatedFlightPlanRouteList,
                             List<FlightPlanWork> consolidatedFlightPlanWorkList)
            throws ServiceException {
        for (FlightPlanRoute nextDayRoute : nextDayFlightPlanRouteList) {
            //航线与航线对比
            for (FlightPlanRoute compRoute : consolidatedFlightPlanRouteList) {
                findRouteAndRouteConflicts(nextDayRoute, compRoute);
            }
            //航线与工作区对比
            for (FlightPlanWork flightPlanWork : consolidatedFlightPlanWorkList) {
                findRouteAndWorkConflicts(nextDayRoute, flightPlanWork);
            }
        }
    }


    private void findWorkAndRouteConflicts(FlightPlanWork flightPlanWork, FlightPlanRoute nextDayRoute) {
        List<FlightPlanRouteLongLat> longLatsA = nextDayRoute.getFlightPlanRouteLongLatList();

        List<FlightPlanWorkLongLat> flightPlanWorkSurfaceList = flightPlanWork.getFlightPlanWorkLongLatList().stream().filter(r -> r.getGroupNumber() == 0).collect(Collectors.toList());

        if (flightPlanWork.getWorkType().equals(WorkType.REGION)) {
            //作业区航线与作业区面对比
            List<Point3D> point3DList = flightPlanWorkSurfaceList.stream()
                    .map(e -> new Point3D(e.getDoubleLongitude().doubleValue(),
                            e.getDoubleLatitude().doubleValue(),
                            e.getHeight() == null ? 0 : e.getHeight().doubleValue()))
                    .collect(Collectors.toList());

            for (int i = 0; i < longLatsA.size() - 1; i++) {
                Point3D l1p1 = longLatToPoint3D(longLatsA.get(i));
                Point3D l1p2 = longLatToPoint3D(longLatsA.get(i + 1));
                boolean flag = LongLatUtil.findInSurface(l1p1, l1p2, point3DList, flightPlanWork.getMinHeight().doubleValue(), flightPlanWork.getMaxHeight().doubleValue());
                if (flag) {
                    appendWorkAndRouteConflictInfo(flightPlanWork, nextDayRoute, l1p1, l1p2, null);
                }
            }
        } else {
            //作业区航线与作业区航线对比
            for (int i = 0; i < longLatsA.size() - 1; i++) {
                Point3D l1p1 = longLatToPoint3D(longLatsA.get(i));
                Point3D l1p2 = longLatToPoint3D(longLatsA.get(i + 1));
                for (int j = 0; j < flightPlanWorkSurfaceList.size() - 1; j++) {
                    Point3D l2p1 = longLatToPoint3D(flightPlanWorkSurfaceList.get(j));
                    Point3D l2p2 = longLatToPoint3D(flightPlanWorkSurfaceList.get(j + 1));
                    Point3D intersection = LongLatUtil.findIntersection(l1p1, l1p2, l2p1, l2p2);
                    if (intersection != null) {
                        appendWorkAndRouteConflictInfo(flightPlanWork, nextDayRoute, l1p1, l1p2, intersection);
//                    return; // 找到冲突即可返回，避免重复检测
                    }
                }
            }
        }
    }


    private void findRouteAndWorkConflicts(FlightPlanRoute nextDayRoute, FlightPlanWork flightPlanWork) {
        List<FlightPlanRouteLongLat> longLatsA = nextDayRoute.getFlightPlanRouteLongLatList();

        List<FlightPlanWorkLongLat> flightPlanWorkSurfaceList = flightPlanWork.getFlightPlanWorkLongLatList().stream().filter(r -> r.getGroupNumber() == 0).collect(Collectors.toList());

        if (flightPlanWork.getWorkType().equals(WorkType.REGION)) {
            //作业区航线与作业区面对比
            List<Point3D> point3DList = flightPlanWorkSurfaceList.stream()
                    .map(e -> new Point3D(e.getDoubleLongitude().doubleValue(),
                            e.getDoubleLatitude().doubleValue(),
                            e.getHeight() == null ? 0 : e.getHeight().doubleValue()))
                    .collect(Collectors.toList());

            for (int i = 0; i < longLatsA.size() - 1; i++) {
                Point3D l1p1 = longLatToPoint3D(longLatsA.get(i));
                Point3D l1p2 = longLatToPoint3D(longLatsA.get(i + 1));
                boolean flag = LongLatUtil.findInSurface(l1p1, l1p2, point3DList, flightPlanWork.getMinHeight().doubleValue(), flightPlanWork.getMaxHeight().doubleValue());
                if (flag) {
                    appendWorkConflictInfo(nextDayRoute, flightPlanWork, l1p1, l1p2, null);
                }
            }
        } else {
            //作业区航线与作业区航线对比
            for (int i = 0; i < longLatsA.size() - 1; i++) {
                Point3D l1p1 = longLatToPoint3D(longLatsA.get(i));
                Point3D l1p2 = longLatToPoint3D(longLatsA.get(i + 1));
                for (int j = 0; j < flightPlanWorkSurfaceList.size() - 1; j++) {
                    Point3D l2p1 = longLatToPoint3D(flightPlanWorkSurfaceList.get(j));
                    Point3D l2p2 = longLatToPoint3D(flightPlanWorkSurfaceList.get(j + 1));
                    Point3D intersection = LongLatUtil.findIntersection(l1p1, l1p2, l2p1, l2p2);
                    if (intersection != null) {
                        appendWorkConflictInfo(nextDayRoute, flightPlanWork, l1p1, l1p2, intersection);
//                    return; // 找到冲突即可返回，避免重复检测
                    }
                }
            }
        }
    }


    private void findRouteAndRouteConflicts(FlightPlanRoute routeA, FlightPlanRoute routeB) {
        List<FlightPlanRouteLongLat> longLatsA = routeA.getFlightPlanRouteLongLatList();
        List<FlightPlanRouteLongLat> longLatsB = routeB.getFlightPlanRouteLongLatList();

        for (int i = 0; i < longLatsA.size() - 1; i++) {
            Point3D l1p1 = longLatToPoint3D(longLatsA.get(i));
            Point3D l1p2 = longLatToPoint3D(longLatsA.get(i + 1));
            for (int j = 0; j < longLatsB.size() - 1; j++) {
                Point3D l2p1 = longLatToPoint3D(longLatsB.get(j));
                Point3D l2p2 = longLatToPoint3D(longLatsB.get(j + 1));
                Point3D intersection = LongLatUtil.findIntersection(l1p1, l1p2, l2p1, l2p2);
                if (intersection != null) {
                    appendRouteConflictInfo(routeA, routeB, l2p1, l2p2, intersection);
//                    return; // 找到冲突即可返回，避免重复检测
                }
            }
        }
    }


    /**
     * 写入作业区与作业区的冲突
     *
     * @param workA
     * @param workB
     * @param l2p1
     * @param l2p2
     * @param intersection
     */
    private void appendWorkAndWorkConflictInfo(FlightPlanWork workB, FlightPlanWork workA, Point3D l2p1, Point3D
            l2p2, Point3D intersection) {
        StringBuilder message = new StringBuilder(workA.getWorkConflictDescription() + "");
        if (workA.getWorkConflictDescription() == null) {
            message = new StringBuilder();
        }

        StringBuilder messageNew = new StringBuilder();
        messageNew.append("作业区：(").append(workA.getWorkName()).append(") 与 作业区：(计划ID:").append(workB.getFlightPlanId()).append(":")
                .append(workB.getWorkName()).append(") 存在冲突。");
        List<ConflictWorkVo> conflictWorkVoList = workA.getConflictWorkVoList();
        if (conflictWorkVoList == null) {
            conflictWorkVoList = new ArrayList<>();
        }
        ConflictWorkVo conflictWorkVo = new ConflictWorkVo();
        BeanUtils.copyProperties(workB, conflictWorkVo);
        if (null != intersection) {
            conflictWorkVo.setIntersectionLong(intersection.x);
            conflictWorkVo.setIntersectionLat(intersection.y);
            conflictWorkVo.setIntersectionHeight(intersection.z);
            messageNew.append("交点为：(" + intersection.x + "," + intersection.y + "," + intersection.z + ")");
        }
        if (message.indexOf(messageNew.toString()) == -1) {
            message.append(messageNew);
        }

        workA.setWorkConflictDescription(message.toString());
        conflictWorkVoList.add(conflictWorkVo);
        workA.setConflictWorkVoList(conflictWorkVoList);
    }


    /**
     * 写入作业区与航线的冲突
     *
     * @param flightPlanWork
     * @param route
     * @param l2p1
     * @param l2p2
     * @param intersection
     */
    private void appendWorkAndRouteConflictInfo(FlightPlanWork flightPlanWork, FlightPlanRoute route, Point3D
            l2p1, Point3D l2p2, Point3D intersection) {
        StringBuilder message = new StringBuilder(flightPlanWork.getWorkConflictDescription() + "");
        if (flightPlanWork.getWorkConflictDescription() == null) {
            message = new StringBuilder();
        }

        StringBuilder messageNew = new StringBuilder();
        messageNew.append("作业区：(").append(flightPlanWork.getWorkName()).append(") 与 航线：(计划ID:").append(route.getFlightPlanId()).append(":")
                .append(route.getRouteCode()).append(") 存在冲突。");

        List<ConflictRoute> conflictRouteList = flightPlanWork.getConflictRoute();
        if (conflictRouteList == null) {
            conflictRouteList = new ArrayList<>();
        }
        ConflictRoute conflictRoute = new ConflictRoute();
        BeanUtils.copyProperties(route, conflictRoute);
        if (null != intersection) {
            conflictRoute.setIntersectionLong(intersection.x);
            conflictRoute.setIntersectionLat(intersection.y);
            conflictRoute.setIntersectionHeight(intersection.z);
            messageNew.append("交点为：(" + intersection.x + "," + intersection.y + "," + intersection.z + ")");
        }
        if (message.indexOf(messageNew.toString()) == -1) {
            message.append(messageNew);
        }

        flightPlanWork.setWorkConflictDescription(message.toString());
        conflictRouteList.add(conflictRoute);
        flightPlanWork.setConflictRoute(conflictRouteList);
    }


    /**
     * 写入航线与航线的冲突
     *
     * @param routeA
     * @param routeB
     * @param l2p1
     * @param l2p2
     * @param intersection
     */
    private void appendRouteConflictInfo(FlightPlanRoute routeA, FlightPlanRoute routeB, Point3D l2p1, Point3D
            l2p2, Point3D intersection) {
        StringBuilder message = new StringBuilder(routeA.getRouteConflictDescription() + "");
        if (routeA.getRouteConflictDescription() == null) {
            message = new StringBuilder();
        }
        StringBuilder messageNew = new StringBuilder();
        messageNew.append("航线：(").append(routeA.getRouteCode()).append(") 与 航线：(计划ID:").append(routeB.getFlightPlanId()).append(":")
                .append(routeB.getRouteCode()).append(") 存在冲突;").append("交点为：(" + intersection.x + "," + intersection.y + "," + intersection.z + ")").toString();
        if (message.indexOf(messageNew.toString()) == -1) {
            message.append(messageNew);
        }
        routeA.setRouteConflictDescription(message.toString());
        List<ConflictRoute> conflictRouteList = routeA.getConflictRoute();
        if (conflictRouteList == null) {
            conflictRouteList = new ArrayList<>();
        }
        ConflictRoute conflictRoute = new ConflictRoute();
        conflictRoute.setRouteCode(routeB.getRouteCode());
        conflictRoute.setIntersectionLong(intersection.x);
        conflictRoute.setIntersectionLat(intersection.y);
        conflictRoute.setIntersectionHeight(intersection.z);
        conflictRoute.setFlightPlanRouteLongLatList(routeB.getFlightPlanRouteLongLatList());
        conflictRoute.setId(routeB.getId());
        conflictRouteList.add(conflictRoute);
        routeA.setConflictRoute(conflictRouteList);
    }

    /**
     * 写入航线与工作区冲突
     *
     * @param routeA
     * @param workA
     * @param l2p1
     * @param l2p2
     * @param intersection
     */
    private void appendWorkConflictInfo(FlightPlanRoute routeA, FlightPlanWork workA, Point3D l2p1, Point3D
            l2p2, Point3D intersection) {
        StringBuilder message = new StringBuilder(routeA.getRouteConflictDescription() + "");
        if (routeA.getRouteConflictDescription() == null) {
            message = new StringBuilder();
        }


        StringBuilder messageNew = new StringBuilder();

        messageNew.append("航线：(").append(routeA.getRouteCode()).append(") 与 作业区：(计划ID:").append(workA.getFlightPlanId()).append(":")
                .append(workA.getWorkName()).append(") 存在冲突。");
        List<ConflictWorkVo> conflictWorkVoList = routeA.getConflictWorkVoList();
        if (conflictWorkVoList == null) {
            conflictWorkVoList = new ArrayList<>();
        }
        ConflictWorkVo conflictWorkVo = new ConflictWorkVo();
        BeanUtils.copyProperties(workA, conflictWorkVo);
        if (null != intersection) {
            conflictWorkVo.setIntersectionLong(intersection.x);
            conflictWorkVo.setIntersectionLat(intersection.y);
            conflictWorkVo.setIntersectionHeight(intersection.z);

            messageNew.append("交点为：(" + intersection.x + "," + intersection.y + "," + intersection.z + ")");
        }
        if (message.indexOf(messageNew.toString()) == -1) {
            message.append(messageNew);
        }

        routeA.setRouteConflictDescription(message.toString());

        conflictWorkVoList.add(conflictWorkVo);
        routeA.setConflictWorkVoList(conflictWorkVoList);
    }


    private Point3D longLatToPoint3D(FlightPlanWorkLongLat longLat) {
        return new Point3D(
                longLat.getDoubleLongitude().doubleValue(),
                longLat.getDoubleLatitude().doubleValue(),
                longLat.getHeight().doubleValue()
        );
    }

    private Point3D longLatToPoint3D(FlightPlanRouteLongLat longLat) {
        Point3D point3D = new Point3D(longLat.getDoubleLongitude().doubleValue(),
                longLat.getDoubleLatitude().doubleValue(),
                longLat.getHeight().doubleValue());
        return point3D;
    }

    /**
     * 批量删除长期飞行计划
     * 软删除
     *
     * @param ids 需要删除的长期飞行计划主键
     * @return 结果
     */
    @Override
    public int deleteLongTermFlightPlanByIds(Long[] ids) {
        for (Long id : ids) {
            selectLongTermFlightPlanById(id);
        }
        return longTermFlightPlanMapper.deleteLongTermFlightPlanByIds(ids);
    }

    /**
     * 删除长期飞行计划信息
     *
     * @param id 长期飞行计划主键
     * @return 结果
     */
    @Override
    public int deleteLongTermFlightPlanById(Long id) {
        return longTermFlightPlanMapper.deleteLongTermFlightPlanById(id);
    }
}
