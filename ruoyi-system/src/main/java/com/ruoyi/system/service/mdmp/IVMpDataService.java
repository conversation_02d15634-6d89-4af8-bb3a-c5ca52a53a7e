package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.VMpData;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
public interface IVMpDataService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public VMpData selectVMpDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param vMpData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<VMpData> selectVMpDataList(VMpData vMpData);

    /**
     * 新增【请填写功能名称】
     *
     * @param vMpData 【请填写功能名称】
     * @return 结果
     */
    public int insertVMpData(VMpData vMpData);

    /**
     * 修改【请填写功能名称】
     *
     * @param vMpData 【请填写功能名称】
     * @return 结果
     */
    public int updateVMpData(VMpData vMpData);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteVMpDataByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteVMpDataById(Long id);

    double selectVMpDataByMeteInfo(MeteInfoParam meteInfoParam);

    List<VMpData> getList(MeteParam meteParam);
}
