package com.ruoyi.system.service.mdmp.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.dto.AddFlightDTO;
import com.ruoyi.system.domain.mdmp.dto.QueryFlightListDTO;
import com.ruoyi.system.domain.mdmp.dto.UpdateFlightDTO;
import com.ruoyi.system.domain.mdmp.dto.UpdateProgressDTO;
import com.ruoyi.system.domain.mdmp.vo.FlightQueueVO;
import com.ruoyi.system.mapper.mdmp.*;
import com.ruoyi.system.service.mdmp.FlightService;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * <AUTHOR>
 */
@Service
public class FlightServiceImpl implements FlightService {

    private final FlightMapper flightMapper;
    private final FlightLogMapper flightLogMapper;
    private final EnterLeavePortMapper enterLeavePortMapper;
    private final RunwayMapper runwayMapper;
    private final AddHeightMapper addHeightMapper;
    private final FlightCommandLogMapper flightCommandLogMapper;
    @Autowired
    @Qualifier("sendCommandJmsTemplate")
    private JmsTemplate sendCommandJmsTemplate;

    public FlightServiceImpl(FlightMapper flightMapper, FlightLogMapper flightLogMapper, EnterLeavePortMapper enterLeavePortMapper, RunwayMapper runwayMapper, AddHeightMapper addHeightMapper, FlightCommandLogMapper flightCommandLogMapper) {
        this.flightMapper = flightMapper;
        this.flightLogMapper = flightLogMapper;
        this.enterLeavePortMapper = enterLeavePortMapper;
        this.runwayMapper = runwayMapper;
        this.addHeightMapper = addHeightMapper;
        this.flightCommandLogMapper = flightCommandLogMapper;
    }

    @Override
    public PageCommonResult<List<Flight>> list(QueryFlightListDTO dto) {
        Flight flight = new Flight();
        BeanUtils.copyProperties(dto, flight);
        flight.setProgress("完成");
        PageMethod.startPage(dto.getPageNum(), dto.getPageSize());
        List<Flight> flights = flightMapper.selectFlightList(flight);
        PageInfo<Flight> info = new PageInfo<>(flights);
        return PageCommonResult.success(flights, info.getTotal());
    }

    @Override
    public CommonResult<String> insertBatch(List<Flight> flights) {
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> insert(AddFlightDTO dto) {
        if (dto.getIoSign() == 1) {
            if (!(StringUtils.hasText(dto.getHeightAttribute()) && StringUtils.hasText(dto.getDefaultHeight()))) {
                return CommonResult.error("默认高度属性和默认高度不能为空");
            }
        }
        Flight flight = new Flight();
        BeanUtils.copyProperties(dto, flight);
        flight.setProgress("等待区");
        flight.setCreateTime(DateUtils.getTime());
        int rows = flightMapper.insert(flight);
        if (rows > 0) {
            FlightLog flightLog = new FlightLog().setFlightId(flight.getId()).setLogMessage("新增一条航班" + flight.getCallSign())
                    .setCallSign(flight.getCallSign()).setCreateTime(DateUtils.getTime());
            flightLogMapper.insert(flightLog);
            //添加航班时新增进离场信息
            EnterLeavePort enterLeavePort = EnterLeavePort.setting(flight.getId());
            int insertRow = enterLeavePortMapper.insert(enterLeavePort);
            if (insertRow > 0 && dto.getIoSign() == 1) {
                AddHeight addHeight = new AddHeight().setEnterLeaveId(enterLeavePort.getId()).setAttribute(dto.getHeightAttribute())
                        .setHeight(dto.getDefaultHeight()).setHeightType(1).setKeep(0);
                addHeightMapper.insert(addHeight);
                //生成操作日志
                String msg = "新增高度：" + "上升-" + addHeight.getAttribute() + addHeight.getHeight() + "-" + "高度不保持" + ";";
                FlightLog flightLog2 = new FlightLog().setFlightId(flight.getId()).setLogMessage(msg)
                        .setCallSign(flight.getCallSign()).setCreateTime(DateUtils.getTime());
                flightLogMapper.insert(flightLog2);
                //生成航班指令消息
                insertFlightCommandLog(flight, flightLog2);
            }
        }
        return CommonResult.toResult(rows);
    }

    @Override
    public CommonResult<Flight> getInfo(Integer flightId) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        Flight flight = flightMapper.queryById(flightId);
        if (deptCodeList.contains(flight.getDeptCode())) {
            return CommonResult.success(flight);
        } else {
            throw new ServiceException("无权限访问该数据");
        }


    }

    @Override
    public CommonResult<String> update(UpdateFlightDTO dto) {
        if (dto.getIoSign() == 1) {
            if (!(StringUtils.hasText(dto.getHeightAttribute()) && StringUtils.hasText(dto.getDefaultHeight()))) {
                return CommonResult.error("默认高度属性和默认高度不能为空");
            }
        }
        Flight flight = new Flight();
        BeanUtils.copyProperties(dto, flight);
        flight.setUpdateTime(DateUtils.getTime());
        getInfo(dto.getId());
        int rows = flightMapper.update(flight);
        return CommonResult.toResult(rows);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> delete(Integer flightId) {

        Flight flight = getInfo(flightId).getData();
        flight.setFlightStatus(4);
        int row = flightMapper.update(flight);
        if (row > 0) {
            FlightLog flightLog = new FlightLog().setFlightId(flight.getId()).setLogMessage("删除一条航班" + flight.getCallSign())
                    .setCallSign(flight.getCallSign()).setCreateTime(DateUtils.getTime());
            flightLogMapper.insert(flightLog);
        }
        return CommonResult.success();
    }

    @Override
    public CommonResult<List<Flight>> flightList(List<String> deptCodeList) {
        List<Flight> flights = flightMapper.queryAll(deptCodeList);
        if (CollectionUtils.isEmpty(flights)) {
            return CommonResult.success(new ArrayList<>());
        }
        for (Flight flight : flights) {
            EnterLeavePort enterLeavePort = enterLeavePortMapper.queryByFlightId(flight.getId());
            if (Objects.isNull(enterLeavePort)) {
                enterLeavePort = new EnterLeavePort();
                enterLeavePort.setVip(0);
                enterLeavePort.setAirConflicts(0);
                enterLeavePort.setAlternate(0);
                enterLeavePort.setTurnBack(0);
                enterLeavePort.setReceivedMessage(0);
                enterLeavePort.setEstReport(0);
                enterLeavePort.setAirForceCoordination(0);
            }
            flight.setEnterLeavePort(enterLeavePort);
            String sortTime = flight.getIoSign() == 0 ? flight.getPlanArriveTime() : flight.getPlanDepartTime();
            flight.setSortTime(sortTime);
        }
        //排序
        flights.sort(Comparator.comparing(Flight::getSortTime));
        return CommonResult.success(flights);
    }

    @Override
    public CommonResult<FlightQueueVO> flightQueue(List<String> deptCodeList) {
        FlightQueueVO vo = new FlightQueueVO();
        List<Flight> flights = flightMapper.queryAll(deptCodeList);
        //分组
        Map<Integer, List<Flight>> map = flights.stream().collect(Collectors.groupingBy(Flight::getIoSign));
        List<Flight> inboundFlight = CollectionUtils.isEmpty(map.get(0)) ? new ArrayList<>() : map.get(0);
        List<Flight> outboundFlight = CollectionUtils.isEmpty(map.get(1)) ? new ArrayList<>() : map.get(0);
        //排序
        inboundFlight.sort(Comparator.comparing(Flight::getPlanArriveTime));
        outboundFlight.sort(Comparator.comparing(Flight::getPlanDepartTime));
        vo.setInboundFlight(inboundFlight);
        vo.setOutboundFlight(outboundFlight);
        return CommonResult.success(vo);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> updateProgress(UpdateProgressDTO dto) {

        Flight flight = getInfo(dto.getId()).getData();
        //飞机进入跑道
        String enterRunway = enterRunway(dto, flight);
        if (StringUtils.hasLength(enterRunway)) return CommonResult.error(enterRunway);
        //飞机离开跑道
        String leaveRunway = leaveRunway(dto, flight);
        if (StringUtils.hasLength(leaveRunway)) return CommonResult.error(leaveRunway);
        //添加操作日志
        String msg;
        if (Objects.equals(dto.getProgress(), "完成")) {
            msg = "管制结束";
        } else {
            msg = "进入" + dto.getProgress();
        }
        FlightLog flightLog = new FlightLog().setFlightId(dto.getId())
                .setCallSign(flight.getCallSign()).setLogMessage(msg).setCreateTime(DateUtils.getTime());
        flightLogMapper.insert(flightLog);
        //修改航班进或离场状态
        flight.setProgress(dto.getProgress());
        int rows = flightMapper.update(flight);
        //添加电子行程单的航班指令消息
        if (dto.getSendFlag()) {
            insertFlightCommandLog(flight, flightLog);
        }
        return CommonResult.toResult(rows);
    }

    private void insertFlightCommandLog(Flight flight, FlightLog flightLog) {
        FlightCommandLog flightCommandLog = new FlightCommandLog();
        flightCommandLog.setFlightId(flight.getId());
        flightCommandLog.setCommandLog(flightLog.getLogMessage());
        flightCommandLog.setCallSign(flight.getCallSign());
        flightCommandLog.setDelivered("已送达");
        flightCommandLog.setCreateDate(DateUtils.getDate());
        String time = DateUtils.getTime();
        flightCommandLog.setCreateTime(time);
        flightCommandLog.setUpdateTime(time);
        flightCommandLogMapper.insert(flightCommandLog);
        //发送给无人机系统
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("commandId", flightCommandLog.getId());
        jsonObject.put("commandLog", "("+flightCommandLog.getCallSign()+")"+flightCommandLog.getCommandLog());
        jsonObject.put("createDate", flightCommandLog.getCreateDate());
        jsonObject.put("createTime", flightCommandLog.getCreateTime());
        sendCommandJmsTemplate.convertAndSend("Command", JSON.toJSONString(jsonObject));
    }

    /**
     * 飞机离开跑道
     */
    private String leaveRunway(UpdateProgressDTO dto, Flight flight) {
        if ("跑道".equals(flight.getProgress()) && !"跑道".equals(dto.getProgress())) {
            if (Objects.isNull(dto.getRunwayId())) {
                return "未查询到跑道信息！";
            }
            Runway runway = runwayMapper.queryById(dto.getRunwayId());
            runway.setPlaneOccupy(0);
            runwayMapper.update(runway);
        }
        return null;
    }

    /**
     * 飞机进入跑道
     */
    private String enterRunway(UpdateProgressDTO dto, Flight flight) {
        if ("跑道".equals(dto.getProgress()) && !"跑道".equals(flight.getProgress())) {
            EnterLeavePort enterLeavePort = enterLeavePortMapper.selectByFlightId(dto.getId());
            if (Objects.isNull(enterLeavePort) || Objects.isNull(enterLeavePort.getRunwayId())) {
                return "进入跑道前需生成进离场信息！";
            }
            if (Objects.isNull(dto.getRunwayId())) {
                return "未查询到跑道信息！";
            }
            if (!Objects.equals(enterLeavePort.getRunwayId(), dto.getRunwayId())) {
                return "跑道信息不一致，请先提交跑道信息";
            }
            Runway runway = runwayMapper.queryById(dto.getRunwayId());
            if (runway.getPlaneOccupy() != 0) {
                return "当前跑道正被占用";
            }
            if (runway.getPersonOccupy() != 0) {
                return "当前跑道人员占用";
            }
            runway.setPlaneOccupy(1);
            runwayMapper.update(runway);
        }
        return null;
    }

}
