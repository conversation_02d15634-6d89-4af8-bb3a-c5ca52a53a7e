package com.ruoyi.system.service.mdmp.impl;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.mdmp.Runway;
import com.ruoyi.system.domain.mdmp.dto.UpdateRunwayStatusDTO;
import com.ruoyi.system.mapper.mdmp.RunwayMapper;
import com.ruoyi.system.service.mdmp.RunwayService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class RunwayServiceImpl implements RunwayService {

    private final RunwayMapper runwayMapper;
    @Override
    public CommonResult<String> updateRunwayStatus(UpdateRunwayStatusDTO dto) {
        Runway runway = runwayMapper.queryById(dto.getId());
        if (Objects.isNull(runway)) {
            return CommonResult.error("跑道不存在！");
        }
        if (runway.getPlaneOccupy() != 0) {
            return CommonResult.error("跑道已被飞机占用,不允许修改跑道状态！");
        }
        if (dto.getPersonOccupy() != 0 && runway.getPersonOccupy() != 0) {
            return CommonResult.error("跑道已人员占用,请不要重复操作！");
        }
        if (dto.getPersonOccupy() == 0 && runway.getPersonOccupy() == 0) {
            return CommonResult.error("跑道当前空闲,请不要重复操作！");
        }
        runway.setPersonOccupy(dto.getPersonOccupy());
        int rows = runwayMapper.update(runway);
        return CommonResult.toResult(rows);
    }

    @Override
    public CommonResult<List<Runway>> queryAll() {
        List<Runway> runways = runwayMapper.queryAll();
        return CommonResult.success(runways);
    }
}
