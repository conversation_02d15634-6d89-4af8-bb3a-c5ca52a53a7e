package com.ruoyi.system.service.mdmp;

import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.param.mdmp.LongTermFlightPlanListParam;

import java.util.List;

/**
 * 长期飞行计划Service接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface ILongTermFlightPlanService {
    /**
     * 查询长期飞行计划
     *
     * @param id 长期飞行计划主键
     * @return 长期飞行计划
     */
    public LongTermFlightPlan selectLongTermFlightPlanById(Long id);

    /**
     * 查询长期飞行计划列表
     *
     * @param longTermFlightPlan 长期飞行计划
     * @return 长期飞行计划集合
     */
    public List<LongTermFlightPlan> selectLongTermFlightPlanList(LongTermFlightPlanListParam longTermFlightPlan);

    /**
     * 新增长期飞行计划
     *
     * @param longTermFlightPlan 长期飞行计划
     * @return 结果
     */
    public int insertLongTermFlightPlan(LongTermFlightPlan longTermFlightPlan);

    /**
     * 修改长期飞行计划
     *
     * @param longTermFlightPlan 长期飞行计划
     * @return 结果
     */
    public int updateLongTermFlightPlan(LongTermFlightPlan longTermFlightPlan);

    /**
     * 批量删除长期飞行计划
     *
     * @param ids 需要删除的长期飞行计划主键集合
     * @return 结果
     */
    public int deleteLongTermFlightPlanByIds(Long[] ids);

    /**
     * 删除长期飞行计划信息
     *
     * @param id 长期飞行计划主键
     * @return 结果
     */
    public int deleteLongTermFlightPlanById(Long id);


    /**
     * 新增航班计划 关联表数据
     *航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划
     * @param id
     * @param planModelAircraftList 航班计划机型
     * @param flightPlanAirportList 航班计划机场
     * @param flightPlanRouteList   航班计划航线
     * @param flightPlanFileList    航班计划文件
     * @param flightPlanWorkList    航班计划工作区
     */
    void addAssociatedEntitiesLongTermFlightPlan(Integer planType, Long id, List<FlightPlanAircraftModel> planModelAircraftList, List<FlightPlanAirport> flightPlanAirportList, List<FlightPlanRoute> flightPlanRouteList, List<FlightPlanFile> flightPlanFileList, List<FlightPlanWork> flightPlanWorkList);


    /**
     * 删除飞行计划 关联信息
     *
     * @param id 长期飞行计划主键
     * @param i  航班计划类型 1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划
     * @return 结果
     */
    void deleteExistingAssociated(Long id, int i);

    /**
     * 校验飞行计划
     *
     * @param longTermFlightPlan
     * @return
     */
    Object verifyLongTermFlightPlan(Object longTermFlightPlan);


    /**
     * 审核长期计划
     * @param longTermFlightPlan
     * @return
     */
    int auditing(LongTermFlightPlan longTermFlightPlan);
}
