package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.WorkInBasis;

/**
 * 作业区内基础信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface IWorkInBasisService 
{
    /**
     * 查询作业区内基础信息
     * 
     * @param id 作业区内基础信息主键
     * @return 作业区内基础信息
     */
    public WorkInBasis selectWorkInBasisById(Long id);

    /**
     * 查询作业区内基础信息列表
     * 
     * @param workInBasis 作业区内基础信息
     * @return 作业区内基础信息集合
     */
    public List<WorkInBasis> selectWorkInBasisList(WorkInBasis workInBasis);

    /**
     * 新增作业区内基础信息
     * 
     * @param workInBasis 作业区内基础信息
     * @return 结果
     */
    public int insertWorkInBasis(List<WorkInBasis> workInBasisList,Long workBasisId);

    /**
     * 修改作业区内基础信息
     * 
     * @param workInBasis 作业区内基础信息
     * @return 结果
     */
    public int updateWorkInBasis(WorkInBasis workInBasis);

    /**
     * 批量删除作业区内基础信息
     * 
     * @param ids 需要删除的作业区内基础信息主键集合
     * @return 结果
     */
    public int deleteWorkInBasisByIds(Long[] ids);

    /**
     * 删除作业区内基础信息信息
     * 
     * @param id 作业区内基础信息主键
     * @return 结果
     */
    public int deleteWorkInBasisById(Long id);
}
