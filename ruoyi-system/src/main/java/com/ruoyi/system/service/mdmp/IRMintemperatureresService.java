package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RMintemperatureres;

/**
 * 最小温度Service接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface IRMintemperatureresService
{
    /**
     * 查询最小温度
     *
     * @param id 最小温度主键
     * @return 最小温度
     */
    public RMintemperatureres selectRMintemperatureresById(Long id);

    /**
     * 查询最小温度列表
     *
     * @param rMintemperatureres 最小温度
     * @return 最小温度集合
     */
    public List<RMintemperatureres> selectRMintemperatureresList(RMintemperatureres rMintemperatureres);

    /**
     * 新增最小温度
     *
     * @param rMintemperatureres 最小温度
     * @return 结果
     */
    public int insertRMintemperatureres(RMintemperatureres rMintemperatureres);

    /**
     * 修改最小温度
     *
     * @param rMintemperatureres 最小温度
     * @return 结果
     */
    public int updateRMintemperatureres(RMintemperatureres rMintemperatureres);

    /**
     * 批量删除最小温度
     *
     * @param ids 需要删除的最小温度主键集合
     * @return 结果
     */
    public int deleteRMintemperatureresByIds(Long[] ids);

    /**
     * 删除最小温度信息
     *
     * @param id 最小温度主键
     * @return 结果
     */
    public int deleteRMintemperatureresById(Long id);
}
