package com.ruoyi.system.service.mdmp.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.mdmp.RMaxtemperatureresMapper;
import com.ruoyi.system.domain.mdmp.RMaxtemperatureres;
import com.ruoyi.system.service.mdmp.IRMaxtemperatureresService;

/**
 * 最大温度Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class RMaxtemperatureresServiceImpl implements IRMaxtemperatureresService
{
    @Autowired
    private RMaxtemperatureresMapper rMaxtemperatureresMapper;

    /**
     * 查询最大温度
     *
     * @param id 最大温度主键
     * @return 最大温度
     */
    @Override
    public RMaxtemperatureres selectRMaxtemperatureresById(Long id)
    {
        return rMaxtemperatureresMapper.selectRMaxtemperatureresById(id);
    }

    /**
     * 查询最大温度列表
     *
     * @param rMaxtemperatureres 最大温度
     * @return 最大温度
     */
    @Override
    public List<RMaxtemperatureres> selectRMaxtemperatureresList(RMaxtemperatureres rMaxtemperatureres)
    {
        return rMaxtemperatureresMapper.selectRMaxtemperatureresList(rMaxtemperatureres);
    }

    /**
     * 新增最大温度
     *
     * @param rMaxtemperatureres 最大温度
     * @return 结果
     */
    @Override
    public int insertRMaxtemperatureres(RMaxtemperatureres rMaxtemperatureres)
    {
        return rMaxtemperatureresMapper.insertRMaxtemperatureres(rMaxtemperatureres);
    }

    /**
     * 修改最大温度
     *
     * @param rMaxtemperatureres 最大温度
     * @return 结果
     */
    @Override
    public int updateRMaxtemperatureres(RMaxtemperatureres rMaxtemperatureres)
    {
        return rMaxtemperatureresMapper.updateRMaxtemperatureres(rMaxtemperatureres);
    }

    /**
     * 批量删除最大温度
     *
     * @param ids 需要删除的最大温度主键
     * @return 结果
     */
    @Override
    public int deleteRMaxtemperatureresByIds(Long[] ids)
    {
        return rMaxtemperatureresMapper.deleteRMaxtemperatureresByIds(ids);
    }

    /**
     * 删除最大温度信息
     *
     * @param id 最大温度主键
     * @return 结果
     */
    @Override
    public int deleteRMaxtemperatureresById(Long id)
    {
        return rMaxtemperatureresMapper.deleteRMaxtemperatureresById(id);
    }
}
