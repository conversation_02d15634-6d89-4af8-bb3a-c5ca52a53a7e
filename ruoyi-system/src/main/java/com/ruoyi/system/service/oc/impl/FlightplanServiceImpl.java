package com.ruoyi.system.service.oc.impl;

import com.github.pagehelper.page.PageMethod;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.oc.dto.AddFlightPlanDTO;
import com.ruoyi.system.domain.oc.dto.QueryFlightPlanDTO;
import com.ruoyi.system.domain.oc.entity.Flightplan;
import com.ruoyi.system.mapper.oc.FlightplanMapper;
import com.ruoyi.system.service.oc.IFlightplanService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;


/**
 * 航班计划信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-09-22
 */
@Service
public class FlightplanServiceImpl implements IFlightplanService {

    @Resource
    private FlightplanMapper flightplanMapper;

    /**
     * 查询航班计划信息
     *
     * @param flightplanId 航班计划信息主键
     * @return 航班计划信息
     */
    @Override
    public Flightplan selectFlightplanByFlightplanId(Long flightplanId) {
        return flightplanMapper.selectFlightplanByFlightplanId(flightplanId);
    }

    /**
     * 查询航班计划信息列表
     *
     * @param dto 航班计划信息
     * @return 航班计划信息
     */
    @Override
    public List<Flightplan> selectFlightplanList(QueryFlightPlanDTO dto, String companyCode, Integer pageNum, Integer pageSize) {
        // 航班计划
        dto.setCompanyCode(companyCode);
        PageMethod.startPage(pageNum, pageSize);
        List<Flightplan> flightplanList = flightplanMapper.queryFlightPlanList(dto);
        return flightplanList;
    }

    @Override
    public List<Flightplan> selectFlightplan(QueryFlightPlanDTO dto, String companyCode) {
        dto.setCompanyCode(companyCode);
        List<Flightplan> flightplanList = flightplanMapper.queryFlightPlanList(dto);
        return flightplanList;
    }

    /**
     * 新增航班计划信息
     *
     * @param dto 航班计划信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int insertFlightplan(AddFlightPlanDTO dto, String companyCode, String createBy) {
        Flightplan flightplan = new Flightplan();
        BeanUtils.copyProperties(dto, flightplan);
        flightplan.setCompanyCode(companyCode);
        flightplan.setCreateBy(createBy);
        flightplan.setCreateTime(DateUtils.getNowDate());
        flightplan.setFlightStatus(1);
        flightplan.setFlyStatus(1);
        flightplan.setTaskProgress(1);
        if ((null == flightplan.getFlightDate()) && (null != flightplan.getFlightDateBatchStart() && null != flightplan.getFlightDateBatchEnd())) {
            List<Flightplan> flightplanList = new ArrayList<>();
            Date flightDate = flightplan.getFlightDateBatchStart();
            while (flightDate.compareTo(flightplan.getFlightDateBatchEnd()) == 0 || flightDate.compareTo(flightplan.getFlightDateBatchEnd()) == -1) {
                Flightplan flightplan1 = new Flightplan();
                BeanUtils.copyProperties(flightplan, flightplan1);
                flightplan1.setFlightDate(flightDate);
                flightplanList.add(flightplan1);
                Calendar c = Calendar.getInstance();
                c.setTime(flightDate);
                c.add(Calendar.DAY_OF_MONTH, 1);
                flightDate = c.getTime();
                flightplanMapper.insertFlightplan(flightplan1);
            }
            return flightplanList.size();
        } else {
            int rows = flightplanMapper.insertFlightplan(flightplan);
            return rows;
        }
    }

    /**
     * 修改航班计划信息
     *
     * @param flightplan 航班计划信息
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int updateFlightplan(Flightplan flightplan) {
        flightplan.setUpdateTime(DateUtils.getNowDate());
        flightplanMapper.deleteAircraftByAircraftId(flightplan.getFlightplanId());
        return flightplanMapper.updateFlightplan(flightplan);
    }

    /**
     * 批量删除航班计划信息
     *
     * @param flightplanIds 需要删除的航班计划信息主键
     * @return 结果
     */
    @Transactional(rollbackFor = Exception.class)
    @Override
    public int deleteFlightplanByFlightplanIds(Long[] flightplanIds) {
        flightplanMapper.deleteSortiesByflightplanIds(flightplanIds);
        return flightplanMapper.deleteFlightplanByFlightplanIds(flightplanIds);
    }

    /**
     * 删除航班计划信息信息
     *
     * @param flightplanId 航班计划信息主键
     * @return 结果
     */
    @Override
    public int deleteFlightplanByFlightplanId(Long flightplanId) {
        flightplanMapper.deleteAircraftByAircraftId(flightplanId);
        return flightplanMapper.deleteFlightplanByFlightplanId(flightplanId);
    }
}
