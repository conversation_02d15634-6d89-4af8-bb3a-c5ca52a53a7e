package com.ruoyi.system.service.mdmp.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.vo.MapDataLongLatVo;
import com.ruoyi.system.domain.mdmp.vo.QueryMapDataVo;
import com.ruoyi.system.domain.type.GraphicsType;
import com.ruoyi.system.mapper.mdmp.WorkInBasisLongLatMapper;
import com.ruoyi.system.mapper.mdmp.WorkInMapDataMapper;
import com.ruoyi.system.service.mdmp.IMapDataService;
import com.ruoyi.system.util.LongLatUtil;
import com.ruoyi.system.util.WorkBasisUtil;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.mdmp.WorkInBasisMapper;
import com.ruoyi.system.service.mdmp.IWorkInBasisService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

/**
 * 作业区内基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@Service
public class WorkInBasisServiceImpl implements IWorkInBasisService {
    @Resource
    private WorkInBasisMapper workInBasisMapper;

    @Resource
    private WorkInBasisLongLatMapper workInBasisLongLatMapper;
    @Resource
    private WorkInMapDataMapper workInMapDataMapper;
    @Resource
    private IMapDataService mapDataService;

    /**
     * 查询作业区内基础信息
     *
     * @param id 作业区内基础信息主键
     * @return 作业区内基础信息
     */
    @Override
    public WorkInBasis selectWorkInBasisById(Long id) {
        return workInBasisMapper.selectWorkInBasisById(id);
    }

    /**
     * 查询作业区内基础信息列表
     *
     * @param workInBasis 作业区内基础信息
     * @return 作业区内基础信息
     */
    @Override
    public List<WorkInBasis> selectWorkInBasisList(WorkInBasis workInBasis) {
        return workInBasisMapper.selectWorkInBasisList(workInBasis);
    }

    /**
     * 新增作业区内基础信息
     *
     * @return 结果
     */
    @Override
    @Transactional
    public int insertWorkInBasis(List<WorkInBasis> workInBasisList, Long workBasisId) {
        int result = 0;
        for (WorkInBasis workInBasis : workInBasisList) {
            workInBasis.setWorkBasisId(workBasisId);
            if (workInBasisMapper.insertWorkInBasis(workInBasis) > 0) {
                long id = workInBasis.getId();
                List<WorkInBasisLongLat> processedLongLatList = new ArrayList<>();
                if (workInBasis.getGraphType() == GraphicsType.CIRCULAR) {
                    double circleCenterLong = LongLatUtil.convertDMSToDD(workInBasis.getCircleCenterLong());
                    double circleCenterLat = LongLatUtil.convertDMSToDD(workInBasis.getCircleCenterLat());
                    double radius = workInBasis.getRadius().doubleValue();
                    processedLongLatList = WorkBasisUtil.getWorkInBasisLongLat(id, circleCenterLong, circleCenterLat, radius, 50);
                } else {
                    List<WorkInBasisLongLat> workInBasisLongLatList = workInBasis.getWorkInBasisLongLatList();
                    // 检查坐标列表是否为空
                    if (workInBasisLongLatList == null || workInBasisLongLatList.isEmpty()) {
                        throw new ServiceException("坐标不能为空");
                    }
                    for (int i = 0; i < workInBasisLongLatList.size(); i++) {
                        WorkInBasisLongLat longLat = workInBasisLongLatList.get(i);
                        longLat.setWorkInBasisId(id);
                        longLat.setSortNumber(i); // 直接使用索引
                        longLat.setDoubleLongitude(LongLatUtil.convertDMSToDD(longLat.getLongitude()));
                        longLat.setDoubleLatitude(LongLatUtil.convertDMSToDD(longLat.getLatitude()));
                        processedLongLatList.add(longLat); // 添加到新的列表中
                    }
                    // 将第一个坐标添加到列表末尾，形成闭合多边形
                    processedLongLatList.add(processedLongLatList.get(0));
                    // 设置最后一个元素的groupNumber
                    int listSize = processedLongLatList.size();
                    processedLongLatList.get(listSize - 1).setSortNumber(listSize);
                }

                if (!processedLongLatList.isEmpty()) {
                    workInBasisLongLatMapper.insertWorkBasisLongLatList(processedLongLatList);
                    result = 1; // 插入成功
                    //保存作业区内地图数据
//                    saveWorkInMapData(id, workInBasis.getMinHeight().intValue(), workInBasis.getMaxHeight().intValue(), processedLongLatList);
                }
            }
        }
        return result;
    }

    private void saveWorkInMapData(long id, Integer startHeight, Integer endHeight, List<WorkInBasisLongLat> processedLongLatList) {
        QueryMapDataVo queryMapDataVo = new QueryMapDataVo();
        queryMapDataVo.setStartHeight(startHeight);
        queryMapDataVo.setEndHeight(endHeight);
        queryMapDataVo.setMapDataLongLatVoList(transformationMapDataLongLatVo(processedLongLatList));
        //查询该高度区间内的地图数据
        List<MapData> mapDataIndexList = mapDataService.queryMapData(queryMapDataVo);
        if (mapDataIndexList != null && !mapDataIndexList.isEmpty()) {
            List<WorkInMapData> workMapDataList = mapDataIndexList.stream()
                    .map(map -> {
                        WorkInMapData mapData = new WorkInMapData();
                        mapData.setIndex(map.getIndex());
                        mapData.setWorkInId(id);
                        return mapData;
                    })
                    .collect(Collectors.toList());
            workInMapDataMapper.insertAllWorkInMapData(workMapDataList);
        }
    }

    private List<MapDataLongLatVo> transformationMapDataLongLatVo(List<WorkInBasisLongLat> processedLongLatList) {
        List<MapDataLongLatVo> mapDataLongLatVoList = new ArrayList<>();
        processedLongLatList.forEach(workBasisLongLat -> {
            MapDataLongLatVo mapDataLongLatVo = new MapDataLongLatVo();
            mapDataLongLatVo.setLatitude(BigDecimal.valueOf(workBasisLongLat.getDoubleLatitude()));
            mapDataLongLatVo.setLongitude(BigDecimal.valueOf(workBasisLongLat.getDoubleLongitude()));
            mapDataLongLatVo.setSortNumber(workBasisLongLat.getSortNumber());
            mapDataLongLatVoList.add(mapDataLongLatVo);
        });
        return mapDataLongLatVoList;
    }

    /**
     * 修改作业区内基础信息
     *
     * @param workInBasis 作业区内基础信息
     * @return 结果
     */
    @Override
    public int updateWorkInBasis(WorkInBasis workInBasis) {
        return workInBasisMapper.updateWorkInBasis(workInBasis);
    }

    /**
     * 批量删除作业区内基础信息
     *
     * @param ids 需要删除的作业区内基础信息主键
     * @return 结果
     */
    @Override
    public int deleteWorkInBasisByIds(Long[] ids) {
        return workInBasisMapper.deleteWorkInBasisByIds(ids);
    }

    /**
     * 删除作业区内基础信息信息
     *
     * @param id 作业区内基础信息主键
     * @return 结果
     */
    @Override
    public int deleteWorkInBasisById(Long id) {
        return workInBasisMapper.deleteWorkInBasisById(id);
    }
}
