package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.TopographicMapData;
import com.ruoyi.system.param.mdmp.MeteInfoParam;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
public interface ITopographicMapDataService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public TopographicMapData selectTopographicMapDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param topographicMapData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<TopographicMapData> selectTopographicMapDataList(TopographicMapData topographicMapData);

    /**
     * 新增【请填写功能名称】
     *
     * @param topographicMapData 【请填写功能名称】
     * @return 结果
     */
    public int insertTopographicMapData(TopographicMapData topographicMapData);

    /**
     * 修改【请填写功能名称】
     *
     * @param topographicMapData 【请填写功能名称】
     * @return 结果
     */
    public int updateTopographicMapData(TopographicMapData topographicMapData);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteTopographicMapDataByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteTopographicMapDataById(Long id);

    int editTopographic(List<TopographicMapData> param);

    int selectTopographicMapData(MeteInfoParam meteInfoParam);
}
