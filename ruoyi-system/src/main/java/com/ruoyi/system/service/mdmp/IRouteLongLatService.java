package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RouteLongLat;

/**
 * 航线经纬度Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface IRouteLongLatService 
{
    /**
     * 查询航线经纬度
     * 
     * @param id 航线经纬度主键
     * @return 航线经纬度
     */
    public RouteLongLat selectRouteLongLatById(Long id);

    /**
     * 查询航线经纬度列表
     * 
     * @param routeLongLat 航线经纬度
     * @return 航线经纬度集合
     */
    public List<RouteLongLat> selectRouteLongLatList(RouteLongLat routeLongLat);

    /**
     * 新增航线经纬度
     * 
     * @param routeLongLat 航线经纬度
     * @return 结果
     */
    public int insertRouteLongLat(RouteLongLat routeLongLat);

    /**
     * 修改航线经纬度
     * 
     * @param routeLongLat 航线经纬度
     * @return 结果
     */
    public int updateRouteLongLat(RouteLongLat routeLongLat);

    /**
     * 批量删除航线经纬度
     * 
     * @param ids 需要删除的航线经纬度主键集合
     * @return 结果
     */
    public int deleteRouteLongLatByIds(Long[] ids);

    /**
     * 删除航线经纬度信息
     * 
     * @param id 航线经纬度主键
     * @return 结果
     */
    public int deleteRouteLongLatById(Long id);
}
