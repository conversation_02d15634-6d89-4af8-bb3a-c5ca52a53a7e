package com.ruoyi.system.service.mdmp;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.mdmp.Runway;
import com.ruoyi.system.domain.mdmp.dto.UpdateRunwayStatusDTO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RunwayService {

    /**
     * 修改跑道状态
     * @param dto 跑道状态
     * @return 结果
     */
    CommonResult<String> updateRunwayStatus(UpdateRunwayStatusDTO dto);

    /**
     * 查询所有跑道
     * @return 结果
     */
    CommonResult<List<Runway>> queryAll();

}
