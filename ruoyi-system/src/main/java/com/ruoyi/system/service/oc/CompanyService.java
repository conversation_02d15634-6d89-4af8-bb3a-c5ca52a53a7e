package com.ruoyi.system.service.oc;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.oc.Company;
import com.ruoyi.system.domain.oc.CompanyMenu;
import com.ruoyi.system.domain.oc.dto.*;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/18 10:58
 * @mood 功能
 */
public interface CompanyService {
    /**
     * 查询列表
     * @param company 公司信息
     * @param pageNum 页数
     * @param pageSize 条数
     * @return 结果
     */
    PageCommonResult<List<Company>> selectList(Company company, Integer pageNum, Integer pageSize);

    /**
     * 查询列表
     * @param dto 公司ID
     * @return 结果
     */
    CommonResult<Company> companyInfo(CompanyDTO dto);

    /**
     * 添加公司信息
     * @param dto 公司信息
     * @return 结果
     */
    CommonResult<String> addCompany(AddCompanyDTO dto);

    /**
     * 修改公司信息
     * @param dto 公司信息
     * @return 结果
     */
    CommonResult<String> updateCompany(UpdateCompanyDTO dto);

    /**
     * 删除公司信息
     * @param dto 公司ID
     * @return 结果
     */
    CommonResult<String> deleteCompany(CompanyDTO dto);

    /**
     * 查询公司菜单权限
     * @param dto 公司代码
     * @return 结果
     */
    CommonResult<List<CompanyMenu>> selectCompanyMenu(CompanyMenuDTO dto);

    /**
     * 添加公司菜单权限
     * @param dto 添加公司菜单入参
     * @return 结果
     */
    CommonResult<String> addCompanyMenu(AddCompanyMenuDTO dto);

    /**
     * 添加公司管理员账号
     * @param dto 添加公司菜单入参
     * @return 结果
     */
    CommonResult<String> initCompanyAdmin(InitCompanyAdminDTO dto);
}
