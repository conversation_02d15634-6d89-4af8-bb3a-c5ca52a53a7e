package com.ruoyi.system.service.mdmp.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.vo.MapDataLongLatVo;
import com.ruoyi.system.domain.mdmp.vo.QueryMapDataVo;
import com.ruoyi.system.domain.type.GraphicsType;
import com.ruoyi.system.domain.type.PositiveOrNegativeType;
import com.ruoyi.system.domain.type.WorkType;
import com.ruoyi.system.mapper.mdmp.*;
import com.ruoyi.system.service.mdmp.IMapDataService;
import com.ruoyi.system.util.LongLatUtil;
import com.ruoyi.system.util.WorkBasisUtil;
import org.springframework.stereotype.Service;
import com.ruoyi.system.service.mdmp.IWorkBasisService;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 作业区基础信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@Service
public class WorkBasisServiceImpl implements IWorkBasisService {

    @Resource
    private WorkBasisMapper workBasisMapper;

    @Resource
    private WorkBasisLongLatMapper workBasisLongLatMapper;

    @Resource
    private WorkInBasisMapper workInBasisMapper;

    @Resource
    private WorkInBasisLongLatMapper workInBasisLongLatMapper;

    @Resource
    private WorkInBasisServiceImpl workInBasisServiceImpl;

    @Resource
    private MapDataMapper mapDataMapper;
    @Resource
    private IMapDataService mapDataService;
    @Resource
    private WorkMapDataMapper workMapDataMapper;
    @Resource
    private WorkInMapDataMapper workInMapDataMapper;

    /**
     * 查询作业区基础信息
     *
     * @param id 作业区基础信息主键
     * @return 作业区基础信息
     */
    @Override
    public WorkBasis selectWorkBasisById(Long id) {
        WorkBasis workBasis = workBasisMapper.selectWorkBasisById(id);
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        if (deptCodeList.contains(workBasis.getDeptCode())) {
            return workBasis;
        } else {
            throw new ServiceException("无权限访问该数据");
        }
    }

    /**
     * 查询作业区基础信息列表
     *
     * @param workBasis 作业区基础信息
     * @return 作业区基础信息
     */
    @Override
    public List<WorkBasis> selectWorkBasisList(WorkBasis workBasis) {
        return workBasisMapper.selectWorkBasisList(workBasis);
    }

    private boolean insertWorkBasisRecord(WorkBasis workBasis) {
        //判断新增还是修改
        int result;
        if (workBasis.getId() == null || workBasis.getId() == 0) {
            result = workBasisMapper.insertWorkBasis(workBasis);
        } else {
            result = workBasisMapper.updateWorkBasis(workBasis);
        }
        if (result <= 0) {
            return false;
        }
        return true;
    }

    /**
     * 新增作业区基础信息
     *
     * @param workBasis 作业区基础信息
     * @return 结果
     */
    @Override
    @Transactional
    public int insertWorkBasis(WorkBasis workBasis) {
        if (!insertWorkBasisRecord(workBasis)) {
            return 0; // 插入失败，直接返回
        }
        long id = workBasis.getId();
        List<WorkBasisLongLat> workBasisLongLatList = generateWorkBasisLongLatList(id, workBasis);
        if (!workBasisLongLatList.isEmpty()) {
            workBasisLongLatMapper.insertWorkBasisLongLatList(workBasisLongLatList);
            //新增作业区地图数据
//            saveWorkDataMap(id, workBasis.getMinHeight().intValue(), workBasis.getMaxHeight().intValue(), workBasisLongLatList);
        }
        insertWorkInBasis(workBasis);
        return 1; // 插入成功
    }

    private void saveWorkDataMap(Long workId, Integer startHeight, Integer endHeight, List<WorkBasisLongLat> workBasisLongLatList) {
        QueryMapDataVo queryMapDataVo = new QueryMapDataVo();
        queryMapDataVo.setStartHeight(startHeight);
        queryMapDataVo.setEndHeight(endHeight);
        queryMapDataVo.setMapDataLongLatVoList(transformationMapDataLongLatVo(workBasisLongLatList));
        //查询该高度区间内的地图数据
        List<MapData> mapDataIndexList = mapDataService.queryMapData(queryMapDataVo);
        if (mapDataIndexList != null && !mapDataIndexList.isEmpty()) {
            Set<WorkMapData> workMapDataList = mapDataIndexList.stream()
                    .map(map -> {
                        WorkMapData mapData = new WorkMapData();
                        mapData.setIndex(map.getIndex());
                        mapData.setWorkId(workId);
                        mapData.setValue(10);
                        return mapData;
                    })
                    .collect(Collectors.toSet());
            workMapDataMapper.insertAllWorkMapData(workMapDataList);
        }
    }

    private List<MapDataLongLatVo> transformationMapDataLongLatVo(List<WorkBasisLongLat> workBasisLongLatList) {
        List<MapDataLongLatVo> mapDataLongLatVoList = new ArrayList<>();
        workBasisLongLatList.forEach(workBasisLongLat -> {
            MapDataLongLatVo mapDataLongLatVo = new MapDataLongLatVo();
            mapDataLongLatVo.setLatitude(BigDecimal.valueOf(workBasisLongLat.getDoubleLatitude()));
            mapDataLongLatVo.setLongitude(BigDecimal.valueOf(workBasisLongLat.getDoubleLongitude()));
            mapDataLongLatVo.setSortNumber(workBasisLongLat.getSortNumber());
            mapDataLongLatVoList.add(mapDataLongLatVo);
        });
        return mapDataLongLatVoList;
    }


    public List<WorkBasisLongLat> processWorkBasisLongLatList(WorkBasis workBasis, long id, boolean isClose) {
        List<WorkBasisLongLat> workBasisLongLatList = workBasis.getWorkBasisLongLatList();
        List<WorkBasisLongLat> processedLongLatList = new ArrayList<>();

        // 检查坐标列表是否为空
        if (workBasisLongLatList == null || workBasisLongLatList.isEmpty()) {
            throw new ServiceException("坐标不能为空");
        }

        for (int i = 0; i < workBasisLongLatList.size(); i++) {
            WorkBasisLongLat longLat = workBasisLongLatList.get(i);
            longLat.setWorkBasisId(id);
            longLat.setSortNumber(i); // 直接使用索引作为排序号
            longLat.setDoubleLongitude(LongLatUtil.convertDMSToDD(longLat.getLongitude()));
            longLat.setDoubleLatitude(LongLatUtil.convertDMSToDD(longLat.getLatitude()));
            longLat.setGroupNumber(0);
            processedLongLatList.add(longLat); // 添加到新的列表中
        }
        if (isClose) {
            // 将第一个坐标添加到列表末尾，形成闭合多边形
            processedLongLatList.add(processedLongLatList.get(0));
            // 设置最后一个元素的groupNumber
            int listSize = processedLongLatList.size();
            processedLongLatList.get(listSize - 1).setSortNumber(listSize);
        }
        return processedLongLatList;
    }

    private List<WorkBasisLongLat> generateWorkBasisLongLatList(long id, WorkBasis workBasis) {
        List<WorkBasisLongLat> processedLongLatList;
        if (workBasis.getWorkType() == WorkType.REGION) {
            if (workBasis.getGraphType() == GraphicsType.CIRCULAR) {
                //根据圆心半径算出50个点
                double circleCenterLong = LongLatUtil.convertDMSToDD(workBasis.getCircleCenterLong());
                double circleCenterLat = LongLatUtil.convertDMSToDD(workBasis.getCircleCenterLat());
                double radius = workBasis.getRadius().doubleValue();
                double offset = 0;
                if (workBasis.getPositiveOrNegative() == PositiveOrNegativeType.POSITIVE) {
                    offset = workBasis.getWorkOffset().doubleValue();
                } else if (workBasis.getPositiveOrNegative() == PositiveOrNegativeType.NEGATIVE) {
                    offset = -workBasis.getWorkOffset().doubleValue();
                }
                processedLongLatList = WorkBasisUtil.getWorkBasisLongLat(id, circleCenterLong, circleCenterLat, radius, offset, 50);
            } else {
                processedLongLatList = processWorkBasisLongLatList(workBasis, id, true);
            }
        } else {
            processedLongLatList = processWorkBasisLongLatList(workBasis, id, false);
        }
        return processedLongLatList;

    }

    private void insertWorkInBasis(WorkBasis workBasis) {
        if (workBasis.getWorkInBasisList() != null && !workBasis.getWorkInBasisList().isEmpty()) {
            workInBasisServiceImpl.insertWorkInBasis(workBasis.getWorkInBasisList(), workBasis.getId());
        }
    }


    /**
     * 修改作业区基础信息
     *
     * @param workBasis 作业区基础信息
     * @return 结果
     */
    @Override
    @Transactional
    public int updateWorkBasis(WorkBasis workBasis) {
        //判断ID是否为空
        if (workBasis.getId() == null || workBasis.getId().equals("")) {
            throw new ServiceException("作业区ID不能为空");
        }
        selectWorkBasisById(workBasis.getId());
        deleteWorkBasisAndRelatedData(workBasis.getId());
        // 插入新的作业区
        return insertWorkBasis(workBasis);
    }

    /**
     * 批量删除作业区基础信息
     *
     * @param ids 需要删除的作业区基础信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkBasisByIds(Long[] ids) {
        int count = 0;
        // 删除作业区相关的作业经纬度数据
        for (Long id : ids) {
            selectWorkBasisById(id);
        }
        for (Long id : ids) {
            count += deleteWorkBasisById(id);

        }

        return count;
    }


    public void deleteWorkBasisAndRelatedData(long id) {
        // 删除作业区相关的作业经纬度数据
        workBasisLongLatMapper.deleteWorkBasisLongLatByWorkBasisId(id);

        //删除作业区地图数据
//        workMapDataMapper.delByWorkId(id);

        // 根据作业区ID查询所有作业区内集合
        WorkInBasis workInBasis = new WorkInBasis();
        workInBasis.setWorkBasisId(id);
        List<WorkInBasis> workInBasisList = workInBasisMapper.selectWorkInBasisList(workInBasis);
        // 如果存在作业区内作业，则获取它们的ID数组
        Long[] ids = new Long[workInBasisList.size()];
        for (int i = 0; i < workInBasisList.size(); i++) {
            ids[i] = workInBasisList.get(i).getId();
        }
        // 如果有作业区内作业ID，则删除作业区内作业经纬度
        if (ids.length > 0) {
            workInBasisLongLatMapper.deleteWorkInBasisLongLatByWorkInBasisIds(ids);

            //删除作业区内地图数据
//            workInMapDataMapper.delByWorkInId(ids);
        }

        // 删除作业区内作业
        workInBasisMapper.deleteWorkInBasisByWorkId(id);
    }

    /**
     * 删除作业区基础信息信息
     *
     * @param id 作业区基础信息主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteWorkBasisById(Long id) {
        deleteWorkBasisAndRelatedData(id);
        return workBasisMapper.deleteWorkBasisById(id);

    }
}
