package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RMaxtemperatureres;

/**
 * 最大温度Service接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface IRMaxtemperatureresService
{
    /**
     * 查询最大温度
     *
     * @param id 最大温度主键
     * @return 最大温度
     */
    public RMaxtemperatureres selectRMaxtemperatureresById(Long id);

    /**
     * 查询最大温度列表
     *
     * @param rMaxtemperatureres 最大温度
     * @return 最大温度集合
     */
    public List<RMaxtemperatureres> selectRMaxtemperatureresList(RMaxtemperatureres rMaxtemperatureres);

    /**
     * 新增最大温度
     *
     * @param rMaxtemperatureres 最大温度
     * @return 结果
     */
    public int insertRMaxtemperatureres(RMaxtemperatureres rMaxtemperatureres);

    /**
     * 修改最大温度
     *
     * @param rMaxtemperatureres 最大温度
     * @return 结果
     */
    public int updateRMaxtemperatureres(RMaxtemperatureres rMaxtemperatureres);

    /**
     * 批量删除最大温度
     *
     * @param ids 需要删除的最大温度主键集合
     * @return 结果
     */
    public int deleteRMaxtemperatureresByIds(Long[] ids);

    /**
     * 删除最大温度信息
     *
     * @param id 最大温度主键
     * @return 结果
     */
    public int deleteRMaxtemperatureresById(Long id);
}
