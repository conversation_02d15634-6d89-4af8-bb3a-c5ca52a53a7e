package com.ruoyi.system.service.mdmp.impl;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.mapper.mdmp.MapDataMapper;
import com.ruoyi.system.mapper.mdmp.RouteLongLatMapper;
import com.ruoyi.system.mapper.mdmp.RouteMapDataMapper;
import com.ruoyi.system.mapper.mdmp.RouteMapper;
import com.ruoyi.system.service.mdmp.IRouteService;
import com.ruoyi.system.util.LongLatUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 航线Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Service
public class RouteServiceImpl implements IRouteService {
    @Resource
    private RouteMapper routeMapper;

    @Resource
    private RouteLongLatMapper routeLongLatMapper;
    @Resource
    private MapDataMapper mapDataMapper;
    @Resource
    private RouteMapDataMapper routeMapDataMapper;

    /**
     * 查询航线
     *
     * @param id 航线主键
     * @return 航线
     */
    @Override
    public Route selectRouteById(Long id) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        //判断deptCodeList是否包含aircraft.getDeptCode()
        Route route = routeMapper.selectRouteById(id);
        if (deptCodeList.contains(route.getDeptCode())) {
            return route;
        } else {
            throw new ServiceException("无权限访问该数据");
        }
    }

    /**
     * 查询航线列表
     *
     * @param route 航线
     * @return 航线
     */
    @Override
    public List<Route> selectRouteList(Route route) {
        return routeMapper.selectRouteList(route);
    }

    /**
     * 新增航线
     *
     * @param route 航线
     * @return 结果
     */
    @Override
    @Transactional
    public int insertRoute(Route route) {
        if (routeMapper.insertRoute(route) > 0) {
            List<RouteLongLat> routeLongLatList = route.getRouteLongLatList();
            if (!routeLongLatList.isEmpty()) {
                // 遍历RouteLongLat列表，转换经纬度格式，设置路线ID和排序编号
                for (int i = 0; i < routeLongLatList.size(); i++) {
                    RouteLongLat routeLongLat = routeLongLatList.get(i);
                    routeLongLat.setRouteId(route.getId());

                    // 转换经纬度格式
                    double doubleLong = LongLatUtil.convertDMSToDD(routeLongLat.getLongitude());
                    double doubleLat = LongLatUtil.convertDMSToDD(routeLongLat.getLatitude());

                    // 设置转换后的经纬度
                    routeLongLat.setDoubleLongitude(doubleLong);
                    routeLongLat.setDoubleLatitude(doubleLat);

                    // 设置排序编号（索引）
                    routeLongLat.setSortNumber(i);
                }

                // 执行批量插入
                routeLongLatMapper.bulkInsertRouteLongLats(routeLongLatList);

                //保存航线5公里范围内的格子数据

//                saveRouteMapData(route.getId(), routeLongLatList);


            }
        }
        return 1;
    }


    private void delRouteMapData(Long routeId) {
        routeMapDataMapper.delByRouteId(routeId);
    }

//    private void saveRouteMapData(Long routeId, List<RouteLongLat> routeLongLatList) {
//        for (int i = 0; i < routeLongLatList.size() - 1; i++) {
//            //筛选出格子数据
//            List<MapData> mapDataList = mapDataMapper.selectMapDataByLongLatAndHeight(BigDecimal.valueOf(routeLongLatList.get(i).
//                            getDoubleLongitude()), BigDecimal.valueOf(routeLongLatList.get(i).getDoubleLatitude()), routeLongLatList.
//                            get(i).getHeight(), BigDecimal.valueOf(routeLongLatList.get(i + 1).getDoubleLongitude()),
//                    BigDecimal.valueOf(routeLongLatList.get(i + 1).getDoubleLatitude()), routeLongLatList.get(i + 1).getHeight());
//            List<MapData> idList = LongLatUtil.routeCompareMapData(routeLongLatList.get(i), routeLongLatList.get(i + 1), mapDataList, 5000);
//            //保存航线格子数据
//            List<RouteMapData> routeMapDataList = new ArrayList<>();
//            idList.forEach(e -> {
//                RouteMapData routeMapData = new RouteMapData();
//                routeMapData.setRouteId(routeId);
//                routeMapData.setIndex(e.getIndex());
//                routeMapData.setValue(10);
//                routeMapDataList.add(routeMapData);
//            });
//            int routeCount = routeMapDataMapper.insertAllRouteMapData(routeMapDataList);
//        }
//    }

    /**
     * 修改航线
     *
     * @param route 航线
     * @return 结果
     */
    @Override
    @Transactional
    public int updateRoute(Route route) {
        selectRouteById(route.getId());
        // 直接删除指定路线的经纬度数据，不管是否实际删除记录，均继续执行。
        routeLongLatMapper.deleteRouteLongLatByRouteId(route.getId());
        //删除航线地图数据
        delRouteMapData(route.getId());
        List<RouteLongLat> routeLongLatList = route.getRouteLongLatList();
        if (!routeLongLatList.isEmpty()) {
            // 转换经纬度格式并设置路线ID
            for (int i = 0; i < routeLongLatList.size(); i++) {
                RouteLongLat routeLongLat = routeLongLatList.get(i);
                routeLongLat.setRouteId(route.getId());
                double doubleLong = LongLatUtil.convertDMSToDD(routeLongLat.getLongitude());
                routeLongLat.setDoubleLongitude(doubleLong);
                double doubleLat = LongLatUtil.convertDMSToDD(routeLongLat.getLatitude());
                routeLongLat.setDoubleLatitude(doubleLat);
                routeLongLat.setSortNumber(i);
            }
            // 执行批量插入
            if (!routeLongLatList.isEmpty()) {
                routeLongLatMapper.bulkInsertRouteLongLats(routeLongLatList);
                // 保存航线格子数据
//                saveRouteMapData(route.getId(), routeLongLatList);
            }
        }

        // 更新航线信息
        return routeMapper.updateRoute(route);
    }

    /**
     * 批量删除航线
     *
     * @param ids 需要删除的航线主键
     * @return 结果
     */
    @Override
    public int deleteRouteByIds(Long[] ids) {
        return routeMapper.deleteRouteByIds(ids);
    }

    /**
     * 删除航线信息
     *
     * @param id 航线主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteRouteById(Long id) {
            selectRouteById(id);
            int longLatDeleted = routeLongLatMapper.deleteRouteLongLatByRouteId(id);
            //删除航线地图数据
//            delRouteMapData(id);
            if (longLatDeleted > 0) {
                return routeMapper.deleteRouteById(id);
            } else {
                throw new ServiceException("删除经纬度数据失败");
            }

    }
}
