package com.ruoyi.system.service.mdmp.impl;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.config.websocket.CustomWebSocketHandler;
import com.ruoyi.common.utils.Threads;
import com.ruoyi.system.domain.vo.SendEarlyWarningVo;
import com.ruoyi.system.param.mdmp.SendEarlyWarningParam;
import com.ruoyi.system.service.mdmp.EarlyWarningService;
import com.ruoyi.system.util.DeepSeekApiClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 预警信息Service业务层处理
 */
@Service
public class EarlyWarningServiceImpl implements EarlyWarningService {


    private static final Logger logger = LoggerFactory.getLogger(EarlyWarningServiceImpl.class);


    @Resource
    private CustomWebSocketHandler webSocketHandler;

    @Override
    @Async
    public void sendEarlyWarning(SendEarlyWarningParam param) throws Exception {

        logger.info("开始发送预警信息 ");
        logger.info(param.toString());


//        //调用接口
//        String result = DeepSeekApiClient.soloChat("");
//
//
//        //发送给前端
//        // 发送WebSocket消息
//        Map<String, Object> finalMap = new HashMap<>();
//        List<SendEarlyWarningVo> list = new ArrayList<>();
//        finalMap.put("earlyWarning", list);
//        webSocketHandler.broadcastMessage2(JSON.toJSONString(finalMap));
    }
}

