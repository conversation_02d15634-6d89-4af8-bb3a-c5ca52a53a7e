package com.ruoyi.system.service.mdmp;

import com.ruoyi.system.domain.mdmp.DailyFlightPlan;
import com.ruoyi.system.param.mdmp.DailyFlightPlanActualTimeParam;
import com.ruoyi.system.param.mdmp.DailyFlightPlanListParam;

import java.util.List;

/**
 * 当日计划飞行Service接口
 * 
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface IDailyFlightPlanService 
{
    /**
     * 查询当日计划飞行
     * 
     * @param id 当日计划飞行主键
     * @return 当日计划飞行
     */
    public DailyFlightPlan selectDailyFlightPlanById(Long id);

    /**
     * 查询当日计划飞行列表
     * 
     * @param param 当日计划飞行
     * @return 当日计划飞行集合
     */
    public List<DailyFlightPlan> selectDailyFlightPlanList(DailyFlightPlanListParam param);

    /**
     * 新增当日计划飞行
     * 
     * @param dailyFlightPlan 当日计划飞行
     * @return 结果
     */
    public int insertDailyFlightPlan(DailyFlightPlan dailyFlightPlan);

    /**
     * 修改当日计划飞行
     * 
     * @param dailyFlightPlan 当日计划飞行
     * @return 结果
     */
    public int updateDailyFlightPlan(DailyFlightPlan dailyFlightPlan);

    /**
     * 批量删除当日计划飞行
     * 
     * @param ids 需要删除的当日计划飞行主键集合
     * @return 结果
     */
    public int deleteDailyFlightPlanByIds(Long[] ids);

    /**
     * 删除当日计划飞行信息
     * 
     * @param id 当日计划飞行主键
     * @return 结果
     */
    public int deleteDailyFlightPlanById(Long id);

    /**
     * 验证当日飞行计划
     * @param dailyFlightPlan
     * @return
     */
    DailyFlightPlan verifyDailyFlightPlan(DailyFlightPlan dailyFlightPlan);


    /**
     * 审核当日计划飞行
     * @param dailyFlightPlan
     * @return
     */
    int auditing(DailyFlightPlan dailyFlightPlan);


    /**
     * 修改当日计划飞行 实际时间
     * @param param
     * @return
     */
    int updateDailyFlightPlanActualTime(DailyFlightPlanActualTimeParam param);
}
