package com.ruoyi.system.service.oc.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.oc.*;
import com.ruoyi.system.domain.oc.dto.UpdateWxUserDTO;
import com.ruoyi.system.domain.oc.dto.WxUserDTO;
import com.ruoyi.system.domain.oc.entity.Flightplan;
import com.ruoyi.system.domain.oc.vo.*;
import com.ruoyi.system.mapper.oc.*;
import com.ruoyi.system.service.oc.WxUserService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class WxUserServiceImpl implements WxUserService {
    private static final Logger logger = LoggerFactory.getLogger(WxUserServiceImpl.class);
    @Resource
    private WxRoleMapper wxRoleMapper;
    @Resource
    private WxMenuMapper wxMenuMapper;
    @Resource
    private WxUserMapper wxUserMapper;
    @Resource
    private WxUserRoleMapper wxUserRoleMapper;
    @Resource
    private WxUserMenuMapper wxUserMenuMapper;
    @Resource
    private WxQualificationMapper wxQualificationMapper;
    @Resource
    private FlightplanMapper flightPlanMapper;
    @Resource
    private CompanyMapper companyMapper;
    @Resource
    private RedisCache redisCache;

    @Override
    public CommonResult<Map<String, List<CrewUserVO>>> listUserByRole(String companyCode) {
        List<WxRole> wxRoleList = wxRoleMapper.selectWxRoleList();
        Map<String, List<CrewUserVO>> map = new HashMap<>();
        for (WxRole wxRole : wxRoleList) {
            List<WxUser> wxUserList = wxUserMapper.selectWxUsersByRoleId(wxRole.getRoleId(), companyCode);
            List<CrewUserVO> crewUserList = new ArrayList<>();
            for (WxUser user : wxUserList) {
                List<Flightplan> monthFlightPlans = flightPlanMapper.selectByUserIdAndFlightDateBetween(Long.toString(user.getUserId()), DateUtils.getMonthFirstDay(), DateUtils.getDate());
                List<Flightplan> weekFlightPlans = monthFlightPlans.stream()
                        .filter(flightPlan -> !flightPlan.getFlightDate().before(DateUtils.parseDate(DateUtils.getDateWeekAgo())) && !flightPlan.getFlightDate().after(DateUtils.getNowDate()))
                        .collect(Collectors.toList());
                CrewUserVO crewUser = CrewUserVO.setting(user, monthFlightPlans, weekFlightPlans);
                crewUserList.add(crewUser);
            }
            map.put(wxRole.getRoleKey(), crewUserList);
        }
        return CommonResult.success(map);
    }

    @Override
    public CommonResult<List<CrewVO>> getUserArr(Long[] userIds, Long flightPlanId) {
        List<WxUser> wxUserList = wxUserMapper.selectWxUserByIds(userIds);
        List<CrewVO> voList = new ArrayList<>();
        for (WxUser wxUser : wxUserList) {
            WxQualification wxQualification = wxQualificationMapper.selectByUserId(wxUser.getUserId());
            CrewVO wxUserVO = CrewVO.setting(wxUser, wxQualification);
            voList.add(wxUserVO);
        }
        //设置航班角色
        for (CrewVO vo : voList) {
            Long userId = vo.getUserId();
            Flightplan flightplan = flightPlanMapper.selectFlightplanByFlightplanId(flightPlanId);
            String capId = flightplan.getCaptainUserId();
            String copId = flightplan.getCopilotUserId();
            String mainId = flightplan.getMaintenanceId();
            String safeId = flightplan.getSafetyOfficerId();
            String[] cap = capId.split(",");
            String[] cop = copId.split(",");
            String[] main = mainId.split(",");
            String[] safe = safeId.split(",");
            for (String s : cap) {
                if (userId.equals(Long.parseLong(s))) {
                    vo.setRoleType(1);
                }
            }
            for (String s : cop) {
                if (userId.equals(Long.parseLong(s))) {
                    vo.setRoleType(2);
                }
            }
            //机务和安全员都属于机务
            for (String s : main) {
                if (userId.equals(Long.parseLong(s))) {
                    vo.setRoleType(4);
                }
            }
            for (String s : safe) {
                if (userId.equals(Long.parseLong(s))) {
                    vo.setRoleType(4);
                }
            }
        }
        return CommonResult.success(voList);
    }

    @Override
    public CommonResult<CrewVO> getInfo(Long userId, String roleKey) {
        WxUser wxUser = wxUserMapper.selectByUserId(userId);
        WxQualification wxQualification = wxQualificationMapper.selectByUserId(wxUser.getUserId());
        CrewVO pilotInfo = CrewVO.setting(wxUser, wxQualification);
        pilotInfo.setRoleKey(roleKey);
        return CommonResult.success(pilotInfo);
    }

    @Override
    public PageCommonResult<List<WxUserVO>> list(WxUser wxUser, String companyCode, Integer pageNum, Integer pageSize) {
        Integer companyType = companyMapper.selectByCompanyCode(companyCode).getCompanyType();
        //通航公司查询对应航司下的用户
        if (companyType == 1) {
            wxUser.setCompanyCode(companyCode);
        }
        PageMethod.startPage(pageNum, pageSize);
        List<WxUser> wxUserList = wxUserMapper.selectList(wxUser);
        PageInfo<WxUser> info = new PageInfo<>(wxUserList);
        List<WxUserVO> wxUserVO = WxUserVO.setting(wxUserList);
        return PageCommonResult.success(wxUserVO, info.getTotal());
    }

    @Override
    public CommonResult<WxUserInfoVO> selectOne(WxUserDTO dto) {
        WxUser wxUser = wxUserMapper.selectByUserId(dto.getUserId());
        List<WxRoleVO> wxRoles = wxRoleMapper.selectRoleByUserId(dto.getUserId());
        List<WxMenu> wxMenus = wxMenuMapper.selectMenuByUserId(dto.getUserId());
        WxUserInfoVO wxUserVO = WxUserInfoVO.setting(wxUser, wxRoles, wxMenus);
        return CommonResult.success(wxUserVO);
    }

    private void insertWxUserMenu(UpdateWxUserDTO dto) {
        Long[] menuIds = dto.getMenuIds();
        if (menuIds == null || menuIds.length == 0) {
            return;
        }
        List<WxUserMenu> userMenuList = new ArrayList<>(menuIds.length); // 预分配列表大小以提高性能
        for (Long menuId : menuIds) {
            WxUserMenu wxUserMenu = new WxUserMenu();
            wxUserMenu.setMenuId(menuId);
            wxUserMenu.setUserId(dto.getUserId());
            userMenuList.add(wxUserMenu);
        }

        wxUserMenuMapper.batchInsert(userMenuList);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> updateWxUser(UpdateWxUserDTO dto) {

        //删除微信用户角色关联
        wxUserRoleMapper.deleteByUserId(dto.getUserId());
        wxUserMenuMapper.deleteByUserId(dto.getUserId());
        //新增微信用户角色关联
        insertWxUserRole(dto);
        insertWxUserMenu(dto);
        //修改微信用户
        WxUser wxUser = new WxUser();
        wxUser.setUserId(dto.getUserId());
        wxUser.setUserStatus(dto.getUserStatus());
        wxUser.setFlyTime(dto.getFlyTime() * 60);
        wxUser.setFlyNumber(dto.getFlyNumber());
        int row = wxUserMapper.updateWxUser(wxUser);
        return CommonResult.toResult(row);
    }

    @Override
    public void getAvatar(HttpServletResponse response, String openId, String fileNameAndSuffix) {
        String fileAddress = redisCache.getCacheObject("avatar:" + openId);
        if (StringUtils.hasLength(fileAddress)) {
            response.setContentType("image/" + fileNameAndSuffix.substring(fileNameAndSuffix.indexOf(".") + 1));
            File file = new File(fileAddress);
            response.addHeader("Content-Length", "" + file.length());
            try (InputStream is = Files.newInputStream(file.toPath()); OutputStream os = response.getOutputStream()) {
                IOUtils.copy(is, os);
            } catch (IOException e) {
                logger.error("获取图片异常[{}]", e.getMessage());
            }
        }
    }

    private void insertWxUserRole(UpdateWxUserDTO dto) {
        Long[] roleIds = dto.getRoleIds();
        List<WxUserRole> list = new ArrayList<>();
        for (Long roleId : roleIds) {
            WxUserRole userRole = new WxUserRole();
            userRole.setRoleId(roleId);
            userRole.setUserId(dto.getUserId());
            list.add(userRole);
        }
        if (!CollectionUtils.isEmpty(list)) {
            wxUserRoleMapper.batchInsert(list);
        }
    }
}
