package com.ruoyi.system.service.mdmp;

import java.util.List;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.system.domain.mdmp.Airport;

/**
 * 机场Service接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface IAirportService
{
    /**
     * 查询机场
     *
     * @param id 机场主键
     * @return 机场
     */
    public Airport selectAirportById(Long id);

    /**
     * 查询机场列表
     *
     * @param airport 机场
     * @return 机场集合
     */
    public List<Airport> selectAirportList(Airport airport);

    /**
     * 新增机场
     *
     * @param airport 机场
     * @return 结果
     */
    public int insertAirport(Airport airport);

    /**
     * 修改机场
     *
     * @param airport 机场
     * @return 结果
     */
    public int updateAirport(Airport airport);

    /**
     * 批量删除机场
     *
     * @param ids 需要删除的机场主键集合
     * @return 结果
     */
    public int deleteAirportByIds(Long[] ids);

    /**
     * 删除机场信息
     *
     * @param id 机场主键
     * @return 结果
     */
    public int deleteAirportById(Long id);
}
