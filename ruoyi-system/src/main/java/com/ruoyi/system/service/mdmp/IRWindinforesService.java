package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RWindinfores;

/**
 * 风Service接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface IRWindinforesService
{
    /**
     * 查询风
     *
     * @param id 风主键
     * @return 风
     */
    public RWindinfores selectRWindinforesById(Long id);

    /**
     * 查询风列表
     *
     * @param rWindinfores 风
     * @return 风集合
     */
    public List<RWindinfores> selectRWindinforesList(RWindinfores rWindinfores);

    /**
     * 新增风
     *
     * @param rWindinfores 风
     * @return 结果
     */
    public int insertRWindinfores(RWindinfores rWindinfores);

    /**
     * 修改风
     *
     * @param rWindinfores 风
     * @return 结果
     */
    public int updateRWindinfores(RWindinfores rWindinfores);

    /**
     * 批量删除风
     *
     * @param ids 需要删除的风主键集合
     * @return 结果
     */
    public int deleteRWindinforesByIds(Long[] ids);

    /**
     * 删除风信息
     *
     * @param id 风主键
     * @return 结果
     */
    public int deleteRWindinforesById(Long id);
}
