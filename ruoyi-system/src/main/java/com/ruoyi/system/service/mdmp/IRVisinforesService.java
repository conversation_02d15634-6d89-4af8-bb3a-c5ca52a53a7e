package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RVisinfores;

/**
 * 能见度Service接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface IRVisinforesService
{
    /**
     * 查询能见度
     *
     * @param id 能见度主键
     * @return 能见度
     */
    public RVisinfores selectRVisinforesById(Long id);

    /**
     * 查询能见度列表
     *
     * @param rVisinfores 能见度
     * @return 能见度集合
     */
    public List<RVisinfores> selectRVisinforesList(RVisinfores rVisinfores);

    /**
     * 新增能见度
     *
     * @param rVisinfores 能见度
     * @return 结果
     */
    public int insertRVisinfores(RVisinfores rVisinfores);

    /**
     * 修改能见度
     *
     * @param rVisinfores 能见度
     * @return 结果
     */
    public int updateRVisinfores(RVisinfores rVisinfores);

    /**
     * 批量删除能见度
     *
     * @param ids 需要删除的能见度主键集合
     * @return 结果
     */
    public int deleteRVisinforesByIds(Long[] ids);

    /**
     * 删除能见度信息
     *
     * @param id 能见度主键
     * @return 结果
     */
    public int deleteRVisinforesById(Long id);
}
