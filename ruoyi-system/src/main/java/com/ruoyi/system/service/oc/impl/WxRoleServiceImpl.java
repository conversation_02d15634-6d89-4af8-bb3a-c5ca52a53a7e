package com.ruoyi.system.service.oc.impl;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.oc.WxRole;
import com.ruoyi.system.mapper.oc.WxRoleMapper;
import com.ruoyi.system.service.oc.WxRoleService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class WxRoleServiceImpl implements WxRoleService {
    @Resource
    private WxRoleMapper wxRoleMapper;

    @Override
    public CommonResult<List<WxRole>> list() {
        List<WxRole> wxRoleList = wxRoleMapper.selectWxRoleList();
        return CommonResult.success(wxRoleList);
    }
}
