package com.ruoyi.system.service.mdmp;

import com.ruoyi.system.domain.mdmp.AircraftAirspace;

import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
public interface IAircraftAirspaceService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public AircraftAirspace selectAircraftAirspaceById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param aircraftAirspace 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<AircraftAirspace> selectAircraftAirspaceList(AircraftAirspace aircraftAirspace);

    /**
     * 新增【请填写功能名称】
     *
     * @param aircraftAirspace 【请填写功能名称】
     * @return 结果
     */
    public int insertAircraftAirspace(AircraftAirspace aircraftAirspace);

    /**
     * 修改【请填写功能名称】
     *
     * @param aircraftAirspace 【请填写功能名称】
     * @return 结果
     */
    public int updateAircraftAirspace(AircraftAirspace aircraftAirspace);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteAircraftAirspaceByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteAircraftAirspaceById(Long id);
}
