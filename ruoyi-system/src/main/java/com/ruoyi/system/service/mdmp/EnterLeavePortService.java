package com.ruoyi.system.service.mdmp;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.mdmp.EnterLeavePort;
import com.ruoyi.system.domain.mdmp.dto.UpdateEnterLeavePortDTO;

/**
 * <AUTHOR>
 */
public interface EnterLeavePortService {
    /**
     * 根据航班ID查询进离场区详情
     *
     * @param flightId 航班ID
     * @return 结果
     */
    CommonResult<EnterLeavePort> getInfo(Integer flightId);

    /**
     * 添加进离场区信息
     *
     * @param enterLeavePort 进离场区信息
     * @return 结果
     */
    CommonResult<String> insert(EnterLeavePort enterLeavePort);

    /**
     * 修改进离场区信息
     *
     * @param enterLeavePort 进离场区信息
     * @return 结果
     */
    CommonResult<String> update(UpdateEnterLeavePortDTO dto);
}
