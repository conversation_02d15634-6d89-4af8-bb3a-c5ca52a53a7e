package com.ruoyi.system.service.mdmp.impl;

import java.util.ArrayList;
import java.util.List;

import com.ruoyi.system.domain.mdmp.MpData;
import com.ruoyi.system.domain.mdmp.TemperatureMpData;
import com.ruoyi.system.domain.mdmp.VisibilityMpData;
import com.ruoyi.system.mapper.mdmp.TemperatureMpDataMapper;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;
import com.ruoyi.system.service.mdmp.ITemperatureMpDataService;
import com.ruoyi.system.util.MeteUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class TemperatureMpDataServiceImpl implements ITemperatureMpDataService {

    @Resource
    private TemperatureMpDataMapper temperatureMpDataMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public TemperatureMpData selectTemperatureMpDataById(Long id) {
        return temperatureMpDataMapper.selectTemperatureMpDataById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param temperatureMpData 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<TemperatureMpData> selectTemperatureMpDataList(TemperatureMpData temperatureMpData) {
        return temperatureMpDataMapper.selectTemperatureMpDataList(temperatureMpData);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param temperatureMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertTemperatureMpData(TemperatureMpData temperatureMpData) {
        return temperatureMpDataMapper.insertTemperatureMpData(temperatureMpData);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param temperatureMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateTemperatureMpData(TemperatureMpData temperatureMpData) {
        return temperatureMpDataMapper.updateTemperatureMpData(temperatureMpData);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTemperatureMpDataByIds(Long[] ids) {
        return temperatureMpDataMapper.deleteTemperatureMpDataByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteTemperatureMpDataById(Long id) {
        return temperatureMpDataMapper.deleteTemperatureMpDataById(id);
    }

    @Override
    public double selectTemperatureMpDataByMeteInfo(MeteInfoParam meteInfoParam) {
//        TemperatureMpData temperatureMpData = temperatureMpDataMapper.selectTemperatureMpDataByMeteInfo(meteInfoParam);
//        if (temperatureMpData != null) {
//            String dataValue = temperatureMpData.getDataValue();
//            return MeteUtil.getDateValue(dataValue, meteInfoParam.getLongitude());
//        }
        return 0;
    }

    @Override
    public List<Double> getList(MeteParam meteParam) {
        List<Double> list = temperatureMpDataMapper.getList(meteParam);
        return list;
    }
}
