package com.ruoyi.system.service.mdmp.impl;

import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import com.ruoyi.system.domain.mdmp.vo.MapDataVo;
import com.ruoyi.system.domain.type.*;
import com.ruoyi.system.job.AsynchronousConsumer;
import com.ruoyi.system.mapper.mdmp.*;
import com.ruoyi.system.param.mdmp.SendEarlyWarningParam;
import com.ruoyi.system.service.mdmp.EarlyWarningService;
import com.ruoyi.system.service.mdmp.IFlightAlertsService;
import com.ruoyi.system.service.mdmp.IMapDataObstacleService;
import com.ruoyi.system.util.App;
import com.ruoyi.system.util.CircularUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
@Service
public class FlightAlertsServiceImpl implements IFlightAlertsService {

    private static final Logger log = LoggerFactory.getLogger(FlightAlertsServiceImpl.class);
    @Resource
    private FlightAlertsMapper flightAlertsMapper;

    @Resource
    private MapDataMapper mapDataMapper;

    @Resource
    private DailyFlightPlanMapper dailyFlightPlanMapper;

    @Resource
    private FlightDataMapper flightDataMapper;

    @Resource
    private AircraftMapper aircraftMapper;

    @Resource
    private RainfallMpDataMapper rainfallMpDataMapper;

    @Resource
    private VisibilityMpDataMapper visibilityMpDataMapper;

    @Resource
    private TemperatureMpDataMapper temperatureMpDataMapper;

    @Resource
    private ThunderstormMpDataMapper thunderstormMpDataMapper;

    @Resource
    private IMapDataObstacleService iMapDataObstacleService;

    @Resource
    private WorkMapDataMapper workMapDataMapper;

    @Resource
    private FlightAlertsInOutMapper flightAlertsInOutMapper;

    @Resource
    private EarlyWarningService earlyWarningService;


    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public FlightAlerts selectFlightAlertsById(Long id) {
        return flightAlertsMapper.selectFlightAlertsById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param flightAlerts 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<FlightAlerts> selectFlightAlertsList(FlightAlerts flightAlerts) {
        return flightAlertsMapper.selectFlightAlertsList(flightAlerts);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param flightAlerts 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertFlightAlerts(FlightAlerts flightAlerts) {
        return flightAlertsMapper.insertFlightAlerts(flightAlerts);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param flightAlerts 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateFlightAlerts(FlightAlerts flightAlerts) {
        return flightAlertsMapper.updateFlightAlerts(flightAlerts);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteFlightAlertsByIds(Long[] ids) {
        return flightAlertsMapper.deleteFlightAlertsByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteFlightAlertsById(Long id) {
        return flightAlertsMapper.deleteFlightAlertsById(id);
    }


    private static final ExecutorService executor = Executors.newFixedThreadPool(4);

    @Override
    public List<FlightAlerts> getTopographicMapAlerts(WebsocketFlightData flightData) {
        List<FlightAlerts> flightAlertsListReturn = new ArrayList<>();
        //查询是否有地形数据
        List<MapData> topographicMapDataList = mapDataMapper.getTopographicMapDate(flightData);
        // 获取飞行告警信息
        List<FlightAlerts> flightAlertsList = flightAlertsMapper.selectFlightAlertsListByAircraftReg(flightData.getAircraftReg(), AlertType.NEAR_EARTH);
        if (topographicMapDataList.isEmpty()) {
            // 如果没有地形数据，则对所有告警进行释放操作
            if (!flightAlertsList.isEmpty()) {
                Long maxVersion = flightAlertsMapper.selectMaxVersion(flightData.getAircraftReg());
                //获取地形颜色
                for (FlightAlerts flightAlerts : flightAlertsList) {
                    setAlerts(flightData.getAircraftReg(), StatusCodeType.TOPOGRAPHIC, StatusCodeType.TOPOGRAPHIC, AlarmNameType.TERRAIN, AlertType.NEAR_EARTH, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
                }
            }
        } else {
            // 如果有告警信息，则根据告警索引与告警信息的匹配情况处理告警
            Long maxVersion = flightAlertsMapper.selectMaxVersion(flightData.getAircraftReg());
            MapData mapData = topographicMapDataList.get(0);
            for (FlightAlerts flightAlerts : flightAlertsList) {
                if (!flightAlerts.getIndex().equals(mapData.getIndex())) {
                    setAlerts(flightData.getAircraftReg(), StatusCodeType.TOPOGRAPHIC, StatusCodeType.TOPOGRAPHIC, AlarmNameType.TERRAIN, AlertType.NEAR_EARTH, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
                }
            }
            setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.TOPOGRAPHIC, AlarmNameType.TERRAIN, AlertType.NEAR_EARTH, maxVersion, AlertStatus.ALARM, flightData, mapData.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
        }
        // 设置飞行告警列表
        return flightAlertsListReturn;
    }

    @Override
    public List<FlightAlerts> getObstaclesAlerts(WebsocketFlightData flightData) {
        List<FlightAlerts> flightAlertsListReturn = new ArrayList<>();
        //查询是否有障碍物告警
        List<MapData> obstaclesList = mapDataMapper.getObstaclesList(flightData);
        // 获取飞行告警信息
        List<FlightAlerts> flightAlertsList = flightAlertsMapper.selectFlightAlertsListByAircraftReg(flightData.getAircraftReg(), AlertType.OBSTACLE);
        if (obstaclesList.isEmpty()) {
            // 如果没有地形数据，则对所有告警进行释放操作
            if (!flightAlertsList.isEmpty()) {
                Long maxVersion = flightAlertsMapper.selectMaxVersion(flightData.getAircraftReg());
                //获取障碍物颜色
                for (FlightAlerts flightAlerts : flightAlertsList) {
                    setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), flightAlerts.getAlarmName(), AlertType.OBSTACLE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
                }
            }
        } else {
            // 如果有告警信息，则根据告警索引与告警信息的匹配情况处理告警
            for (MapData mapData : obstaclesList) {
                Long maxVersion = flightAlertsMapper.selectMaxVersion(flightData.getAircraftReg());
                for (FlightAlerts flightAlerts : flightAlertsList) {
                    if (!flightAlerts.getIndex().equals(mapData.getIndex())) {
                        setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), flightAlerts.getAlarmName(), AlertType.OBSTACLE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
                    }
                }
                setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, mapData.getStatusCodes(), mapData.getAirspaceName(), AlertType.OBSTACLE, maxVersion, AlertStatus.ALARM, flightData, mapData.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
            }
        }
        // 设置飞行告警列表
        return flightAlertsListReturn;
    }

    @Override
    public List<FlightAlerts> getControlZoneAlerts(WebsocketFlightData flightData) {
        List<FlightAlerts> flightAlertsListReturn = new ArrayList<>();
        //查询是否空域告警
        List<MapData> controlZoneList = mapDataMapper.getControlZoneAlerts(flightData);
        // 获取飞行告警信息
        List<FlightAlerts> flightAlertsList = flightAlertsMapper.selectFlightAlertsListByAircraftReg(flightData.getAircraftReg(), AlertType.CONTROL_ZONE);
        if (controlZoneList.isEmpty()) {
            // 如果没有地形数据，则对所有告警进行释放操作
            if (!flightAlertsList.isEmpty()) {
                Long maxVersion = flightAlertsMapper.selectMaxVersion(flightData.getAircraftReg());
                //获取障碍物颜色
                for (FlightAlerts flightAlerts : flightAlertsList) {
                    setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), flightAlerts.getAlarmName(), AlertType.CONTROL_ZONE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
                }
            }
        } else {
            // 如果有告警信息，则根据告警索引与告警信息的匹配情况处理告警
            for (MapData mapData : controlZoneList) {
                Long maxVersion = flightAlertsMapper.selectMaxVersion(flightData.getAircraftReg());
                for (FlightAlerts flightAlerts : flightAlertsList) {
                    if (!flightAlerts.getIndex().equals(mapData.getIndex())) {
                        setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), flightAlerts.getAlarmName(), AlertType.CONTROL_ZONE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
                    }
                }
                setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, mapData.getStatusCodes(), mapData.getAirspaceName(), AlertType.CONTROL_ZONE, maxVersion, AlertStatus.ALARM, flightData, mapData.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
            }
        }
        // 设置飞行告警列表
        return flightAlertsListReturn;
    }

    @Override
    public List<FlightAlerts> getNoFlightPlanAlerts(WebsocketFlightData flightData, Long maxVersion) {
        DailyFlightPlan dailyFlightPlan = dailyFlightPlanMapper.getNoFlightPlanAlerts(flightData);
        List<FlightAlerts> flightAlertsListReturn = new ArrayList<>();
        if (dailyFlightPlan == null) {
            setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, AlarmNameType.NO_FLIGHT_PLAN, AlertType.NO_FLIGHT_PLAN, maxVersion, AlertStatus.ALARM, flightData, StatusCodeType.NO_FLIGHT_PLAN, flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
        } else {
            //查询是否有航线警告
            if (dailyFlightPlan.getIsWork() == 0) {
                Long routeAlerts = dailyFlightPlanMapper.getRouteAlerts(flightData);
                if (routeAlerts <= 0) {
                    setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, AlarmNameType.ROUTE, AlertType.FLIGHT_PLAN, maxVersion, AlertStatus.ALARM, flightData, StatusCodeType.NO_FLIGHT_PLAN, flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
                }
            } else {
                FlightAlertsInOut flightAlertsInOutAfter = flightAlertsInOutMapper.selectByAircraftRegAndWorkId(flightData.getAircraftReg());
                if (flightAlertsInOutAfter == null) {
                    FlightAlertsInOut flightAlertsInOut = new FlightAlertsInOut();
                    flightAlertsInOut.setAircraftReg(flightData.getAircraftReg());
                    flightAlertsInOut.setAddTime(flightData.getRealTime());
                    flightAlertsInOut.setStatusCode(0);
                    flightAlertsInOut.setOddOrEven(0);
                    flightAlertsInOutMapper.insertFlightAlertsInOut(flightAlertsInOut);
                } else {
                    Integer statusCode = flightAlertsInOutAfter.getStatusCode();
                    Integer index = flightData.getMapData().getIndex();
                    //通过格子查询是否进入作业区
                    WorkMapData workMapData = workMapDataMapper.selectWorkMap(flightData, index);
                    if (statusCode == 0) {
                        if (workMapData != null && workMapData.getGroupType() != 0) {
                            flightAlertsInOutAfter.setStatusCode(1);
                            flightAlertsInOutAfter.setCircleId(workMapData.getGroupType());
                            flightAlertsInOutAfter.setWorkId(workMapData.getWorkId());
                            flightAlertsInOutMapper.updateFlightAlertsInOut(flightAlertsInOutAfter);
                        }
                    } else if (statusCode == 1) {
                        if (workMapData == null) {
                            flightAlertsInOutAfter.setStatusCode(0);
                            flightAlertsInOutMapper.updateFlightAlertsInOut(flightAlertsInOutAfter);
                        } else {
                            if (workMapData.getGroupType() == 0) {
                                flightAlertsInOutAfter.setStatusCode(2);
                                flightAlertsInOutMapper.updateFlightAlertsInOut(flightAlertsInOutAfter);
                            }
                        }
                    } else if (statusCode == 2) {
                        if (workMapData == null) {
                            setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, AlarmNameType.ROUTE, AlertType.FLIGHT_PLAN, maxVersion, AlertStatus.ALARM, flightData, StatusCodeType.NO_FLIGHT_PLAN, flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
                        } else {
                            if (workMapData.getGroupType() != 0) {
                                flightAlertsInOutAfter.setStatusCode(1);
                                flightAlertsInOutAfter.setCircleId(workMapData.getGroupType());
                                flightAlertsInOutAfter.setWorkId(workMapData.getWorkId());
                                flightAlertsInOutMapper.updateFlightAlertsInOut(flightAlertsInOutAfter);
                            }
                        }
                    }
                }
            }
        }
        return flightAlertsListReturn;
    }

    private boolean civilAviation(List<FlightAlerts> batchInsertList, WebsocketFlightData websocketFlightData,
                                  FlightData flightData,
                                  double websocketLongitude,
                                  double websocketLatitude,
                                  Long maxVersion,
                                  List<FlightAlerts> flightAlertsListReturn) {

        // 1. 使用常量代替魔数 (magic numbers)
        //final double HEIGHT_THRESHOLD = 300.0; // 垂直高度阈值(米)
        // final double DISTANCE_THRESHOLD = 20000.0; // 水平距离阈值(米)

        final double HEIGHT_THRESHOLD = 5000.0; // 垂直高度阈值(米)
        final double DISTANCE_THRESHOLD = 50000.0; // 水平距离阈值(米)
        String aircraftReg = flightData.getAircraftReg();
        StringBuilder alarmBuilder = new StringBuilder("与").append(aircraftReg);
        boolean hasAlarm = false;

        // 2. 提取重复计算值
        double elevationDiff = Math.abs(websocketFlightData.getElevation() - flightData.getElevation());
        double latitude = flightData.getLatitude().doubleValue();
        double longitude = flightData.getLongitude().doubleValue();

        // 3. 分离垂直和水平告警判断
        if (elevationDiff < HEIGHT_THRESHOLD) {
            alarmBuilder.append("垂直高度:").append(new Formatter().format("%.2f", elevationDiff)).append("米,");
            double distance = CircularUtil.distance(websocketLongitude, websocketLatitude, longitude, latitude);
            if (distance < DISTANCE_THRESHOLD) {
                alarmBuilder.append("水平距离:").append(new Formatter().format("%.2f", distance / 1000)).append("km");
                hasAlarm = true;
            }
        }


        // 4. 重构告警处理逻辑
        if (hasAlarm) {
            String alarmName = alarmBuilder.toString();

            // 4. 新增障碍物告警处理 - 只添加到批量列表不立即插入
            setAlerts(alarmName, websocketFlightData.getAircraftReg(),
                    StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, alarmName,
                    AlertType.AIRCRAFT_SPACING, maxVersion, AlertStatus.ALARM,
                    websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN,
                    flightAlertsListReturn, IsSendAlert.SENDING,
                    aircraftReg, websocketFlightData.getUniqueId(), websocketFlightData.getTargetIdentification());

            // 将生成的告警添加到批量列表
            batchInsertList.addAll(flightAlertsListReturn);

            // 5. 重构连线告警处理 - 只添加到批量列表不立即插入
            List<FlightAlerts> connectionInfoList = new ArrayList<>();
            setAlerts(alarmName, websocketFlightData.getAircraftReg(),
                    StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, alarmName + "告警连线",
                    AlertType.CONNECTION, maxVersion, AlertStatus.ALARM,
                    websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN,
                    connectionInfoList, IsSendAlert.SENDING,
                    aircraftReg, websocketFlightData.getUniqueId(), websocketFlightData.getTargetIdentification());

            // 将生成的告警添加到批量列表
            batchInsertList.addAll(connectionInfoList);


            // 6. 优化空集合处理
            Set<FlightAlerts> connectionSet = websocketFlightData.getConnectionInfoList();
            if (connectionSet == null) {
                connectionSet = new HashSet<>();
                websocketFlightData.setConnectionInfoList(connectionSet);
            }

            // 7. 避免不必要的集合操作
            connectionSet.addAll(connectionInfoList);
        }
        return hasAlarm;
    }

    private boolean general(List<FlightAlerts> batchInsertList, WebsocketFlightData websocketFlightData, FlightData flightData, double websocketLongitude, double websocketLatitude, Long maxVersion, List<FlightAlerts> flightAlertsListReturn) {
        // 1. 使用常量代替魔数 (magic numbers)
        // final double HEIGHT_THRESHOLD = 300.0; // 垂直高度阈值(米)
        //final double DISTANCE_THRESHOLD = 20000.0; // 水平距离阈值(米)

        final double HEIGHT_THRESHOLD = 5000.0; // 垂直高度阈值(米)
        final double DISTANCE_THRESHOLD = 50000.0; // 水平距离阈值(米)
        String aircraftReg = flightData.getAircraftReg();
        StringBuilder alarmBuilder = new StringBuilder("与").append(aircraftReg);
        boolean hasAlarm = false;

        // 2. 提取重复计算值
        double elevationDiff = Math.abs(websocketFlightData.getElevation() - flightData.getElevation());
        double latitude = flightData.getLatitude().doubleValue();
        double longitude = flightData.getLongitude().doubleValue();

        // 3. 分离垂直和水平告警判断
        if (elevationDiff < HEIGHT_THRESHOLD) {
            alarmBuilder.append("垂直高度:").append(new Formatter().format("%.2f", elevationDiff)).append("米,");
            double distance = CircularUtil.distance(websocketLongitude, websocketLatitude, longitude, latitude);
            if (distance < DISTANCE_THRESHOLD) {
                alarmBuilder.append("水平距离:").append(new Formatter().format("%.2f", distance / 1000)).append("km");
                hasAlarm = true;
            }
        }
        // 4. 重构告警处理逻辑
        if (hasAlarm) {
            String alarmName = alarmBuilder.toString();
            // 4. 新增障碍物告警处理 - 只添加到批量列表不立即插入
            setAlerts(alarmName, websocketFlightData.getAircraftReg(),
                    StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, alarmName,
                    AlertType.AIRCRAFT_SPACING, maxVersion, AlertStatus.ALARM,
                    websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN,
                    flightAlertsListReturn, IsSendAlert.SENDING,
                    aircraftReg, websocketFlightData.getUniqueId(), websocketFlightData.getTargetIdentification());

            // 将生成的告警添加到批量列表
            batchInsertList.addAll(flightAlertsListReturn);

            // 5. 重构连线告警处理 - 只添加到批量列表不立即插入
            List<FlightAlerts> connectionInfoList = new ArrayList<>();
            setAlerts(alarmName, websocketFlightData.getAircraftReg(),
                    StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, alarmName + "告警连线",
                    AlertType.CONNECTION, maxVersion, AlertStatus.ALARM,
                    websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN,
                    connectionInfoList, IsSendAlert.SENDING,
                    aircraftReg, websocketFlightData.getUniqueId(), websocketFlightData.getTargetIdentification());

            // 将生成的告警添加到批量列表
            batchInsertList.addAll(connectionInfoList);


            // 6. 优化空集合处理
            Set<FlightAlerts> connectionSet = websocketFlightData.getConnectionInfoList();
            if (connectionSet == null) {
                connectionSet = new HashSet<>();
                websocketFlightData.setConnectionInfoList(connectionSet);
            }

            // 7. 避免不必要的集合操作
            connectionSet.addAll(connectionInfoList);
        }
        return hasAlarm;
    }


    private void shortestConnection(WebsocketFlightData websocketFlightData, List<FlightData> flightAlerts, double wsLon, double wsLat, Long maxVersion) {
        double wsEle = websocketFlightData.getElevation().doubleValue();
        double minSqrDistance = Double.MAX_VALUE;
        FlightData nearestFlight = null;
        for (FlightData data : flightAlerts) {
            if (data.getIsCivil() != 2) {
                double lat = data.getLatitude().doubleValue();
                double lon = data.getLongitude().doubleValue();
                double ele = data.getElevation().doubleValue();
                double xyDist = CircularUtil.distance(wsLon, wsLat, lon, lat);
                double zDist = Math.abs(wsEle - ele);
                double sqrDist = xyDist * xyDist + zDist * zDist;
                if (sqrDist < minSqrDistance) {
                    minSqrDistance = sqrDist;
                    nearestFlight = data;
                }
            }
        }
        if (nearestFlight != null) {
            List<FlightAlerts> connectionInfoList = new ArrayList<>();
            setAlerts("最短距离", websocketFlightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, "最短距离连线", AlertType.CONNECTION, maxVersion, AlertStatus.ALARM, websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN, connectionInfoList, IsSendAlert.SENDING, nearestFlight.getAircraftReg(), websocketFlightData.getUniqueId(), websocketFlightData.getTargetIdentification());
            if (websocketFlightData.getConnectionInfoList() == null) {
                websocketFlightData.setConnectionInfoList(new HashSet<>());
            }
            websocketFlightData.getConnectionInfoList().addAll(connectionInfoList);
            if (!connectionInfoList.isEmpty()) {
                flightAlertsMapper.insertBatchFlightAlerts(connectionInfoList);
            }
            FlightData finalNearestFlight = nearestFlight;
            executor.submit(() -> {
                try {
                    SendEarlyWarningParam param = new SendEarlyWarningParam();
                    //处理预警
                    param.setAircraftA(flightDataMapper.selectFlightDataListByOneMinute(websocketFlightData.getAircraftReg()));
                    param.setAircraftB(flightDataMapper.selectFlightDataListByOneMinute(finalNearestFlight.getAircraftReg()));
                    param.setStatus(1);
                    param.setWarningTime(DateUtils.getCurrentTimePlusOneMinute());
                    earlyWarningService.sendEarlyWarning(param);
                } catch (Exception e) {
                    // 添加异常处理
                    log.error("异步处理预警失败", e);
                }
            });
        }
    }


    private boolean uav(List<FlightAlerts> batchInsertList, WebsocketFlightData websocketFlightData, FlightData
            flightData, double websocketLongitude, double websocketLatitude, Long
                                maxVersion, List<FlightAlerts> flightAlertsListReturn) {
        //final double HEIGHT_THRESHOLD = 600.0; // 垂直高度阈值(米)
        // final double DISTANCE_THRESHOLD = 10000.0; // 水平距离阈值(米)

        final double HEIGHT_THRESHOLD = 3000.0; // 垂直高度阈值(米)
        final double DISTANCE_THRESHOLD = 10000.0; // 水平距离阈值(米)
        String aircraftReg = flightData.getAircraftReg();
        StringBuilder alarmBuilder = new StringBuilder("与").append(aircraftReg);
        boolean hasAlarm = false;

        // 2. 提取重复计算值
        double elevationDiff = Math.abs(websocketFlightData.getElevation() - flightData.getElevation());
        double latitude = flightData.getLatitude().doubleValue();
        double longitude = flightData.getLongitude().doubleValue();

        // 3. 分离垂直和水平告警判断
        if (elevationDiff < HEIGHT_THRESHOLD) {
            alarmBuilder.append("垂直高度:").append(new Formatter().format("%.2f", elevationDiff)).append("米,");
            double distance = CircularUtil.distance(websocketLongitude, websocketLatitude, longitude, latitude);
            if (distance < DISTANCE_THRESHOLD) {
                alarmBuilder.append("水平距离:").append(new Formatter().format("%.2f", distance / 1000)).append("km");
                hasAlarm = true;
            }
        }
        // 4. 重构告警处理逻辑
        if (hasAlarm) {
            String alarmName = alarmBuilder.toString();
            // 4. 新增障碍物告警处理 - 只添加到批量列表不立即插入
            setAlerts(alarmName, websocketFlightData.getAircraftReg(),
                    StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, alarmName,
                    AlertType.AIRCRAFT_SPACING, maxVersion, AlertStatus.ALARM,
                    websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN,
                    flightAlertsListReturn, IsSendAlert.SENDING,
                    aircraftReg, websocketFlightData.getUniqueId(), websocketFlightData.getTargetIdentification());

            // 将生成的告警添加到批量列表
            batchInsertList.addAll(flightAlertsListReturn);

            // 5. 重构连线告警处理 - 只添加到批量列表不立即插入
            List<FlightAlerts> connectionInfoList = new ArrayList<>();
            setAlerts(alarmName, websocketFlightData.getAircraftReg(),
                    StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, alarmName + "告警连线",
                    AlertType.CONNECTION, maxVersion, AlertStatus.ALARM,
                    websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN,
                    connectionInfoList, IsSendAlert.SENDING,
                    aircraftReg, websocketFlightData.getUniqueId(), websocketFlightData.getTargetIdentification());

            // 将生成的告警添加到批量列表
            batchInsertList.addAll(connectionInfoList);


            // 6. 优化空集合处理
            Set<FlightAlerts> connectionSet = websocketFlightData.getConnectionInfoList();
            if (connectionSet == null) {
                connectionSet = new HashSet<>();
                websocketFlightData.setConnectionInfoList(connectionSet);
            }

            // 7. 避免不必要的集合操作
            connectionSet.addAll(connectionInfoList);
        }
        return hasAlarm;
    }

    @Override
    public List<FlightAlerts> getFlightAlerts(WebsocketFlightData websocketFlightData, Long maxVersion) {
        List<FlightAlerts> flightAlertsListReturn = new ArrayList<>();
        //判断飞机坐标是否在跑道内
        App app = new App();
//        boolean insideRunway = app.doContains(websocketFlightData.getLongitude().doubleValue(), websocketFlightData.getLatitude().doubleValue());
//        if (insideRunway) return new ArrayList<>();
        AircraftFlightAlertsInOut aircraftFlightAlertsInOut = flightAlertsInOutMapper.selectFlightAlertsInOut(websocketFlightData.getAircraftReg());
        if (null != aircraftFlightAlertsInOut) {
            List<FlightData> flightAlerts = flightDataMapper.getFlightAlerts(websocketFlightData);
            double websocketLatitude = websocketFlightData.getLatitude().doubleValue();
            double websocketLongitude = websocketFlightData.getLongitude().doubleValue();
            if (aircraftFlightAlertsInOut.getIsCivil() == 0) {
                if (null != flightAlerts && flightAlerts.size() > 0) {
                    List<FlightAlerts> batchInsertList = new ArrayList<>();
                    for (FlightData flightData : flightAlerts) {
                        if (flightData.getIsCivil() == 1) {
                            general(batchInsertList, websocketFlightData, flightData, websocketLongitude, websocketLatitude, maxVersion, flightAlertsListReturn);
                        }
                    }
                    // 批量插入所有告警
                    if (!batchInsertList.isEmpty()) {
                        flightAlertsMapper.insertBatchFlightAlerts(batchInsertList);
                    }
                }
            } else if (aircraftFlightAlertsInOut.getIsCivil() == 1) {
                if (null != flightAlerts && flightAlerts.size() > 0) {
                    List<FlightAlerts> batchInsertList = new ArrayList<>();
                    for (FlightData flightData : flightAlerts) {
                        if (flightData.getIsCivil() == 0 || flightData.getIsCivil() == 1) {
                            civilAviation(batchInsertList, websocketFlightData, flightData, websocketLongitude, websocketLatitude, maxVersion, flightAlertsListReturn);
                        }
                    }
                    // 批量插入所有告警
                    if (!batchInsertList.isEmpty()) {
                        flightAlertsMapper.insertBatchFlightAlerts(batchInsertList);
                    }
                }


            } else if (aircraftFlightAlertsInOut.getIsCivil() == 2) {
                int i = 0;
                boolean connected = false;
                if (null != flightAlerts && flightAlerts.size() > 0) {
                    List<FlightAlerts> batchInsertList = new ArrayList<>();
                    for (FlightData flightData : flightAlerts) {
                        if (flightData.getIsCivil() == 0 || flightData.getIsCivil() == 1) {
                            connected = uav(batchInsertList, websocketFlightData, flightData, websocketLongitude, websocketLatitude, maxVersion, flightAlertsListReturn);
                        }
                        if (connected) {
                            i++;
                        }
                    }
                    // 批量插入所有告警
                    if (!batchInsertList.isEmpty()) {
                        flightAlertsMapper.insertBatchFlightAlerts(batchInsertList);
                    }
                    if (i == 0) {
                        shortestConnection(websocketFlightData, flightAlerts, websocketLongitude, websocketLatitude, maxVersion);
                    }
                }
            }

        }


//            if (aircraftFlightAlertsInOut.getIsCivil() == 1) {
//                double height = 300;
//                List<FlightData> flightAlerts = flightDataMapper.getConnectionInfoList(websocketFlightData, height);
//                double websocketLatitude = websocketFlightData.getLatitude().doubleValue();
//                double websocketLongitude = websocketFlightData.getLongitude().doubleValue();
//                if (null != flightAlerts && flightAlerts.size() > 0) {
//                    for (FlightData flightData : flightAlerts) {
//                        double latitude = flightData.getLatitude().doubleValue();
//                        double longitude = flightData.getLongitude().doubleValue();
//                        double distance = CircularUtil.distance(websocketLongitude, websocketLatitude, longitude, latitude);
//                        if (distance < 20000) {
//                            String a = "民航飞机:" + websocketFlightData.getAircraftReg() + "告警飞机" + flightData.getAircraftReg() + "经纬度:" + websocketLongitude + "," + websocketLatitude + "与" + longitude + "," + latitude + "距离为：" + distance;
//                            String alarmName = "与" + flightData.getAircraftReg() + AlarmNameType.AIRCRAFT_SPACING;
//                            setAlerts(a, websocketFlightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, alarmName, AlertType.AIRCRAFT_SPACING, maxVersion, AlertStatus.ALARM, websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN, flightAlertsListReturn, IsSendAlert.SENDING, flightData.getAircraftReg(), websocketFlightData.getUniqueId());
//                            List<FlightAlerts> connectionInfoList = new ArrayList<>();
//                            setAlerts(a, websocketFlightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, "连线", AlertType.CONNECTION, maxVersion, AlertStatus.ALARM, websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN, connectionInfoList, IsSendAlert.SENDING, flightData.getAircraftReg(), websocketFlightData.getUniqueId());
//                            if (websocketFlightData.getConnectionInfoList() == null) {
//                                websocketFlightData.setConnectionInfoList(new HashSet<>());
//                            }
//                            websocketFlightData.getConnectionInfoList().addAll(connectionInfoList);
//                            if ((websocketFlightData.getConnectionInfoList() != null && websocketFlightData.getConnectionInfoList().size() > 0) || (websocketFlightData.getFlightAlertsList() != null && websocketFlightData.getFlightAlertsList().size() > 0)) {
//                                log.error("告警ID" + websocketFlightData.getUniqueId() + "告警信息：{}", JSON.toJSONString(websocketFlightData));
//                            }
//                        }
//                    }
//                }
//            } else if (aircraftFlightAlertsInOut.getIsCivil() == 0) {
//                double height = 300;
//                List<FlightData> flightAlerts = flightDataMapper.getFlightAlertsIsCivilTh(websocketFlightData, height);
//                double websocketLatitude = websocketFlightData.getLatitude().doubleValue();
//                double websocketLongitude = websocketFlightData.getLongitude().doubleValue();
//                if (null != flightAlerts && flightAlerts.size() > 0) {
//                    for (FlightData flightData : flightAlerts) {
//                        double latitude = flightData.getLatitude().doubleValue();
//                        double longitude = flightData.getLongitude().doubleValue();
//                        double distance = CircularUtil.distance(websocketLongitude, websocketLatitude, longitude, latitude);
//                        if (distance < 20000) {
//                            String a = "通航飞机:" + websocketFlightData.getAircraftReg() + "告警飞机" + flightData.getAircraftReg() + "经纬度:" + websocketLongitude + "," + websocketLatitude + "与" + longitude + "," + latitude + "距离为：" + distance;
//                            String alarmName = "与" + flightData.getAircraftReg() + AlarmNameType.AIRCRAFT_SPACING;
//                            setAlerts(a, websocketFlightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, alarmName, AlertType.AIRCRAFT_SPACING, maxVersion, AlertStatus.ALARM, websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN, flightAlertsListReturn, IsSendAlert.SENDING, flightData.getAircraftReg(), websocketFlightData.getUniqueId());
//                            List<FlightAlerts> connectionInfoList = new ArrayList<>();
//                            setAlerts(a, websocketFlightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, "连线", AlertType.CONNECTION, maxVersion, AlertStatus.ALARM, websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN, connectionInfoList, IsSendAlert.SENDING, flightData.getAircraftReg(), websocketFlightData.getUniqueId());
//                            if (websocketFlightData.getConnectionInfoList() == null) {
//                                websocketFlightData.setConnectionInfoList(new HashSet<>());
//                            }
//                            websocketFlightData.getConnectionInfoList().addAll(connectionInfoList);
//                            if ((websocketFlightData.getConnectionInfoList() != null && websocketFlightData.getConnectionInfoList().size() > 0) || (websocketFlightData.getFlightAlertsList() != null && websocketFlightData.getFlightAlertsList().size() > 0)) {
//                                log.error("告警ID" + websocketFlightData.getUniqueId() + "告警信息：{}", JSON.toJSONString(websocketFlightData));
//                            }
//                        }
//                    }
//                }
//
//            } else if (aircraftFlightAlertsInOut.getIsCivil() == 2) {
//                double height = 600;
//                double wsLat = websocketFlightData.getLatitude().doubleValue();
//                double wsLon = websocketFlightData.getLongitude().doubleValue();
//                List<FlightData> alert = flightDataMapper.getFlightAlerts(websocketFlightData, height);
//                if (null != alert && alert.size() > 0) {
//                    for (FlightData flightData : alert) {
//                        double latitude = flightData.getLatitude().doubleValue();
//                        double longitude = flightData.getLongitude().doubleValue();
//                        double distance = CircularUtil.distance(wsLon, wsLat, longitude, latitude);
//                        double heightDifference =Math.abs(websocketFlightData.getElevation() - flightData.getElevation());
//                        if (heightDifference < height || distance < 10000) {
//                            String a = "无人机:" + websocketFlightData.getAircraftReg() + "告警飞机" + flightData.getAircraftReg() + "经纬度:" + wsLon + "," + wsLat + "与" + longitude + "," + latitude + "距离为：" + distance;
//                            String alarmName = "与" + flightData.getAircraftReg() + AlarmNameType.AIRCRAFT_UAV;
//                            setAlerts(a, websocketFlightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, alarmName, AlertType.AIRCRAFT_SPACING, maxVersion, AlertStatus.ALARM, websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN, flightAlertsListReturn, IsSendAlert.SENDING, flightData.getAircraftReg(), websocketFlightData.getUniqueId());
//                            List<FlightAlerts> connectionInfoList = new ArrayList<>();
//                            setAlerts(a, websocketFlightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, "连线", AlertType.CONNECTION, maxVersion, AlertStatus.ALARM, websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN, connectionInfoList, IsSendAlert.SENDING, flightData.getAircraftReg(), websocketFlightData.getUniqueId());
//                            if (websocketFlightData.getConnectionInfoList() == null) {
//                                websocketFlightData.setConnectionInfoList(new HashSet<>());
//                            }
//                            websocketFlightData.getConnectionInfoList().addAll(connectionInfoList);
//                            if ((websocketFlightData.getConnectionInfoList() != null && websocketFlightData.getConnectionInfoList().size() > 0) || (websocketFlightData.getFlightAlertsList() != null && websocketFlightData.getFlightAlertsList().size() > 0)) {
//                                log.error("告警ID" + websocketFlightData.getUniqueId() + "告警信息：{}", JSON.toJSONString(websocketFlightData));
//                            }
//                        }
//                    }
//                }
//                List<FlightData> flightAlerts = flightDataMapper.getConnectionInfoList(websocketFlightData, height);
//                if (null != flightAlerts && flightAlerts.size() > 0) {
//                    double wsEle = websocketFlightData.getElevation().doubleValue();
//                    double minSqrDistance = Double.MAX_VALUE;
//                    FlightData nearestFlight = null;
//                    for (FlightData data : flightAlerts) {
//                        double lat = data.getLatitude().doubleValue();
//                        double lon = data.getLongitude().doubleValue();
//                        double ele = data.getElevation().doubleValue();
//                        double xyDist = CircularUtil.distance(wsLon, wsLat, lon, lat);
//                        double zDist = Math.abs(wsEle - ele);
//                        double sqrDist = xyDist * xyDist + zDist * zDist;
//                        if (sqrDist < minSqrDistance) {
//                            minSqrDistance = sqrDist;
//                            nearestFlight = data;
//                        }
//                    }
//                    if (nearestFlight != null) {
//                        List<FlightAlerts> connectionInfoList = new ArrayList<>();
//                        setAlerts("最短距离", websocketFlightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, "连线", AlertType.CONNECTION, maxVersion, AlertStatus.ALARM, websocketFlightData, StatusCodeType.NO_FLIGHT_PLAN, connectionInfoList, IsSendAlert.SENDING, nearestFlight.getAircraftReg(), websocketFlightData.getUniqueId());
//                        if (websocketFlightData.getConnectionInfoList() == null) {
//                            websocketFlightData.setConnectionInfoList(new HashSet<>());
//                        }
//                        websocketFlightData.getConnectionInfoList().addAll(connectionInfoList);
//                        if ((websocketFlightData.getConnectionInfoList() != null && websocketFlightData.getConnectionInfoList().size() > 0) || (websocketFlightData.getFlightAlertsList() != null && websocketFlightData.getFlightAlertsList().size() > 0)) {
//                            log.error("告警ID" + websocketFlightData.getUniqueId() + "告警信息：{}", JSON.toJSONString(websocketFlightData));
//                        }
//
//                    }
//                }
//            }
//            }
        return flightAlertsListReturn;
    }


    @Override
    public List<FlightAlerts> getUAndVAlerts(long time, AircraftMp aircraftMp, WebsocketFlightData
            flightData, Long maxVersion) {
        List<FlightAlerts> flightAlertsListReturn = new ArrayList<>();
        flightData.setLongitude(flightData.getLongitude().setScale(2, RoundingMode.DOWN));
        flightData.setLatitude(flightData.getLatitude().setScale(2, RoundingMode.DOWN));
        MapDataVo mapData = mapDataMapper.getUAndVAlerts(time, aircraftMp, flightData);
        //List<FlightAlerts> flightAlertsList = flightAlertsMapper.selectFlightAlertsListByAircraftReg(flightData.getAircraftReg(), AlertType.METE);
        if (null != mapData) {
//            // 如果没有数据，则对所有告警进行释放操作
//            if (flightAlertsList.size() > 0) {
//                //获取颜色
//                for (FlightAlerts flightAlerts : flightAlertsList) {
//                    setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), AlarmNameType.METE, AlertType.METE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                }
//            }
//        } else {
//            // 如果有告警信息，则根据告警索引与告警信息的匹配情况处理告警
//            for (FlightAlerts flightAlerts : flightAlertsList) {
//                if (!flightAlerts.getIndex().equals(mapData.getIndex())) {
//                    setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), AlarmNameType.METE, AlertType.METE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                }
//            }
            setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, mapData.getStatusCodes(), AlarmNameType.U_V_METE, AlertType.METE, maxVersion, AlertStatus.ALARM, flightData, mapData.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
        }
        return flightAlertsListReturn;
    }

    @Override
    public List<FlightAlerts> getRainfallAlerts(long time, AircraftMp aircraftMp, WebsocketFlightData
            flightData, Long maxVersion) {
        List<FlightAlerts> flightAlertsListReturn = new ArrayList<>();
        flightData.setLongitude(flightData.getLongitude().setScale(2, RoundingMode.DOWN));
        flightData.setLatitude(flightData.getLatitude().setScale(2, RoundingMode.DOWN));
        MapDataVo mapData = rainfallMpDataMapper.getRainfallAlerts(time, aircraftMp, flightData);
        //List<FlightAlerts> flightAlertsList = flightAlertsMapper.selectFlightAlertsListByAircraftReg(flightData.getAircraftReg(), AlertType.METE);
        if (null != mapData) {
            // 如果没有数据，则对所有告警进行释放操作
//            if (flightAlertsList.size() > 0) {
//                //获取颜色
//                for (FlightAlerts flightAlerts : flightAlertsList) {
//                    setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), AlarmNameType.METE, AlertType.METE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                }
//            }
//        } else {
//            // 如果有告警信息，则根据告警索引与告警信息的匹配情况处理告警
//            for (FlightAlerts flightAlerts : flightAlertsList) {
//                if (!flightAlerts.getIndex().equals(mapData.getIndex())) {
//                    setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), AlarmNameType.METE, AlertType.METE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                }
//            }
            setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, mapData.getStatusCodes(), AlarmNameType.RAINFALL_METE, AlertType.METE, maxVersion, AlertStatus.ALARM, flightData, mapData.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
        }
        return flightAlertsListReturn;
    }

    @Override
    public List<FlightAlerts> getVisibilityAlerts(long time, AircraftMp aircraftMp, WebsocketFlightData
            flightData, Long maxVersion) {
        List<FlightAlerts> flightAlertsListReturn = new ArrayList<>();
        flightData.setLongitude(flightData.getLongitude().setScale(2, RoundingMode.DOWN));
        flightData.setLatitude(flightData.getLatitude().setScale(2, RoundingMode.DOWN));
        MapDataVo mapData = visibilityMpDataMapper.getVisibilityAlerts(time, aircraftMp, flightData);
        // List<FlightAlerts> flightAlertsList = flightAlertsMapper.selectFlightAlertsListByAircraftReg(flightData.getAircraftReg(), AlertType.METE);
        if (null != mapData) {
            // 如果没有数据，则对所有告警进行释放操作
//            if (flightAlertsList.size() > 0) {
//                //获取颜色
//                for (FlightAlerts flightAlerts : flightAlertsList) {
//                    setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), AlarmNameType.METE, AlertType.METE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                }
//            }
//        } else {
//            // 如果有告警信息，则根据告警索引与告警信息的匹配情况处理告警
//            for (FlightAlerts flightAlerts : flightAlertsList) {
//                if (!flightAlerts.getIndex().equals(mapData.getIndex())) {
//                    setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), AlarmNameType.METE, AlertType.METE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                }
//            }
            setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, mapData.getStatusCodes(), AlarmNameType.VIS_METE, AlertType.METE, maxVersion, AlertStatus.ALARM, flightData, mapData.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
        }
        return flightAlertsListReturn;
    }

    @Override
    public List<FlightAlerts> getTemperatureAlerts(long time, AircraftMp aircraftMp, WebsocketFlightData
            flightData, Long maxVersion) {
        List<FlightAlerts> flightAlertsListReturn = new ArrayList<>();
        flightData.setLongitude(flightData.getLongitude().setScale(2, RoundingMode.DOWN));
        flightData.setLatitude(flightData.getLatitude().setScale(2, RoundingMode.DOWN));
        MapDataVo mapData = temperatureMpDataMapper.getTemperatureAlerts(time, aircraftMp, flightData);
        // List<FlightAlerts> flightAlertsList = flightAlertsMapper.selectFlightAlertsListByAircraftReg(flightData.getAircraftReg(), AlertType.METE);
        if (null != mapData) {
            // 如果没有数据，则对所有告警进行释放操作
//            if (flightAlertsList.size() > 0) {
//                //获取颜色
//                for (FlightAlerts flightAlerts : flightAlertsList) {
//                    setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), AlarmNameType.METE, AlertType.METE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                }
//            }
//        } else {
//            // 如果有告警信息，则根据告警索引与告警信息的匹配情况处理告警
//            for (FlightAlerts flightAlerts : flightAlertsList) {
//                if (!flightAlerts.getIndex().equals(mapData.getIndex())) {
//                    setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), AlarmNameType.METE, AlertType.METE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                }
//            }
            setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, mapData.getStatusCodes(), AlarmNameType.TP_METE, AlertType.METE, maxVersion, AlertStatus.ALARM, flightData, mapData.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
        }
        return flightAlertsListReturn;
    }

    @Override
    public List<FlightAlerts> getThunderstormAlerts(long time, AircraftMp aircraftMp, WebsocketFlightData
            flightData, Long maxVersion) {
        List<FlightAlerts> flightAlertsListReturn = new ArrayList<>();
        flightData.setLongitude(flightData.getLongitude().setScale(2, RoundingMode.DOWN));
        flightData.setLatitude(flightData.getLatitude().setScale(2, RoundingMode.DOWN));
        MapDataVo mapData = thunderstormMpDataMapper.getThunderstormAlerts(time, aircraftMp, flightData);
        // List<FlightAlerts> flightAlertsList = flightAlertsMapper.selectFlightAlertsListByAircraftReg(flightData.getAircraftReg(), AlertType.METE);
        if (null != mapData) {
            // 如果没有数据，则对所有告警进行释放操作
//            if (flightAlertsList.size() > 0) {
//                //获取颜色
//                for (FlightAlerts flightAlerts : flightAlertsList) {
//                    setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), AlarmNameType.METE, AlertType.METE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                }
//            }
//        } else {
//            // 如果有告警信息，则根据告警索引与告警信息的匹配情况处理告警
//            for (FlightAlerts flightAlerts : flightAlertsList) {
//                if (!flightAlerts.getIndex().equals(mapData.getIndex())) {
//                    setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), AlarmNameType.METE, AlertType.METE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                }
//            }
            setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, mapData.getStatusCodes(), AlarmNameType.TS_METE, AlertType.METE, maxVersion, AlertStatus.ALARM, flightData, mapData.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
        }
        return flightAlertsListReturn;
    }

//    @Override
//    public List<FlightAlerts> getUnregisteredAlerts(WebsocketFlightData flightData, Long maxVersion) {
//        Aircraft unregisteredAlerts = aircraftMapper.selectAircraft(flightData.getAircraftReg());
//        List<FlightAlerts> flightAlertsListReturn = new ArrayList<>();
//        if (null == unregisteredAlerts) {
//            setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, AlarmNameType.NO_AIRCRAFT, AlertType.NO_AIRCRAFT, maxVersion, AlertStatus.ALARM, flightData, StatusCodeType.NO_FLIGHT_PLAN, flightAlertsListReturn, IsSendAlert.SENDING);
//        } else {
//            if (unregisteredAlerts.getIsCivil() == 0) {
//                flightAlertsListReturn.addAll(getNoFlightPlanAlerts(flightData, maxVersion));
//            }
//        }
//        return flightAlertsListReturn;
//    }

    public List<FlightAlerts> getUnregisteredAlerts(WebsocketFlightData flightData, Long maxVersion) {
        String aircraftReg = flightData.getAircraftReg();
        Aircraft aircraft = aircraftMapper.selectAircraft(aircraftReg);
        if (aircraft == null) {
            return createUnregisteredAircraftAlerts(flightData, maxVersion);
        }
        return handleRegisteredAircraft(aircraft, flightData, maxVersion);
    }

    private List<FlightAlerts> createUnregisteredAircraftAlerts(WebsocketFlightData flightData, Long maxVersion) {
        List<FlightAlerts> alerts = new ArrayList<>();
        setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, StatusCodeType.NO_FLIGHT_PLAN, AlarmNameType.NO_AIRCRAFT, AlertType.NO_AIRCRAFT, maxVersion, AlertStatus.ALARM, flightData, StatusCodeType.NO_FLIGHT_PLAN, alerts, IsSendAlert.SENDING, flightData.getUniqueId());
        return alerts;
    }

    private List<FlightAlerts> handleRegisteredAircraft(Aircraft aircraft, WebsocketFlightData flightData, Long
            maxVersion) {
        if (isNonCivilAircraft(aircraft)) {
            return getNoFlightPlanAlerts(flightData, maxVersion);
        }
        return Collections.emptyList();
    }

    private boolean isNonCivilAircraft(Aircraft aircraft) {
        return aircraft.getIsCivil() == 0;
    }

    @Override
    public List<FlightAlerts> getAlerts(WebsocketFlightData flightData, Long maxVersion) {
        List<FlightAlerts> flightAlertsListReturn = new ArrayList<>();
        //查询是否有地形数据
        List<MapData> topographicMapDataList = mapDataMapper.getAlerts(flightData);

        // 获取飞行告警信息
//        List<FlightAlerts> flightAlertsList = flightAlertsMapper.selectFlightAlertsListByAircraftReg(flightData.getAircraftReg(), null);
//        Long maxVersion = flightAlertsMapper.selectMaxVersion(flightData.getAircraftReg());
//        if (topographicMapDataList.isEmpty()) {
//            // 如果没有数据，则对所有告警进行释放操作
//            if (!flightAlertsList.isEmpty()) {
//                //获取地形颜色
//                for (FlightAlerts flightAlerts : flightAlertsList) {
//                    if (flightAlerts.getAlertType().equals(AlertType.NEAR_EARTH)) {
//                        setAlerts(flightData.getAircraftReg(), StatusCodeType.TOPOGRAPHIC, StatusCodeType.TOPOGRAPHIC, AlarmNameType.TERRAIN, AlertType.NEAR_EARTH, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                    } else if (flightAlerts.getAlertType().equals(AlertType.OBSTACLE)) {
//                        setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), flightAlerts.getAlarmName(), AlertType.OBSTACLE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                    } else if (flightAlerts.getAlertType().equals(AlertType.CONTROL_ZONE)) {
//                        setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), flightAlerts.getAlarmName(), AlertType.CONTROL_ZONE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                    }
//                }
//            }
//        } else {
//            MapData mapData = topographicMapDataList.get(0);
//            for (FlightAlerts flightAlerts : flightAlertsList) {
//                if (flightAlerts.getAlertType().equals(AlertType.NEAR_EARTH)) {
//                    if (!flightAlerts.getIndex().equals(mapData.getIndex())) {
//                        setAlerts(flightData.getAircraftReg(), StatusCodeType.TOPOGRAPHIC, StatusCodeType.TOPOGRAPHIC, AlarmNameType.TERRAIN, AlertType.NEAR_EARTH, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                    }
//                } else if (flightAlerts.getAlertType().equals(AlertType.OBSTACLE)) {
//                    if (!flightAlerts.getIndex().equals(mapData.getIndex())) {
//                        setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), flightAlerts.getAlarmName(), AlertType.OBSTACLE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                    }
//                } else if (flightAlerts.getAlertType().equals(AlertType.CONTROL_ZONE)) {
//                    if (!flightAlerts.getIndex().equals(mapData.getIndex())) {
//                        setAlerts(flightData.getAircraftReg(), flightAlerts.getColor(), flightAlerts.getColor(), flightAlerts.getAlarmName(), AlertType.CONTROL_ZONE, maxVersion, AlertStatus.RELEASE_ALARM, flightData, flightAlerts.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING);
//                    }
//                }
//            }
        if (null != topographicMapDataList && !topographicMapDataList.isEmpty()) {
            for (MapData m : topographicMapDataList) {
                if (m.getType().equals(AlertType.NEAR_EARTH)) {
                    setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, m.getStatusCodes(), AlarmNameType.TERRAIN, AlertType.NEAR_EARTH, maxVersion, AlertStatus.ALARM, flightData, m.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
                } else if (m.getType().equals(AlertType.OBSTACLE)) {
                    Integer subIndex = iMapDataObstacleService.selectObstacleByIndex(m.getAirspaceId(), m.getIndex(), flightData);
                    if (subIndex != null) {
                        setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, m.getStatusCodes(), m.getAirspaceName(), AlertType.OBSTACLE, maxVersion, AlertStatus.ALARM, flightData, m.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, subIndex, flightData.getUniqueId());
                    }
                } else if (m.getType().equals(AlertType.CONTROL_ZONE)) {
                    setAlerts(flightData.getAircraftReg(), StatusCodeType.ALARM, m.getStatusCodes(), m.getAirspaceName(), AlertType.CONTROL_ZONE, maxVersion, AlertStatus.ALARM, flightData, m.getIndex(), flightAlertsListReturn, IsSendAlert.SENDING, flightData.getUniqueId());
                }
            }
        }
        //  }
        // 设置飞行告警列表
        return flightAlertsListReturn;
    }

    @Override
    public List<FlightAlerts> getOldAlerts(WebsocketFlightData flightData, Long maxVersion) {
        FlightAlerts flightAlerts = new FlightAlerts();
        flightAlerts.setAircraftReg(flightData.getAircraftReg());
        flightAlerts.setVersion(maxVersion);
        List<FlightAlerts> flightAlertsList = flightAlertsMapper.selectFlightAlertsLists(flightAlerts);
        return Optional.ofNullable(flightAlertsList).orElseGet(Collections::emptyList).stream().peek(f -> {
            f.setStatusCode(f.getColor());
            f.setAlertStatus(AlertStatus.RELEASE_ALARM);
        }).collect(Collectors.toList());
    }

    @Override
    public void markAlertsAsSent(List<FlightAlerts> oldAlerts) {
        List<Long> ids = oldAlerts.stream().map(FlightAlerts::getId).collect(Collectors.toList());
        if (null != ids && !ids.isEmpty()) {
            flightAlertsMapper.batchUpdateSentStatus(ids);
        }
    }

    @Override
    public List<FlightAlerts> getOldAlerts2(WebsocketFlightData flightData, Long maxVersion) {
        FlightAlerts flightAlerts = new FlightAlerts();
        flightAlerts.setAircraftReg(flightData.getAircraftReg());
        flightAlerts.setVersion(maxVersion);
        List<FlightAlerts> flightAlertsList = flightAlertsMapper.selectConnection(flightAlerts);
        return Optional.ofNullable(flightAlertsList).orElseGet(Collections::emptyList).stream().peek(f -> {
            f.setStatusCode(f.getColor());
            f.setAlertStatus(AlertStatus.RELEASE_ALARM);
        }).collect(Collectors.toList());
    }

    private void setAlerts(String aircraftReg, Integer statusCode, Integer color, String alarmName, Long
            alertType, Long maxVersion, Long alertStatus, WebsocketFlightData flightData, Integer
                                   index, List<FlightAlerts> flightAlertsListReturn, Long isSendAlert, Long uniqueId) {
        //障碍物告警
        FlightAlerts f = new FlightAlerts();
        f.setAircraftReg(aircraftReg);
        f.setAlarmName(alarmName);
        f.setAlertStatus(alertStatus);
        f.setAlertTime(new Date());
        f.setAlertType(alertType);
        f.setIndex(index);
        f.setStatusCode(statusCode);
        f.setColor(color);
        f.setFlightDataId(flightData.getId());
        f.setUniqueId(uniqueId);
        f.setTargetIdentification(flightData.getTargetIdentification());
        //设置版本号，如果maxVersion为空，则版本号为0，否则版本号为maxVersion+1
        f.setVersion(maxVersion == null ? 0 : maxVersion + 1);
        f.setIsSent(0);
        flightAlertsMapper.insertFlightAlerts(f);
        //如果isSendAlert为发送状态，则将告警添加到返回的告警列表中
        if (isSendAlert.equals(IsSendAlert.SENDING)) {
            flightAlertsListReturn.add(f);
        }
    }

//    private void setAlerts(String a, String aircraftReg, Integer statusCode, Integer color, String alarmName, Long alertType, Long maxVersion, Long alertStatus, WebsocketFlightData flightData, Integer index, List<FlightAlerts> flightAlertsListReturn, Long isSendAlert, String relatedAircraftReg, Long uniqueId, String targetIdentification) {
//        //障碍物告警
//        FlightAlerts f = new FlightAlerts();
//        f.setAircraftReg(aircraftReg);
//        f.setAlarmName(alarmName);
//        f.setAlertStatus(alertStatus);
//        f.setAlertTime(new Date());
//        f.setAlertType(alertType);
//        f.setIndex(index);
//        f.setStatusCode(statusCode);
//        f.setColor(color);
//        f.setFlightDataId(flightData.getId());
//        f.setTargetIdentification(targetIdentification);
//        //设置版本号，如果maxVersion为空，则版本号为0，否则版本号为maxVersion+1
//        f.setVersion(maxVersion == null ? 0 : maxVersion + 1);
//        f.setIsSent(0);
//        f.setRelatedAircraftReg(relatedAircraftReg);
//        f.setUniqueId(uniqueId);
//        f.setX(a);
//        flightAlertsMapper.insertFlightAlerts(f);
//        //如果isSendAlert为发送状态，则将告警添加到返回的告警列表中
//        if (isSendAlert.equals(IsSendAlert.SENDING)) {
//            flightAlertsListReturn.add(f);
//        }
//    }


    private void setAlerts(String a, String aircraftReg, Integer statusCode, Integer color, String alarmName,
                           Long alertType, Long maxVersion, Long alertStatus, WebsocketFlightData flightData,
                           Integer index, List<FlightAlerts> flightAlertsListReturn, Long isSendAlert,
                           String relatedAircraftReg, Long uniqueId, String targetIdentification) {

        // 创建告警对象但不立即插入
        FlightAlerts f = new FlightAlerts();
        f.setAircraftReg(aircraftReg);
        f.setAlarmName(alarmName);
        f.setAlertStatus(alertStatus);
        f.setAlertTime(new Date());
        f.setAlertType(alertType);
        f.setIndex(index);
        f.setStatusCode(statusCode);
        f.setColor(color);
        f.setFlightDataId(flightData.getId());
        f.setTargetIdentification(targetIdentification);
        // 设置版本号
        f.setVersion(maxVersion == null ? 0 : maxVersion + 1);
        f.setIsSent(0);
        f.setRelatedAircraftReg(relatedAircraftReg);
        f.setUniqueId(uniqueId);
        f.setX(a);

        // 添加到返回列表
        if (isSendAlert.equals(IsSendAlert.SENDING)) {
            flightAlertsListReturn.add(f);
        }
    }


    private void setAlerts(String aircraftReg, Integer statusCode, Integer color, String alarmName, Long
            alertType, Long maxVersion, Long alertStatus, WebsocketFlightData flightData, Integer
                                   index, List<FlightAlerts> flightAlertsListReturn, Long isSendAlert, Integer subIndex, Long uniqueId) {
        //障碍物告警
        FlightAlerts f = new FlightAlerts();
        f.setAircraftReg(aircraftReg);
        f.setAlarmName(alarmName);
        f.setAlertStatus(alertStatus);
        f.setAlertTime(new Date());
        f.setAlertType(alertType);
        f.setIndex(index);
        f.setStatusCode(statusCode);
        f.setColor(color);
        f.setFlightDataId(flightData.getId());
        f.setTargetIdentification(flightData.getTargetIdentification());
        //设置版本号，如果maxVersion为空，则版本号为0，否则版本号为maxVersion+1
        f.setVersion(maxVersion == null ? 0 : maxVersion + 1);
        f.setIsSent(0);
        f.setSubIndex(subIndex);
        f.setUniqueId(uniqueId);
        flightAlertsMapper.insertFlightAlerts(f);
        //如果isSendAlert为发送状态，则将告警添加到返回的告警列表中
        if (isSendAlert.equals(IsSendAlert.SENDING)) {
            flightAlertsListReturn.add(f);
        }
    }


}
