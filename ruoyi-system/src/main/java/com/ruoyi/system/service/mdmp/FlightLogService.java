package com.ruoyi.system.service.mdmp;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.mdmp.FlightLog;
import com.ruoyi.system.domain.mdmp.dto.QueryFlightLogListDTO;
import com.ruoyi.system.domain.mdmp.vo.FlightLogVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FlightLogService {
    /**
     * 通过航班ID查询操作日志
     *
     * @param flightId 航班ID
     * @return 结果
     */
    CommonResult<List<FlightLog>> queryFlightLog(Integer flightId);

    List<FlightLogVO> list(QueryFlightLogListDTO dto);
}
