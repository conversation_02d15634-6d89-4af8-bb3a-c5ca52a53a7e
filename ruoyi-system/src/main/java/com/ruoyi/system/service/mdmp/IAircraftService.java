package com.ruoyi.system.service.mdmp;

import com.ruoyi.system.domain.mdmp.Aircraft;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
public interface IAircraftService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public Aircraft selectAircraftById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param aircraft 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<Aircraft> selectAircraftList(Aircraft aircraft);

    /**
     * 新增【请填写功能名称】
     *
     * @param aircraft 【请填写功能名称】
     * @return 结果
     */
    public int insertAircraft(Aircraft aircraft);

    /**
     * 修改【请填写功能名称】
     *
     * @param aircraft 【请填写功能名称】
     * @return 结果
     */
    public int updateAircraft(Aircraft aircraft);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteAircraftByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteAircraftById(Long id);

    boolean checkAircraftUnique(Aircraft aircraft);

    boolean checkAircraftOverlap(Aircraft aircraft);

    boolean checkAircraftOverlapWhenUpdate(Aircraft aircraft);
}
