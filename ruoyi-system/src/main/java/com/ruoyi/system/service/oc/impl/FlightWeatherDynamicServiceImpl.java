package com.ruoyi.system.service.oc.impl;

import cn.hutool.core.util.StrUtil;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.oc.entity.FlightDynamicInfo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.mapper.oc.FlightDynamicInfoMapper;
import com.ruoyi.system.mapper.oc.FlightWeatherInfoMapper;
import com.ruoyi.system.service.oc.IFlightWeatherDynamicService;
import com.ruoyi.system.util.word.WeatherAndDynamicWordUtils;
import com.ruoyi.system.util.word.WeatherRecordPdfUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FlightWeatherDynamicServiceImpl implements IFlightWeatherDynamicService {
    @Resource
    private FlightWeatherInfoMapper flightWeatherInfoBizMapper;
    @Resource
    private FlightDynamicInfoMapper flightDynamicInfoMapper;
    @Value("${template.weatherAndDynamicTemplatePath}")
    private String weatherAndDynamicWordTemplate;

    @Override
    public FlightWeatherDynamicVo selectFlightWeatherDynamicInfoById(Long flightTaskBookId, String companyCode) {
        FlightWeatherDynamicVo flightWeatherDynamicVo = new FlightWeatherDynamicVo();
        FlightWeatherVo flightWeatherVo = flightWeatherInfoBizMapper.selectFlightWeatherInfoById(flightTaskBookId, companyCode);
        if (flightWeatherVo != null) {
            //查询任务书信息查询动态信息详情
            List<FlightDynamicInfo> flightDynamicInfos = flightDynamicInfoMapper.selectFlightDynamicInfoByTaskBookNumber(flightWeatherVo.getTaskBookNumber(), companyCode);
            flightWeatherDynamicVo.setFlightWeatherInfoVos(flightWeatherVo.getFlightWeatherInfoVos());
            flightWeatherDynamicVo.setFlightDynamicInfoList(flightDynamicInfos);
        }
        return flightWeatherDynamicVo;
    }


    @Override
    public void exportWeatherDynamicRecordWord(HttpServletResponse response, Integer flightTaskBookId, String companyCode) {
        log.info("开始导出气象记录Word文档，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
        try {
            //查询对应数据
            FlightWeatherDynamicVo flightWeatherDynamicVo = this.selectFlightWeatherDynamicInfoById(flightTaskBookId.longValue(), companyCode);
            if (flightWeatherDynamicVo == null) {
                log.error("未找到对应的气象数据，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
            }
            //组装数据
            WordMeteorologicalVo wordMeteorologicalVo = getMeteorologicalVo(flightWeatherDynamicVo);
            // 生成Word文档
            byte[] docBytes = WeatherAndDynamicWordUtils.generateWeatherRecordDocument(weatherAndDynamicWordTemplate, wordMeteorologicalVo, null);
            // 设置响应头
            String fileName = WeatherAndDynamicWordUtils.generateFileName("气象记录表", "docx");
            WeatherAndDynamicWordUtils.setWordResponseHeaders(response, fileName);
            // 使用流方式输出文件到前端
            WeatherAndDynamicWordUtils.streamWordDocumentToResponse(response, docBytes);
            log.info("气象记录Word文档导出成功，文件名: {}", fileName);
        } catch (Exception e) {
            log.error("导出气象记录Word文档失败，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode, e);
        }
    }

    @Override
    public void exportWeatherDynamicRecordPdf(HttpServletResponse response, Integer flightTaskBookId, String companyCode) {
        log.info("开始导出气象记录PDF文档，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
        try {
            //查询对应数据
            FlightWeatherDynamicVo flightWeatherDynamicVo = this.selectFlightWeatherDynamicInfoById(flightTaskBookId.longValue(), companyCode);
            if (flightWeatherDynamicVo == null) {
                log.error("未找到对应的气象数据，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
                return;
            }

            //封装Word数据
            //组装数据
            WordMeteorologicalVo wordMeteorologicalVo = getMeteorologicalVo(flightWeatherDynamicVo);

            //生成PDF文档
            byte[] pdfBytes = WeatherRecordPdfUtils.generateWeatherRecordPdf(weatherAndDynamicWordTemplate, wordMeteorologicalVo, null);

            //设置响应头
            String fileName = WeatherRecordPdfUtils.generateDefaultFileName("气象信息记录页", "pdf");
            WeatherRecordPdfUtils.setPdfResponseHeaders(response, fileName);

            //写入响应流
            WeatherRecordPdfUtils.writeToResponse(response, pdfBytes);

            log.info("气象记录PDF文档导出完成，文件名: {}", fileName);

        } catch (Exception e) {
            log.error("导出气象记录PDF文档失败", e);
            throw new RuntimeException("导出PDF文档失败: " + e.getMessage(), e);
        }
    }

    @Override
    public void exportWeatherDynamicRecordPdfBatch(HttpServletResponse response, List<Integer> flightTaskBookIds, String companyCode) {
        log.info("开始批量导出气象记录PDF文档，任务ID数量: {}, 公司代码: {}", flightTaskBookIds.size(), companyCode);
        try {
            //查询所有数据并转换为Word数据格式
            List<WordMeteorologicalVo> wordDataList = flightTaskBookIds.stream()
                    .map(id -> {
                        try {
                            FlightWeatherDynamicVo flightWeatherDynamicVo = this.selectFlightWeatherDynamicInfoById(id.longValue(), companyCode);
                            if (flightWeatherDynamicVo != null) {
                                return getMeteorologicalVo(flightWeatherDynamicVo);
                            }
                            return null;
                        } catch (Exception e) {
                            log.error("查询任务ID: {} 的气象数据失败: {}", id, e.getMessage());
                            return null;
                        }
                    })
                    .filter(Objects::nonNull)
                    .collect(Collectors.toList());

            if (wordDataList.isEmpty()) {
                log.error("未找到任何有效的气象数据");
                return;
            }

            //批量生成PDF并压缩
            byte[] zipBytes = WeatherRecordPdfUtils.generateWeatherRecordPdfBatch(
                    weatherAndDynamicWordTemplate, wordDataList, null, "气象信息记录页");

            //设置响应头
            String fileName = WeatherRecordPdfUtils.generateDefaultFileName("气象信息记录页批量导出", "zip");
            WeatherRecordPdfUtils.setZipResponseHeaders(response, fileName);

            //写入响应流
            WeatherRecordPdfUtils.writeToResponse(response, zipBytes);

            log.info("批量气象记录PDF文档导出完成，文件名: {}, 成功处理数量: {}", fileName, wordDataList.size());

        } catch (Exception e) {
            log.error("批量导出气象记录PDF文档失败", e);
            throw new RuntimeException("批量导出PDF文档失败: " + e.getMessage(), e);
        }
    }


    private WordMeteorologicalVo getMeteorologicalVo(FlightWeatherDynamicVo flightWeatherDynamicVo) {
        // 创建目标对象实例并拷贝属性
        WordMeteorologicalVo wordMeteorologicalVo = new WordMeteorologicalVo();
        BeanUtils.copyProperties(flightWeatherDynamicVo, wordMeteorologicalVo);
        List<FlightWeatherInfoVo> flightWeatherInfoVos = flightWeatherDynamicVo.getFlightWeatherInfoVos();
        if (flightWeatherInfoVos == null || flightWeatherInfoVos.isEmpty()) {
            throw new RuntimeException("未找到对应的气象数据");
        }
        flightWeatherInfoVos = flightWeatherInfoVos.stream().filter(flightWeatherInfoVo -> StrUtil.isNotEmpty(flightWeatherInfoVo.getLocationName())).collect(Collectors.toList());

        List<WordFlightWeatherInfoVo> departureWeatherInfoList = new ArrayList<>();
        List<WordFlightWeatherInfoVo> arrivalWeatherInfoList = new ArrayList<>();
        flightWeatherInfoVos.forEach(flightWeatherInfoVo -> {
            if (flightWeatherInfoVo.getLocationType() == 0) {
                WordFlightWeatherInfoVo wordFlightWeatherInfoVo = new WordFlightWeatherInfoVo();
                BeanUtils.copyProperties(flightWeatherInfoVo, wordFlightWeatherInfoVo);
                departureWeatherInfoList.add(wordFlightWeatherInfoVo);
            } else if (flightWeatherInfoVo.getLocationType() == 1) {
                WordFlightWeatherInfoVo arrivalWeatherInfo = new WordFlightWeatherInfoVo();
                BeanUtils.copyProperties(flightWeatherInfoVo, arrivalWeatherInfo);
                arrivalWeatherInfoList.add(arrivalWeatherInfo);
            }
        });
        wordMeteorologicalVo.setDepartureWeatherInfoList(departureWeatherInfoList);
        wordMeteorologicalVo.setArrivalWeatherInfoList(arrivalWeatherInfoList);

        List<FlightDynamicInfo> flightDynamicInfoList = flightWeatherDynamicVo.getFlightDynamicInfoList();
        List<WordFlightWeatherDynamicVo> dynamicInfoList = new ArrayList<>();
        flightDynamicInfoList.forEach(flightDynamicInfo -> {
            WordFlightWeatherDynamicVo wordFlightWeatherInfoVo = new WordFlightWeatherDynamicVo();
            BeanUtils.copyProperties(flightDynamicInfo, wordFlightWeatherInfoVo);
            dynamicInfoList.add(wordFlightWeatherInfoVo);
        });
        wordMeteorologicalVo.setDynamicInfoList(dynamicInfoList);
        return wordMeteorologicalVo;
    }

}
