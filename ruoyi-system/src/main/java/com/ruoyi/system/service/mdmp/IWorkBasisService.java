package com.ruoyi.system.service.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.WorkBasis;

/**
 * 作业区基础信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface IWorkBasisService 
{
    /**
     * 查询作业区基础信息
     * 
     * @param id 作业区基础信息主键
     * @return 作业区基础信息
     */
    public WorkBasis selectWorkBasisById(Long id);

    /**
     * 查询作业区基础信息列表
     * 
     * @param workBasis 作业区基础信息
     * @return 作业区基础信息集合
     */
    public List<WorkBasis> selectWorkBasisList(WorkBasis workBasis);

    /**
     * 新增作业区基础信息
     * 
     * @param workBasis 作业区基础信息
     * @return 结果
     */
    public int insertWorkBasis(WorkBasis workBasis);

    /**
     * 修改作业区基础信息
     * 
     * @param workBasis 作业区基础信息
     * @return 结果
     */
    public int updateWorkBasis(WorkBasis workBasis);

    /**
     * 批量删除作业区基础信息
     * 
     * @param ids 需要删除的作业区基础信息主键集合
     * @return 结果
     */
    public int deleteWorkBasisByIds(Long[] ids);

    /**
     * 删除作业区基础信息信息
     * 
     * @param id 作业区基础信息主键
     * @return 结果
     */
    public int deleteWorkBasisById(Long id);
}
