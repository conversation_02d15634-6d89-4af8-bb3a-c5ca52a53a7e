package com.ruoyi.system.service.oc.impl;

import com.ruoyi.common.config.RuoYiConfig;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.file.FileNameLengthLimitExceededException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.Util;
import com.ruoyi.common.utils.file.FileUploadUtils;
import com.ruoyi.common.utils.file.MimeTypeUtils;
import com.ruoyi.system.domain.oc.FileDir;
import com.ruoyi.system.mapper.oc.FileDirMapper;
import com.ruoyi.system.service.oc.IFileDirService;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.io.File;
import java.util.List;
import java.util.UUID;

/**
 * 文件目录Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-23
 */
@Service
public class FileDirServiceImpl implements IFileDirService {
    @Resource
    private FileDirMapper fileDirMapper;

    /**
     * 查询文件目录
     *
     * @param fileDirId 文件目录主键
     * @return 文件目录
     */
    @Override
    public FileDir selectFileDirByFileDirId(Long fileDirId) {
        return fileDirMapper.selectFileDirByFileDirId(fileDirId);
    }

    /**
     * 查询文件目录列表
     *
     * @param fileDir 文件目录
     * @return 文件目录
     */
    @Override
    public List<FileDir> selectFileDirList(FileDir fileDir) {
        return fileDirMapper.selectFileDirList(fileDir);
    }

    /**
     * 新增文件目录
     *
     * @param fileDir 文件目录
     * @return 结果
     */
    @Override
    public int insertFileDir(FileDir fileDir) {
        fileDir.setCreateTime(DateUtils.getNowDate());
        fileDir.setType(1);
        if (null != fileDir.getParentId()) {
            FileDir b_fileDir = fileDirMapper.selectFileDirByFileDirId(fileDir.getParentId());
            if (null != b_fileDir) {
                b_fileDir.setHaveChildren(Boolean.TRUE);
                fileDirMapper.updateFileDir(b_fileDir);
            }
        }
        return fileDirMapper.insertFileDir(fileDir);
    }

    /**
     * 修改文件目录
     *
     * @param fileDir 文件目录
     * @return 结果
     */
    @Override
    public int updateFileDir(FileDir fileDir) {
        fileDir.setUpdateTime(DateUtils.getNowDate());
        return fileDirMapper.updateFileDir(fileDir);
    }

    /**
     * 批量删除文件目录
     *
     * @param fileDirIds 需要删除的文件目录主键
     * @return 结果
     */
    @Override
    public int deleteFileDirByFileDirIds(Long[] fileDirIds) {
        return fileDirMapper.deleteFileDirByFileDirIds(fileDirIds);
    }

    /**
     * 删除文件目录信息
     *
     * @param fileDirId 文件目录主键
     * @return 结果
     */
    @Override
    public int deleteFileDirByFileDirId(Long fileDirId) {
        return fileDirMapper.deleteFileDirByFileDirId(fileDirId);
    }


    @Override
    public AjaxResult importFile(MultipartFile file, Long fileDirId) {

        try {
            // 上传文件路径
            String filePath = RuoYiConfig.getDownloadPath() + "queryfile";
            String fileName = file.getOriginalFilename();
            FileDir fileDirToDB = new FileDir();
            fileDirToDB.setType(2);
            fileDirToDB.setFileName(fileName);
            fileDirToDB.setFileDirName(fileName);
            fileDirToDB.setParentId(null);
            AjaxResult ajax = AjaxResult.success();
            ajax.put("fileName", fileName);
            Long id = fileDirId;
            String str = "";
            while (true) {
                FileDir fileDir = fileDirMapper.selectFileDirByFileDirId(id);
                str = fileDir.getFileDirName() + "/" + str;
                if (fileDir.getParentId() == 0) {
                    break;
                } else {
                    FileDir b_fileDir = fileDirMapper.selectFileDirByFileDirId(fileDir.getParentId());
                    id = b_fileDir.getFileDirId();
                }
            }
            FileDir b_fileDir = fileDirMapper.selectFileDirByFileDirId(fileDirId);
            if (null != b_fileDir) {
                fileDirToDB.setParentId(b_fileDir.getFileDirId());
                b_fileDir.setHaveChildren(Boolean.TRUE);
                fileDirMapper.updateFileDir(b_fileDir);
            }
            filePath = filePath + "/" + str;
            int fileNamelength = file.getOriginalFilename().length();
            if (fileNamelength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH) {
                throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
            }
            FileUploadUtils.assertAllowed(file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
            String fileNameSubstring = ".png";
            if (null != fileDirToDB && null != fileDirToDB.getFileName() && !fileDirToDB.getFileName().equals("") && fileDirToDB.getFileName().endsWith("doc")) {
                fileNameSubstring = ".doc";
            } else if (null != fileDirToDB && null != fileDirToDB.getFileName() && !fileDirToDB.getFileName().equals("") && fileDirToDB.getFileName().endsWith("docx")) {
                fileNameSubstring = ".docx";
            } else if (null != fileDirToDB && null != fileDirToDB.getFileName() && !fileDirToDB.getFileName().equals("") && fileDirToDB.getFileName().endsWith("pdf")) {
                fileNameSubstring = ".pdf";
            } else if (null != fileDirToDB && null != fileDirToDB.getFileName() && !fileDirToDB.getFileName().equals("") && fileDirToDB.getFileName().endsWith("png")) {
                fileNameSubstring = ".png";
            } else if (null != fileDirToDB && null != fileDirToDB.getFileName() && !fileDirToDB.getFileName().equals("") && fileDirToDB.getFileName().endsWith("txt")) {
                fileNameSubstring = ".txt";
            }
            fileName = DateUtils.dateTimeNow() + UUID.randomUUID().toString().replaceAll("-", "") + fileNameSubstring;
            File desc = FileUploadUtils.getAbsoluteFile(filePath, fileName);
            file.transferTo(desc);
            FileUploadUtils.getPathFileName(filePath, fileName);

            // 地址更新到数据库

            fileDirToDB.setSaveUrl(filePath + fileName);
            fileDirMapper.insertFileDir(fileDirToDB);
            if (null != fileDirToDB && null != fileDirToDB.getSaveUrl() && !fileDirToDB.getSaveUrl().equals("") && fileDirToDB.getSaveUrl().endsWith("doc")) {
                Util.doc2img(fileDirToDB.getSaveUrl(), fileDirToDB.getSaveUrl().replaceAll("doc", "png"));
            } else if (null != fileDirToDB && null != fileDirToDB.getSaveUrl() && !fileDirToDB.getSaveUrl().equals("") && fileDirToDB.getSaveUrl().endsWith("docx")) {
                Util.doc2img(fileDirToDB.getSaveUrl(), fileDirToDB.getSaveUrl().replaceAll("docx", "png"));
            } else if (null != fileDirToDB && null != fileDirToDB.getSaveUrl() && !fileDirToDB.getSaveUrl().equals("") && fileDirToDB.getSaveUrl().endsWith("pdf")) {
                Util.pdf2img(fileDirToDB.getSaveUrl(), fileDirToDB.getSaveUrl().replaceAll("pdf", "png"));
            }
            return ajax;
        } catch (Exception e) {
            return AjaxResult.error(e.getMessage());
        }

    }


}
