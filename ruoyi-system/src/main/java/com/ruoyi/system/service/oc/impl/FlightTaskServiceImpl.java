package com.ruoyi.system.service.oc.impl;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.oc.FlightTaskBook;
import com.ruoyi.system.domain.oc.FlightTaskBookExample;
import com.ruoyi.system.domain.oc.FlightTaskInfoExample;
import com.ruoyi.system.domain.oc.dto.FlightTaskAddDTO;
import com.ruoyi.system.domain.oc.dto.FlightTaskQueryDTO;
import com.ruoyi.system.mapper.oc.FlightTaskBookMapper;
import com.ruoyi.system.mapper.oc.FlightTaskInfoMapper;
import com.ruoyi.system.service.oc.FlightTaskService;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 飞行任务书
 * @date 2025/7/18 14:42:37
 */
@Service("flightTaskService")
public class FlightTaskServiceImpl implements FlightTaskService {

    @Resource
    private FlightTaskBookMapper flightTaskBookMapper;

    @Resource
    private FlightTaskInfoMapper flightTaskInfoMapper;

    @Resource
    private RedisTemplate<String, String> redisTemplate;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean add(String companyCode, FlightTaskAddDTO dto) {
        //编号规则 20250718-0001 日期+编号
        String today = DateUtils.dateTimeNowStrYYYYMMDD();
        String noRedisKey = "oc:flight-task:no:" + today;
        String redisNumber = redisTemplate.opsForValue().get(noRedisKey);
        String taskBookNumber;
        if (StringUtils.isNotEmpty(redisNumber)) {
            redisNumber = String.valueOf(Integer.parseInt(redisNumber) + 1);
            //转换为4位字符串,不足4位前面补0
            redisNumber = StringUtils.leftPad(redisNumber, 4, "0");
            taskBookNumber = today +"-"+ redisNumber;
        } else {
            taskBookNumber = today + "-0001";
            redisNumber = "0001";
        }
        dto.setId(null);
        dto.setCompanyCode(companyCode);
        dto.setTaskStatus(0);
        dto.setTaskBookNumber(taskBookNumber);
        dto.setCreatedAt(DateUtils.getNowDate());
        dto.setUpdatedAt(DateUtils.getNowDate());
        flightTaskBookMapper.insert(dto);

        dto.getBatches().forEach(flightTaskInfo -> {
            flightTaskInfo.setCompanyCode(companyCode);
            flightTaskInfo.setTaskBookNumber(taskBookNumber);
            flightTaskInfo.setCreatedAt(DateUtils.getNowDate());
            flightTaskInfo.setUpdatedAt(DateUtils.getNowDate());
            flightTaskInfoMapper.insert(flightTaskInfo);
        });
        redisTemplate.opsForValue().set(noRedisKey, redisNumber, 1, TimeUnit.DAYS);
        return true;
    }

    @Override
    public List<FlightTaskBook> queryFlightTaskInfo(String companyCode, FlightTaskQueryDTO dto) {
        FlightTaskBookExample example = new FlightTaskBookExample();
        FlightTaskBookExample.Criteria criteria = example.createCriteria();
        criteria.andCompanyCodeEqualTo(companyCode);
        if (StringUtils.isNotEmpty(dto.getStartDate())) {
            criteria.andFlightDateGreaterThanOrEqualTo(dto.getStartDate());
        }
        if (StringUtils.isNotEmpty(dto.getEndDate())) {
            criteria.andFlightDateLessThanOrEqualTo(dto.getEndDate());
        }
        if (StringUtils.isNotEmpty(dto.getRegistrationNumber())) {
            criteria.andRegistrationNumberEqualTo(dto.getRegistrationNumber());
        }

        //按照航班日期、呼号排序
        example.setOrderByClause("flight_date desc,call_sign asc");
        return flightTaskBookMapper.selectByExample(example);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean update(String companyCode, FlightTaskAddDTO dto) {

        FlightTaskBook dbTaskBook = flightTaskBookMapper.selectByPrimaryKey(dto.getId());
        if (dbTaskBook != null && dbTaskBook.getCompanyCode().equals(companyCode)) {

            FlightTaskAddDTO.updateFlightTaskBook(dto, dbTaskBook);
            dbTaskBook.setUpdatedAt(DateUtils.getNowDate());
            flightTaskBookMapper.updateByPrimaryKey(dbTaskBook);

            //删除
            FlightTaskInfoExample deleteExample = new FlightTaskInfoExample();
            deleteExample.createCriteria().andTaskBookNumberEqualTo(dbTaskBook.getTaskBookNumber());
            flightTaskInfoMapper.deleteByExample(deleteExample);

            dto.getBatches().forEach(b -> {
                b.setId(null);
                b.setCompanyCode(companyCode);
                b.setTaskBookNumber(dbTaskBook.getTaskBookNumber());
                flightTaskInfoMapper.insert(b);
            });
            return true;
        }
        return false;
    }

    @Override
    public FlightTaskAddDTO getInfo(String companyCode, Long id) {
        FlightTaskBook dbTaskBook = flightTaskBookMapper.selectByPrimaryKey(id);
        if (dbTaskBook != null && dbTaskBook.getCompanyCode().equals(companyCode)) {
            FlightTaskAddDTO dto = new FlightTaskAddDTO();
            BeanUtils.copyBeanProp(dto, dbTaskBook);

            FlightTaskInfoExample example = new FlightTaskInfoExample();
            example.createCriteria().andTaskBookNumberEqualTo(dto.getTaskBookNumber());
            example.setOrderByClause("batch asc");
            dto.setBatches(flightTaskInfoMapper.selectByExample(example));
            return dto;
        }
        return null;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean delete(String companyCode, Long id) {
        FlightTaskBook dbTaskBook = flightTaskBookMapper.selectByPrimaryKey(id);
        if (dbTaskBook != null && dbTaskBook.getCompanyCode().equals(companyCode)) {
            flightTaskBookMapper.deleteByPrimaryKey(id);
            FlightTaskInfoExample example = new FlightTaskInfoExample();
            example.createCriteria().andTaskBookNumberEqualTo(dbTaskBook.getTaskBookNumber());
            flightTaskInfoMapper.deleteByExample(example);
            return true;
        }
        return false;
    }
}
