package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.system.domain.mdmp.RCloudinfores;
import com.ruoyi.system.mapper.mdmp.RCloudinforesMapper;
import com.ruoyi.system.service.mdmp.IRCloudinforesService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 云Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class RCloudinforesServiceImpl implements IRCloudinforesService {
    @Autowired
    private RCloudinforesMapper rCloudinforesMapper;

    /**
     * 查询云
     *
     * @param id 云主键
     * @return 云
     */
    @Override
    public RCloudinfores selectRCloudinforesById(Long id) {
        return rCloudinforesMapper.selectRCloudinforesById(id);
    }

    /**
     * 查询云列表
     *
     * @param rCloudinfores 云
     * @return 云
     */
    @Override
    public List<RCloudinfores> selectRCloudinforesList(RCloudinfores rCloudinfores) {
        return rCloudinforesMapper.selectRCloudinforesList(rCloudinfores);
    }

    /**
     * 新增云
     *
     * @param rCloudinfores 云
     * @return 结果
     */
    @Override
    public int insertRCloudinfores(RCloudinfores rCloudinfores) {
        return rCloudinforesMapper.insertRCloudinfores(rCloudinfores);
    }

    /**
     * 修改云
     *
     * @param rCloudinfores 云
     * @return 结果
     */
    @Override
    public int updateRCloudinfores(RCloudinfores rCloudinfores) {
        return rCloudinforesMapper.updateRCloudinfores(rCloudinfores);
    }

    /**
     * 批量删除云
     *
     * @param ids 需要删除的云主键
     * @return 结果
     */
    @Override
    public int deleteRCloudinforesByIds(Long[] ids) {
        return rCloudinforesMapper.deleteRCloudinforesByIds(ids);
    }

    /**
     * 删除云信息
     *
     * @param id 云主键
     * @return 结果
     */
    @Override
    public int deleteRCloudinforesById(Long id) {
        return rCloudinforesMapper.deleteRCloudinforesById(id);
    }

    @Override
    public void insertRCloudinforesList(List<RCloudinfores> cloudResDtoList) {
        if (cloudResDtoList != null && cloudResDtoList.size() > 0) {
            rCloudinforesMapper.insertRCloudinforesList(cloudResDtoList);
        }
    }
}
