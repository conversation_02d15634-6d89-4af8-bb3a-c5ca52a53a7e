package com.ruoyi.system.service.mdmp;

import com.ruoyi.common.config.rule.HomeMapDataRuleConfig;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.vo.*;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.util.Point3D;

import java.util.List;

/**
 * 地图数据Service接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface IMapDataService {


    /**
     * 查询地图数据
     *
     * @param mapData 地图数据
     * @return 地图数据集合
     */
    public List<MapData> selectMapDataList();

    /**
     * 新增地图数据
     *
     * @param mapData 地图数据
     * @return 结果
     */
    public int insertMapData();


    int deleteMapData();

    /**
     * 查询地图数据
     * 根据高度区间
     * 经纬度
     *
     * @param queryMapDataVo
     * @return
     */
    List<MapData> queryMapData(QueryMapDataVo queryMapDataVo);

    MapData mapData(QueryMapDataLongLatVo param);

    int topographic(List<AddTopographicVo> param);

    List<MapData> mapDataList(AirspaceLongLat airspaceLongLat);

    MapData getMapData(QueryMapDataLongLatVo param);

    MeteInfo getMeteInfo(MeteInfoParam meteInfoParam);


    void saveWorkMapData(FlightPlanWork flightPlanWork);


    void saveRouteMapData(FlightPlanRoute e);


    void processAndInsertAirspaceData(Airspace airspace, int graphicsType, List<AirspaceLongLat> processedLongLatList, long id);

    HomeMapDataRuleConfig homeMapRule(List<String> deptCodeList);


    /**
     * 设置 查询障碍物缓存
     *
     * @return
     */
    void setAirspaceCache(List<String> deptCodeList);

    /**
     * 航路规划
     */
    List<Point3D> routePlanning(QueryRoutePlanningVo routePlanningVo,List<String> stringList);
}
