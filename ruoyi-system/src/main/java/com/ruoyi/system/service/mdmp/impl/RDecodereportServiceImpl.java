package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.system.domain.mdmp.vo.DecodeReportVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.mdmp.RDecodereportMapper;
import com.ruoyi.system.domain.mdmp.RDecodereport;
import com.ruoyi.system.service.mdmp.IRDecodereportService;

/**
 * 报文信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class RDecodereportServiceImpl implements IRDecodereportService
{
    @Autowired
    private RDecodereportMapper rDecodereportMapper;

    /**
     * 查询报文信息
     *
     * @param id 报文信息主键
     * @return 报文信息
     */
    @Override
    public RDecodereport selectRDecodereportById(Long id)
    {
        return rDecodereportMapper.selectRDecodereportById(id);
    }

    /**
     * 查询报文信息列表
     *
     * @param rDecodereport 报文信息
     * @return 报文信息
     */
    @Override
    public List<RDecodereport> selectRDecodereportList(RDecodereport rDecodereport)
    {
        return rDecodereportMapper.selectRDecodereportList(rDecodereport);
    }

    /**
     * 新增报文信息
     *
     * @param rDecodereport 报文信息
     * @return 结果
     */
    @Override
    public int insertRDecodereport(RDecodereport rDecodereport)
    {
        return rDecodereportMapper.insertRDecodereport(rDecodereport);
    }

    /**
     * 修改报文信息
     *
     * @param rDecodereport 报文信息
     * @return 结果
     */
    @Override
    public int updateRDecodereport(RDecodereport rDecodereport)
    {
        return rDecodereportMapper.updateRDecodereport(rDecodereport);
    }

    /**
     * 批量删除报文信息
     *
     * @param ids 需要删除的报文信息主键
     * @return 结果
     */
    @Override
    public int deleteRDecodereportByIds(Long[] ids)
    {
        return rDecodereportMapper.deleteRDecodereportByIds(ids);
    }

    /**
     * 删除报文信息信息
     *
     * @param id 报文信息主键
     * @return 结果
     */
    @Override
    public int deleteRDecodereportById(Long id)
    {
        return rDecodereportMapper.deleteRDecodereportById(id);
    }

    @Override
    public List<DecodeReportVo> selectRDecodereportList() {
        return rDecodereportMapper.selectRDecodereportListByTime();
    }

    @Override
    public List<String> selectNewContentList() {
      return  rDecodereportMapper.selectNewContentList();
    }
}
