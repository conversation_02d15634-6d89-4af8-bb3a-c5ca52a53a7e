package com.ruoyi.system.service.oc;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.oc.AirportBase;
import com.ruoyi.system.domain.oc.dto.AddAirportBaseDTO;
import com.ruoyi.system.domain.oc.dto.QueryAirportBaseDTO;
import com.ruoyi.system.domain.oc.dto.UpdateAirportBaseDTO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/18 10:58
 * @mood 功能
 */
public interface AirportBaseService {
    /**
     * 查询列表
     */
    PageCommonResult<List<AirportBase>> selectList(QueryAirportBaseDTO dto, Integer pageNum, Integer pageSize, String companyCode);
    PageCommonResult<List<AirportBase>> queryAll(String companyCode);

    /**
     * 查询一条
     */
    CommonResult<AirportBase> selectOneById(Long id);

    /**
     * 新增一条
     */
    CommonResult<String> insertOne(AddAirportBaseDTO dto, String companyCode);

    /**
     * 修改一条
     */
    CommonResult<String> updateOne(UpdateAirportBaseDTO dto);

    /**
     * 删除一条
     */
    public int deleteOneById(Long id);

    /**
     * 批量删除
     */
    CommonResult<String> deleteAllByIds(Long[] ids);
}
