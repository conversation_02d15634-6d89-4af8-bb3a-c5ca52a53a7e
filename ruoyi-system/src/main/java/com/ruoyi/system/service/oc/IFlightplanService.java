package com.ruoyi.system.service.oc;

import java.util.List;

import com.ruoyi.system.domain.oc.dto.AddFlightPlanDTO;
import com.ruoyi.system.domain.oc.dto.QueryFlightPlanDTO;
import com.ruoyi.system.domain.oc.entity.Flightplan;

/**
 * 航班计划信息Service接口
 *
 * <AUTHOR>
 * @date 2021-09-22
 */
public interface IFlightplanService {
    /**
     * 查询航班计划信息
     *
     * @param flightplanId 航班计划信息主键
     * @return 航班计划信息
     */
    public Flightplan selectFlightplanByFlightplanId(Long flightplanId);

    /**
     * 查询航班计划信息列表
     *
     * @param dto 航班计划信息
     * @return 航班计划信息集合
     */
    List<Flightplan> selectFlightplanList(QueryFlightPlanDTO dto, String companyCode, Integer pageNum, Integer pageSize);

    List<Flightplan> selectFlightplan(QueryFlightPlanDTO dto, String companyCode);

    /**
     * 新增航班计划信息
     *
     * @param dto 航班计划信息
     * @return 结果
     */
    public int insertFlightplan(AddFlightPlanDTO dto, String companyCode, String createBy);

    /**
     * 修改航班计划信息
     *
     * @param flightplan 航班计划信息
     * @return 结果
     */
    public int updateFlightplan(Flightplan flightplan);

    /**
     * 批量删除航班计划信息
     *
     * @param flightplanIds 需要删除的航班计划信息主键集合
     * @return 结果
     */
    public int deleteFlightplanByFlightplanIds(Long[] flightplanIds);

    /**
     * 删除航班计划信息信息
     *
     * @param flightplanId 航班计划信息主键
     * @return 结果
     */
    public int deleteFlightplanByFlightplanId(Long flightplanId);
}
