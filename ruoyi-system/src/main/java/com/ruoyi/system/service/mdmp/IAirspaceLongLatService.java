package com.ruoyi.system.service.mdmp;

import com.ruoyi.system.domain.mdmp.AirspaceLongLat;

import java.util.List;


/**
 * 空域经纬度Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface IAirspaceLongLatService 
{
    /**
     * 查询空域经纬度
     * 
     * @param id 空域经纬度主键
     * @return 空域经纬度
     */
    public AirspaceLongLat selectAirspaceLongLatById(Long id);

    /**
     * 查询空域经纬度列表
     * 
     * @param airspaceLongLat 空域经纬度
     * @return 空域经纬度集合
     */
    public List<AirspaceLongLat> selectAirspaceLongLatList(AirspaceLongLat airspaceLongLat);

    /**
     * 新增空域经纬度
     * 
     * @param airspaceLongLat 空域经纬度
     * @return 结果
     */
    public int insertAirspaceLongLat(AirspaceLongLat airspaceLongLat);

    /**
     * 修改空域经纬度
     * 
     * @param airspaceLongLat 空域经纬度
     * @return 结果
     */
    public int updateAirspaceLongLat(AirspaceLongLat airspaceLongLat);

    /**
     * 批量删除空域经纬度
     * 
     * @param ids 需要删除的空域经纬度主键集合
     * @return 结果
     */
    public int deleteAirspaceLongLatByIds(Long[] ids);

    /**
     * 删除空域经纬度信息
     * 
     * @param id 空域经纬度主键
     * @return 结果
     */
    public int deleteAirspaceLongLatById(Long id);
}
