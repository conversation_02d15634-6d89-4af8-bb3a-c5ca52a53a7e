package com.ruoyi.system.service.oc.impl;

import com.ruoyi.system.domain.oc.entity.DperationStandard;
import com.ruoyi.system.mapper.oc.DperationStandardMapper;
import com.ruoyi.system.service.oc.DperationStandardService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/24 11:10
 * @mood 功能
 */
@Service
public class DperationStandardServiceImpl implements DperationStandardService {
    @Resource
    private DperationStandardMapper dperationStandardMapper;

    /**
     * 查询列表
     */
    @Override
    public List<DperationStandard> selectList(DperationStandard param) {
        List<DperationStandard> list = dperationStandardMapper.selectList(param);
        return list;
    }

    /**
     * 查询一条
     */
    @Override
    public DperationStandard selectOneById(Long id) {
        return dperationStandardMapper.selectOneById(id);
    }

    /**
     * 新增一条
     */
    @Override
    public int insertOne(DperationStandard param) {
        return dperationStandardMapper.insertOne(param);
    }

    /**
     * 修改一条
     */
    @Override
    public int updateOne(DperationStandard param) {
        return dperationStandardMapper.updateOne(param);
    }

    /**
     * 删除一条
     */
    @Override
    public int deleteOneById(Long id) {
        return dperationStandardMapper.deleteOneById(id);
    }

    /**
     * 批量删除
     */
    @Override
    public int deleteAllByIds(Long[] ids) {
        return dperationStandardMapper.deleteAllByIds(ids);
    }
}

