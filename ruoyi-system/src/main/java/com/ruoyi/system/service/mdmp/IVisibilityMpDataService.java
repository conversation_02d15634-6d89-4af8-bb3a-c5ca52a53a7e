package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.VisibilityMpData;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface IVisibilityMpDataService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public VisibilityMpData selectVisibilityMpDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param visibilityMpData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<VisibilityMpData> selectVisibilityMpDataList(VisibilityMpData visibilityMpData);

    /**
     * 新增【请填写功能名称】
     *
     * @param visibilityMpData 【请填写功能名称】
     * @return 结果
     */
    public int insertVisibilityMpData(VisibilityMpData visibilityMpData);

    /**
     * 修改【请填写功能名称】
     *
     * @param visibilityMpData 【请填写功能名称】
     * @return 结果
     */
    public int updateVisibilityMpData(VisibilityMpData visibilityMpData);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteVisibilityMpDataByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteVisibilityMpDataById(Long id);


    double selectVisibilityMpDataByMeteInfo(MeteInfoParam meteInfoParam);

    List<Double> getList(MeteParam meteParam);
}
