package com.ruoyi.system.service.oc;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.oc.dto.AircrewRecordDTO;
import com.ruoyi.system.domain.oc.dto.PilotDetailDTO;
import com.ruoyi.system.domain.oc.dto.PilotQualificationDTO;
import com.ruoyi.system.domain.oc.dto.QueryPilotQualificationDTO;
import com.ruoyi.system.domain.oc.vo.PilotDetailVO;
import com.ruoyi.system.domain.oc.vo.PilotQualificationVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface PilotService {

    /**
     * 查询飞行员资质信息
     *
     * @param dto 查询飞行员资质请求参数
     * @param companyCode 航司代码
     * @return 结果
     */
    List<PilotQualificationVO> getPilotQualification(QueryPilotQualificationDTO dto, String companyCode);

    /**
     * 编辑飞行员资质信息
     *
     * @param dto 修改飞行员资质请求参数
     * @return 结果
     */
    int editQualification(PilotQualificationDTO dto);

    /**
     * 飞行员详情
     * @param dto 用户ID
     * @return 结果
     */
    CommonResult<PilotDetailVO> pilotDetails(PilotDetailDTO dto);

    /**
     * 飞行员文件记录上传
     *
     * @param files          文件
     * @param userId         用户ID
     * @param firstFileType  文件第一类型
     * @param secondFileType 文件第二类型
     * @param remark         文件简介
     * @param authId       上传人
     * @return 结果
     */
    AjaxResult uploadAircrewRecord(MultipartFile[] files, Long userId, Integer firstFileType, Integer secondFileType, String remark, Long authId);

    /**
     * 飞行员文件记录下载
     *
     * @param aircrewRecordId 飞行员记录ID
     * @param response        response
     */
    void downloadAircrewRecord(Long aircrewRecordId, HttpServletResponse response);

    /**
     * 飞行员文件记录查询
     *
     * @param userId        用户ID
     * @param firstFileType 文件第一类型
     * @return 结果
     */
    AjaxResult queryAircrewRecord(Long userId, Integer firstFileType);

    /**
     * 飞行员记录查询（单个）
     *
     * @param aircrewRecordId 飞行员记录ID
     * @return 结果
     */
    AjaxResult selectAircrewRecord(Long aircrewRecordId);

    /**
     * 飞行员记录修改
     *
     * @param aircrewRecordDTO 修改参数
     * @param authId 修改人
     * @return 结果
     */
    int updateAircrewRecord(AircrewRecordDTO aircrewRecordDTO, Long authId);

    /**
     * 飞行员记录删除
     *
     * @param aircrewRecordId 飞行员记录ID
     * @return 结果
     */
    int deleteAircrewRecord(Long aircrewRecordId);

    /**
     * 飞行员记录文件预览
     *
     * @param aircrewRecordId 飞行员记录ID
     * @return 结果
     */
    AjaxResult filePreview(Long aircrewRecordId);

    /**
     * IO流读取文件
     *
     * @param response response
     * @param fileType 文件类型
     * @param newFileName 文件名
     */
    void getFilePreview(HttpServletResponse response, String fileType, String newFileName);
}
