package com.ruoyi.system.service.mdmp.impl;

import com.ruoyi.common.config.rule.HomeMapDataRuleConfig;
import com.ruoyi.common.config.rule.Obstacle;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.vo.*;
import com.ruoyi.system.domain.type.AirspaceType;
import com.ruoyi.system.domain.type.GraphicsType;
import com.ruoyi.system.domain.type.WorkType;
import com.ruoyi.system.mapper.mdmp.*;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.service.mdmp.IMapDataObstacleService;
import com.ruoyi.system.service.mdmp.IMapDataService;
import com.ruoyi.system.util.*;
import org.locationtech.jts.geom.Polygon;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

/**
 * 长期飞行计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Service
@EnableAsync
public class MapDataServiceImpl implements IMapDataService {
    @Resource
    private MapDataMapper mapDataMapper;

    @Resource
    private AirspaceMapper airspaceMapper;

    @Resource
    private WorkMapDataMapper workMapDataMapper;

    @Resource
    private RouteMapDataMapper routeMapDataMapper;

    @Resource
    private AircraftAirspaceMapper aircraftAirspaceMapper;

    @Resource
    private AirspaceMapDataMapper airspaceMapDataMapper;


    @Resource
    private IMapDataObstacleService mapDataObstacleService;


    @Resource
    private HomeMapDataRuleConfig homeMapRule;

    @Resource
    private RedisCache redisCache;


    Logger logger = LoggerFactory.getLogger(MapDataServiceImpl.class);


    @Override
    public List<MapData> selectMapDataList() {
        return mapDataMapper.selectMapDataList();
    }


    @Override
    public int insertMapData() {

        BigDecimal lng = new BigDecimal("84.80");
        BigDecimal lat = new BigDecimal("43.70");


        List<MapData> mapDataList = new ArrayList<>();

        BigDecimal add = new BigDecimal("0.01");

        Integer heightStart = 354;
        Integer heightEnd = 750;

        Integer pressureStart = 950;
        Integer pressureEnd = 950;

        Integer time = 0;

        for (int k = 0; k < 8; k++) {
            if (k == 1) {
                heightStart = 750;
                heightEnd = 825;

                pressureStart = 925;
                pressureEnd = 925;
            } else if (k == 2) {
                heightStart = 825;
                heightEnd = 1200;

                pressureStart = 900;
                pressureEnd = 900;
            } else if (k == 3) {
                heightStart = 1200;
                heightEnd = 1710;

                pressureStart = 850;
                pressureEnd = 850;
            } else if (k == 4) {
                heightStart = 1710;
                heightEnd = 2460;

                pressureStart = 800;
                pressureEnd = 800;
            } else if (k == 5) {
                heightStart = 2460;
                heightEnd = 3600;

                pressureStart = 700;
                pressureEnd = 700;
            } else if (k == 6) {
                heightStart = 3600;
                heightEnd = 4800;

                pressureStart = 600;
                pressureEnd = 600;
            } else if (k == 7) {
                heightStart = 4800;
                heightEnd = 6300;

                pressureStart = 500;
                pressureEnd = 500;
            }
            lat = new BigDecimal("43.70");
            lng = new BigDecimal("84.80");
            for (int i = 0; i < 120; i++) {
                lng = new BigDecimal("84.80");
                for (int j = 0; j < 165; j++) {
                    MapData mapData = new MapData();
                    mapData.setLongitudeStart(lng);
                    mapData.setLongitudeEnd(lng.add(add));
                    mapData.setLayer(k + 1);
                    mapData.setLongitudeIncrease(add);
                    mapData.setLatitudeStart(lat);
                    mapData.setLatitudeEnd(lat.add(add));
                    mapData.setLatitudeIncrease(add);
                    mapData.setHeightStart(heightStart);
                    mapData.setHeightEnd(heightEnd);
                    mapData.setPressureStart(pressureStart);
                    mapData.setPressureEnd(pressureEnd);
                    mapData.setStatusCode(0);
                    mapData.setIndex(time);
                    mapDataList.add(mapData);
                    time++;
                    lng = lng.add(add);
                }
                lat = lat.add(add);

            }


        }

        List<MapData> mapDataList1 = mapDataList.stream().limit(mapDataList.size() / 2).collect(Collectors.toList());
        List<MapData> mapDataList2 = mapDataList.stream().skip(mapDataList.size() / 2).collect(Collectors.toList());

        mapDataMapper.insertMapData(mapDataList1);
        mapDataMapper.insertMapData(mapDataList2);


        return 1;
    }


//    @Override
    public int insertMapData1() {

        BigDecimal lng = new BigDecimal("84.73");
        BigDecimal lat = new BigDecimal("43.60");


        List<MapData> mapDataList = new ArrayList<>();

        BigDecimal add = new BigDecimal("0.01");

        Integer heightStart = 354;
        Integer heightEnd = 750;

        Integer pressureStart = 950;
        Integer pressureEnd = 950;

        Integer time = 0;

        for (int k = 0; k < 8; k++) {
            if (k == 1) {
                heightStart = 750;
                heightEnd = 825;

                pressureStart = 925;
                pressureEnd = 925;
            } else if (k == 2) {
                heightStart = 825;
                heightEnd = 1200;

                pressureStart = 900;
                pressureEnd = 900;
            } else if (k == 3) {
                heightStart = 1200;
                heightEnd = 1710;

                pressureStart = 850;
                pressureEnd = 850;
            } else if (k == 4) {
                heightStart = 1710;
                heightEnd = 2460;

                pressureStart = 800;
                pressureEnd = 800;
            } else if (k == 5) {
                heightStart = 2460;
                heightEnd = 3600;

                pressureStart = 700;
                pressureEnd = 700;
            } else if (k == 6) {
                heightStart = 3600;
                heightEnd = 4800;

                pressureStart = 600;
                pressureEnd = 600;
            } else if (k == 7) {
                heightStart = 4800;
                heightEnd = 6300;

                pressureStart = 500;
                pressureEnd = 500;
            }
            lat = new BigDecimal("43.70");
            lng = new BigDecimal("43.60");
            for (int i = 0; i < 256; i++) {
                lng = new BigDecimal("84.73");
                for (int j = 0; j < 244; j++) {
                    MapData mapData = new MapData();
                    mapData.setLongitudeStart(lng);
                    mapData.setLongitudeEnd(lng.add(add));
                    mapData.setLayer(k + 1);
                    mapData.setLongitudeIncrease(add);
                    mapData.setLatitudeStart(lat);
                    mapData.setLatitudeEnd(lat.add(add));
                    mapData.setLatitudeIncrease(add);
                    mapData.setHeightStart(heightStart);
                    mapData.setHeightEnd(heightEnd);
                    mapData.setPressureStart(pressureStart);
                    mapData.setPressureEnd(pressureEnd);
                    mapData.setStatusCode(0);
                    mapData.setIndex(time);
                    mapDataList.add(mapData);
                    time++;
                    lng = lng.add(add);
                }
                lat = lat.add(add);

            }


        }


        // 分割成每组1000个
        int batchSize = 1000;
        List<List<MapData>> list = IntStream.range(0, (mapDataList.size() + batchSize - 1) / batchSize)
                .mapToObj(i -> mapDataList.subList(
                        i * batchSize,
                        Math.min((i + 1) * batchSize, mapDataList.size())
                ))
                .collect(Collectors.toList());


        System.out.println(list.size());

        list.forEach(e ->
        {
            mapDataMapper.insertMapData(e);
        });


//        mapDataMapper.insertMapData(mapDataList1);
//        mapDataMapper.insertMapData(mapDataList2);


        return 1;
    }


    public static void main(String[] args) {


        // 创建40万数据的List
        List<Integer> largeList = IntStream.rangeClosed(1, 432100)
                .boxed()
                .collect(Collectors.toList());

        // 分割成每组1000个
        int batchSize = 1000;
        List<List<Integer>> partitions = IntStream.range(0, (largeList.size() + batchSize - 1) / batchSize)
                .mapToObj(i -> largeList.subList(
                        i * batchSize,
                        Math.min((i + 1) * batchSize, largeList.size())
                ))
                .collect(Collectors.toList());

    }

    @Override
    public int deleteMapData() {

        return mapDataMapper.deleteAll();
    }


    @Override
    public List<MapData> queryMapData(QueryMapDataVo queryMapDataVo) {
        // 验证输入点数量
        if (queryMapDataVo.getMapDataLongLatVoList().size() < 2) {
            throw new RuntimeException("请输入至少二个经纬度点以组成多边形");
        }

        // 排序点集
        List<MapDataLongLatVo> sortedPoints = queryMapDataVo.getMapDataLongLatVoList().stream()
                .sorted(Comparator.comparingInt(MapDataLongLatVo::getSortNumber))
                .collect(Collectors.toList());

        // 确保多边形闭合
        if (!isClosedPath(sortedPoints)) {
            // 添加闭合点（复制第一个点）
            MapDataLongLatVo closurePoint = new MapDataLongLatVo();
            BeanUtils.copyProperties(sortedPoints.get(0), closurePoint);
            closurePoint.setSortNumber(sortedPoints.size() + 1);
            sortedPoints.add(closurePoint);
        }

        // 获取多边形边界
        BigDecimal[] bounds = calculatePolygonBounds(sortedPoints);
        BigDecimal minLon = bounds[0];
        BigDecimal maxLon = bounds[1];
        BigDecimal minLat = bounds[2];
        BigDecimal maxLat = bounds[3];

        // 获取高度范围内的所有网格
        List<MapData> heightFilteredData = mapDataMapper.selectGridsInBoundary(
                minLon, maxLon, minLat, maxLat,
                queryMapDataVo.getStartHeight(),
                queryMapDataVo.getEndHeight()
        );

        // 获取边界框内的所有网格
//        List<MapData> candidateGrids = filterGridsByBoundary(heightFilteredData, minLon, maxLon, minLat, maxLat);

        // 过滤出多边形内的网格
        return heightFilteredData.stream()
                .filter(cell -> isCellInsidePolygon(cell, sortedPoints))
                .collect(Collectors.toList());
    }

    // 检查多边形是否闭合
    private boolean isClosedPath(List<MapDataLongLatVo> points) {
        MapDataLongLatVo first = points.get(0);
        MapDataLongLatVo last = points.get(points.size() - 1);
        return first.getLongitude().compareTo(last.getLongitude()) == 0 &&
                first.getLatitude().compareTo(last.getLatitude()) == 0;
    }

    // 计算多边形边界
    private BigDecimal[] calculatePolygonBounds(List<MapDataLongLatVo> points) {
        BigDecimal minLon = points.get(0).getLongitude();
        BigDecimal maxLon = minLon;
        BigDecimal minLat = points.get(0).getLatitude();
        BigDecimal maxLat = minLat;

        for (MapDataLongLatVo point : points) {
            minLon = minLon.min(point.getLongitude());
            maxLon = maxLon.max(point.getLongitude());
            minLat = minLat.min(point.getLatitude());
            maxLat = maxLat.max(point.getLatitude());
        }

        return new BigDecimal[]{minLon, maxLon, minLat, maxLat};
    }

    // 过滤出边界框内的网格
    private List<MapData> filterGridsByBoundary(List<MapData> grids,
                                                BigDecimal minLon, BigDecimal maxLon,
                                                BigDecimal minLat, BigDecimal maxLat) {
        return grids.stream()
                .filter(cell ->
                        cell.getLongitudeEnd().compareTo(minLon) >= 0 &&
                                cell.getLongitudeStart().compareTo(maxLon) <= 0 &&
                                cell.getLatitudeEnd().compareTo(minLat) >= 0 &&
                                cell.getLatitudeStart().compareTo(maxLat) <= 0)
                .collect(Collectors.toList());
    }

    // 判断网格是否在多边形内
    private boolean isCellInsidePolygon(MapData cell, List<MapDataLongLatVo> polygon) {
        // 使用网格中心点进行判断
        BigDecimal centerLon = cell.getLongitudeStart().add(cell.getLongitudeEnd())
                .divide(BigDecimal.valueOf(2), 10, RoundingMode.HALF_UP);
        BigDecimal centerLat = cell.getLatitudeStart().add(cell.getLatitudeEnd())
                .divide(BigDecimal.valueOf(2), 10, RoundingMode.HALF_UP);

        return isPointInPolygon(centerLon, centerLat, polygon);
    }

    // 判断点是否在多边形内（使用射线法）
    private boolean isPointInPolygon(BigDecimal pointLon, BigDecimal pointLat, List<MapDataLongLatVo> polygon) {
        boolean inside = false;
        int size = polygon.size();

        for (int i = 0, j = size - 1; i < size; j = i++) {
            BigDecimal iLon = polygon.get(i).getLongitude();
            BigDecimal iLat = polygon.get(i).getLatitude();
            BigDecimal jLon = polygon.get(j).getLongitude();
            BigDecimal jLat = polygon.get(j).getLatitude();

            // 检查点是否在网格边线上
            if (pointLon.compareTo(iLon) == 0 && pointLat.compareTo(iLat) == 0) {
                return true;
            }

            // 检查是否与当前边交叉
            boolean intersect = ((iLat.compareTo(pointLat) > 0) != (jLat.compareTo(pointLat) > 0)) &&
                    (pointLon.compareTo(
                            jLon.subtract(iLon)
                                    .multiply(pointLat.subtract(iLat))
                                    .divide(jLat.subtract(iLat), 10, RoundingMode.HALF_UP)
                                    .add(iLon)) < 0);

            if (intersect) {
                inside = !inside;
            }
        }

        return inside;
    }

    //    @Override
    public List<MapData> queryMapDatabeif(QueryMapDataVo queryMapDataVo) {
        System.out.println(queryMapDataVo.getMapDataLongLatVoList());

        if (queryMapDataVo.getMapDataLongLatVoList().size() < 2) {
            throw new RuntimeException("请输入至少两个经纬度点");
        }
        //排序
        Collections.sort(queryMapDataVo.getMapDataLongLatVoList(), new Comparator<MapDataLongLatVo>() {
            @Override
            public int compare(MapDataLongLatVo p1, MapDataLongLatVo p2) {
                return Integer.compare(p1.getSortNumber(), p2.getSortNumber());
            }
        });
        if (queryMapDataVo.getMapDataLongLatVoList().get(0).getLatitude().equals
                (queryMapDataVo.getMapDataLongLatVoList().get(queryMapDataVo.getMapDataLongLatVoList().size() - 1).getLatitude()) &&
                queryMapDataVo.getMapDataLongLatVoList().get(0).getLongitude().equals
                        (queryMapDataVo.getMapDataLongLatVoList().get(queryMapDataVo.getMapDataLongLatVoList().size() - 1).getLongitude())
        ) {
            System.out.println("首尾相连");
        } else {
            System.out.println("首尾不相连");
            MapDataLongLatVo mapDataLongLatVo4 = new MapDataLongLatVo();
            BeanUtils.copyProperties(queryMapDataVo.getMapDataLongLatVoList().get(0), mapDataLongLatVo4);
            int sortNumber = queryMapDataVo.getMapDataLongLatVoList().get(queryMapDataVo.getMapDataLongLatVoList().size() - 1).getSortNumber();
            mapDataLongLatVo4.setSortNumber(sortNumber + 1);
            queryMapDataVo.getMapDataLongLatVoList().add(mapDataLongLatVo4);
        }
        List<MapData> mapDataListAll = new ArrayList<>();
        //TODO 高度筛选 还可进一步优化 根据线段起点和终点去除多余的格子
        List<MapData> mapDataListVo = mapDataMapper.selectMapDataByHeight(queryMapDataVo.getStartHeight(), queryMapDataVo.getEndHeight());
        for (int i = 0; i < queryMapDataVo.getMapDataLongLatVoList().size() - 1; i++) {
            //取到第一条线段
            MapDataLongLatVo voA = queryMapDataVo.getMapDataLongLatVoList().get(i);
            MapDataLongLatVo voB = queryMapDataVo.getMapDataLongLatVoList().get(i + 1);
            //获取每个相交的格子
            List<MapData> mapDataList = selectMapDataByLongLatAndHeight(voA, voB, mapDataListVo, queryMapDataVo.getStartHeight(), queryMapDataVo.getEndHeight());
            mapDataListAll.addAll(mapDataList);
        }
        //补齐中间的格子
        Map<BigDecimal, List<MapData>> usersByAge = mapDataListAll.stream()
                .collect(Collectors.groupingBy(MapData::getLatitudeStart));
        Map<BigDecimal, List<MapData>> minMaxUsersByAge = usersByAge.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> {
                            List<MapData> users = entry.getValue();
                            MapData minUser = users.stream()
                                    .min(Comparator.comparing(MapData::getLongitudeStart))
                                    .orElse(null);
                            MapData maxUser = users.stream()
                                    .max(Comparator.comparing(MapData::getLongitudeStart))
                                    .orElse(null);
                            return minUser != null && maxUser != null ? Arrays.asList(minUser, maxUser) : Collections.emptyList();
                        }
                ));
        List<MapData> mapDataList = new ArrayList<>();
        minMaxUsersByAge.forEach((k, v) -> {
            if (v.size() == 1) {
                mapDataList.add(v.get(0));
            } else {
                mapDataList.addAll(mapDataMapper.selectMapDataByHeightAndMaxAndMinAndLat(k, v.get(0).getLongitudeStart(), v.get(1).getLongitudeStart(), queryMapDataVo.getStartHeight(), queryMapDataVo.getEndHeight()));
            }
        });
        return mapDataList;
    }

    @Override
    public MapData mapData(QueryMapDataLongLatVo param) {
        List<MapData> mapDataList = mapDataMapper.mapData(param);
        if (mapDataList.size() > 0)
            return mapDataList.get(0);
        else
            return null;
    }

    @Override
    public int topographic(List<AddTopographicVo> param) {
        //List<Long> indexList = mapDataMapper.queryMapDate(param);
//        List<Long> indexList = new ArrayList<>();
//        for (AddTopographicVo addTopographicVo : param) {
//            Long index = mapDataMapper.queryMapDateByOne(addTopographicVo);
//            indexList.add(index);
//        }
//        return mapDataMapper.insertTopographic(indexList);

        List<Long> indexList = new ArrayList<>();
        int batchSize = 100;
        for (int i = 0; i < param.size(); i += batchSize) {
            int toIndex = Math.min(i + batchSize, param.size());
            List<AddTopographicVo> batch = param.subList(i, toIndex);
            List<Long> index = mapDataMapper.queryMapDate(batch);
            indexList.addAll(index);
        }
        return mapDataMapper.insertTopographic(indexList);
    }

    @Override
    public List<MapData> mapDataList(AirspaceLongLat airspaceLongLat) {
        return mapDataMapper.mapDataList(airspaceLongLat);
    }

    @Override
    public MapData getMapData(QueryMapDataLongLatVo param) {
        List<MapData> mapDataList = mapDataMapper.getMapData(param);
        if (mapDataList.size() > 0)
            return mapDataList.get(0);
        else
            return null;
    }

    @Override
    public MeteInfo getMeteInfo(MeteInfoParam meteInfoParam) {
        return mapDataMapper.getMeteInfo(meteInfoParam);
    }

    @Override
    @Async
    public void saveWorkMapData(FlightPlanWork flightPlanWork) {
        logger.info("保存作业区地图数据 异步开始" + new Date());
        // 1. 参数校验
        validateFlightPlanWork(flightPlanWork);

        Set<WorkMapData> workMapDataList = new HashSet<>();
        List<MapDataLongLatVo> coordinates = convertToMapDataLongLatList(flightPlanWork.getFlightPlanWorkLongLatList());

        if (flightPlanWork.getWorkType() == WorkType.REGION) {
            // 处理区域作业
            List<MapData> regionCubes = queryRegionMapData(flightPlanWork, coordinates);
            // 添加作业区进出点区域 每个点的半圆
            if (flightPlanWork.getGraphType().equals(GraphicsType.POLYGON)) {

                workMapDataList.addAll(convertToWorkMapData(regionCubes, flightPlanWork.getId(), 0, null));

                // 先收集所有已存在的index（减少重复遍历）
                Set<Integer> existingIndices = workMapDataList.stream()
                        .map(WorkMapData::getIndex)
                        .collect(Collectors.toSet());

                // 截取除最后一位的所有元素
                List<MapDataLongLatVo> coordinatesCopy = new ArrayList<>(
                        coordinates.subList(0, coordinates.size() - 1)
                );
                for (int i = 0; i < coordinatesCopy.size(); i++) {
                    //每个点生成一个圆
                    List<FlightPlanWorkLongLat> flightPlanWorkLongLatList = WorkBasisUtil.getFlightPlanWorkLongLat(flightPlanWork.getId(), coordinatesCopy.get(i).getLongitude().doubleValue(), coordinatesCopy.get(i).getLatitude().doubleValue(), 2000.0, 0.0, 50);
                    //转换坐标数据到 MapDataLongLatVo 列表
                    List<MapDataLongLatVo> coordinatesE = convertToMapDataLongLatList(flightPlanWorkLongLatList);
                    //查询地图数据MapData
                    List<MapData> regionCubesE = queryRegionMapData(flightPlanWork, coordinatesE);
                    //由于是Set集合 所以不会重复添加 添加为另外的颜色
                    List<WorkMapData> workMapDataListE = convertToWorkMapData(regionCubesE, flightPlanWork.getId(), i + 1, 60);
                    workMapDataListE.stream()
                            .filter(data -> !existingIndices.contains(data.getIndex()))
                            .forEach(workMapDataList::add);
                }
            }
        } else {
            // 处理航线作业
            processRouteSegments(coordinates, flightPlanWork.getId(), workMapDataList);
        }

        // 批量插入数据
        if (!workMapDataList.isEmpty()) {
            batchInsertWorkMapData(workMapDataList);
        }
        logger.info("保存作业区地图数据 异步结束" + new Date());

    }

    @Override
    @Async
    public void saveRouteMapData(FlightPlanRoute e) {

        logger.info("保存航线地图数据 异步开始" + new Date());
        // 参数校验
        if (e.getId() == null) {
            throw new IllegalArgumentException("航线ID不能为空");
        }
        List<FlightPlanRouteLongLat> routeLongLatList = e.getFlightPlanRouteLongLatList();
        if (routeLongLatList == null || routeLongLatList.size() < 2) {
            throw new IllegalArgumentException("航点数据至少需要两个点");
        }

        Set<RouteMapData> allRouteMapData = new HashSet<>();

        // 遍历相邻航点构建线段
        for (int i = 0; i < routeLongLatList.size() - 1; i++) {
            FlightPlanRouteLongLat start = routeLongLatList.get(i);
            FlightPlanRouteLongLat end = routeLongLatList.get(i + 1);
            // 1. 构建线段
            LineSegment3D lineSegment = buildLineSegment3D(start, end);
            // 2. 获取线段的最小边界矩形（MBR）
            Map<String, Object> mbr = lineSegment.getMBR();
            // 3. 查询与MBR相交的立方体
            List<MapData> candidateCubes = mapDataMapper.findIntersectingMapDataByMBR(mbr);
//            List<MapData> candidateCubes = mapDataMapper.selectMapDataByHeight(start.getHeight().intValue(),start.getHeight().intValue());
            // 4. 筛选与线段实际相交的立方体
//            List<RouteMapData> segmentData = processCandidateCubes(routeId, lineSegment, candidateCubes);
            //计算航线与格子的距离
            List<RouteMapData> routeMapDataList = filterCubesByDistance(start, end, e.getId(), candidateCubes, 500);
            // 5. 收集所有数据
            allRouteMapData.addAll(routeMapDataList);
//            allRouteMapData.addAll(segmentData);
        }
        // 6. 统一插入数据
        if (allRouteMapData.isEmpty()) {
            throw new ServiceException("航线未经过任何地图立方体");
        }
        int insertedCount = routeMapDataMapper.insertAllRouteMapData(allRouteMapData);
        if (insertedCount != allRouteMapData.size()) {
            throw new ServiceException("数据保存不完整，预期保存 " + allRouteMapData.size() + " 条，实际保存 " + insertedCount);
        }
        logger.info("保存航线地图数据 异步截止" + new Date());

    }

    @Override
    @Async
    public void processAndInsertAirspaceData(Airspace airspace, int graphicsType, List<AirspaceLongLat> originalLongLatList, long id) {
        logger.info("保存空域地图数据 异步开始" + new Date());
        List<AirspaceLongLat> processedLongLatList = originalLongLatList;
        if (processedLongLatList.isEmpty()) {
            throw new ServiceException("空域经纬度数据为空");
        }
        if (processedLongLatList.size() == 1) {

            List<MapData> mapDataList = mapDataMapper.mapDataList(processedLongLatList.get(0));
            if (mapDataList.isEmpty()) {
                throw new ServiceException("没有找到对应的MapData");
            }

            List<AirspaceMapData> airspaceMapDataList = mapDataList.stream()
                    .map(mapData -> {
                        AirspaceMapData airspaceMapData = new AirspaceMapData();
                        airspaceMapData.setAirspaceId(id);
                        airspaceMapData.setValue(airspace.getColorType());
                        airspaceMapData.setIndex(mapData.getIndex());
                        return airspaceMapData;
                    })
                    .collect(Collectors.toList());

            // 插入数据库
            airspaceMapDataMapper.insertAirspaceListMapData(airspaceMapDataList);

            //判断如果是障碍物 需要保存障碍物地图数据
            if (airspace.getAirspaceType().equals(AirspaceType.OBSTACLES)) {
                mapDataObstacleService.saveObstacle(airspace, airspaceMapDataList, processedLongLatList.get(0));
            }

        } else {
            // 如果需要过滤，则进行过滤
            if (airspace.getAirspaceType() != AirspaceType.OBSTACLES && graphicsType == GraphicsType.CIRCULAR) {
                processedLongLatList = processedLongLatList.stream()
                        .filter(item -> item.getGroupNumber() == 0)
                        .collect(Collectors.toList());
            }
            // 转换 AirspaceLongLat 列表到 MapDataLongLatVo 列表
            List<MapDataLongLatVo> mapDataLongLatVoList = processedLongLatList.stream()
                    .map(this::convertToMapDataLongLatVo) // 假设您实现了这个转换方法
                    .collect(Collectors.toList());
            // 设置查询对象
            QueryMapDataVo queryMapDataVo = new QueryMapDataVo();
            queryMapDataVo.setMapDataLongLatVoList(mapDataLongLatVoList);
            queryMapDataVo.setStartHeight(airspace.getStartingHeight());
            queryMapDataVo.setEndHeight(airspace.getTerminationHeight());
            // 查询MapData
            List<MapData> mapDateIdList = queryMapData(queryMapDataVo);

            if (!mapDateIdList.isEmpty()) {
                // 创建AirspaceMapDataList
                List<AirspaceMapData> airspaceMapDataList = mapDateIdList.stream()
                        .map(mapData -> {
                            AirspaceMapData airspaceMapData = new AirspaceMapData();
                            airspaceMapData.setAirspaceId(id);
                            airspaceMapData.setValue(airspace.getColorType());
                            airspaceMapData.setIndex(mapData.getIndex());
                            return airspaceMapData;
                        })
                        .collect(Collectors.toList());
                // 插入数据库
                airspaceMapDataMapper.insertAirspaceListMapData(airspaceMapDataList);
            } else {
                throw new ServiceException("没有找到对应的MapData");
            }
        }


        logger.info("保存空域地图数据 异步结束" + new Date());

    }

    @Override
    public HomeMapDataRuleConfig homeMapRule(List<String> deptCodeList) {


        List<Obstacle> obstacleList = airspaceMapper.selectAirspaceListByCodeAndType(deptCodeList, 3);
        homeMapRule.setObstacle(obstacleList);
        return homeMapRule;
        // 1. 优化缓存键设计
//        String key = generateCacheKey(deptCodeList);
//        // 2. 尝试从缓存获取（使用更高效的缓存操作）
//        List<Obstacle> obstacle  = redisCache.getCacheList(key);
//        if (obstacle != null && !obstacle.isEmpty()) {
//            homeMapRule.setObstacle(obstacle);
//            return homeMapRule;
//        }
//        // 3. 加锁防止缓存击穿
//        synchronized (key.intern()) {
//            // 双重检查避免重复查询
//            obstacle  = redisCache.getCacheList(key);
//            if (obstacle != null  && !obstacle.isEmpty()) {
//                homeMapRule.setObstacle(obstacle);
//                return homeMapRule;
//            }
//            // 4. 优化数据库查询
//            List<Obstacle> obstacleList =  airspaceMapper.selectAirspaceListByCodeAndType(deptCodeList,3);
//            homeMapRule.setObstacle(obstacleList);
//            // 5. 优化缓存写入 - 异步且设置过期时间
//            cacheResultAsync(key, obstacleList);
//            return homeMapRule;
//        }


    }


    // 异步缓存结果
    private void cacheResultAsync(String cacheKey, List<Obstacle> obstacle) {
        CompletableFuture.runAsync(() -> {
            try {
                redisCache.setCacheList(cacheKey, obstacle);
            } catch (Exception e) {
                logger.error("异步缓存写入失败", e);
            }
        });
    }

    // 优化后的缓存键生成
    private String generateCacheKey(List<String> deptCodeList) {
        deptCodeList.sort(null); // 排序确保参数顺序不影响缓存键
        String keyBase = "homeMapRule:obstacle:" + String.join(",", deptCodeList);
        return DigestUtils.md5DigestAsHex(keyBase.getBytes()); // 使用MD5缩短键长度
    }

    @Override
    @Async
    public void setAirspaceCache(List<String> deptCodeList) {
//        logger.info("开始更新空域缓存");
//        // 1. 优化缓存键设计
//        String key = generateCacheKey(deptCodeList);
//        redisCache.deleteObject(key);
//        // 3. 加锁防止缓存击穿
//        synchronized (key.intern()) {
//            // 4. 优化数据库查询
//            List<Obstacle> obstacleList =  airspaceMapper.selectAirspaceListByCodeAndType(deptCodeList,3);
//            // 5. 优化缓存写入 - 异步且设置过期时间
//            cacheResultAsync(key, obstacleList);
//        }
//        logger.info("开始更新空域缓存 结束");
    }

    @Override
    public List<Point3D> routePlanning(QueryRoutePlanningVo routePlanningVo, List<String> stringList) {
        routePlanningVo.setBufferDistance(BigDecimal.valueOf(1000.0));
        List<Long> ids = new ArrayList<>();
        ids.add(388L);
        //查询空域
        List<Airspace> airspaceList = airspaceMapper.selectAirspaceByIds(ids);
        // 正确设置起点和终点（含高度）
        double[] start = {
                routePlanningVo.getStartLongitude().doubleValue(),
                routePlanningVo.getStartLatitude().doubleValue(),
                1000.0 // 添加高度
        };

        double[] end = {
                routePlanningVo.getEndLongitude().doubleValue(),
                routePlanningVo.getEndLatitude().doubleValue(),  // 修复纬度
                1000.0   // 添加高度
        };
        // 创建多个空域
        List<Polygon> noFlyZones = new ArrayList<>();
        double bufferDistance = routePlanningVo.getBufferDistance().doubleValue();

        for (Airspace airspace : airspaceList) {
            List<AirspaceLongLat> airspaceLongLatList = airspace.getAirspaceLongLatList();
            List<double[]> zoneCoords = new ArrayList<>();

            // 确保空域坐标集合不为空
            if (airspaceLongLatList.isEmpty()) continue;

            // 将坐标转为二维数组并确保闭合
            for (AirspaceLongLat coord : airspaceLongLatList) {
                zoneCoords.add(new double[]{coord.getDoubleLongitude(), coord.getDoubleLatitude()});
            }

            // 确保多边形闭合：添加第一个点作为最后一个点
            if (zoneCoords.size() > 1 &&
                    !Arrays.equals(zoneCoords.get(0), zoneCoords.get(zoneCoords.size() - 1))) {
                zoneCoords.add(zoneCoords.get(0));
            }

            noFlyZones.add(FlightPathPlanner.createBufferedPolygon(zoneCoords, bufferDistance));
        }

        // 计算最优路径
        List<double[]> pathPoints = FlightPathPlanner.calculateOptimalPath(start, end, noFlyZones, bufferDistance);

        // 转换为Point3D，保留高度信息
        return pathPoints.stream()
                .map(p -> new Point3D(p[0], p[1], p.length > 2 ? p[2] : start[2]))
                .collect(Collectors.toList());
    }

    // 转换方法
    private MapDataLongLatVo convertToMapDataLongLatVo(AirspaceLongLat airspaceLongLat) {
        MapDataLongLatVo mapDataLongLatVo = new MapDataLongLatVo();
        // 假设有适当的setter方法
        mapDataLongLatVo.setLatitude(BigDecimal.valueOf(airspaceLongLat.getDoubleLatitude()));
        mapDataLongLatVo.setLongitude(BigDecimal.valueOf(airspaceLongLat.getDoubleLongitude()));
        mapDataLongLatVo.setSortNumber(airspaceLongLat.getSortNumber());
        return mapDataLongLatVo;
    }


    /**
     * 计算候选立方体中到线段距离小于500米的立方体
     *
     * @param start
     * @param end
     * @param routeId
     * @param candidateCubes
     * @return
     */
    private List<RouteMapData> filterCubesByDistance(FlightPlanRouteLongLat start, FlightPlanRouteLongLat end, Long routeId, List<MapData> candidateCubes, double distance) {

        MapDataFilter mapDataFilter = new MapDataFilter();
        Point3D a = mapDataFilter.convertTo3DPointFlightPlanRouteLongLat(start);
        Point3D b = mapDataFilter.convertTo3DPointFlightPlanRouteLongLat(end);
        List<MapData> mapDataList = mapDataFilter.filterMapDataWithin500Meters(candidateCubes, a, b);

        if (!mapDataList.isEmpty()) {
            return mapDataList.parallelStream()
                    .map(cube -> createRouteMapData(routeId, cube))
                    .collect(Collectors.toList());
        } else {
            return Collections.EMPTY_LIST;
        }
    }

    /**
     * 创建航线地图数据对象
     */
    private RouteMapData createRouteMapData(Long routeId, MapData cube) {
        RouteMapData data = new RouteMapData();
        data.setRouteId(routeId);
        data.setIndex(cube.getIndex());
        data.setValue(10); // 此处假设固定值，建议替换为业务逻辑或常量
        return data;
    }


    /**
     * 构建3D线段
     */
    private LineSegment3D buildLineSegment3D(FlightPlanRouteLongLat start, FlightPlanRouteLongLat end) {
        LineSegment3D lineSegment = new LineSegment3D();
        lineSegment.setLongitudeA(start.getDoubleLongitude());
        lineSegment.setLatitudeA(start.getDoubleLatitude());
        lineSegment.setHeightA(start.getHeight().intValue());
        lineSegment.setLongitudeB(end.getDoubleLongitude());
        lineSegment.setLatitudeB(end.getDoubleLatitude());
        lineSegment.setHeightB(end.getHeight().intValue());
        return lineSegment;
    }

    /**
     * 批量插入数据
     */
    private void batchInsertWorkMapData(Set<WorkMapData> data) {
        int expected = data.size();
        int actual = workMapDataMapper.insertAllWorkMapData(data);
        if (actual != expected) {
            throw new ServiceException("数据保存不完整，预期保存 " + expected + " 条，实际保存 " + actual);
        }
    }


    /**
     * 构建3D线段
     */
    private LineSegment3D buildLineSegment(MapDataLongLatVo start, MapDataLongLatVo end) {
        LineSegment3D segment = new LineSegment3D();
        segment.setLongitudeA(start.getLongitude());
        segment.setLatitudeA(start.getLatitude());
        segment.setHeightA(start.getHeight().intValue());
        segment.setLongitudeB(end.getLongitude());
        segment.setLatitudeB(end.getLatitude());
        segment.setHeightB(end.getHeight().intValue());
        return segment;
    }

    /**
     * 处理航线线段数据
     */
    private void processRouteSegments(
            List<MapDataLongLatVo> coordinates,
            Long workId,
            Set<WorkMapData> result
    ) {
        for (int i = 0; i < coordinates.size() - 1; i++) {
            LineSegment3D segment = buildLineSegment(coordinates.get(i), coordinates.get(i + 1));
            Map<String, Object> mbr = segment.getMBR();
            List<MapData> candidates = mapDataMapper.findIntersectingMapDataByMBR(mbr);
            // 4. 筛选与线段实际相交的立方体
//            List<WorkMapData> segmentData = processCandidateCubesWork(workId, segment, candidates);
            LongLatUtil longLatUtil = new LongLatUtil();

            MapDataFilter mapDataFilter = new MapDataFilter();
            Point3D a = mapDataFilter.convertTo3DPointMapDataLongLatVo(coordinates.get(i));
            Point3D b = mapDataFilter.convertTo3DPointMapDataLongLatVo(coordinates.get(i + 1));
            List<MapData> mapDataList = mapDataFilter.filterMapDataWithin500Meters(candidates, a, b);
            //保存相交的
            if (mapDataList != null) {
                //保存500内的
                result.addAll(convertToWorkMapData(mapDataList, workId, 0, 60));
            }


        }
    }

    /**
     * 转换 MapData 到 WorkMapData
     */
    private List<WorkMapData> convertToWorkMapData(List<MapData> cubes, Long workId, int groupType, Integer color) {
        int DEFAULT_VALUE; // 常量提取
        if (color != null) {
            DEFAULT_VALUE = color;
        } else {
            DEFAULT_VALUE = 10;
        }
        return cubes.stream()
                .map(cube -> {
                    WorkMapData data = new WorkMapData();
                    data.setWorkId(workId);
                    data.setIndex(cube.getIndex());
                    data.setValue(DEFAULT_VALUE);
                    data.setGroupType(groupType);
                    return data;
                })
                .collect(Collectors.toList());
    }

    /**
     * 查询区域作业的地图数据
     */
    private List<MapData> queryRegionMapData(FlightPlanWork work, List<MapDataLongLatVo> coordinates) {
        QueryMapDataVo query = new QueryMapDataVo();
        query.setMapDataLongLatVoList(coordinates);
        query.setStartHeight(work.getMinHeight().intValue());
        query.setEndHeight(work.getMaxHeight().intValue());
        return queryMapData(query);
    }


    /**
     * 转换坐标数据到 MapDataLongLatVo 列表
     */
    private List<MapDataLongLatVo> convertToMapDataLongLatList(List<FlightPlanWorkLongLat> flightPlanWorkLongLatList) {
        return flightPlanWorkLongLatList.stream()
                .map(e -> {
                    MapDataLongLatVo vo = new MapDataLongLatVo();
                    // 经度（double → BigDecimal）
                    vo.setLongitude(e.getDoubleLongitude());
                    // 纬度（double → BigDecimal）
                    vo.setLatitude(e.getDoubleLatitude());
                    // 高度（假设 e.getHeight() 返回 BigDecimal）
                    vo.setHeight(e.getHeight());
                    // 排序号（直接赋值 Integer）
                    vo.setSortNumber(e.getSortNumber());
                    return vo;
                })
                .collect(Collectors.toList());
    }

    /**
     * 参数校验
     */
    private void validateFlightPlanWork(FlightPlanWork flightPlanWork) {
        if (flightPlanWork == null) {
            throw new IllegalArgumentException("作业区数据不能为空");
        }
        if (flightPlanWork.getFlightPlanWorkLongLatList() == null
                || flightPlanWork.getFlightPlanWorkLongLatList().isEmpty()) {
            throw new IllegalArgumentException("作业区航点数据不能为空");
        }
    }


    private List<MapData> selectMapDataByLongLatAndHeight(MapDataLongLatVo voA, MapDataLongLatVo voB, List<MapData> mapDataListVo, Integer startHeight, Integer endHeight) {
        List<MapData> mapDataListResult = new ArrayList<>();

        Point3D point3DA = new Point3D(voA.getLongitude().doubleValue(), voA.getLatitude().doubleValue(), startHeight);

        Point3D point3DB = new Point3D(voB.getLongitude().doubleValue(), voB.getLatitude().doubleValue(), startHeight);

        mapDataListVo.forEach(e -> {
            List<Point3D> list = transformationMapData(e, startHeight);
            boolean flag = LongLatUtil.findInSurface(point3DA, point3DB, list, startHeight, endHeight);
            if (flag) {
                mapDataListResult.add(e);
            }
        });
        return mapDataListResult;


    }

    private List<Point3D> transformationMapData(MapData e, Integer startHeight) {

        List<Point3D> point3DList = new ArrayList<>();

        Point3D point3DA = new Point3D(e.getLongitudeStart().doubleValue(), e.getLatitudeStart().doubleValue(), startHeight);
        Point3D point3DB = new Point3D(e.getLongitudeEnd().doubleValue(), e.getLatitudeStart().doubleValue(), startHeight);
        Point3D point3DC = new Point3D(e.getLongitudeEnd().doubleValue(), e.getLatitudeEnd().doubleValue(), startHeight);
        Point3D point3DD = new Point3D(e.getLongitudeStart().doubleValue(), e.getLatitudeEnd().doubleValue(), startHeight);
        point3DList.add(point3DA);
        point3DList.add(point3DB);
        point3DList.add(point3DC);
        point3DList.add(point3DD);
        point3DList.add(point3DA);

        return point3DList;

    }
}
