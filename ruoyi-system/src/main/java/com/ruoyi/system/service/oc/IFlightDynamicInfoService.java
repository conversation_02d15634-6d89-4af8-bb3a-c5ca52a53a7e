package com.ruoyi.system.service.oc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.oc.dto.FlightDynamicDto;
import com.ruoyi.system.domain.oc.entity.FlightDynamicInfo;
import com.ruoyi.system.domain.oc.vo.FlightDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 动态信息服务接口
 */
public interface IFlightDynamicInfoService extends IService<FlightDynamicInfo> {

    /**
     * 查询动态信息
     *
     * @param flightTaskBookId 任务书ID
     * @param companyCode 公司代码
     * @return 动态信息
     */
    FlightDynamicVo selectFlightDynamicInfoById(Integer flightTaskBookId, String companyCode);

    /**
     * 导出动态信息Word文档
     *
     * @param response 响应对象
     * @param flightTaskBookId 任务书ID
     * @param companyCode 公司代码
     */
    void exportDynamicInfoWord(HttpServletResponse response, Integer flightTaskBookId, String companyCode);

    /**
     * 导出动态信息PDF文档
     *
     * @param response 响应对象
     * @param flightTaskBookId 任务书ID
     * @param companyCode 公司代码
     */
    void exportDynamicInfoPdf(HttpServletResponse response, Integer flightTaskBookId, String companyCode);

    /**
     * 批量导出动态信息PDF文档
     *
     * @param response 响应对象
     * @param flightTaskBookIds 任务书ID列表
     * @param companyCode 公司代码
     */
    void exportDynamicInfoPdfBatch(HttpServletResponse response, List<Integer> flightTaskBookIds, String companyCode);

    /**
     * 生成动态信息Word文档字节数组
     * @param wordMeteorologicalVo 导出数据
     * @return Word文档字节数组
     */
    byte[] generateDynamicWordDocument(WordMeteorologicalVo wordMeteorologicalVo);

    int saveFlightDynamicInfo(String companyCode, FlightDynamicDto flightDynamicDto);
}
