package com.ruoyi.system.service.mdmp;

import com.ruoyi.system.domain.mdmp.NextDayFlightPlan;
import com.ruoyi.system.param.mdmp.NexDayFlightPlanListParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 次日计划飞行Service接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface INextDayFlightPlanService {
    /**
     * 查询次日计划飞行
     *
     * @param id 次日计划飞行主键
     * @return 次日计划飞行
     */
    public NextDayFlightPlan selectNextDayFlightPlanById(Long id);

    /**
     * 查询次日计划飞行列表
     *
     * @param param 次日计划飞行
     * @return 次日计划飞行集合
     */
    public List<NextDayFlightPlan> selectNextDayFlightPlanList(NexDayFlightPlanListParam param);

    /**
     * 新增次日计划飞行
     *
     * @param nextDayFlightPlan 次日计划飞行
     * @return 结果
     */
    public int insertNextDayFlightPlan(NextDayFlightPlan nextDayFlightPlan);

    /**
     * 修改次日计划飞行
     *
     * @param nextDayFlightPlan 次日计划飞行
     * @return 结果
     */
    public int updateNextDayFlightPlan(NextDayFlightPlan nextDayFlightPlan);

    /**
     * 批量删除次日计划飞行
     *
     * @param ids 需要删除的次日计划飞行主键集合
     * @return 结果
     */
    public int deleteNextDayFlightPlanByIds(Long[] ids);

    /**
     * 删除次日计划飞行信息
     *
     * @param id 次日计划飞行主键
     * @return 结果
     */
    public int deleteNextDayFlightPlanById(Long id);

    /**
     * 验证次日计划飞行
     *
     * @param nextDayFlightPlan
     * @return
     */
    NextDayFlightPlan verifyNextDayFlightPlan(NextDayFlightPlan nextDayFlightPlan);

    /**
     * 审核次日计划飞行
     *
     * @param nextDayFlightPlan
     * @return
     */
    int auditing(NextDayFlightPlan nextDayFlightPlan);
}
