package com.ruoyi.system.service.oc.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.domain.entity.SysRole;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.common.utils.SecurityUtils;
import com.ruoyi.system.domain.SysRoleMenu;
import com.ruoyi.system.domain.SysUserRole;
import com.ruoyi.system.domain.oc.Company;
import com.ruoyi.system.domain.oc.CompanyMenu;
import com.ruoyi.system.domain.oc.dto.*;
import com.ruoyi.system.mapper.SysRoleMapper;
import com.ruoyi.system.mapper.SysRoleMenuMapper;
import com.ruoyi.system.mapper.SysUserRoleMapper;
import com.ruoyi.system.mapper.oc.CompanyMapper;
import com.ruoyi.system.mapper.oc.CompanyMenuMapper;
import com.ruoyi.system.mapper.oc.UserMapper;
import com.ruoyi.system.service.oc.CompanyService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2022/4/18 11:02
 * @mood 功能
 */
@Service
@RequiredArgsConstructor
public class CompanyServiceImpl implements CompanyService {
    @Resource
    private CompanyMapper companyMapper;
    @Resource
    private CompanyMenuMapper companyMenuMapper;
    @Resource
    private UserMapper userMapper;
    @Resource
    private SysRoleMapper sysRoleMapper;
    @Resource
    private SysRoleMenuMapper roleMenuMapper;
    @Resource
    private SysUserRoleMapper sysUserRoleMapper;

    @Override
    public PageCommonResult<List<Company>> selectList(Company company, Integer pageNum, Integer pageSize) {
        PageMethod.startPage(pageNum, pageSize);
        List<Company> companyList = companyMapper.selectList(company);
        PageInfo<Company> info = new PageInfo<>(companyList);
        return PageCommonResult.success(companyList, info.getTotal());
    }

    @Override
    public CommonResult<Company> companyInfo(CompanyDTO dto) {
        Company company = companyMapper.selectById(dto.getId());
        return CommonResult.success(company);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> addCompany(AddCompanyDTO dto) {
        //新增公司超级管理员角色
        insertSysRole(dto.getCompanyCode());
        //新建公司
        Company data = companyMapper.selectByCompanyCode(dto.getCompanyCode());
        if (Objects.nonNull(data)) {
            return CommonResult.error("新增公司'" + dto.getCompanyCode() + "'失败，航司代码已存在");
        }
        Company company = new Company();
        BeanUtils.copyProperties(dto, company);
        company.setExistAdmin(0);
        int row = companyMapper.insertCompany(company);
        return CommonResult.toResult(row);
    }

    private void insertSysRole(String companyCode) {
        SysRole sysRole = new SysRole();
        sysRole.setRoleName("超级管理员");
        sysRole.setRoleKey("superAdmin");
        sysRole.setRoleSort(0);
        sysRole.setDataScope(null);
        sysRole.setMenuCheckStrictly(true);
        sysRole.setDeptCheckStrictly(true);
        sysRole.setStatus("0");
        sysRole.setDelFlag(null);
        sysRole.setFlag(false);
        sysRole.setMenuIds(null);
        sysRole.setDeptIds(null);
        sysRole.setCompanyCode(companyCode);
        sysRole.setSearchValue(null);
        sysRole.setCreateBy(SecurityUtils.getLoginUser().getUsername());
        sysRole.setCreateTime(null);
        sysRole.setUpdateBy(null);
        sysRole.setUpdateTime(null);
        sysRole.setRemark(null);
        sysRole.setParams(null);
        sysRoleMapper.insertRole(sysRole);
    }

    @Override
    public CommonResult<String> updateCompany(UpdateCompanyDTO dto) {
        Company company = new Company();
        BeanUtils.copyProperties(dto, company);
        int row = companyMapper.updateCompany(company);
        return CommonResult.toResult(row);
    }

    @Override
    public CommonResult<String> deleteCompany(CompanyDTO dto) {
        int row = companyMapper.deleteCompany(dto.getId());
        return CommonResult.toResult(row);
    }

    @Override
    public CommonResult<List<CompanyMenu>> selectCompanyMenu(CompanyMenuDTO dto) {
        List<CompanyMenu> companyMenus = companyMenuMapper.selectByCompanyCode(dto.getCompanyCode());
        return CommonResult.success(companyMenus);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> addCompanyMenu(AddCompanyMenuDTO dto) {
        //添加公司菜单
        companyMenuMapper.deleteByCompanyCode(dto.getCompanyCode());
        Long[] menuIds = dto.getMenuIds();
        if (menuIds.length > 0) {
            List<CompanyMenu> companyMenuList = new ArrayList<>();
            for (Long menuId : menuIds) {
                CompanyMenu companyMenu = new CompanyMenu();
                companyMenu.setCompanyCode(dto.getCompanyCode());
                companyMenu.setMenuId(menuId);
                companyMenuList.add(companyMenu);
            }
            companyMenuMapper.batchInsertCompanyMenu(companyMenuList);
        }
        Integer companyType = companyMapper.selectByCompanyCode(dto.getCompanyCode()).getCompanyType();
        if (companyType == 0) {
            return CommonResult.success("新增成功");
        }
        //添加公司管理员角色菜单
        SysRole sysRole = sysRoleMapper.selectRoleByRoleKey("superAdmin", dto.getCompanyCode());
        Long roleId = sysRole.getRoleId();
        roleMenuMapper.deleteRoleMenuByRoleId(roleId);
        if (menuIds.length > 0) {
            List<SysRoleMenu> roleMenuList = new ArrayList<>();
            for (Long menuId : menuIds) {
                SysRoleMenu roleMenu = new SysRoleMenu();
                roleMenu.setRoleId(roleId);
                roleMenu.setMenuId(menuId);
                roleMenuList.add(roleMenu);
            }
            roleMenuMapper.batchRoleMenu(roleMenuList);
        }
        return CommonResult.success("新增成功");
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> initCompanyAdmin(InitCompanyAdminDTO dto) {
        //初始化管理员
        SysUser user = new SysUser();
        user.setUserName(dto.getUserName());
        user.setNickName(dto.getUserName());
        user.setPassword(SecurityUtils.encryptPassword(dto.getPassWord()));
        user.setCarrierCode(dto.getCompanyCode());
        user.setRoleType(0);
        userMapper.insertSysUser(user);
        //用户关联管理员角色
        Long roleId = sysRoleMapper.selectRoleByRoleKey("superAdmin", dto.getCompanyCode()).getRoleId();
        List<SysUserRole> userRoles = new ArrayList<>();
        SysUserRole userRole = new SysUserRole();
        userRole.setRoleId(roleId);
        userRole.setUserId(user.getUserId());
        userRoles.add(userRole);
        sysUserRoleMapper.batchUserRole(userRoles);
        //修改公司状态
        Company company = companyMapper.selectByCompanyCode(dto.getCompanyCode());
        company.setExistAdmin(1);
        companyMapper.updateCompany(company);
        return CommonResult.success("新增成功");
    }
}
