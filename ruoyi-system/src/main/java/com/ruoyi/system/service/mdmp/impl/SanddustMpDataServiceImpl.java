package com.ruoyi.system.service.mdmp.impl;

import java.math.BigDecimal;
import java.util.List;

import com.ruoyi.system.domain.mdmp.SanddustMpData;
import com.ruoyi.system.mapper.mdmp.SanddustMpDataMapper;
import com.ruoyi.system.param.mdmp.MeteParam;
import com.ruoyi.system.service.mdmp.ISanddustMpDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
@Service
public class SanddustMpDataServiceImpl implements ISanddustMpDataService
{
    @Autowired
    private SanddustMpDataMapper sanddustMpDataMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param dataValue 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public SanddustMpData selectSanddustMpDataByDataValue(BigDecimal dataValue)
    {
        return sanddustMpDataMapper.selectSanddustMpDataByDataValue(dataValue);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param sanddustMpData 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<SanddustMpData> selectSanddustMpDataList(SanddustMpData sanddustMpData)
    {
        return sanddustMpDataMapper.selectSanddustMpDataList(sanddustMpData);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param sanddustMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertSanddustMpData(SanddustMpData sanddustMpData)
    {
        return sanddustMpDataMapper.insertSanddustMpData(sanddustMpData);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param sanddustMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateSanddustMpData(SanddustMpData sanddustMpData)
    {
        return sanddustMpDataMapper.updateSanddustMpData(sanddustMpData);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param dataValues 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSanddustMpDataByDataValues(BigDecimal[] dataValues)
    {
        return sanddustMpDataMapper.deleteSanddustMpDataByDataValues(dataValues);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param dataValue 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteSanddustMpDataByDataValue(BigDecimal dataValue)
    {
        return sanddustMpDataMapper.deleteSanddustMpDataByDataValue(dataValue);
    }

    @Override
    public List<Double> getList(MeteParam meteParam) {
        return sanddustMpDataMapper.getList(meteParam);
    }
}
