package com.ruoyi.system.service.mdmp.impl;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.vo.*;
import com.ruoyi.system.domain.type.AlarmType;
import com.ruoyi.system.mapper.mdmp.*;
import com.ruoyi.system.param.mdmp.DailyFlightPlanListParam;
import com.ruoyi.system.param.mdmp.MonitorAirspaceParams;
import com.ruoyi.system.param.mdmp.MonitorHistoryFlightDataParams;
import com.ruoyi.system.param.mdmp.MonitorRouteParams;
import com.ruoyi.system.service.mdmp.FlightMonitorService;
import com.ruoyi.system.service.mdmp.IAirspaceService;
import com.ruoyi.system.service.mdmp.IDailyFlightPlanService;
import com.ruoyi.system.util.NearestAnyTimestamp;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class FlightMonitorServiceImpl implements FlightMonitorService {

    private final DailyFlightPlanMapper dailyFlightPlanMapper;
    private final FlightPlanRouteMapper flightPlanRouteMapper;
    private final FlightPlanAircraftModelMapper flightPlanAircraftModelMapper;
    private final AirspaceMapper airspaceMapper;
    private final AirspaceLongLatMapper airspaceLongLatMapper;
    private final FlightDataMapper flightDataMapper;
    private final FlightPlanRouteLongLatMapper flightPlanRouteLongLatMapper;
    private final FlightPlanWorkMapper flightPlanWorkMapper;
    private final FlightPlanWorkLongLatMapper flightPlanWorkLongLatMapper;
    private final FlightPlanWorkInMapper flightPlanWorkInMapper;
    private final WorkinLongLatMapper workinLongLatMapper;
    @Resource
    private AircraftMpMapper aircraftMpMapper;

    @Resource
    private MapDataMapper mapDataMapper;

    @Resource
    private RainfallMpDataMapper rainfallMpDataMapper;

    @Resource
    private VisibilityMpDataMapper visibilityMpDataMapper;

    @Resource
    private TemperatureMpDataMapper temperatureMpDataMapper;

    @Resource
    private ThunderstormMpDataMapper thunderstormMpDataMapper;

    @Resource
    private IAirspaceService airspaceService;

    @Resource
    private IDailyFlightPlanService dailyFlightPlanService;

    @Override
    public PageCommonResult<List<AirspaceVO>> airspaceList() {
        Airspace airspace = new Airspace();
        airspace.setEffectiveStartDate(DateUtils.getDate());
        airspace.setEffectiveEndDate(DateUtils.getDate());
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        airspace.setDeptCodeList(deptCodeList);
        List<Airspace> airspaceList = airspaceMapper.selectAirspaceList(airspace);
        if (CollectionUtils.isEmpty(airspaceList)) {
            return PageCommonResult.success(new ArrayList<>(), 0L);
        }
        List<AirspaceVO> airspaceVOList = AirspaceVO.setting(airspaceList);
        return PageCommonResult.success(airspaceVOList, airspaceList.size());
    }

    @Override
    public PageCommonResult<List<RouteVO>> routeList() {
        //当日航班计划
        DailyFlightPlanListParam dailyFlightPlan = new DailyFlightPlanListParam();
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        dailyFlightPlan.setDeptCodeList(deptCodeList);
        dailyFlightPlan.setStatus(2);
        dailyFlightPlan.setFlightDate(DateUtils.getDate());
        List<DailyFlightPlan> flightPlanList = dailyFlightPlanMapper.selectDailyFlightPlanList(dailyFlightPlan);
        if (CollectionUtils.isEmpty(flightPlanList)) {
            return PageCommonResult.success(new ArrayList<>(), 0L);
        }
        List<Long> flightPlanIds = flightPlanList.stream().map(DailyFlightPlan::getId).collect(Collectors.toList());
        //当日航班计划对应航线信息
        List<RouteVO> routeVoList = new ArrayList<>();
        for (Long flightPlanId : flightPlanIds) {
            List<FlightPlanRoute> routeList = flightPlanRouteMapper.selectFlightPlanRouteByFlightPlanId(flightPlanId,3);
            //通过routeCode和routeName对routeList去重
            List<FlightPlanRoute> routeList2 = routeList.stream().collect(Collectors.collectingAndThen(
                    Collectors.toCollection(() -> new TreeSet<>
                            (Comparator.comparing(FlightPlanRoute::getRouteCode))), ArrayList::new));
            List<RouteVO> routeVos = RouteVO.setting(flightPlanId, routeList2);
            routeVoList.addAll(routeVos);
        }
        return PageCommonResult.success(routeVoList, routeVoList.size());
    }

    @Override
    public PageCommonResult<List<AircraftVO>> aircraftList() {
        DailyFlightPlanListParam dailyFlightPlan = new DailyFlightPlanListParam();
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        dailyFlightPlan.setDeptCodeList(deptCodeList);
        dailyFlightPlan.setStatus(2);
        dailyFlightPlan.setFlightDate(DateUtils.getDate());
        //当日航班计划
        List<DailyFlightPlan> flightPlanList = dailyFlightPlanMapper.selectDailyFlightPlanList(dailyFlightPlan);
//        List<DailyFlightPlan> flightPlanList = dailyFlightPlanMapper.selectListByStatus(2, "2024-04-28");
        if (CollectionUtils.isEmpty(flightPlanList)) {
            return PageCommonResult.success(new ArrayList<>(), 0L);
        }
        //查询当日航空器返回信息
        List<AircraftVO> aircraftVoList = new ArrayList<>();
        List<Long> flightPlanIds = flightPlanList.stream().map(DailyFlightPlan::getId).collect(Collectors.toList());
        for (Long flightPlanId : flightPlanIds) {
            List<FlightPlanAircraftModel> aircraftList = flightPlanAircraftModelMapper.selectByFlightPlanIdAndOPlanType(flightPlanId, 3);
            List<AircraftVO> aircraftVos = AircraftVO.setting(flightPlanId, aircraftList);
            aircraftVoList.addAll(aircraftVos);
        }
        return PageCommonResult.success(aircraftVoList, aircraftVoList.size());
    }

    @Override
    public PageCommonResult<List<DailyFlightPlanVO>> flightPanList() {
        DailyFlightPlanListParam dailyFlightPlan = new DailyFlightPlanListParam();
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        dailyFlightPlan.setDeptCodeList(deptCodeList);
        dailyFlightPlan.setStatus(2);
        dailyFlightPlan.setFlightDate(DateUtils.getDate());
        List<DailyFlightPlan> flightPlanList = dailyFlightPlanMapper.selectDailyFlightPlanList(dailyFlightPlan);
        if (CollectionUtils.isEmpty(flightPlanList)) {
            return PageCommonResult.success(new ArrayList<>(), 0L);
        }
        List<DailyFlightPlanVO> voList = new ArrayList<>();
        for (DailyFlightPlan flightPlan : flightPlanList) {
            List<FlightPlanAircraftModel> aircraftList = flightPlanAircraftModelMapper.selectByFlightPlanIdAndOPlanType(flightPlan.getId(), 3);
            List<String> tailNumberList = new ArrayList<>();
            aircraftList.forEach(aircraft -> {
                String tailNumber = aircraft.getTailNumber();
                tailNumberList.add(tailNumber);
            });
            DailyFlightPlanVO vo = DailyFlightPlanVO.setting(flightPlan, tailNumberList);
            voList.add(vo);
        }
        return PageCommonResult.success(voList, voList.size());
    }

    @Override
    public PageCommonResult<List<Airspace>> queryAirspace(MonitorAirspaceParams params) {
        List<Long> airspaceIds = params.getAirspaceIds();
        System.out.println("参数：" + params);
//        for (Long id : airspaceIds) {
//            airspaceService.selectAirspaceById(id);
//        }
        if (CollectionUtils.isEmpty(airspaceIds)) {
            return PageCommonResult.success(new ArrayList<>(), 0);
        }
        List<Airspace> airspaceList = airspaceMapper.selectAirspaceByIds(airspaceIds);
        return PageCommonResult.success(airspaceList, airspaceList.size());
    }

    @Override
    public CommonResult<RouteCoordinateVO> queryPlanRoute(MonitorRouteParams params) {
        RouteCoordinateVO vo = new RouteCoordinateVO();
        dailyFlightPlanService.selectDailyFlightPlanById(params.getFlightPlanId());
        //航线
        List<FlightPlanRoute> routes = flightPlanRouteMapper.selectFlightPlanRouteByFlightPlanId(params.getFlightPlanId(),3);
        for (FlightPlanRoute route : routes) {
            List<FlightPlanRouteLongLat> flightPlanRouteLongLats = flightPlanRouteLongLatMapper.selectByFlightPlanRouteId(route.getId());
            route.setFlightPlanRouteLongLatList(flightPlanRouteLongLats);
        }
        //作业区
        List<FlightPlanWork> works = flightPlanWorkMapper.selectByFlightPlanId(params.getFlightPlanId(),3);
//        for (FlightPlanWork work : works) {
//            //作业区经纬度
//            List<FlightPlanWorkLongLat> flightPlanWorkLongLats = flightPlanWorkLongLatMapper.selectWorkLongLatByFlightPlanWorkId(work.getId());
//            //作业区内经纬度
//            List<FlightPlanWorkIn> workIns = flightPlanWorkInMapper.selectByFlightPlanWorkId(work.getId());
//            for (FlightPlanWorkIn workIn : workIns) {
//                List<WorkinLongLat> workInLongLats = workinLongLatMapper.selectByFlightPlanWorkInId(workIn.getId());
//                workIn.setWorkinLongLatList(workInLongLats);
//            }
//            work.setFlightPlanWorkLongLatList(flightPlanWorkLongLats);
//            work.setFlightPlanWorkInList(workIns);
//        }
        List<AircraftMp> aircraftMpList = aircraftMpMapper.getAircraftMpListByAircraftReg(params.getAircraftReg());
        List<MapDataVo> mapDataVoList = new ArrayList<>();
        for (AircraftMp aircraftMp : aircraftMpList) {
            Integer alarmType = aircraftMp.getAlarmType();
            long time = NearestAnyTimestamp.getNearestTimestamp();
            if (alarmType == AlarmType.WIND) {
                List<MapDataVo> mapDataUAndV = mapDataMapper.getUAndVMapData(time, aircraftMp);
                mapDataVoList.addAll(mapDataUAndV);
            } else if (alarmType == AlarmType.RAINFALL) {
                List<MapDataVo> mapData = rainfallMpDataMapper.getRainfallMpMapData(time, aircraftMp);
                mapDataVoList.addAll(mapData);
            } else if (alarmType == AlarmType.VISIBILITY) {
                List<MapDataVo> mapData = visibilityMpDataMapper.getVisibilityMpMapData(time, aircraftMp);
                mapDataVoList.addAll(mapData);
            } else if (alarmType == AlarmType.TEMPERATURE) {
                List<MapDataVo> mapData = temperatureMpDataMapper.getTemperatureMpMapData(time, aircraftMp);
                mapDataVoList.addAll(mapData);
            } else if (alarmType == AlarmType.THUNDERSTORMS) {
                List<MapDataVo> mapData = thunderstormMpDataMapper.getThunderstormMapData(time, aircraftMp);
                mapDataVoList.addAll(mapData);
            }
        }
        List<MapDataVo> mapData = mapDataMapper.getAirspaceAlarm(params.getAircraftReg());
        mapDataVoList.addAll(mapData);
        // 去除重复的index数据
        Set<Integer> seenIndexes = new HashSet<>();
        mapDataVoList = mapDataVoList.stream()
                .filter(data -> seenIndexes.add(data.getIndex())) // 保留首次出现的index
                .collect(Collectors.toList());
        vo.setMapDataVos(mapDataVoList);
        vo.setFlightPlanRouteList(routes);
        vo.setFlightPlanWorkList(works);
//        vo.setFlightPlanWorkList(new ArrayList<>());
        return CommonResult.success(vo);
    }


    @Override
    public CommonResult<List<FlightData>> historyData(MonitorHistoryFlightDataParams params) {
        String flightDate = DateUtils.getDay();
        String dynamicTime = DateUtils.getHms();
        List<FlightData> flightDataList = flightDataMapper.selectByFlightDateAndAircraftReg(flightDate, params.getAircraftReg(), dynamicTime);
        return CommonResult.success(flightDataList);
    }

    @Override
    public CommonResult<List<FlightPlanAircraftModel>> queryAircraft(String flightDate) {
        DailyFlightPlanListParam dailyFlightPlan = new DailyFlightPlanListParam();
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        dailyFlightPlan.setDeptCodeList(deptCodeList);
        dailyFlightPlan.setStatus(2);
        dailyFlightPlan.setFlightDate(DateUtils.getDate());
        List<DailyFlightPlan> flightPlanList = dailyFlightPlanMapper.selectDailyFlightPlanList(dailyFlightPlan);
        //List<DailyFlightPlan> flightPlanList = dailyFlightPlanMapper.selectListByStatus(2, flightDate);
        List<FlightPlanAircraftModel> aircraftModels = new ArrayList<>();
        for (DailyFlightPlan flightPlan : flightPlanList) {
            List<FlightPlanAircraftModel> aircraftModel = flightPlanAircraftModelMapper.selectByFlightPlanIdAndOPlanType(flightPlan.getId(), 3);
            aircraftModels.addAll(aircraftModel);
        }
        return CommonResult.success(aircraftModels);
    }

}
