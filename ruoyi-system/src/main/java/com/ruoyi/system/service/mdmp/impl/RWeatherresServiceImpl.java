package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.mdmp.RWeatherresMapper;
import com.ruoyi.system.domain.mdmp.RWeatherres;
import com.ruoyi.system.service.mdmp.IRWeatherresService;

/**
 * 天气现象Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class RWeatherresServiceImpl implements IRWeatherresService {
    @Autowired
    private RWeatherresMapper rWeatherresMapper;

    /**
     * 查询天气现象
     *
     * @param id 天气现象主键
     * @return 天气现象
     */
    @Override
    public RWeatherres selectRWeatherresById(Long id) {
        return rWeatherresMapper.selectRWeatherresById(id);
    }

    /**
     * 查询天气现象列表
     *
     * @param rWeatherres 天气现象
     * @return 天气现象
     */
    @Override
    public List<RWeatherres> selectRWeatherresList(RWeatherres rWeatherres) {
        return rWeatherresMapper.selectRWeatherresList(rWeatherres);
    }

    /**
     * 新增天气现象
     *
     * @param rWeatherres 天气现象
     * @return 结果
     */
    @Override
    public int insertRWeatherres(RWeatherres rWeatherres) {
        return rWeatherresMapper.insertRWeatherres(rWeatherres);
    }

    /**
     * 修改天气现象
     *
     * @param rWeatherres 天气现象
     * @return 结果
     */
    @Override
    public int updateRWeatherres(RWeatherres rWeatherres) {
        return rWeatherresMapper.updateRWeatherres(rWeatherres);
    }

    /**
     * 批量删除天气现象
     *
     * @param ids 需要删除的天气现象主键
     * @return 结果
     */
    @Override
    public int deleteRWeatherresByIds(Long[] ids) {
        return rWeatherresMapper.deleteRWeatherresByIds(ids);
    }

    /**
     * 删除天气现象信息
     *
     * @param id 天气现象主键
     * @return 结果
     */
    @Override
    public int deleteRWeatherresById(Long id) {
        return rWeatherresMapper.deleteRWeatherresById(id);
    }

    @Override
    public void insertRWeatherresList(List<RWeatherres> weatherResDtoList) {
        if (weatherResDtoList != null && weatherResDtoList.size() > 0) {
            rWeatherresMapper.insertRWeatherresList(weatherResDtoList);
        }
    }
}
