package com.ruoyi.system.service.oc;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.oc.WxUser;
import com.ruoyi.system.domain.oc.dto.UpdateWxUserDTO;
import com.ruoyi.system.domain.oc.dto.WxUserDTO;
import com.ruoyi.system.domain.oc.vo.CrewUserVO;
import com.ruoyi.system.domain.oc.vo.CrewVO;
import com.ruoyi.system.domain.oc.vo.WxUserInfoVO;
import com.ruoyi.system.domain.oc.vo.WxUserVO;

import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
public interface WxUserService {
    /**
     * 查询角色对应的微信用户
     *
     * @param companyCode 航司代码
     * @return 结果
     */
    CommonResult<Map<String, List<CrewUserVO>>> listUserByRole(String companyCode);

    /**
     * 获取机组人员信息
     *
     * @param userIds 用户Ids
     * @param flightPlanId 航班计划ID
     * @return 结果
     */
    CommonResult<List<CrewVO>> getUserArr(Long[] userIds, Long flightPlanId);

    /**
     * 获取飞行员详细信息（包括资质）
     *
     * @param userId 飞行员用户Id
     * @param roleKey 当前微信用户角色
     * @return 结果
     */
    CommonResult<CrewVO> getInfo(Long userId, String roleKey);

    /**
     * 查询微信用户列表
     *
     * @param wxUser 用户信息
     * @param companyCode 航司diamagnetic
     * @param pageNum 页数
     * @param pageSize 条数
     * @return 结果
     */
    PageCommonResult<List<WxUserVO>> list(WxUser wxUser, String companyCode, Integer pageNum, Integer pageSize);

    /**
     * 查询微信用户
     * @param dto userId
     * @return 结果
     */
    CommonResult<WxUserInfoVO> selectOne(WxUserDTO dto);

    /**
     * 修改微信用户
     * @param dto 用户信息
     * @return 结果
     */
    CommonResult<String> updateWxUser(UpdateWxUserDTO dto);

    /**
     * IO流读取用户头像图片
     *
     * @param response response
     * @param openId openId
     * @param fileNameAndSuffix 文件名
     */
    void getAvatar(HttpServletResponse response, String openId, String fileNameAndSuffix);
}
