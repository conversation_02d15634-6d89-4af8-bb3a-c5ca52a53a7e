package com.ruoyi.system.service.oc.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.oc.AirportBase;
import com.ruoyi.system.domain.oc.dto.AddAirportBaseDTO;
import com.ruoyi.system.domain.oc.dto.QueryAirportBaseDTO;
import com.ruoyi.system.domain.oc.dto.UpdateAirportBaseDTO;
import com.ruoyi.system.mapper.oc.AirportBaseMapper;
import com.ruoyi.system.service.oc.AirportBaseService;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/18 11:02
 * @mood 功能
 */
@Service
@RequiredArgsConstructor
public class AirportBaseServiceImpl implements AirportBaseService {
    @Resource
    private AirportBaseMapper airportBaseMapper;

    /**
     * 查询机场列表
     *
     * @param dto 需要查询的机场信息
     * @return 结果
     */
    @Override
    public PageCommonResult<List<AirportBase>> selectList(QueryAirportBaseDTO dto, Integer pageNum, Integer pageSize, String companyCode) {
        AirportBase airportBase = new AirportBase();
        BeanUtils.copyProperties(dto, airportBase);
        airportBase.setCompanyCode(companyCode);
        PageMethod.startPage(pageNum, pageSize);
        List<AirportBase> airportBaseList = airportBaseMapper.selectList(airportBase);
        PageInfo<AirportBase> info = new PageInfo<>(airportBaseList);
        return PageCommonResult.success(airportBaseList, info.getTotal());
    }

    @Override
    public PageCommonResult<List<AirportBase>> queryAll(String companyCode) {
        AirportBase airportBase = new AirportBase();
        airportBase.setCompanyCode(companyCode);
        List<AirportBase> airportBaseList = airportBaseMapper.selectList(airportBase);
        PageInfo<AirportBase> info = new PageInfo<>(airportBaseList);
        return PageCommonResult.success(airportBaseList, info.getTotal());
    }

    /**
     * 查询一条机场信息
     *
     * @param id 需要查询的机场信息主键
     * @return 结果
     */
    @Override
    public CommonResult<AirportBase> selectOneById(Long id) {
        AirportBase airportBase = airportBaseMapper.selectOneById(id);
        return CommonResult.success(airportBase);
    }

    /**
     * 新增一条机场信息
     *
     * @param dto 需要新增的机场信息
     * @return 结果
     */
    @Override
    public CommonResult<String> insertOne(AddAirportBaseDTO dto, String companyCode) {
        AirportBase airportBase = new AirportBase();
        BeanUtils.copyProperties(dto, airportBase);
        airportBase.setCompanyCode(companyCode);
        int row = airportBaseMapper.insertOne(airportBase);
        return CommonResult.toResult(row);
    }

    /**
     * 修改一条机场信息
     *
     * @param dto 需要修改的机场信息
     * @return 结果
     */
    @Override
    public CommonResult<String> updateOne(UpdateAirportBaseDTO dto) {
        AirportBase airportBase = new AirportBase();
        BeanUtils.copyProperties(dto, airportBase);
        int row = airportBaseMapper.updateOne(airportBase);
        return CommonResult.toResult(row);
    }

    /**
     * 删除一条机场信息
     *
     * @param id 需要删除的机场信息主键
     * @return 结果
     */
    @Override
    public int deleteOneById(Long id) {
        return airportBaseMapper.deleteOneById(id);
    }

    /**
     * 批量删除一条机场信息
     *
     * @param ids 需要批量删除的所有机场信息主键
     * @return 结果
     */
    @Override
    public CommonResult<String> deleteAllByIds(Long[] ids) {
        int row = airportBaseMapper.deleteAllByIds(ids);
        return CommonResult.toResult(row);
    }

}
