package com.ruoyi.system.service.mdmp;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.mdmp.Flight;
import com.ruoyi.system.domain.mdmp.dto.AddFlightDTO;
import com.ruoyi.system.domain.mdmp.dto.QueryFlightListDTO;
import com.ruoyi.system.domain.mdmp.dto.UpdateFlightDTO;
import com.ruoyi.system.domain.mdmp.dto.UpdateProgressDTO;
import com.ruoyi.system.domain.mdmp.vo.FlightQueueVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FlightService {

    /**
     * 查询航班列表
     *
     * @param dto 入参
     * @return 航班列表
     */
    PageCommonResult<List<Flight>> list(QueryFlightListDTO dto);

    /**
     * 批量新增航班
     * @param flights 航班集合
     * @return 结果
     */
    CommonResult<String> insertBatch(List<Flight> flights);

    /**
     * 新增航班
     * @param dto 航班信息
     * @return 结果
     */
    CommonResult<String> insert(AddFlightDTO dto);

    /**
     * 查询航班详情
     * @param flightId 航班ID
     * @return 航班详情
     */
    CommonResult<Flight> getInfo(Integer flightId);

    /**
     * 修改航班
     * @param dto 航班信息
     * @return 结果
     */
    CommonResult<String> update(UpdateFlightDTO dto);

    /**
     * 删除航班
     * @param flightId 航班ID
     * @return 结果
     */
    CommonResult<String> delete(Integer flightId);

    /**
     * 查询当天航班集合
     *
     * @return 航班集合
     */
    CommonResult<List<Flight>> flightList(List<String> deptCodeList);

    /**
     * 查询当天航班队列
     *
     * @return 航班队列
     */
    CommonResult<FlightQueueVO> flightQueue(List<String> deptCodeList);

    /**
     * 修改航班进场或离场顺序
     * @param dto 入参
     * @return 结果
     */
    CommonResult<String> updateProgress(UpdateProgressDTO dto);


}
