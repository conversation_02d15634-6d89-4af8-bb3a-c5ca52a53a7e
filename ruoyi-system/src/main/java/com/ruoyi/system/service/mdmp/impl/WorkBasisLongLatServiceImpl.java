package com.ruoyi.system.service.mdmp.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.mdmp.WorkBasisLongLatMapper;
import com.ruoyi.system.domain.mdmp.WorkBasisLongLat;
import com.ruoyi.system.service.mdmp.IWorkBasisLongLatService;

/**
 * 作业区经纬度基础信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-02
 */
@Service
public class WorkBasisLongLatServiceImpl implements IWorkBasisLongLatService 
{
    @Autowired
    private WorkBasisLongLatMapper workBasisLongLatMapper;

    /**
     * 查询作业区经纬度基础信息
     * 
     * @param id 作业区经纬度基础信息主键
     * @return 作业区经纬度基础信息
     */
    @Override
    public WorkBasisLongLat selectWorkBasisLongLatById(Long id)
    {
        return workBasisLongLatMapper.selectWorkBasisLongLatById(id);
    }

    /**
     * 查询作业区经纬度基础信息列表
     * 
     * @param workBasisLongLat 作业区经纬度基础信息
     * @return 作业区经纬度基础信息
     */
    @Override
    public List<WorkBasisLongLat> selectWorkBasisLongLatList(WorkBasisLongLat workBasisLongLat)
    {
        return workBasisLongLatMapper.selectWorkBasisLongLatList(workBasisLongLat);
    }

    /**
     * 新增作业区经纬度基础信息
     * 
     * @param workBasisLongLat 作业区经纬度基础信息
     * @return 结果
     */
    @Override
    public int insertWorkBasisLongLat(WorkBasisLongLat workBasisLongLat)
    {
        return workBasisLongLatMapper.insertWorkBasisLongLat(workBasisLongLat);
    }

    /**
     * 修改作业区经纬度基础信息
     * 
     * @param workBasisLongLat 作业区经纬度基础信息
     * @return 结果
     */
    @Override
    public int updateWorkBasisLongLat(WorkBasisLongLat workBasisLongLat)
    {
        return workBasisLongLatMapper.updateWorkBasisLongLat(workBasisLongLat);
    }

    /**
     * 批量删除作业区经纬度基础信息
     * 
     * @param ids 需要删除的作业区经纬度基础信息主键
     * @return 结果
     */
    @Override
    public int deleteWorkBasisLongLatByIds(Long[] ids)
    {
        return workBasisLongLatMapper.deleteWorkBasisLongLatByIds(ids);
    }

    /**
     * 删除作业区经纬度基础信息信息
     * 
     * @param id 作业区经纬度基础信息主键
     * @return 结果
     */
    @Override
    public int deleteWorkBasisLongLatById(Long id)
    {
        return workBasisLongLatMapper.deleteWorkBasisLongLatById(id);
    }
}
