package com.ruoyi.system.service.oc.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.oc.FlightTaskBook;
import com.ruoyi.system.domain.oc.entity.FlightDynamicInfo;
import com.ruoyi.system.domain.oc.vo.FlightDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.mapper.oc.FlightDynamicInfoMapper;
import com.ruoyi.system.mapper.oc.FlightTaskBookMapper;
import com.ruoyi.system.service.oc.IFlightDynamicInfoService;
import com.ruoyi.system.util.word.DynamicInfoWordUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;

/**
 * 动态信息服务实现
 */
@Service
@Slf4j
public class FlightDynamicInfoServiceImpl extends ServiceImpl<FlightDynamicInfoMapper, FlightDynamicInfo> implements IFlightDynamicInfoService {

    @Resource
    private FlightDynamicInfoMapper flightDynamicInfoMapper;
    @Resource
    private FlightTaskBookMapper flightTaskBookMapper;
    @Value("${template.dynamicTemplatePath}")
    private String dynamicTemplate;

    @Override
    public FlightDynamicVo selectFlightDynamicInfoById(Integer flightTaskBookId, String companyCode) {
        log.info("查询动态信息，任务书ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
        FlightDynamicVo flightDynamicVo = new FlightDynamicVo();
        // 查询任务书信息
        FlightTaskBook flightTaskBook = flightTaskBookMapper.selectByPrimaryKey(Long.valueOf(flightTaskBookId));
        if (flightTaskBook != null && flightTaskBook.getCompanyCode().equals(companyCode)) {
            // 复制基本信息
            BeanUtils.copyProperties(flightTaskBook, flightDynamicVo);

            // 查询动态信息列表
            List<FlightDynamicInfo> flightDynamicInfos = flightDynamicInfoMapper.selectFlightDynamicInfoByTaskBookNumber(flightTaskBook.getTaskBookNumber(), companyCode);
            flightDynamicVo.setFlightDynamicInfoList(flightDynamicInfos);

            // 计算统计信息
            calculateTotalInfo(flightDynamicVo, flightDynamicInfos);
        }
        return flightDynamicVo;
    }

    @Override
    public void exportDynamicInfoWord(HttpServletResponse response, Integer flightTaskBookId, String companyCode) {
        log.info("开始导出动态信息Word文档，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
        try {
            // 查询动态信息数据
            FlightDynamicVo flightDynamicVo = this.selectFlightDynamicInfoById(flightTaskBookId, companyCode);
            if (flightDynamicVo == null) {
                log.error("未找到对应的动态数据，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
                return;
            }

            // 转换为Word导出格式
            WordMeteorologicalVo wordMeteorologicalVo = convertToDynamicWordVo(flightDynamicVo);

            // 生成Word文档
            byte[] docBytes = generateDynamicWordDocument(wordMeteorologicalVo);

            // 设置响应头
            String fileName = DynamicInfoWordUtils.generateFileName("动态信息记录表", "docx");
            DynamicInfoWordUtils.setWordResponseHeaders(response, fileName);

            // 输出文件到前端
            DynamicInfoWordUtils.streamWordDocumentToResponse(response, docBytes);

            log.info("动态信息Word文档导出成功，文件名: {}", fileName);
        } catch (Exception e) {
            log.error("导出动态信息Word文档失败，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode, e);
        }
    }

    @Override
    public void exportDynamicInfoPdf(HttpServletResponse response, Integer flightTaskBookId, String companyCode) {
        // TODO: 实现PDF导出功能
        log.info("动态信息PDF导出功能待实现");
    }

    @Override
    public void exportDynamicInfoPdfBatch(HttpServletResponse response, List<Integer> flightTaskBookIds, String companyCode) {
        // TODO: 实现批量PDF导出功能
        log.info("动态信息批量PDF导出功能待实现");
    }

    @Override
    public byte[] generateDynamicWordDocument(WordMeteorologicalVo wordMeteorologicalVo) {
        try {
            return DynamicInfoWordUtils.generateDynamicInfoDocument(dynamicTemplate, wordMeteorologicalVo, null);
        } catch (Exception e) {
            log.error("生成动态信息Word文档失败", e);
            throw new RuntimeException("生成Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 转换动态信息为Word导出格式（仅动态信息，不包含气象信息）
     */
    private WordMeteorologicalVo convertToDynamicWordVo(FlightDynamicVo flightDynamicVo) {
        WordMeteorologicalVo wordMeteorologicalVo = new WordMeteorologicalVo();
        BeanUtils.copyProperties(flightDynamicVo, wordMeteorologicalVo);

        // 转换动态信息列表
        List<FlightDynamicInfo> flightDynamicInfoList = flightDynamicVo.getFlightDynamicInfoList();
        List<WordFlightWeatherDynamicVo> dynamicInfoList = new ArrayList<>();

        if (flightDynamicInfoList != null) {
            flightDynamicInfoList.forEach(flightDynamicInfo -> {
                WordFlightWeatherDynamicVo wordFlightWeatherDynamicVo = new WordFlightWeatherDynamicVo();
                BeanUtils.copyProperties(flightDynamicInfo, wordFlightWeatherDynamicVo);
                dynamicInfoList.add(wordFlightWeatherDynamicVo);
            });
        }

        wordMeteorologicalVo.setDynamicInfoList(dynamicInfoList);

        // 动态信息导出不包含气象信息
        wordMeteorologicalVo.setDepartureWeatherInfoList(new ArrayList<>());
        wordMeteorologicalVo.setArrivalWeatherInfoList(new ArrayList<>());

        return wordMeteorologicalVo;
    }

    /**
     * 计算统计信息
     */
    private void calculateTotalInfo(FlightDynamicVo flightDynamicVo, List<FlightDynamicInfo> flightDynamicInfos) {
        if (flightDynamicInfos == null || flightDynamicInfos.isEmpty()) {
            return;
        }

        int totalGroundTimeMin = 0;
        int totalAirTimeMin = 0;
        int totalTimeMin = 0;
        int totalSortieCount = 0;

        for (FlightDynamicInfo info : flightDynamicInfos) {
            if (info.getGroundTimeMin() != null) {
                totalGroundTimeMin += info.getGroundTimeMin();
            }
            if (info.getAirTimeMin() != null) {
                totalAirTimeMin += info.getAirTimeMin();
            }
            if (info.getTotalTimeMin() != null) {
                totalTimeMin += info.getTotalTimeMin();
            }
            if (info.getSortieCount() != null) {
                totalSortieCount += info.getSortieCount();
            }
        }

        flightDynamicVo.setGroundTimeMinTotal(String.valueOf(totalGroundTimeMin));
        flightDynamicVo.setAirTimeMinTotal(String.valueOf(totalAirTimeMin));
        flightDynamicVo.setTotalTimeMinTotal(String.valueOf(totalTimeMin));
        flightDynamicVo.setSortieCountTotal(String.valueOf(totalSortieCount));
    }
}
