package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.system.domain.mdmp.MpData;
import com.ruoyi.system.mapper.mdmp.MpDataMapper;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;
import com.ruoyi.system.service.mdmp.IUMpDataService;
import com.ruoyi.system.util.MeteUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
@Service
public class UMpDataServiceImpl implements IUMpDataService {
    @Autowired
    private MpDataMapper uMpDataMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public MpData selectUMpDataById(Long id) {
        return uMpDataMapper.selectMpDataById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param uMpData 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<MpData> selectUMpDataList(MpData uMpData) {
        return uMpDataMapper.selectMpDataList(uMpData);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param uMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertUMpData(MpData uMpData) {
        return uMpDataMapper.insertMpData(uMpData);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param uMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateUMpData(MpData uMpData) {
        return uMpDataMapper.updateMpData(uMpData);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteUMpDataByIds(Long[] ids) {
        return uMpDataMapper.deleteMpDataByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteUMpDataById(Long id) {
        return uMpDataMapper.deleteMpDataById(id);
    }

    @Override
    public double selectUMpDataByMeteInfo(MeteInfoParam meteInfoParam) {
        MpData mpData = uMpDataMapper.selectUMpDataByMeteInfo(meteInfoParam);
        if (mpData != null) {
            // String dataValue = mpData.getDataValue();
            //return MeteUtil.getDateValue(dataValue, meteInfoParam.getLongitude());
            return 1;
        }
        return 0;
    }

    @Override
    public List<MpData> getList(MeteParam meteParam) {
        List<MpData> mpDataList=uMpDataMapper.getList(meteParam);
        return mpDataList;
    }
}
