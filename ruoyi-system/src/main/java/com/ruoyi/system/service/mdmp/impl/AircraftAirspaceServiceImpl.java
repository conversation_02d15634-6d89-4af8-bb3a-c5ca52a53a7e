package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.system.domain.mdmp.AircraftAirspace;
import com.ruoyi.system.mapper.mdmp.AircraftAirspaceMapper;
import com.ruoyi.system.service.mdmp.IAircraftAirspaceService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
@Service
public class AircraftAirspaceServiceImpl implements IAircraftAirspaceService
{
    @Autowired
    private AircraftAirspaceMapper aircraftAirspaceMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public AircraftAirspace selectAircraftAirspaceById(Long id)
    {
        return aircraftAirspaceMapper.selectAircraftAirspaceById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param aircraftAirspace 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<AircraftAirspace> selectAircraftAirspaceList(AircraftAirspace aircraftAirspace)
    {
        return aircraftAirspaceMapper.selectAircraftAirspaceList(aircraftAirspace);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param aircraftAirspace 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertAircraftAirspace(AircraftAirspace aircraftAirspace)
    {
        return aircraftAirspaceMapper.insertAircraftAirspace(aircraftAirspace);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param aircraftAirspace 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateAircraftAirspace(AircraftAirspace aircraftAirspace)
    {
        return aircraftAirspaceMapper.updateAircraftAirspace(aircraftAirspace);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteAircraftAirspaceByIds(Long[] ids)
    {
        return aircraftAirspaceMapper.deleteAircraftAirspaceByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteAircraftAirspaceById(Long id)
    {
        return aircraftAirspaceMapper.deleteAircraftAirspaceById(id);
    }
}
