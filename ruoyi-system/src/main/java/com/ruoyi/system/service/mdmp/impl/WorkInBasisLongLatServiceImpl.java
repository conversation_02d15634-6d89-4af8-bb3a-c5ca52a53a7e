package com.ruoyi.system.service.mdmp.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.mdmp.WorkInBasisLongLatMapper;
import com.ruoyi.system.domain.mdmp.WorkInBasisLongLat;
import com.ruoyi.system.service.mdmp.IWorkInBasisLongLatService;

/**
 * 作业区内经纬度基础信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-02
 */
@Service
public class WorkInBasisLongLatServiceImpl implements IWorkInBasisLongLatService 
{
    @Autowired
    private WorkInBasisLongLatMapper workInBasisLongLatMapper;

    /**
     * 查询作业区内经纬度基础信息
     * 
     * @param id 作业区内经纬度基础信息主键
     * @return 作业区内经纬度基础信息
     */
    @Override
    public WorkInBasisLongLat selectWorkInBasisLongLatById(Long id)
    {
        return workInBasisLongLatMapper.selectWorkInBasisLongLatById(id);
    }

    /**
     * 查询作业区内经纬度基础信息列表
     * 
     * @param workInBasisLongLat 作业区内经纬度基础信息
     * @return 作业区内经纬度基础信息
     */
    @Override
    public List<WorkInBasisLongLat> selectWorkInBasisLongLatList(WorkInBasisLongLat workInBasisLongLat)
    {
        return workInBasisLongLatMapper.selectWorkInBasisLongLatList(workInBasisLongLat);
    }

    /**
     * 新增作业区内经纬度基础信息
     * 
     * @param workInBasisLongLat 作业区内经纬度基础信息
     * @return 结果
     */
    @Override
    public int insertWorkInBasisLongLat(WorkInBasisLongLat workInBasisLongLat)
    {
        return workInBasisLongLatMapper.insertWorkInBasisLongLat(workInBasisLongLat);
    }

    /**
     * 修改作业区内经纬度基础信息
     * 
     * @param workInBasisLongLat 作业区内经纬度基础信息
     * @return 结果
     */
    @Override
    public int updateWorkInBasisLongLat(WorkInBasisLongLat workInBasisLongLat)
    {
        return workInBasisLongLatMapper.updateWorkInBasisLongLat(workInBasisLongLat);
    }

    /**
     * 批量删除作业区内经纬度基础信息
     * 
     * @param ids 需要删除的作业区内经纬度基础信息主键
     * @return 结果
     */
    @Override
    public int deleteWorkInBasisLongLatByIds(Long[] ids)
    {
        return workInBasisLongLatMapper.deleteWorkInBasisLongLatByIds(ids);
    }

    /**
     * 删除作业区内经纬度基础信息信息
     * 
     * @param id 作业区内经纬度基础信息主键
     * @return 结果
     */
    @Override
    public int deleteWorkInBasisLongLatById(Long id)
    {
        return workInBasisLongLatMapper.deleteWorkInBasisLongLatById(id);
    }
}
