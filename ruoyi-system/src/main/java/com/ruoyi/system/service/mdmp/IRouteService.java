package com.ruoyi.system.service.mdmp;

import java.util.List;

import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.mdmp.Route;

/**
 * 航线Service接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface IRouteService
{
    /**
     * 查询航线
     *
     * @param id 航线主键
     * @return 航线
     */
    public Route selectRouteById(Long id);

    /**
     * 查询航线列表
     *
     * @param route 航线
     * @return 航线集合
     */
    public List<Route> selectRouteList(Route route);

    /**
     * 新增航线
     *
     * @param route 航线
     * @return 结果
     */
    public int insertRoute(Route route);

    /**
     * 修改航线
     *
     * @param route 航线
     * @return 结果
     */
    public int updateRoute(Route route);

    /**
     * 批量删除航线
     *
     * @param ids 需要删除的航线主键集合
     * @return 结果
     */
    public int deleteRouteByIds(Long[] ids);

    /**
     * 删除航线信息
     *
     * @param id 航线主键
     * @return 结果
     */
    public int deleteRouteById(Long id);
}
