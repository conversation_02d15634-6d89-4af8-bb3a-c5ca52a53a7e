package com.ruoyi.system.service.mdmp;

import com.ruoyi.system.domain.mdmp.WorkInBasisLongLat;

import java.util.List;


/**
 * 作业区内经纬度基础信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface IWorkInBasisLongLatService 
{
    /**
     * 查询作业区内经纬度基础信息
     * 
     * @param id 作业区内经纬度基础信息主键
     * @return 作业区内经纬度基础信息
     */
    public WorkInBasisLongLat selectWorkInBasisLongLatById(Long id);

    /**
     * 查询作业区内经纬度基础信息列表
     * 
     * @param workInBasisLongLat 作业区内经纬度基础信息
     * @return 作业区内经纬度基础信息集合
     */
    public List<WorkInBasisLongLat> selectWorkInBasisLongLatList(WorkInBasisLongLat workInBasisLongLat);

    /**
     * 新增作业区内经纬度基础信息
     * 
     * @param workInBasisLongLat 作业区内经纬度基础信息
     * @return 结果
     */
    public int insertWorkInBasisLongLat(WorkInBasisLongLat workInBasisLongLat);

    /**
     * 修改作业区内经纬度基础信息
     * 
     * @param workInBasisLongLat 作业区内经纬度基础信息
     * @return 结果
     */
    public int updateWorkInBasisLongLat(WorkInBasisLongLat workInBasisLongLat);

    /**
     * 批量删除作业区内经纬度基础信息
     * 
     * @param ids 需要删除的作业区内经纬度基础信息主键集合
     * @return 结果
     */
    public int deleteWorkInBasisLongLatByIds(Long[] ids);

    /**
     * 删除作业区内经纬度基础信息信息
     * 
     * @param id 作业区内经纬度基础信息主键
     * @return 结果
     */
    public int deleteWorkInBasisLongLatById(Long id);
}
