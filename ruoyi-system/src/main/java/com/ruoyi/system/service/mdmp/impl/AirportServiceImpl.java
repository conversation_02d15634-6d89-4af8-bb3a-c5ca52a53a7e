package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.mdmp.AirportMapper;
import com.ruoyi.system.domain.mdmp.Airport;
import com.ruoyi.system.service.mdmp.IAirportService;

import javax.annotation.Resource;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 机场Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Service
public class AirportServiceImpl implements IAirportService {
    @Resource
    private AirportMapper airportMapper;

    /**
     * 查询机场
     *
     * @param id 机场主键
     * @return 机场
     */
    @Override
    public Airport selectAirportById(Long id) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        //判断deptCodeList是否包含aircraft.getDeptCode()
        Airport airport = airportMapper.selectAirportById(id);
        if (deptCodeList.contains(airport.getDeptCode())) {
            return airport;
        } else {
            throw new ServiceException("无权限访问该数据");
        }
    }

    /**
     * 查询机场列表
     *
     * @param airport 机场
     * @return 机场
     */
    @Override
    public List<Airport> selectAirportList(Airport airport) {
        return airportMapper.selectAirportList(airport);
    }

    /**
     * 新增机场
     *
     * @param airport 机场
     * @return 结果
     */
    @Override
    public int insertAirport(Airport airport) {
        return airportMapper.insertAirport(airport);
    }

    /**
     * 修改机场
     *
     * @param airport 机场
     * @return 结果
     */
    @Override
    public int updateAirport(Airport airport) {
        selectAirportById(airport.getId());
        return airportMapper.updateAirport(airport);
    }

    /**
     * 批量删除机场
     *
     * @param ids 需要删除的机场主键
     * @return 结果
     */
    @Override
    public int deleteAirportByIds(Long[] ids) {
        return airportMapper.deleteAirportByIds(ids);
    }

    /**
     * 删除机场信息
     *
     * @param id 机场主键
     * @return 结果
     */
    @Override
    public int deleteAirportById(Long id) {
        selectAirportById(id);
        return airportMapper.deleteAirportById(id);
    }
}
