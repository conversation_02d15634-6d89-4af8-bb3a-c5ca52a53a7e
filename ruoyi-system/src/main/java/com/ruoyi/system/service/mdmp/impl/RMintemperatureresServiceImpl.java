package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.system.domain.mdmp.RMintemperatureres;
import com.ruoyi.system.mapper.mdmp.RMintemperatureresMapper;
import com.ruoyi.system.service.mdmp.IRMintemperatureresService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 最小温度Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class RMintemperatureresServiceImpl implements IRMintemperatureresService
{
    @Autowired
    private RMintemperatureresMapper rMintemperatureresMapper;

    /**
     * 查询最小温度
     *
     * @param id 最小温度主键
     * @return 最小温度
     */
    @Override
    public RMintemperatureres selectRMintemperatureresById(Long id)
    {
        return rMintemperatureresMapper.selectRMintemperatureresById(id);
    }

    /**
     * 查询最小温度列表
     *
     * @param rMintemperatureres 最小温度
     * @return 最小温度
     */
    @Override
    public List<RMintemperatureres> selectRMintemperatureresList(RMintemperatureres rMintemperatureres)
    {
        return rMintemperatureresMapper.selectRMintemperatureresList(rMintemperatureres);
    }

    /**
     * 新增最小温度
     *
     * @param rMintemperatureres 最小温度
     * @return 结果
     */
    @Override
    public int insertRMintemperatureres(RMintemperatureres rMintemperatureres)
    {

        return rMintemperatureresMapper.insertRMintemperatureres(rMintemperatureres);
    }

    /**
     * 修改最小温度
     *
     * @param rMintemperatureres 最小温度
     * @return 结果
     */
    @Override
    public int updateRMintemperatureres(RMintemperatureres rMintemperatureres)
    {
        return rMintemperatureresMapper.updateRMintemperatureres(rMintemperatureres);
    }

    /**
     * 批量删除最小温度
     *
     * @param ids 需要删除的最小温度主键
     * @return 结果
     */
    @Override
    public int deleteRMintemperatureresByIds(Long[] ids)
    {
        return rMintemperatureresMapper.deleteRMintemperatureresByIds(ids);
    }

    /**
     * 删除最小温度信息
     *
     * @param id 最小温度主键
     * @return 结果
     */
    @Override
    public int deleteRMintemperatureresById(Long id)
    {
        return rMintemperatureresMapper.deleteRMintemperatureresById(id);
    }
}
