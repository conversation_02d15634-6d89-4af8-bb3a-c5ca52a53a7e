package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.SingleFlightTask;
import com.ruoyi.system.param.mdmp.SingleFlightTaskListParam;

/**
 * 单一飞行任务Service接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface ISingleFlightTaskService
{
    /**
     * 查询单一飞行任务
     *
     * @param id 单一飞行任务主键
     * @return 单一飞行任务
     */
    public SingleFlightTask selectSingleFlightTaskById(Long id);

    /**
     * 查询单一飞行任务列表
     *
     * @param singleFlightTask 单一飞行任务
     * @return 单一飞行任务集合
     */
    public List<SingleFlightTask> selectSingleFlightTaskList(SingleFlightTaskListParam singleFlightTask);

    /**
     * 新增单一飞行任务
     *
     * @param singleFlightTask 单一飞行任务
     * @return 结果
     */
    public int insertSingleFlightTask(SingleFlightTask singleFlightTask);

    /**
     * 修改单一飞行任务
     *
     * @param singleFlightTask 单一飞行任务
     * @return 结果
     */
    public int updateSingleFlightTask(SingleFlightTask singleFlightTask);

    /**
     * 批量删除单一飞行任务
     *
     * @param ids 需要删除的单一飞行任务主键集合
     * @return 结果
     */
    public int deleteSingleFlightTaskByIds(Long[] ids);

    /**
     * 删除单一飞行任务信息
     *
     * @param id 单一飞行任务主键
     * @return 结果
     */
    public int deleteSingleFlightTaskById(Long id);
}
