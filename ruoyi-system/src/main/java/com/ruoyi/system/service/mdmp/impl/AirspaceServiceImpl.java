package com.ruoyi.system.service.mdmp.impl;

import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

import com.ruoyi.common.config.rule.Obstacle;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.vo.AirspaceListVO;
import com.ruoyi.system.domain.mdmp.vo.MapDataLongLatVo;
import com.ruoyi.system.domain.mdmp.vo.QueryMapDataLongLatVo;
import com.ruoyi.system.domain.mdmp.vo.QueryMapDataVo;
import com.ruoyi.system.domain.type.AirspaceType;
import com.ruoyi.system.domain.type.GraphicsType;
import com.ruoyi.system.domain.type.PointType;
import com.ruoyi.system.domain.type.PositiveOrNegativeType;
import com.ruoyi.system.mapper.mdmp.*;
import com.ruoyi.system.service.mdmp.IAirspaceService;
import com.ruoyi.system.service.mdmp.IMapDataObstacleService;
import com.ruoyi.system.service.mdmp.IMapDataService;
import com.ruoyi.system.util.AirspaceUtil;
import com.ruoyi.system.util.CircularUtil;
import com.ruoyi.system.util.GeoCalculatorUtil;
import com.ruoyi.system.util.LongLatUtil;
import com.ruoyi.system.validator.AirspaceValidator;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;


/**
 * 空域Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
public class AirspaceServiceImpl implements IAirspaceService {

    @Resource
    private AirspaceMapper airspaceMapper;

    @Resource
    private AirspaceLongLatMapper airspaceLongLatMapper;

    @Resource
    private AirspaceMapDataMapper airspaceMapDataMapper;


    @Resource
    private IMapDataService mapDataService;

    @Resource
    private MapDataObstacleMapper mapDataObstacleMapper;


    @Resource
    private AircraftAirspaceMapper aircraftAirspaceMapper;

    @Resource
    private AircraftMapper aircraftMapper;
    @Resource
    private RedisCache redisCache;


    Logger logger = LoggerFactory.getLogger(AirspaceServiceImpl.class);

    /**
     * 查询空域
     *
     * @param id 空域主键
     * @return 空域
     */
    @Override
    public Airspace selectAirspaceById(Long id) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        //判断deptCodeList是否包含aircraft.getDeptCode()
        Airspace airspace = airspaceMapper.selectAirspaceById(id);
        if (deptCodeList.contains(airspace.getDeptCode())) {
            List<AirspaceLongLat> airspaceLongLats = airspaceLongLatMapper.selectByAirspaceId(airspace.getId());
            airspace.setAirspaceLongLatList(airspaceLongLats);
            List<Aircraft> aircraftList = aircraftMapper.selectByAircraftId(airspace.getId());
            airspace.setAircraftList(aircraftList);
            return airspace;
        } else {
            throw new ServiceException("无权限访问该数据");
        }


    }

    /**
     * 查询空域列表
     *
     * @param airspace 空域
     * @return 空域
     */
    @Override
    public List<Airspace> selectAirspaceList(Airspace airspace) {
        return airspaceMapper.selectAirspaceList(airspace);
    }

    @Override
    public List<AirspaceListVO> selectAirspaceListAndMap(Long[] ids) {
        // 1. 参数校验：处理 null 和空数组
        if (ids == null || ids.length == 0) {
            return Collections.emptyList(); // 返回空集合而非 null
        }

        // 2. 获取当前用户部门权限列表（确保非空）
        List<String> deptCodeList = Optional.ofNullable(getLoginUser().getDeptCodeList())
                .orElse(Collections.emptyList());

        // 3. 查询数据
        List<AirspaceListVO> airspaceList = airspaceMapper.selectAirspaceListAndMap(ids,deptCodeList);
        if (airspaceList.isEmpty()) {
            return Collections.emptyList();
        }

        // 4. 权限校验
//        checkPermission(airspaceList, deptCodeList);

        return airspaceList;
    }

    private void checkPermission(List<Airspace> airspaceList, List<String> deptCodeList) {
        // 收集未被授权的部门代码（便于定位问题）
//        List<String> unauthorizedDeptCodes = airspaceList.stream()
//                .map(Airspace::getDeptCode)
//                .filter(deptCode -> !deptCodeList.contains(deptCode))
//                .collect(Collectors.toList());
//        if (!unauthorizedDeptCodes.isEmpty()) {
//            throw new ServiceException("无权限访问该数据");
//        }
        // 1. 校验输入参数合法性
        if (deptCodeList == null || deptCodeList.isEmpty()) {
            throw new IllegalArgumentException("部门代码列表不能为空");
        }
        // 2. 转换为 Set 提升性能
        Set<String> deptCodeSet = new HashSet<>(deptCodeList);
        // 3. 收集未授权的部门代码（处理空值）
        List<String> unauthorizedDeptCodes = airspaceList.stream()
                .map(Airspace::getDeptCode)
                .filter(Objects::nonNull)  // 根据业务需求决定是否允许空值
                .filter(deptCode -> !deptCodeSet.contains(deptCode))
                .collect(Collectors.toList());
        // 4. 抛出具体异常信息
        if (!unauthorizedDeptCodes.isEmpty()) {
            String errorMsg = String.format("无权限访问部门代码为 %s 的数据", unauthorizedDeptCodes);
            throw new ServiceException(errorMsg);
        }
    }

    public List<AirspaceLongLat> processPolygonAndCircularAirspace(Airspace airspace, long id) {
        // 清除其他类型的数据
        AirspaceUtil.clearEllipseData(airspace);
        AirspaceUtil.clearFanShapedData(airspace);
        // 圆形验证
        AirspaceValidator.processCircularAirspace(airspace);
        // 圆心经度纬度转换
        double radius = airspace.getRadius().doubleValue();
        double offset = 0;
        if (airspace.getPositiveOrNegative() == PositiveOrNegativeType.POSITIVE) {
            offset = airspace.getAirspaceOffset().doubleValue();
        } else if (airspace.getPositiveOrNegative() == PositiveOrNegativeType.NEGATIVE) {
            offset = -airspace.getAirspaceOffset().doubleValue();
        }
        double circleCenterLong = LongLatUtil.convertDMSToDD(airspace.getCircleCenterLong());
        double circleCenterLat = LongLatUtil.convertDMSToDD(airspace.getCircleCenterLat());
        // 创建圆形坐标列表
        List<AirspaceLongLat> processedLongLatList = CircularUtil.createSimpleCircleWKT(id, circleCenterLong, circleCenterLat, radius, offset, 50, airspace.getTerminationHeight());
        return processedLongLatList;
    }


    public List<AirspaceLongLat> processAirspaceCoordinates(Airspace airspace, long id) {
        List<AirspaceLongLat> processedLongLatList = new ArrayList<>();
        // 清除扇形、椭圆、圆数据
        AirspaceUtil.clearFanShapedData(airspace);
        AirspaceUtil.clearEllipseData(airspace);
        AirspaceUtil.clearCircularData(airspace, AirspaceType.OBSTACLES);
        // 获取坐标列表
        List<AirspaceLongLat> airspaceLongLatList = airspace.getAirspaceLongLatList();
        // 检查坐标列表是否为空
        if (airspaceLongLatList == null || airspaceLongLatList.isEmpty()) {
            throw new ServiceException("坐标不能为空");
        }
        // 遍历坐标列表，处理每个坐标
        for (int i = 0; i < airspaceLongLatList.size(); i++) {
            AirspaceLongLat longLat = airspaceLongLatList.get(i);
            longLat.setAirspaceId(id);
            longLat.setSortNumber(i); // 直接使用索引
            if (airspace.getPointType() == PointType.LAL) {
                longLat.setDoubleLongitude(LongLatUtil.convertDMSToDD(longLat.getLongitude()));
                longLat.setDoubleLatitude(LongLatUtil.convertDMSToDD(longLat.getLatitude()));
            } else if (airspace.getPointType() == PointType.CLAL) {
                longLat.setDoubleLongitude(Double.parseDouble(longLat.getLongitude()));
                longLat.setDoubleLatitude(Double.parseDouble(longLat.getLatitude()));
            }
            processedLongLatList.add(longLat); // 添加到新的列表中
        }
        // 将第一个坐标添加到列表末尾，形成闭合多边形
        // 创建一个新的 AirspaceLongLat 对象，其属性与列表的第一个元素相同
        AirspaceLongLat firstLongLatCopy = new AirspaceLongLat();
        firstLongLatCopy.setAirspaceId(processedLongLatList.get(0).getAirspaceId());
        firstLongLatCopy.setLongitude(processedLongLatList.get(0).getLongitude());
        firstLongLatCopy.setLongitude(processedLongLatList.get(0).getLongitude());
        firstLongLatCopy.setDoubleLatitude(processedLongLatList.get(0).getDoubleLatitude());
        firstLongLatCopy.setDoubleLongitude(processedLongLatList.get(0).getDoubleLongitude());
        firstLongLatCopy.setHeight(processedLongLatList.get(0).getHeight());
        firstLongLatCopy.setSortNumber(processedLongLatList.size());
        // 将新创建的对象添加到列表的末尾
        processedLongLatList.add(firstLongLatCopy);
        return processedLongLatList;
    }

    @Transactional
    public int saveAirspaceLongLatList(Airspace airspace,List<String> deptCodeList) {
        int result = 0;
        long id = airspace.getId();
        int graphicsType = airspace.getGraphicsType();
        // 根据图形类型设置坐标点
        List<AirspaceLongLat> processedLongLatList = new ArrayList<>();
        //判断是否为障碍物
        if (airspace.getAirspaceType() != AirspaceType.OBSTACLES) {
            //清除障碍物数据
            AirspaceUtil.clearObstacleData(airspace);
            //判断是否为圆
            if (graphicsType == GraphicsType.CIRCULAR) {
                processedLongLatList = processPolygonAndCircularAirspace(airspace, id);
            }
            //判断是否为多边形
            else if (graphicsType == GraphicsType.POLYGON) {
                processedLongLatList = processAirspaceCoordinates(airspace, id);
            }
        } else {
            processedLongLatList = saveAndProcessObstacles(airspace, id);

        }
        // 批量插入AirspaceLongLat
        if (!processedLongLatList.isEmpty()) {
            airspaceLongLatMapper.insertAirspaceLongLatList(processedLongLatList);
            logger.info("保存航线地图数据 异步");
            mapDataService.processAndInsertAirspaceData(airspace, graphicsType, processedLongLatList, id);
            //更新缓存
            if (airspace.getAirspaceType().equals(AirspaceType.OBSTACLES)) {
                mapDataService.setAirspaceCache(deptCodeList);
            }

            result = 1; // 插入成功
        }
        return result;
    }


    /**
     * 0 保存并处理障碍物信息，返回处理后的障碍物经纬度列表。
     *
     * @param airspace 包含障碍物信息的Airspace对象
     * @param id       障碍物的唯一标识符
     * @return 处理后的障碍物经纬度列表
     * @throws Exception 如果处理过程中发生错误
     */
    public static List<AirspaceLongLat> saveAndProcessObstacles(Airspace airspace, long id) {
        // 清除之前的障碍物数据
        AirspaceUtil.clearFanShapedData(airspace);
        AirspaceUtil.clearEllipseData(airspace);
        AirspaceUtil.clearCircularData(airspace, AirspaceType.OBSTACLES);

        // 障碍物验证
        AirspaceValidator.processCircularObstacles(airspace);

        // 障碍物经度纬度转换
        double benchmarkLong = LongLatUtil.convertDMSToDD(airspace.getBenchmarkLong());
        double benchmarkLat = LongLatUtil.convertDMSToDD(airspace.getBenchmarkLat());
        double magneticBearing = airspace.getMagneticBearing().doubleValue();
        double distance = airspace.getDistance().doubleValue() / 1000;

        // 计算障碍物目标点的经纬度
        double[] longAndLat = GeoCalculatorUtil.calculateDestinationPoint(benchmarkLat, benchmarkLong, magneticBearing, distance);
        // 获取障碍物的半径
        double radius = airspace.getRadius().doubleValue();

        // 提取障碍物的经纬度
        double obstaclesLong = longAndLat[1];
        double obstaclesLat = longAndLat[0];

        // 创建障碍物的简单圆形WKT表示
        // List<AirspaceLongLat> processedLongLatList = CircularUtil.createSimpleCircleWKT(
        //id, obstaclesLong, obstaclesLat, radius, 1);
        List<AirspaceLongLat> processedLongLatList = new ArrayList<>();
        processedLongLatList.add(CircularUtil.createAirspaceLongLat(id, obstaclesLong, obstaclesLat, 0, 0, airspace.getTerminationHeight()));

        return processedLongLatList;
    }

    /**
     * 新增空域
     *
     * @param airspace 空域
     * @return 结果
     */
    @Override
    @Transactional
    public int insertAirspace(Airspace airspace) {
        int result = 0;
        // 插入Airspace
        if (airspaceMapper.insertAirspace(airspace) > 0) {
            result = saveAirspaceLongLatList(airspace,getLoginUser().getDeptCodeList());
        }
        //新增机型关联表
        List<AircraftAirspace> aircraftAirspaceList = createAircraftAirspaceList(airspace);
        if (!aircraftAirspaceList.isEmpty() && airspace.getAirspaceType() == AirspaceType.CONTROL) {
            aircraftAirspaceMapper.insertAircraftAirspaceList(aircraftAirspaceList);
        }
        if (result == 0) {
            // 处理异常，记录日志等
            throw new ServiceException("插入失败");
        }
        return result;
    }

    // 封装创建AircraftAirspace对象的逻辑
    private List<AircraftAirspace> createAircraftAirspaceList(Airspace airspace) {
        List<Aircraft> aircraftList = airspace.getAircraftList();
        List<AircraftAirspace> aircraftAirspaceList = new ArrayList<>();
        for (Aircraft aircraft : aircraftList) {
            AircraftAirspace aircraftAirspace = new AircraftAirspace();
            aircraftAirspace.setAircraftId(aircraft.getId());
            aircraftAirspace.setAircraftReg(aircraft.getAircraftReg());
            aircraftAirspace.setAirspaceId(airspace.getId());
            aircraftAirspaceList.add(aircraftAirspace);
        }
        return aircraftAirspaceList;
    }

    /**
     * 修改空域
     *
     * @param airspace 空域
     * @return 结果
     */
    @Override
    @Transactional
    public int updateAirspace(Airspace airspace,List<String> deptCodeList) {
        selectAirspaceById(airspace.getId());
        //直接删除指定空域的经纬度数据
        airspaceLongLatMapper.deleteAirspaceLongLatByAirspaceId(airspace.getId());
        //删除格子表数据
        airspaceMapDataMapper.deleteAirspaceMapDataByAirspaceId(airspace.getId());
        //删除机型关联表
        aircraftAirspaceMapper.deleteAircraftAirspaceByAirspaceId(airspace.getId());

        //删除格子障碍物表数据
        mapDataObstacleMapper.deleteByAirspaceId(airspace.getId());
        int result = 0;
        // 修改Airspace
        if (airspaceMapper.updateAirspace(airspace) > 0) {
            result = saveAirspaceLongLatList(airspace,deptCodeList);
        }
        //新增机型关联表
        List<AircraftAirspace> aircraftAirspaceList = createAircraftAirspaceList(airspace);
        if (!aircraftAirspaceList.isEmpty() && airspace.getAirspaceType() == AirspaceType.CONTROL) {
            aircraftAirspaceMapper.insertAircraftAirspaceList(aircraftAirspaceList);
        }
        if (result == 0) {
            // 处理异常，记录日志等
            throw new ServiceException("修改失败");
        }
        return result;
    }

    /**
     * 批量删除空域
     *
     * @param ids 需要删除的空域主键
     * @return 结果
     */
    @Override
    public int deleteAirspaceByIds(Long[] ids) {
        return airspaceMapper.deleteAirspaceByIds(ids);
    }

    /**
     * 删除空域信息
     *
     * @param id 空域主键
     * @return 结果
     */
    @Override
    @Transactional
    public int deleteAirspaceById(Long id,List<String> deptCodeList) {
        selectAirspaceById(id);
        int longLatDeleted = airspaceLongLatMapper.deleteAirspaceLongLatByAirspaceId(id);
        //删除格子表数据
        airspaceMapDataMapper.deleteAirspaceMapDataByAirspaceId(id);
        //删除格子障碍物表数据
        mapDataObstacleMapper.deleteByAirspaceId(id);
        //删除机型关联表
        aircraftAirspaceMapper.deleteAircraftAirspaceByAirspaceId(id);

        //更新缓存
        mapDataService.setAirspaceCache(deptCodeList);
        if (longLatDeleted > 0) {
            return airspaceMapper.deleteAirspaceById(id);
        } else {
            throw new ServiceException("删除经纬度数据失败");
        }

    }
}
