package com.ruoyi.system.service.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.AircraftMp;
import com.ruoyi.system.domain.mdmp.FlightAlerts;
import com.ruoyi.system.domain.mdmp.FlightData;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
public interface IFlightAlertsService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public FlightAlerts selectFlightAlertsById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param flightAlerts 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<FlightAlerts> selectFlightAlertsList(FlightAlerts flightAlerts);

    /**
     * 新增【请填写功能名称】
     *
     * @param flightAlerts 【请填写功能名称】
     * @return 结果
     */
    public int insertFlightAlerts(FlightAlerts flightAlerts);

    /**
     * 修改【请填写功能名称】
     *
     * @param flightAlerts 【请填写功能名称】
     * @return 结果
     */
    public int updateFlightAlerts(FlightAlerts flightAlerts);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteFlightAlertsByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteFlightAlertsById(Long id);

    List<FlightAlerts> getTopographicMapAlerts(WebsocketFlightData flightData);

    List<FlightAlerts> getObstaclesAlerts(WebsocketFlightData flightData);

    List<FlightAlerts> getControlZoneAlerts(WebsocketFlightData flightData);

    List<FlightAlerts> getNoFlightPlanAlerts(WebsocketFlightData flightData,Long maxVersion);

// 根据WebsocketFlightData和maxVersion获取FlightAlerts列表
    List<FlightAlerts> getFlightAlerts(WebsocketFlightData flightData, Long maxVersion);

    List<FlightAlerts> getUAndVAlerts(long time, AircraftMp aircraftMp,WebsocketFlightData flightData,Long maxVersion);

    List<FlightAlerts> getRainfallAlerts(long time, AircraftMp aircraftMp, WebsocketFlightData flightData,Long maxVersion);

    List<FlightAlerts> getVisibilityAlerts(long time, AircraftMp aircraftMp, WebsocketFlightData flightData,Long maxVersion);

    List<FlightAlerts> getTemperatureAlerts(long time, AircraftMp aircraftMp, WebsocketFlightData flightData,Long maxVersion);

    List<FlightAlerts> getThunderstormAlerts(long time, AircraftMp aircraftMp, WebsocketFlightData flightData,Long maxVersion);

    List<FlightAlerts> getUnregisteredAlerts(WebsocketFlightData flightData,Long maxVersion);

    List<FlightAlerts> getAlerts(WebsocketFlightData flightData,Long maxVersion);

    List<FlightAlerts> getOldAlerts(WebsocketFlightData flightData,Long maxVersion);

    void markAlertsAsSent(List<FlightAlerts> oldAlerts);

    List<FlightAlerts> getOldAlerts2(WebsocketFlightData flightData, Long maxVersion);
}
