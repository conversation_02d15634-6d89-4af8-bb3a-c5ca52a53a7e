package com.ruoyi.system.service.mdmp.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.mdmp.RVisinforesMapper;
import com.ruoyi.system.domain.mdmp.RVisinfores;
import com.ruoyi.system.service.mdmp.IRVisinforesService;

/**
 * 能见度Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class RVisinforesServiceImpl implements IRVisinforesService
{
    @Autowired
    private RVisinforesMapper rVisinforesMapper;

    /**
     * 查询能见度
     *
     * @param id 能见度主键
     * @return 能见度
     */
    @Override
    public RVisinfores selectRVisinforesById(Long id)
    {
        return rVisinforesMapper.selectRVisinforesById(id);
    }

    /**
     * 查询能见度列表
     *
     * @param rVisinfores 能见度
     * @return 能见度
     */
    @Override
    public List<RVisinfores> selectRVisinforesList(RVisinfores rVisinfores)
    {
        return rVisinforesMapper.selectRVisinforesList(rVisinfores);
    }

    /**
     * 新增能见度
     *
     * @param rVisinfores 能见度
     * @return 结果
     */
    @Override
    public int insertRVisinfores(RVisinfores rVisinfores)
    {
        return rVisinforesMapper.insertRVisinfores(rVisinfores);
    }

    /**
     * 修改能见度
     *
     * @param rVisinfores 能见度
     * @return 结果
     */
    @Override
    public int updateRVisinfores(RVisinfores rVisinfores)
    {
        return rVisinforesMapper.updateRVisinfores(rVisinfores);
    }

    /**
     * 批量删除能见度
     *
     * @param ids 需要删除的能见度主键
     * @return 结果
     */
    @Override
    public int deleteRVisinforesByIds(Long[] ids)
    {
        return rVisinforesMapper.deleteRVisinforesByIds(ids);
    }

    /**
     * 删除能见度信息
     *
     * @param id 能见度主键
     * @return 结果
     */
    @Override
    public int deleteRVisinforesById(Long id)
    {
        return rVisinforesMapper.deleteRVisinforesById(id);
    }
}
