package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.system.domain.mdmp.AirspaceMapData;
import com.ruoyi.system.mapper.mdmp.AirspaceMapDataMapper;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.service.mdmp.IAirspaceMapDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Service
public class AirspaceMapDataServiceImpl implements IAirspaceMapDataService {
    @Autowired
    private AirspaceMapDataMapper airspaceMapDataMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public AirspaceMapData selectAirspaceMapDataById(Long id) {
        return airspaceMapDataMapper.selectAirspaceMapDataById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param airspaceMapData 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<AirspaceMapData> selectAirspaceMapDataList(AirspaceMapData airspaceMapData) {
        return airspaceMapDataMapper.selectAirspaceMapDataList(airspaceMapData);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param airspaceMapData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertAirspaceMapData(AirspaceMapData airspaceMapData) {
        return airspaceMapDataMapper.insertAirspaceMapData(airspaceMapData);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param airspaceMapData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateAirspaceMapData(AirspaceMapData airspaceMapData) {
        return airspaceMapDataMapper.updateAirspaceMapData(airspaceMapData);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteAirspaceMapDataByIds(Long[] ids) {
        return airspaceMapDataMapper.deleteAirspaceMapDataByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteAirspaceMapDataById(Long id) {
        return airspaceMapDataMapper.deleteAirspaceMapDataById(id);
    }

    @Override
    public int selectAirspaceMapData(MeteInfoParam meteInfoParam) {
        AirspaceMapData airspaceMapData = airspaceMapDataMapper.selectAirspaceMapData(meteInfoParam);
        if (null != airspaceMapData)
            return airspaceMapData.getValue();
        return 0;
    }
}
