package com.ruoyi.system.service.mdmp.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.mdmp.RWindinforesMapper;
import com.ruoyi.system.domain.mdmp.RWindinfores;
import com.ruoyi.system.service.mdmp.IRWindinforesService;

/**
 * 风Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class RWindinforesServiceImpl implements IRWindinforesService
{
    @Autowired
    private RWindinforesMapper rWindinforesMapper;

    /**
     * 查询风
     *
     * @param id 风主键
     * @return 风
     */
    @Override
    public RWindinfores selectRWindinforesById(Long id)
    {
        return rWindinforesMapper.selectRWindinforesById(id);
    }

    /**
     * 查询风列表
     *
     * @param rWindinfores 风
     * @return 风
     */
    @Override
    public List<RWindinfores> selectRWindinforesList(RWindinfores rWindinfores)
    {
        return rWindinforesMapper.selectRWindinforesList(rWindinfores);
    }

    /**
     * 新增风
     *
     * @param rWindinfores 风
     * @return 结果
     */
    @Override
    public int insertRWindinfores(RWindinfores rWindinfores)
    {
        return rWindinforesMapper.insertRWindinfores(rWindinfores);
    }

    /**
     * 修改风
     *
     * @param rWindinfores 风
     * @return 结果
     */
    @Override
    public int updateRWindinfores(RWindinfores rWindinfores)
    {
        return rWindinforesMapper.updateRWindinfores(rWindinfores);
    }

    /**
     * 批量删除风
     *
     * @param ids 需要删除的风主键
     * @return 结果
     */
    @Override
    public int deleteRWindinforesByIds(Long[] ids)
    {
        return rWindinforesMapper.deleteRWindinforesByIds(ids);
    }

    /**
     * 删除风信息
     *
     * @param id 风主键
     * @return 结果
     */
    @Override
    public int deleteRWindinforesById(Long id)
    {
        return rWindinforesMapper.deleteRWindinforesById(id);
    }
}
