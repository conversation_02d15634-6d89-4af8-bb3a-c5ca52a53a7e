package com.ruoyi.system.service.mdmp.impl;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.FlightWarn;
import com.ruoyi.system.mapper.mdmp.FlightWarnMapper;
import com.ruoyi.system.service.mdmp.FlightWarnService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.List;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class FlightWarnServiceImpl implements FlightWarnService {

    private final FlightWarnMapper flightWarnMapper;

    @Override
    public CommonResult<List<FlightWarn>> todayFlightWarnList() {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        List<FlightWarn> flightWarns = flightWarnMapper.selectByWarnDate(DateUtils.getDate(),deptCodeList);
        if (CollectionUtils.isEmpty(flightWarns)) {
            return CommonResult.success(new ArrayList<>());
        }
        return CommonResult.success(flightWarns);
    }
}
