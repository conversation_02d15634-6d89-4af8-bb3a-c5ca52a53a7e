package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.system.domain.mdmp.AircraftMp;
import com.ruoyi.system.mapper.mdmp.AircraftMpMapper;
import com.ruoyi.system.service.mdmp.IAircraftMpService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class AircraftMpServiceImpl implements IAircraftMpService
{
    @Autowired
    private AircraftMpMapper aircraftMpMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public AircraftMp selectAircraftMpById(Long id)
    {
        return aircraftMpMapper.selectAircraftMpById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param aircraftMp 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<AircraftMp> selectAircraftMpList(AircraftMp aircraftMp)
    {
        return aircraftMpMapper.selectAircraftMpList(aircraftMp);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param aircraftMp 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertAircraftMp(AircraftMp aircraftMp)
    {
        return aircraftMpMapper.insertAircraftMp(aircraftMp);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param aircraftMp 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateAircraftMp(AircraftMp aircraftMp)
    {
        return aircraftMpMapper.updateAircraftMp(aircraftMp);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteAircraftMpByIds(Long[] ids)
    {
        return aircraftMpMapper.deleteAircraftMpByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteAircraftMpById(Long id)
    {
        return aircraftMpMapper.deleteAircraftMpById(id);
    }
}
