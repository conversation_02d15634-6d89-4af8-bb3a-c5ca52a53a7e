package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RRunwayrvrres;

/**
 * 跑道信息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface IRRunwayrvrresService
{
    /**
     * 查询跑道信息
     *
     * @param id 跑道信息主键
     * @return 跑道信息
     */
    public RRunwayrvrres selectRRunwayrvrresById(Long id);

    /**
     * 查询跑道信息列表
     *
     * @param rRunwayrvrres 跑道信息
     * @return 跑道信息集合
     */
    public List<RRunwayrvrres> selectRRunwayrvrresList(RRunwayrvrres rRunwayrvrres);

    /**
     * 新增跑道信息
     *
     * @param rRunwayrvrres 跑道信息
     * @return 结果
     */
    public int insertRRunwayrvrres(RRunwayrvrres rRunwayrvrres);

    /**
     * 修改跑道信息
     *
     * @param rRunwayrvrres 跑道信息
     * @return 结果
     */
    public int updateRRunwayrvrres(RRunwayrvrres rRunwayrvrres);

    /**
     * 批量删除跑道信息
     *
     * @param ids 需要删除的跑道信息主键集合
     * @return 结果
     */
    public int deleteRRunwayrvrresByIds(Long[] ids);

    /**
     * 删除跑道信息信息
     *
     * @param id 跑道信息主键
     * @return 结果
     */
    public int deleteRRunwayrvrresById(Long id);

    void insertRRunwayrvrresList(List<RRunwayrvrres> runwayRvrResDtoList);
}
