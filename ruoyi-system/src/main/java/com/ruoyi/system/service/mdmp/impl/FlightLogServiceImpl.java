package com.ruoyi.system.service.mdmp.impl;

import com.github.pagehelper.page.PageMethod;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.mdmp.EnterLeavePort;
import com.ruoyi.system.domain.mdmp.Flight;
import com.ruoyi.system.domain.mdmp.FlightLog;
import com.ruoyi.system.domain.mdmp.dto.QueryFlightLogListDTO;
import com.ruoyi.system.domain.mdmp.vo.FlightLogVO;
import com.ruoyi.system.mapper.mdmp.EnterLeavePortMapper;
import com.ruoyi.system.mapper.mdmp.FlightLogMapper;
import com.ruoyi.system.mapper.mdmp.FlightMapper;
import com.ruoyi.system.service.mdmp.FlightLogService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class FlightLogServiceImpl implements FlightLogService {

    private final FlightLogMapper flightLogMapper;
    private final FlightMapper flightMapper;
    private final EnterLeavePortMapper enterLeavePortMapper;

    @Override
    public CommonResult<List<FlightLog>> queryFlightLog(Integer flightId) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        List<FlightLog> flightLogs = flightLogMapper.queryByFlightId(flightId,deptCodeList);
        return CommonResult.success(flightLogs);
    }

    @Override
    public List<FlightLogVO> list(QueryFlightLogListDTO dto) {
        FlightLog flightLog = new FlightLog();
        flightLog.setCreateTime(dto.getCreateTime());
        PageMethod.startPage(dto.getPageNum(), dto.getPageSize());
        List<FlightLog> flightLogs = flightLogMapper.selectFlightLog(dto);
        List<FlightLogVO> voList = new ArrayList<>();
        for (FlightLog log : flightLogs) {
            Flight flight = flightMapper.queryById(log.getFlightId());
            EnterLeavePort enterLeavePort = enterLeavePortMapper.selectByFlightId(log.getFlightId());
            FlightLogVO vo = FlightLogVO.setting(log, flight, enterLeavePort);
            voList.add(vo);
        }
        return voList;
    }
}
