package com.ruoyi.system.service.mdmp.impl;

import java.util.ArrayList;
import java.util.List;

import com.ruoyi.system.domain.mdmp.RainfallMpData;
import com.ruoyi.system.domain.mdmp.TemperatureMpData;
import com.ruoyi.system.domain.mdmp.VisibilityMpData;
import com.ruoyi.system.mapper.mdmp.VisibilityMpDataMapper;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;
import com.ruoyi.system.service.mdmp.IVisibilityMpDataService;
import com.ruoyi.system.util.MeteUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class VisibilityMpDataServiceImpl implements IVisibilityMpDataService {
    @Autowired
    private VisibilityMpDataMapper visibilityMpDataMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public VisibilityMpData selectVisibilityMpDataById(Long id) {
        return visibilityMpDataMapper.selectVisibilityMpDataById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param visibilityMpData 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<VisibilityMpData> selectVisibilityMpDataList(VisibilityMpData visibilityMpData) {
        return visibilityMpDataMapper.selectVisibilityMpDataList(visibilityMpData);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param visibilityMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertVisibilityMpData(VisibilityMpData visibilityMpData) {
        return visibilityMpDataMapper.insertVisibilityMpData(visibilityMpData);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param visibilityMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateVisibilityMpData(VisibilityMpData visibilityMpData) {
        return visibilityMpDataMapper.updateVisibilityMpData(visibilityMpData);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteVisibilityMpDataByIds(Long[] ids) {
        return visibilityMpDataMapper.deleteVisibilityMpDataByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteVisibilityMpDataById(Long id) {
        return visibilityMpDataMapper.deleteVisibilityMpDataById(id);
    }

    @Override
    public double selectVisibilityMpDataByMeteInfo(MeteInfoParam meteInfoParam) {
//        VisibilityMpData visibilityMpData = visibilityMpDataMapper.selectVisibilityMpDataByMeteInfo(meteInfoParam);
//        if (visibilityMpData != null) {
//            String dataValue = visibilityMpData.getDataValue();
//            return MeteUtil.getDateValue(dataValue, meteInfoParam.getLongitude());
//        }
        return 0;
    }

    @Override
    public List<Double> getList(MeteParam meteParam) {
        List<Double> list = visibilityMpDataMapper.getList(meteParam);
        return list;
    }


}
