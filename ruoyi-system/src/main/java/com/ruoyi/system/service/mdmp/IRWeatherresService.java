package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RWeatherres;

/**
 * 天气现象Service接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface IRWeatherresService
{
    /**
     * 查询天气现象
     *
     * @param id 天气现象主键
     * @return 天气现象
     */
    public RWeatherres selectRWeatherresById(Long id);

    /**
     * 查询天气现象列表
     *
     * @param rWeatherres 天气现象
     * @return 天气现象集合
     */
    public List<RWeatherres> selectRWeatherresList(RWeatherres rWeatherres);

    /**
     * 新增天气现象
     *
     * @param rWeatherres 天气现象
     * @return 结果
     */
    public int insertRWeatherres(RWeatherres rWeatherres);

    /**
     * 修改天气现象
     *
     * @param rWeatherres 天气现象
     * @return 结果
     */
    public int updateRWeatherres(RWeatherres rWeatherres);

    /**
     * 批量删除天气现象
     *
     * @param ids 需要删除的天气现象主键集合
     * @return 结果
     */
    public int deleteRWeatherresByIds(Long[] ids);

    /**
     * 删除天气现象信息
     *
     * @param id 天气现象主键
     * @return 结果
     */
    public int deleteRWeatherresById(Long id);

    void insertRWeatherresList(List<RWeatherres> weatherResDtoList);
}
