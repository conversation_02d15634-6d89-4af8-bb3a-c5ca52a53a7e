package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RainfallMpData;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface IRainfallMpDataService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public RainfallMpData selectRainfallMpDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param rainfallMpData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<RainfallMpData> selectRainfallMpDataList(RainfallMpData rainfallMpData);

    /**
     * 新增【请填写功能名称】
     *
     * @param rainfallMpData 【请填写功能名称】
     * @return 结果
     */
    public int insertRainfallMpData(RainfallMpData rainfallMpData);

    /**
     * 修改【请填写功能名称】
     *
     * @param rainfallMpData 【请填写功能名称】
     * @return 结果
     */
    public int updateRainfallMpData(RainfallMpData rainfallMpData);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteRainfallMpDataByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteRainfallMpDataById(Long id);

    double selectRainfallMpDataByMeteInfo(MeteInfoParam meteInfoParam);

    List<Double> getList(MeteParam meteParam);
}
