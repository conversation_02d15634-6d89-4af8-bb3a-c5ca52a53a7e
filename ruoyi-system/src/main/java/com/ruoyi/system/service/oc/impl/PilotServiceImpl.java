package com.ruoyi.system.service.oc.impl;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.runcontrol.AircrewRecordType;
import com.ruoyi.common.utils.ArithmeticUtils;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.PdfConverUtils;
import com.ruoyi.common.utils.file.FileUtils;
import com.ruoyi.system.domain.oc.WxQualification;
import com.ruoyi.system.domain.oc.WxUser;
import com.ruoyi.system.domain.oc.dto.AircrewRecordDTO;
import com.ruoyi.system.domain.oc.dto.PilotDetailDTO;
import com.ruoyi.system.domain.oc.dto.PilotQualificationDTO;
import com.ruoyi.system.domain.oc.dto.QueryPilotQualificationDTO;
import com.ruoyi.system.domain.oc.entity.AircrewRecord;
import com.ruoyi.system.domain.oc.entity.Flightplan;
import com.ruoyi.system.domain.oc.vo.*;
import com.ruoyi.system.mapper.oc.*;
import com.ruoyi.system.service.oc.PilotService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.nio.file.Files;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class PilotServiceImpl implements PilotService {
    private static final Logger logger = LoggerFactory.getLogger(PilotServiceImpl.class);
    @Resource
    private UserMapper userMapper;
    @Resource
    private WxUserMapper wxUserMapper;
    @Resource
    private WxQualificationMapper wxQualificationMapper;
    @Resource
    private AircrewRecordMapper aircrewRecordMapper;
    @Resource
    private FlightplanMapper flightPlanMapper;
    @Resource
    private RedisCache redisCache;

    @Value("${aircrewRecordFilePath}")
    private String aircrewRecordFilePath;
    @Value("${defaultFilePreviewUrl}")
    private String defaultUrl;
    @Value("${defaultAvatarUrl}")
    private String defaultAvatarUrl;


    @Override
    public List<PilotQualificationVO> getPilotQualification(QueryPilotQualificationDTO dto, String companyCode) {
        dto.setCompanyCode(companyCode);
        List<WxUser> userList = wxUserMapper.selectPilotList(dto);
        List<PilotQualificationVO> voList = new ArrayList<>();
        for (WxUser user : userList) {
            WxQualification wxQualification = wxQualificationMapper.selectByUserId(user.getUserId());
            PilotQualificationVO vo = PilotQualificationVO.setting(user, wxQualification, dto.getRoleKey());
            voList.add(vo);
        }
        return voList;
    }

    @Override
    public int editQualification(PilotQualificationDTO dto) {

        WxQualification wxQualification = new WxQualification();
        BeanUtils.copyProperties(dto, wxQualification);
        WxQualification data = wxQualificationMapper.selectByUserId(dto.getUserId());
        if (Objects.isNull(data)) {
            return wxQualificationMapper.insertWxQualification(wxQualification);
        } else {
            wxQualification.setId(data.getId());
            return wxQualificationMapper.updateWxQualification(wxQualification);
        }
    }

    @Override
    public CommonResult<PilotDetailVO> pilotDetails(PilotDetailDTO dto) {
        WxUser wxUser = wxUserMapper.selectByUserId(dto.getUserId());
        WxQualification wxQualification = wxQualificationMapper.selectByUserId(wxUser.getUserId());
        PilotInfoVO pilotInfo = PilotInfoVO.setting(wxUser, wxQualification);
        List<Flightplan> yearFlightPlans = flightPlanMapper.selectByUserIdAndFlightDateBetween(Long.toString(wxUser.getUserId()), DateUtils.getYearFirstDay(), DateUtils.getDate());
        //年,月,周飞行情况
        PilotFlightSituationVO flightSituation = new PilotFlightSituationVO();
        setFlightSituation(yearFlightPlans, flightSituation, DateUtils.parseDate(DateUtils.getDateWeekAgo()), DateUtils.getNowDate(), "week");
        setFlightSituation(yearFlightPlans, flightSituation, DateUtils.parseDate(DateUtils.getMonthFirstDay()), DateUtils.getNowDate(), "month");
        setFlightSituation(yearFlightPlans, flightSituation, DateUtils.parseDate(DateUtils.getYearFirstDay()), DateUtils.getNowDate(), "year");
        flightSituation.setAddUpTime(ArithmeticUtils.minutesToHour2(wxUser.getFlyTime()));
        flightSituation.setAddUpFlightFrequency(wxUser.getFlyNumber());
        String virtualUrl = getAvatarVirtualUrl(wxUser);
        PilotDetailVO pilotDetailVO = new PilotDetailVO(virtualUrl, pilotInfo, flightSituation);
        return CommonResult.success(pilotDetailVO);
    }

    /**
     * 获取头像地址
     */
    private String getAvatarVirtualUrl(WxUser wxUser) {
        String virtualUrl = "";
        String avatarUrl = wxUser.getAvatar();
        if (StringUtils.hasLength(avatarUrl)) {
            String suffixName = avatarUrl.substring(avatarUrl.indexOf(".") + 1);
            String newFileName = DateUtils.dateTimeStr() + UUID.randomUUID();
            virtualUrl = defaultAvatarUrl + wxUser.getOpenId() + "/" + newFileName + "." + suffixName;
            String index = "avatar:" + wxUser.getOpenId();
            redisCache.setCacheObject(index, avatarUrl);
        }
        return virtualUrl;
    }

    /**
     * 设置飞行员飞行情况
     */
    private static void setFlightSituation(List<Flightplan> yearFlightPlans, PilotFlightSituationVO flightSituation, Date starDate, Date endDate, String type) {
        List<Flightplan> monthFlightPlans = yearFlightPlans.stream()
                .filter(flightPlan -> !flightPlan.getFlightDate().before(starDate) && !flightPlan.getFlightDate().after(endDate))
                .collect(Collectors.toList());
        if (CollectionUtils.isEmpty(monthFlightPlans)) {
            PilotFlightSituationVO.setting(flightSituation, 0, 0, type);
        } else {
            int flightFrequency = 0;
            int minutes = 0;
            //设置飞行情况
            for (Flightplan flightPlan : monthFlightPlans) {
                Integer routeType = flightPlan.getRouteType();
                flightFrequency += flightPlan.getFlightFrequency();
                int slideTime = routeType == 0 ? flightPlan.getSlideTime() : flightPlan.getSlideTime() + flightPlan.getSecondSlideTime();
                minutes += slideTime;
            }
            PilotFlightSituationVO.setting(flightSituation, flightFrequency, minutes, type);
        }
    }

    @Override
    public AjaxResult uploadAircrewRecord(MultipartFile[] files, Long userId, Integer firstFileType, Integer secondFileType, String remark, Long authId) {
        StringBuilder msg = new StringBuilder();
        for (MultipartFile file : files) {
            String realFileName = file.getOriginalFilename();
            if (StringUtils.isEmpty(realFileName)) {
                return AjaxResult.error("上传失败，请检查文件格式是否正确！");
            }
            String fileSuffix = realFileName.substring(realFileName.lastIndexOf(".") + 1).toLowerCase();
            if (!"png".equals(fileSuffix) && !"jpg".equals(fileSuffix) && !"jpeg".equals(fileSuffix) && !"pdf".equals(fileSuffix) && !"doc".equals(fileSuffix) && !"docx".equals(fileSuffix)) {
                return AjaxResult.error("上传失败，请检查文件格式是否正确！");
            }
            //上传文件
            String saveUrl = uploadFile(file, msg, realFileName, fileSuffix);
            //保存记录实体
            if (StringUtils.hasLength(saveUrl)) {
                String createBy = userMapper.selectSysUserByUserId(authId).getUserName();
                AircrewRecord aircrewRecord = AircrewRecord.setting(userId, firstFileType, secondFileType, remark, realFileName, saveUrl, createBy);
                aircrewRecordMapper.insertAircrewRecord(aircrewRecord);
            }
        }
        return AjaxResult.success(msg);
    }

    @Override
    public void downloadAircrewRecord(Long aircrewRecordId, HttpServletResponse response) {
        AircrewRecord aircrewRecord = aircrewRecordMapper.selectById(aircrewRecordId);
        String realFileName = aircrewRecord.getFileName() + "." + aircrewRecord.getFileSuffix();
        response.setContentType(MediaType.APPLICATION_OCTET_STREAM_VALUE);
        try {
            FileUtils.setAttachmentResponseHeader(response, realFileName);
            FileUtils.writeBytes(aircrewRecord.getSaveUrl(), response.getOutputStream());
        } catch (IOException e) {
            logger.error("飞行员记录文件下载失败：[{}]", e.getMessage());
        }
    }

    @Override
    public AjaxResult queryAircrewRecord(Long userId, Integer firstFileType) {
        List<AircrewRecord> aircrewRecords = aircrewRecordMapper.selectByUserIdAndFirstFileType(userId, firstFileType);
        List<AircrewRecordInfoVO> aircrewRecordInfos = AircrewRecordInfoVO.convert(aircrewRecords);
        //返回措施记录
        if (firstFileType == AircrewRecordType.FIRST_FILETYPE_3.getCode()) {
            return AjaxResult.success(aircrewRecordInfos);
        }
        //按文件第二类型分组的飞行员记录
        Map<Integer, List<AircrewRecordInfoVO>> map = aircrewRecordInfos.stream()
                .filter(item -> Objects.nonNull(item.getSecondFileType()))
                .collect(Collectors.groupingBy(AircrewRecordInfoVO::getSecondFileType));
        //返回技术文档
        if (firstFileType == AircrewRecordType.FIRST_FILETYPE_1.getCode()) {
            AircrewTechnicalVO vo = AircrewTechnicalVO.setting(map);
            return AjaxResult.success(vo);
        }
        //返回满足条款要求记录
        if (firstFileType == AircrewRecordType.FIRST_FILETYPE_2.getCode()) {
            AircrewMeetTermsVO vo = AircrewMeetTermsVO.setting(map);
            return AjaxResult.success(vo);
        }
        return AjaxResult.error("不存在此类文件");
    }

    @Override
    public AjaxResult selectAircrewRecord(Long aircrewRecordId) {
        AircrewRecord aircrewRecord = aircrewRecordMapper.selectById(aircrewRecordId);
        if (aircrewRecord == null) {
            return AjaxResult.error("当前飞行员记录不存在");
        }
        return AjaxResult.success(aircrewRecord);
    }

    @Override
    public int updateAircrewRecord(AircrewRecordDTO aircrewRecordDTO, Long authId) {
        AircrewRecord aircrewRecord = new AircrewRecord();
        BeanUtils.copyProperties(aircrewRecordDTO, aircrewRecord);
        String updateBy = userMapper.selectSysUserByUserId(authId).getUserName();
        aircrewRecord.setUpdateBy(updateBy);
        aircrewRecord.setUpdateTime(new Date());
        return aircrewRecordMapper.updateAircrewRecord(aircrewRecord);
    }

    @Override
    public int deleteAircrewRecord(Long aircrewRecordId) {
        return aircrewRecordMapper.deleteById(aircrewRecordId);
    }

    @Override
    public AjaxResult filePreview(Long aircrewRecordId) {
        AircrewRecord aircrewRecord = aircrewRecordMapper.selectById(aircrewRecordId);
        String saveUrl = aircrewRecord.getSaveUrl();
        String fileType = aircrewRecord.getFileSuffix();
        String newFileName = DateUtils.dateTimeStr() + UUID.randomUUID();
        redisCache.setCacheObject(newFileName, saveUrl, 2, TimeUnit.MINUTES);
        String virtualUrl = defaultUrl + fileType + "/" + newFileName;
        AircrewRecordPreviewVO previewVO = AircrewRecordPreviewVO.setting(fileType, virtualUrl);
        return AjaxResult.success(previewVO);
    }

    @Override
    public void getFilePreview(HttpServletResponse response, String fileType, String newFileName) {
        String fileAddress = redisCache.getCacheObject(newFileName);
        // 验证过期就不能获取
        if (StringUtils.hasLength(fileAddress)) {
            response.setContentType("png".equals(fileType) ? MediaType.IMAGE_PNG_VALUE :
                    "jpg".equals(fileType) ? MediaType.IMAGE_JPEG_VALUE :
                            MediaType.APPLICATION_PDF_VALUE);
            File file = new File(fileAddress);
            try (InputStream is = Files.newInputStream(file.toPath()); OutputStream os = response.getOutputStream()) {
                if ("docx".equals(fileType) || "doc".equals(fileType)) {
                    PdfConverUtils.wordToPdfByAspose(is, os);
                } else {
                    IOUtils.copy(is, os);
                }
            } catch (IOException e) {
                logger.error("文件预览异常[{}]", e.getMessage());
            }
        }

    }

    @SuppressWarnings("ResultOfMethodCallIgnored")
    private String uploadFile(MultipartFile file, StringBuilder msg, String realFileName, String fileSuffix) {
        String fileName = DateUtils.dateTimeNow() + "-" + UUID.randomUUID().toString().replace("-", "") + "." + fileSuffix;
        String saveUrl = aircrewRecordFilePath + File.separator + fileName;
        //文件上传
        try {
            File desc = new File(saveUrl);
            if (!desc.getParentFile().exists()) {
                desc.getParentFile().mkdirs();
            }
            file.transferTo(desc.getAbsoluteFile());
        } catch (IOException e) {
            logger.error("飞行员记录文件上传失败：[{}]", e.getMessage());
            msg.append(realFileName).append("上传失败<br/>");
            return null;
        }
        return saveUrl;
    }
}
