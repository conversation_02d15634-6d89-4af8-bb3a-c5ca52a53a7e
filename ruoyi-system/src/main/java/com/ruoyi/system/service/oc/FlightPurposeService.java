package com.ruoyi.system.service.oc;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.oc.FlightPurpose;
import com.ruoyi.system.domain.oc.dto.AddFlightPurposeDTO;
import com.ruoyi.system.domain.oc.dto.QueryFlightPurposeDTO;
import com.ruoyi.system.domain.oc.dto.UpdateFlightPurposeDTO;


import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/24 11:08
 * @mood 功能
 */
public interface FlightPurposeService {
    /**
     * 查询列表
     */
    PageCommonResult<List<FlightPurpose>> selectList(QueryFlightPurposeDTO dto, Integer pageNum, Integer pageSize, String companyCode);
    List<FlightPurpose> queryAll(String companyCode);
    /**
     * 查询一条
     */
    CommonResult<FlightPurpose> selectOneById(Long id);

    /**
     * 新增一条
     */
    CommonResult<String> insertOne(AddFlightPurposeDTO dto, String companyCode);

    /**
     * 修改一条
     */
    CommonResult<String> updateOne(UpdateFlightPurposeDTO dto);

    /**
     * 删除一条
     */
    public int deleteOneById(Long id);

    /**
     * 批量删除
     */
    CommonResult<String> deleteAllByIds(Long[] ids);
}
