package com.ruoyi.system.service.mdmp.impl;

import java.util.ArrayList;
import java.util.List;

import com.ruoyi.system.domain.mdmp.TemperatureMpData;
import com.ruoyi.system.domain.mdmp.ThunderstormMpData;
import com.ruoyi.system.mapper.mdmp.ThunderstormMpDataMapper;
import com.ruoyi.system.param.mdmp.MeteParam;
import com.ruoyi.system.service.mdmp.IThunderstormMpDataService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Service
public class ThunderstormMpDataServiceImpl implements IThunderstormMpDataService {
    @Autowired
    private ThunderstormMpDataMapper thunderstormMpDataMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public ThunderstormMpData selectThunderstormMpDataById(Long id) {
        return thunderstormMpDataMapper.selectThunderstormMpDataById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param thunderstormMpData 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<ThunderstormMpData> selectThunderstormMpDataList(ThunderstormMpData thunderstormMpData) {
        return thunderstormMpDataMapper.selectThunderstormMpDataList(thunderstormMpData);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param thunderstormMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertThunderstormMpData(ThunderstormMpData thunderstormMpData) {
        return thunderstormMpDataMapper.insertThunderstormMpData(thunderstormMpData);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param thunderstormMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateThunderstormMpData(ThunderstormMpData thunderstormMpData) {
        return thunderstormMpDataMapper.updateThunderstormMpData(thunderstormMpData);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteThunderstormMpDataByIds(Long[] ids) {
        return thunderstormMpDataMapper.deleteThunderstormMpDataByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteThunderstormMpDataById(Long id) {
        return thunderstormMpDataMapper.deleteThunderstormMpDataById(id);
    }

    @Override
    public List<Integer> getList(MeteParam meteParam) {
//        List<ThunderstormMpData> temperatureMpDataList = thunderstormMpDataMapper.getList(meteParam);
//        List<Integer> result = new ArrayList<>();
//        for (ThunderstormMpData thunderstormMpData : temperatureMpDataList) {
//            String dataValue = thunderstormMpData.getDataValue();
//            String[] uValues = dataValue.split(",");
//            for (String uValue : uValues) {
//                result.add(Integer.parseInt(uValue));
//            }
//        }

        return null;
    }
}
