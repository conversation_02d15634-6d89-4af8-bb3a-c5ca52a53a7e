package com.ruoyi.system.service.mdmp.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.alibaba.fastjson2.TypeReference;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.dto.AddCommandDTO;
import com.ruoyi.system.domain.mdmp.dto.AddHeightDTO;
import com.ruoyi.system.domain.mdmp.dto.AddTransitPointDTO;
import com.ruoyi.system.domain.mdmp.dto.UpdateEnterLeavePortDTO;
import com.ruoyi.system.mapper.mdmp.*;
import com.ruoyi.system.service.mdmp.EnterLeavePortService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.web.client.RestTemplate;

import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class EnterLeavePortServiceImpl implements EnterLeavePortService {
    private final EnterLeavePortMapper enterLeavePortMapper;
    private final AddHeightMapper addHeightMapper;
    private final AddCommandMapper addCommandMapper;
    private final AddTransitPointMapper addTransitPointMapper;
    private final FlightMapper flightMapper;
    private final FlightLogMapper flightLogMapper;
    private final FlightCommandLogMapper flightCommandLogMapper;
    @Autowired
    @Qualifier("sendCommandJmsTemplate")
    private JmsTemplate sendCommandJmsTemplate;

    public EnterLeavePortServiceImpl(EnterLeavePortMapper enterLeavePortMapper, AddHeightMapper addHeightMapper, AddCommandMapper addCommandMapper, AddTransitPointMapper addTransitPointMapper, FlightMapper flightMapper, FlightLogMapper flightLogMapper, FlightCommandLogMapper flightCommandLogMapper) {
        this.enterLeavePortMapper = enterLeavePortMapper;
        this.addHeightMapper = addHeightMapper;
        this.addCommandMapper = addCommandMapper;
        this.addTransitPointMapper = addTransitPointMapper;
        this.flightMapper = flightMapper;
        this.flightLogMapper = flightLogMapper;
        this.flightCommandLogMapper = flightCommandLogMapper;
    }

    @Override
    public CommonResult<EnterLeavePort> getInfo(Integer flightId) {
        EnterLeavePort enterLeavePort = enterLeavePortMapper.queryByFlightId(flightId);
        return CommonResult.success(enterLeavePort);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> insert(EnterLeavePort enterLeavePort) {
        enterLeavePortMapper.insert(enterLeavePort);
        addHeightMapper.insertBatch(enterLeavePort.getHeightList());
        addCommandMapper.insertBatch(enterLeavePort.getCommandList());
        addTransitPointMapper.insertBatch(enterLeavePort.getPointList());
        return CommonResult.success();
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> update(UpdateEnterLeavePortDTO dto) {
        String msg = "";
        //修改飞行实际起飞到达
        Flight flightData = flightMapper.queryById(dto.getFlightId());
        if (Objects.isNull(flightData)) {
            return CommonResult.error("航班信息有误!");
        }
        if (!Objects.equals(flightData.getActualDepartTime(), dto.getActualDepartTime()) || !Objects.equals(flightData.getActualArriveTime(), dto.getActualArriveTime())) {
            flightData.setActualDepartTime(dto.getActualDepartTime());
            flightData.setActualArriveTime(dto.getActualArriveTime());
            flightMapper.update(flightData);
            msg = msg + "设置航班实际起飞时间：" + (StringUtils.isEmpty(dto.getActualDepartTime()) ? ":" : dto.getActualDepartTime()) + ";";
            msg = msg + "设置航班实际到达时间：" + (StringUtils.isEmpty(dto.getActualArriveTime()) ? ":" : dto.getActualArriveTime()) + ";";
        }
        //修改进离场信息
        EnterLeavePort enterLeavePort = new EnterLeavePort();
        BeanUtils.copyProperties(dto, enterLeavePort);
        EnterLeavePort data = null;
        FlightLog flightLog = null;
        if (Objects.nonNull(dto.getId())) {
            data = enterLeavePortMapper.queryById(dto.getId());
            if (Objects.isNull(data)) {
                return CommonResult.error("当前需修改的进离场信息不存在！");
            }
            //添加操作日志
            flightLog = addLog(data, dto, msg);
            //删除对应的关联数据
            addHeightMapper.deleteByEnterLeaveId(dto.getId());
            addCommandMapper.deleteByEnterLeaveId(dto.getId());
            addTransitPointMapper.deleteByEnterLeaveId(dto.getId());
            enterLeavePortMapper.update(enterLeavePort);
        } else {
            //新增进离场信息前，判断其是否存在，若不存在才可继续新增
            data = enterLeavePortMapper.queryByFlightId(flightData.getId());
            if (Objects.nonNull(data)) {
                return CommonResult.error("请不要重复操作！");
            }
            //
            flightLog = addLog(data, dto, msg);
            enterLeavePortMapper.insert(enterLeavePort);
        }
        //修改高度、命令、经过点
        if (!CollectionUtils.isEmpty(dto.getHeightList())) {
            List<AddHeight> heightList = AddHeight.setting(dto.getHeightList());
            heightList.forEach(addHeight -> addHeight.setEnterLeaveId(enterLeavePort.getId()));
            addHeightMapper.insertBatch(heightList);
        }
        if (!CollectionUtils.isEmpty(dto.getCommandList())) {
            List<AddCommand> commandList = AddCommand.setting(dto.getCommandList());
            commandList.forEach(addCommand -> addCommand.setEnterLeaveId(enterLeavePort.getId()));
            addCommandMapper.insertBatch(commandList);
        }
        if (!CollectionUtils.isEmpty(dto.getPointList())) {
            List<AddTransitPoint> pointList = AddTransitPoint.setting(dto.getPointList());
            pointList.forEach(point -> point.setEnterLeaveId(enterLeavePort.getId()));
            addTransitPointMapper.insertBatch(pointList);
        }
        //航班返航，则为异常航班
        if (enterLeavePort.getTurnBack() == 1) {
            Flight flight = new Flight();
            flight.setId(enterLeavePort.getFlightId());
            flight.setFlightStatus(2);
            flight.setProgress("完成");
            flightMapper.update(flight);
        }
        //添加电子行程单的航班指令消息
        if (dto.getSendFlag()) {
            insertFlightCommandLog(flightLog, flightData);
        }
        return CommonResult.success();
    }

    /**添加电子行程单的航班指令消息*/
    private void insertFlightCommandLog(FlightLog flightLog, Flight flightData) {
        if (Objects.nonNull(flightLog)) {
            FlightCommandLog flightCommandLog = new FlightCommandLog();
            flightCommandLog.setFlightId(flightData.getId());
            flightCommandLog.setCommandLog(flightLog.getLogMessage());
            flightCommandLog.setCallSign(flightData.getCallSign());
            flightCommandLog.setCreateDate(DateUtils.getDate());
            String time = DateUtils.getTime();
            flightCommandLog.setCreateTime(time);
            flightCommandLog.setUpdateTime(time);
            flightCommandLog.setDelivered("已送达");
            flightCommandLogMapper.insert(flightCommandLog);
            //发送给无人机系统
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("commandId", flightCommandLog.getId());
            jsonObject.put("commandLog", "("+flightCommandLog.getCallSign()+")"+flightCommandLog.getCommandLog());
            jsonObject.put("createDate", flightCommandLog.getCreateDate());
            jsonObject.put("createTime", flightCommandLog.getCreateTime());
            sendCommandJmsTemplate.convertAndSend("Command", JSON.toJSONString(jsonObject));
            log.info("({}){}", flightCommandLog.getCallSign(), flightCommandLog.getCommandLog());
        }
    }

    /**
     * 添加操作日志
     */
    private FlightLog addLog(EnterLeavePort data, UpdateEnterLeavePortDTO dto, String msg) {
        //数据库中的高度、命令、点信息
        Map<Integer, AddHeight> heightMap = new ConcurrentHashMap<>();
        Map<Integer, AddCommand> commandMap = new ConcurrentHashMap<>();
        Map<Integer, AddTransitPoint> pointMap = new ConcurrentHashMap<>();
        List<AddHeight> heightList = Objects.isNull(data) ? new ArrayList<>() : data.getHeightList();
        List<AddCommand> commandList = Objects.isNull(data) ? new ArrayList<>() : data.getCommandList();
        List<AddTransitPoint> pointList = Objects.isNull(data) ? new ArrayList<>() : data.getPointList();
        heightList.forEach(height -> heightMap.put(height.getId(), height));
        commandList.forEach(command -> commandMap.put(command.getId(), command));
        pointList.forEach(point -> pointMap.put(point.getId(), point));
        //要修改的高度、命令、点信息
        List<AddHeightDTO> heightDTOS = dto.getHeightList();
        List<AddCommandDTO> commandDTOS = dto.getCommandList();
        List<AddTransitPointDTO> pointDTOS = dto.getPointList();
        //比对原数据并添加操作日志
        msg = heightLog(heightDTOS, heightMap, msg);
        msg = commandLog(commandDTOS, commandMap, msg);
        msg = pointLog(pointDTOS, pointMap, msg);
        msg = enterLeavePortLog(data, dto, msg);
        if (StringUtils.isNotEmpty(msg)) {
            String callSign = flightMapper.queryById(dto.getFlightId()).getCallSign();
            FlightLog flightLog = new FlightLog().setFlightId(dto.getFlightId())
                    .setCallSign(callSign).setLogMessage(msg).setCreateTime(DateUtils.getTime());
            flightLogMapper.insert(flightLog);
            return flightLog;
        }
        return null;
    }

    /**
     * 添加进离场信息日志
     */
    private String enterLeavePortLog(EnterLeavePort data, UpdateEnterLeavePortDTO dto, String msg) {
        if (!StringUtils.isEmpty(dto.getRunway()) && (data == null || !Objects.equals(dto.getRunway(), data.getRunway()))) {
            msg = msg + "使用跑道为" + dto.getRunway() + ";";
        }
        if (!StringUtils.isEmpty(dto.getTaxiInstruction()) && (data == null || !Objects.equals(dto.getTaxiInstruction(), data.getTaxiInstruction()))) {
            msg = msg + "滑行指令为" + dto.getTaxiInstruction() + ";";
        }
        if (!StringUtils.isEmpty(dto.getDirection()) && (data == null || !Objects.equals(dto.getDirection(), data.getDirection()))) {
            msg = msg + "方向为" + dto.getDirection() + ";";
        }
        if (!StringUtils.isEmpty(dto.getPushTime()) && (data == null || !Objects.equals(dto.getPushTime(), data.getPushTime()))) {
            msg = msg + "推出时间为" + dto.getPushTime() + ";";
        }
        if (!StringUtils.isEmpty(dto.getStartUpTime()) && (data == null || !Objects.equals(dto.getStartUpTime(), data.getStartUpTime()))) {
            msg = msg + "开车时间为" + dto.getStartUpTime() + ";";
        }
        if ((data != null && !Objects.equals(dto.getVip(), data.getVip())) || (data == null && dto.getVip() == 1)) {
            msg = msg + (dto.getVip() == 1 ? "" : "取消") + "重要客人飞行;";
        }
        if ((data != null && !Objects.equals(dto.getAirConflicts(), data.getAirConflicts())) || (data == null && dto.getAirConflicts() == 1)) {
            msg = msg + (dto.getAirConflicts() == 1 ? "" : "取消") + "空中潜在冲突;";
        }
        if ((data != null && !Objects.equals(dto.getAlternate(), data.getAlternate())) || (data == null && dto.getAlternate() == 1)) {
            msg = msg + (dto.getAlternate() == 1 ? "" : "取消") + "备降;";
        }
        if ((data != null && !Objects.equals(dto.getTurnBack(), data.getTurnBack())) || (data == null && dto.getTurnBack() == 1)) {
            msg = msg + (dto.getTurnBack() == 1 ? "" : "取消") + "返航;";
        }
        if ((data != null && !Objects.equals(dto.getReceivedMessage(), data.getReceivedMessage())) || (data == null && dto.getReceivedMessage() == 1)) {
            msg = msg + (dto.getReceivedMessage() == 1 ? "" : "取消") + "收到ATIS信息;";
        }
        if ((data != null && !Objects.equals(dto.getEstReport(), data.getEstReport())) || (data == null && dto.getEstReport() == 1)) {
            msg = msg + (dto.getEstReport() == 1 ? "" : "取消") + "过境航班拍发EST报;";
        }
        if ((data != null && !Objects.equals(dto.getAirForceCoordination(), data.getAirForceCoordination())) || (data == null && dto.getAirForceCoordination() == 1)) {
            msg = msg + (dto.getAirForceCoordination() == 1 ? "" : "取消") + "与空军协调完毕;";
        }
        if (!StringUtils.isEmpty(dto.getControllerName()) && (data == null || !Objects.equals(dto.getControllerName(), data.getControllerName()))) {
            msg = msg + "管制员为" + dto.getControllerName() + ";";
        }
        if (!StringUtils.isEmpty(dto.getFlyDate()) && (data == null || !Objects.equals(dto.getFlyDate(), data.getFlyDate()))) {
            msg = msg + "飞行日期为" + dto.getFlyDate() + ";";
        }
        if (!StringUtils.isEmpty(dto.getParkingGate()) && (data == null || !Objects.equals(dto.getParkingGate(), data.getParkingGate()))) {
            msg = msg + "停机位为" + dto.getParkingGate() + ";";
        }
        return msg;
    }

    /**
     * 添加经过点信息日志
     */
    private String pointLog(List<AddTransitPointDTO> pointDTOS, Map<Integer, AddTransitPoint> pointMap, String msg) {
        for (AddTransitPointDTO param : pointDTOS) {
            //如果数据库中没有添加过数据则不用比对直接新增
            if (pointMap.isEmpty()) {
                msg = msg + "新增经过点：" + param.getPositionName() + "，预计到达：" + param.getPredictArriveTime() + "，实际到达：" + param.getActualArriveTime() + ";";
            } else {
                //经过点ID为空直接新增
                if (Objects.isNull(param.getId())) {
                    msg = msg + "新增经过点：" + param.getPositionName() + "，预计到达：" + param.getPredictArriveTime() + "，实际到达：" + param.getActualArriveTime() + ";";
                }
            }
            for (Integer id : pointMap.keySet()) {
                if (Objects.nonNull(param.getId())) {
                    //经过点的修改
                    if (id.equals(param.getId())) {
                        AddTransitPoint point = pointMap.get(id);
                        if (!Objects.equals(point.getPositionName(), param.getPositionName()) ||
                                !Objects.equals(point.getPredictArriveTime(), param.getPredictArriveTime()) || !Objects.equals(point.getActualArriveTime(), param.getActualArriveTime())) {
                            msg = msg + "修改经过点;"
                                    + "历史值：" + point.getPositionName() + "，预计到达：" + point.getPredictArriveTime() + "，实际到达：" + point.getActualArriveTime() + ";"
                                    + "当前值：" + param.getPositionName() + "，预计到达：" + param.getPredictArriveTime() + "，实际到达：" + param.getActualArriveTime() + ";";
                        }
                        pointMap.remove(id);
                    }
                }
            }
        }
        //经过点的删除
        for (Integer id : pointMap.keySet()) {
            AddTransitPoint point = pointMap.get(id);
            msg = msg + "删除经过点：" + "点名称：" + point.getPositionName() + "，预计到达：" + point.getPredictArriveTime() + "，实际到达：" + point.getActualArriveTime() + ";";
        }
        return msg;
    }

    /**
     * 添加指令信息日志
     */
    private String commandLog(List<AddCommandDTO> commandDTOS, Map<Integer, AddCommand> commandMap, String msg) {
        for (AddCommandDTO param : commandDTOS) {
            //如果数据库中没有添加过数据则不用比对直接新增
            if (commandMap.isEmpty()) {
                msg = msg + "新增指令：" + param.getCommand() + (Objects.isNull(param.getCommandValue()) ? "" : param.getCommandValue()) + ";";
            } else {
                //指令ID为空直接新增
                if (Objects.isNull(param.getId())) {
                    msg = msg + "新增指令：" + param.getCommand() + (Objects.isNull(param.getCommandValue()) ? "" : param.getCommandValue()) + ";";
                }
            }
            for (Integer id : commandMap.keySet()) {
                if (Objects.nonNull(param.getId())) {
                    //指令的修改
                    if (id.equals(param.getId())) {
                        AddCommand command = commandMap.get(id);
                        if (!Objects.equals(command.getCommand(), param.getCommand()) || !Objects.equals(command.getCommandValue(), param.getCommandValue())) {
                            msg = msg + "修改指令：" + command.getCommand() + (Objects.isNull(command.getCommandValue()) ? "" : command.getCommandValue())
                                    + "修改为" +
                                    param.getCommand() + (Objects.isNull(param.getCommandValue()) ? "" : param.getCommandValue()) + ";";
                        }
                        commandMap.remove(id);
                    }
                }
            }

        }
        //指令的删除
        for (Integer id : commandMap.keySet()) {
            AddCommand command = commandMap.get(id);
            msg = msg + "删除指令：" + command.getCommand() + (Objects.isNull(command.getCommandValue()) ? "" : command.getCommandValue()) + ";";
        }
        return msg;
    }

    /**
     * 添加高度信息日志
     */
    private String heightLog(List<AddHeightDTO> heightDTOS, Map<Integer, AddHeight> heightMap, String msg) {
        //高度信息日志
        for (AddHeightDTO param : heightDTOS) {
            //如果数据库中没有添加过数据则不用比对直接新增
            if (heightMap.isEmpty()) {
                msg = msg + "新增高度：" + (param.getHeightType() == 0 ? "下降" : "上升") + "-" + param.getAttribute() + param.getHeight() + "-" + (param.getKeep() == 0 ? "高度不保持" : "高度保持") + ";";
            } else {
                //高度ID为空直接新增
                if (Objects.isNull(param.getId())) {
                    msg = msg + "新增高度：" + (param.getHeightType() == 0 ? "下降" : "上升") + "-" + param.getAttribute() + param.getHeight() + "-" + (param.getKeep() == 0 ? "高度不保持" : "高度保持") + ";";
                }
            }
            for (Integer id : heightMap.keySet()) {
                if (Objects.nonNull(param.getId())) {
                    //高度指令的修改
                    if (id.equals(param.getId())) {
                        AddHeight height = heightMap.get(id);
                        if (!Objects.equals(height.getHeightType(), param.getHeightType()) || !Objects.equals(height.getHeight(), param.getHeight())
                                || !Objects.equals(height.getKeep(), param.getKeep()) || !Objects.equals(height.getAttribute(), param.getAttribute())) {
                            msg = msg + "修改高度;"
                                    + "历史值：" + (height.getHeightType() == 0 ? "下降" : "上升") + "-" + height.getAttribute() + height.getHeight() + "-" + (height.getKeep() == 0 ? "高度不保持" : "高度保持") + ";"
                                    + "当前值：" + (param.getHeightType() == 0 ? "下降" : "上升") + "-" + param.getAttribute() + param.getHeight() + "-" + (param.getKeep() == 0 ? "高度不保持" : "高度保持") + ";";
                        }
                        heightMap.remove(id);
                    }
                }
            }
        }
        //高度指令的删除
        for (Integer id : heightMap.keySet()) {
            AddHeight height = heightMap.get(id);
            msg = msg + "删除高度：" + (height.getHeightType() == 0 ? "下降" : "上升") + "-" + height.getAttribute() + height.getHeight() + "-" + (height.getKeep() == 0 ? "高度不保持" : "高度保持") + ";";
        }
        return msg;
    }
}
