package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.system.domain.mdmp.MpData;
import com.ruoyi.system.domain.mdmp.VMpData;
import com.ruoyi.system.mapper.mdmp.VMpDataMapper;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;
import com.ruoyi.system.service.mdmp.IVMpDataService;
import com.ruoyi.system.util.MeteUtil;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

/**
 * 【请填写功能名称】Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
@Service
public class VMpDataServiceImpl implements IVMpDataService {

    @Resource
    private VMpDataMapper vMpDataMapper;

    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    @Override
    public VMpData selectVMpDataById(Long id) {
        return vMpDataMapper.selectVMpDataById(id);
    }

    /**
     * 查询【请填写功能名称】列表
     *
     * @param vMpData 【请填写功能名称】
     * @return 【请填写功能名称】
     */
    @Override
    public List<VMpData> selectVMpDataList(VMpData vMpData) {
        return vMpDataMapper.selectVMpDataList(vMpData);
    }

    /**
     * 新增【请填写功能名称】
     *
     * @param vMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int insertVMpData(VMpData vMpData) {
        return vMpDataMapper.insertVMpData(vMpData);
    }

    /**
     * 修改【请填写功能名称】
     *
     * @param vMpData 【请填写功能名称】
     * @return 结果
     */
    @Override
    public int updateVMpData(VMpData vMpData) {
        return vMpDataMapper.updateVMpData(vMpData);
    }

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteVMpDataByIds(Long[] ids) {
        return vMpDataMapper.deleteVMpDataByIds(ids);
    }

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    @Override
    public int deleteVMpDataById(Long id) {
        return vMpDataMapper.deleteVMpDataById(id);
    }

    @Override
    public double selectVMpDataByMeteInfo(MeteInfoParam meteInfoParam) {
//        VMpData vMpData = vMpDataMapper.selectVMpDataByMeteInfo(meteInfoParam);
//        if (vMpData != null) {
//            String dataValue = vMpData.getDataValue();
//            return MeteUtil.getDateValue(dataValue, meteInfoParam.getLongitude());
//        }
        return 0;
    }

    @Override
    public List<VMpData> getList(MeteParam meteParam) {
        return vMpDataMapper.getList(meteParam);
    }
}
