package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RCloudinfores;

/**
 * 云Service接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface IRCloudinforesService
{
    /**
     * 查询云
     *
     * @param id 云主键
     * @return 云
     */
    public RCloudinfores selectRCloudinforesById(Long id);

    /**
     * 查询云列表
     *
     * @param rCloudinfores 云
     * @return 云集合
     */
    public List<RCloudinfores> selectRCloudinforesList(RCloudinfores rCloudinfores);

    /**
     * 新增云
     *
     * @param rCloudinfores 云
     * @return 结果
     */
    public int insertRCloudinfores(RCloudinfores rCloudinfores);

    /**
     * 修改云
     *
     * @param rCloudinfores 云
     * @return 结果
     */
    public int updateRCloudinfores(RCloudinfores rCloudinfores);

    /**
     * 批量删除云
     *
     * @param ids 需要删除的云主键集合
     * @return 结果
     */
    public int deleteRCloudinforesByIds(Long[] ids);

    /**
     * 删除云信息
     *
     * @param id 云主键
     * @return 结果
     */
    public int deleteRCloudinforesById(Long id);

    void insertRCloudinforesList(List<RCloudinfores> cloudResDtoList);
}
