package com.ruoyi.system.service.oc;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.oc.FileDir;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

/**
 * 文件目录Service接口
 * 
 * <AUTHOR>
 * @date 2021-12-23
 */
public interface IFileDirService 
{
    /**
     * 查询文件目录
     * 
     * @param fileDirId 文件目录主键
     * @return 文件目录
     */
    public FileDir selectFileDirByFileDirId(Long fileDirId);

    /**
     * 查询文件目录列表
     * 
     * @param fileDir 文件目录
     * @return 文件目录集合
     */
    public List<FileDir> selectFileDirList(FileDir fileDir);

    /**
     * 新增文件目录
     * 
     * @param fileDir 文件目录
     * @return 结果
     */
    public int insertFileDir(FileDir fileDir);

    /**
     * 修改文件目录
     * 
     * @param fileDir 文件目录
     * @return 结果
     */
    public int updateFileDir(FileDir fileDir);

    /**
     * 批量删除文件目录
     * 
     * @param fileDirIds 需要删除的文件目录主键集合
     * @return 结果
     */
    public int deleteFileDirByFileDirIds(Long[] fileDirIds);

    /**
     * 删除文件目录信息
     * 
     * @param fileDirId 文件目录主键
     * @return 结果
     */
    public int deleteFileDirByFileDirId(Long fileDirId);


    public AjaxResult importFile(MultipartFile file, Long fileDirId);
}
