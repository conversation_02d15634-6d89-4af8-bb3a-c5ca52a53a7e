package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RDecodereport;
import com.ruoyi.system.domain.mdmp.vo.DecodeReportVo;

/**
 * 报文信息Service接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface IRDecodereportService
{
    /**
     * 查询报文信息
     *
     * @param id 报文信息主键
     * @return 报文信息
     */
    public RDecodereport selectRDecodereportById(Long id);

    /**
     * 查询报文信息列表
     *
     * @param rDecodereport 报文信息
     * @return 报文信息集合
     */
    public List<RDecodereport> selectRDecodereportList(RDecodereport rDecodereport);

    /**
     * 新增报文信息
     *
     * @param rDecodereport 报文信息
     * @return 结果
     */
    public int insertRDecodereport(RDecodereport rDecodereport);

    /**
     * 修改报文信息
     *
     * @param rDecodereport 报文信息
     * @return 结果
     */
    public int updateRDecodereport(RDecodereport rDecodereport);

    /**
     * 批量删除报文信息
     *
     * @param ids 需要删除的报文信息主键集合
     * @return 结果
     */
    public int deleteRDecodereportByIds(Long[] ids);

    /**
     * 删除报文信息信息
     *
     * @param id 报文信息主键
     * @return 结果
     */
    public int deleteRDecodereportById(Long id);

    List<DecodeReportVo> selectRDecodereportList();

    List<String> selectNewContentList();
}
