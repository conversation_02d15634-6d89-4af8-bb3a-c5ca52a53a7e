package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.system.domain.mdmp.AirspaceLongLat;
import com.ruoyi.system.mapper.mdmp.AirspaceLongLatMapper;
import com.ruoyi.system.service.mdmp.IAirspaceLongLatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;


/**
 * 空域经纬度Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-04-01
 */
@Service
public class AirspaceLongLatServiceImpl implements IAirspaceLongLatService
{
    @Autowired
    private AirspaceLongLatMapper airspaceLongLatMapper;

    /**
     * 查询空域经纬度
     * 
     * @param id 空域经纬度主键
     * @return 空域经纬度
     */
    @Override
    public AirspaceLongLat selectAirspaceLongLatById(Long id)
    {
        return airspaceLongLatMapper.selectAirspaceLongLatById(id);
    }

    /**
     * 查询空域经纬度列表
     * 
     * @param airspaceLongLat 空域经纬度
     * @return 空域经纬度
     */
    @Override
    public List<AirspaceLongLat> selectAirspaceLongLatList(AirspaceLongLat airspaceLongLat)
    {
        return airspaceLongLatMapper.selectAirspaceLongLatList(airspaceLongLat);
    }

    /**
     * 新增空域经纬度
     * 
     * @param airspaceLongLat 空域经纬度
     * @return 结果
     */
    @Override
    public int insertAirspaceLongLat(AirspaceLongLat airspaceLongLat)
    {
        return airspaceLongLatMapper.insertAirspaceLongLat(airspaceLongLat);
    }

    /**
     * 修改空域经纬度
     * 
     * @param airspaceLongLat 空域经纬度
     * @return 结果
     */
    @Override
    public int updateAirspaceLongLat(AirspaceLongLat airspaceLongLat)
    {
        return airspaceLongLatMapper.updateAirspaceLongLat(airspaceLongLat);
    }

    /**
     * 批量删除空域经纬度
     * 
     * @param ids 需要删除的空域经纬度主键
     * @return 结果
     */
    @Override
    public int deleteAirspaceLongLatByIds(Long[] ids)
    {
        return airspaceLongLatMapper.deleteAirspaceLongLatByIds(ids);
    }

    /**
     * 删除空域经纬度信息
     * 
     * @param id 空域经纬度主键
     * @return 结果
     */
    @Override
    public int deleteAirspaceLongLatById(Long id)
    {
        return airspaceLongLatMapper.deleteAirspaceLongLatById(id);
    }
}
