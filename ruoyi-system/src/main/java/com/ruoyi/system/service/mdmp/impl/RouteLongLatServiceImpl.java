package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.system.domain.mdmp.RouteLongLat;
import com.ruoyi.system.mapper.mdmp.RouteLongLatMapper;
import com.ruoyi.system.service.mdmp.IRouteLongLatService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 航线经纬度Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-03-29
 */
@Service
public class RouteLongLatServiceImpl implements IRouteLongLatService
{
    @Autowired
    private RouteLongLatMapper routeLongLatMapper;

    /**
     * 查询航线经纬度
     * 
     * @param id 航线经纬度主键
     * @return 航线经纬度
     */
    @Override
    public RouteLongLat selectRouteLongLatById(Long id)
    {
        return routeLongLatMapper.selectRouteLongLatById(id);
    }

    /**
     * 查询航线经纬度列表
     * 
     * @param routeLongLat 航线经纬度
     * @return 航线经纬度
     */
    @Override
    public List<RouteLongLat> selectRouteLongLatList(RouteLongLat routeLongLat)
    {
        return routeLongLatMapper.selectRouteLongLatList(routeLongLat);
    }

    /**
     * 新增航线经纬度
     * 
     * @param routeLongLat 航线经纬度
     * @return 结果
     */
    @Override
    public int insertRouteLongLat(RouteLongLat routeLongLat)
    {
        return routeLongLatMapper.insertRouteLongLat(routeLongLat);
    }

    /**
     * 修改航线经纬度
     * 
     * @param routeLongLat 航线经纬度
     * @return 结果
     */
    @Override
    public int updateRouteLongLat(RouteLongLat routeLongLat)
    {
        return routeLongLatMapper.updateRouteLongLat(routeLongLat);
    }

    /**
     * 批量删除航线经纬度
     * 
     * @param ids 需要删除的航线经纬度主键
     * @return 结果
     */
    @Override
    public int deleteRouteLongLatByIds(Long[] ids)
    {
        return routeLongLatMapper.deleteRouteLongLatByIds(ids);
    }

    /**
     * 删除航线经纬度信息
     * 
     * @param id 航线经纬度主键
     * @return 结果
     */
    @Override
    public int deleteRouteLongLatById(Long id)
    {
        return routeLongLatMapper.deleteRouteLongLatById(id);
    }
}
