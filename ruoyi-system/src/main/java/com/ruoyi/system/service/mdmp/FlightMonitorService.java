package com.ruoyi.system.service.mdmp;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.mdmp.Airspace;
import com.ruoyi.system.domain.mdmp.FlightData;
import com.ruoyi.system.domain.mdmp.FlightPlanAircraftModel;
import com.ruoyi.system.domain.mdmp.vo.*;
import com.ruoyi.system.param.mdmp.MonitorAirspaceParams;
import com.ruoyi.system.param.mdmp.MonitorHistoryFlightDataParams;
import com.ruoyi.system.param.mdmp.MonitorRouteParams;

import java.util.List;

/**
 * 航班监控接口
 *
 * <AUTHOR>
 */
public interface FlightMonitorService {

    /**
     * 当日有效空域集合
     *
     * @return 结果
     */
    PageCommonResult<List<AirspaceVO>> airspaceList();

    /**
     * 当日航线集合
     *
     * @return 结果
     */
    PageCommonResult<List<RouteVO>> routeList();

    /**
     * 当日在飞航空器集合
     *
     * @return 结果
     */
    PageCommonResult<List<AircraftVO>> aircraftList();

    /**
     * 当日航班计划集合
     *
     * @return 结果
     */
    PageCommonResult<List<DailyFlightPlanVO>> flightPanList();

    /**
     * 监控空域
     * @param airspaceParams 空域Ids
     * @return 空域集合
     */
    PageCommonResult<List<Airspace>> queryAirspace(MonitorAirspaceParams airspaceParams);

    /**
     * 监控计划航线
     * @param routeParams 航线Ids
     * @return 航线集合
     */
    CommonResult<RouteCoordinateVO> queryPlanRoute(MonitorRouteParams routeParams);

    /**
     * 监控历史飞行数据
     * @param historyDataParams 请求参数
     * @return 结果
     */
    CommonResult<List<FlightData>> historyData(MonitorHistoryFlightDataParams historyDataParams);

    CommonResult<List<FlightPlanAircraftModel>> queryAircraft(String flightDate);
}
