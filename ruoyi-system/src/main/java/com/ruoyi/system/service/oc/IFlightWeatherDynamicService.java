package com.ruoyi.system.service.oc;

import com.ruoyi.system.domain.oc.vo.FlightWeatherDynamicVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 *
 * <AUTHOR>
 */
public interface IFlightWeatherDynamicService  {

    void exportWeatherDynamicRecordWord(HttpServletResponse response, Integer flightTaskBookId, String companyCode);

    /**
     * 导出气象信息记录页PDF文档
     *
     * @param response 响应对象
     * @param flightTaskBookId 飞行任务书ID
     * @param companyCode 公司代码
     */
    void exportWeatherDynamicRecordPdf(HttpServletResponse response, Integer flightTaskBookId, String companyCode);

    /**
     * 批量导出气象信息记录页PDF文档并压缩
     *
     * @param response 响应对象
     * @param flightTaskBookIds 飞行任务书ID列表
     * @param companyCode 公司代码
     */
    void exportWeatherDynamicRecordPdfBatch(HttpServletResponse response, List<Integer> flightTaskBookIds, String companyCode);

    FlightWeatherDynamicVo selectFlightWeatherDynamicInfoById(Long flightTaskBookId, String companyCode);
}
