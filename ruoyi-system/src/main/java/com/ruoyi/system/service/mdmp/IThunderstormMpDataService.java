package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.ThunderstormMpData;
import com.ruoyi.system.param.mdmp.MeteParam;

/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface IThunderstormMpDataService
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ThunderstormMpData selectThunderstormMpDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param thunderstormMpData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ThunderstormMpData> selectThunderstormMpDataList(ThunderstormMpData thunderstormMpData);

    /**
     * 新增【请填写功能名称】
     *
     * @param thunderstormMpData 【请填写功能名称】
     * @return 结果
     */
    public int insertThunderstormMpData(ThunderstormMpData thunderstormMpData);

    /**
     * 修改【请填写功能名称】
     *
     * @param thunderstormMpData 【请填写功能名称】
     * @return 结果
     */
    public int updateThunderstormMpData(ThunderstormMpData thunderstormMpData);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的【请填写功能名称】主键集合
     * @return 结果
     */
    public int deleteThunderstormMpDataByIds(Long[] ids);

    /**
     * 删除【请填写功能名称】信息
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteThunderstormMpDataById(Long id);

    List<Integer> getList(MeteParam meteParam);
}
