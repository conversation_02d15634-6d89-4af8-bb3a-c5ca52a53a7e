package com.ruoyi.system.service.oc.impl;

import com.ruoyi.system.domain.oc.entity.MaintenanceRecord;
import com.ruoyi.system.domain.oc.entity.OcAircraft;
import com.ruoyi.system.domain.oc.entity.RefuelingRecord;
import com.ruoyi.system.mapper.oc.MaintenanceRecordMapper;
import com.ruoyi.system.mapper.oc.OcAircraftMapper;
import com.ruoyi.system.mapper.oc.RefuelingRecordMapper;
import com.ruoyi.system.service.oc.IOcAircraftService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 飞行器信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-09-22
 */
@Service
public class OcAircraftServiceImpl implements IOcAircraftService {
    @Resource
    private OcAircraftMapper ocAircraftMapper;

    @Resource
    private RefuelingRecordMapper refuelingRecordMapper;

    @Resource
    private MaintenanceRecordMapper maintenanceRecordMapper;



    /**
     * 查询飞行器信息列表
     *
     * @param ocAircraft 飞行器信息
     * @return 飞行器信息
     */
    @Override
    public List<OcAircraft> selectAircraftList(OcAircraft ocAircraft, String companyCode) {

        // 航班计划
        ocAircraft.setCompanyCode(companyCode);
        List<OcAircraft> aircraftList = ocAircraftMapper.selectAircraftList(ocAircraft);
        for (OcAircraft f : aircraftList) {
            RefuelingRecord refuelingRecord = new RefuelingRecord();
            refuelingRecord.setAircraftId(f.getAircraftId());
            List<RefuelingRecord> refuelingRecords = refuelingRecordMapper.selectRefuelingRecordList(refuelingRecord);
            f.setRefuelingRecords(refuelingRecords);
            if (!refuelingRecords.isEmpty()) {
                f.setRefuelingTimes(refuelingRecords.size());
                Long totalOilVolume = Long.valueOf(0);
                for (RefuelingRecord refuelingRecord1 : refuelingRecords) {
                    totalOilVolume = totalOilVolume + (refuelingRecord1.getOilQuantity() == null ? Long.valueOf(0) : refuelingRecord1.getOilQuantity());
                }
                f.setTotalOilVolume(totalOilVolume);
            } else {
                f.setRefuelingTimes(0);
                f.setTotalOilVolume(Long.valueOf(0));
            }
            MaintenanceRecord maintenanceRecord = new MaintenanceRecord();
            maintenanceRecord.setAircraftId(f.getAircraftId());
            List<MaintenanceRecord> maintenanceRecords = maintenanceRecordMapper.selectMaintenanceRecordList(maintenanceRecord);
            f.setMaintenanceRecords(maintenanceRecords);
        }
        return aircraftList;
    }

}
