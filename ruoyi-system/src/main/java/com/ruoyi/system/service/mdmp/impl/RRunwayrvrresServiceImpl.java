package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.mdmp.RRunwayrvrresMapper;
import com.ruoyi.system.domain.mdmp.RRunwayrvrres;
import com.ruoyi.system.service.mdmp.IRRunwayrvrresService;

/**
 * 跑道信息Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Service
public class RRunwayrvrresServiceImpl implements IRRunwayrvrresService {
    @Autowired
    private RRunwayrvrresMapper rRunwayrvrresMapper;

    /**
     * 查询跑道信息
     *
     * @param id 跑道信息主键
     * @return 跑道信息
     */
    @Override
    public RRunwayrvrres selectRRunwayrvrresById(Long id) {
        return rRunwayrvrresMapper.selectRRunwayrvrresById(id);
    }

    /**
     * 查询跑道信息列表
     *
     * @param rRunwayrvrres 跑道信息
     * @return 跑道信息
     */
    @Override
    public List<RRunwayrvrres> selectRRunwayrvrresList(RRunwayrvrres rRunwayrvrres) {
        return rRunwayrvrresMapper.selectRRunwayrvrresList(rRunwayrvrres);
    }

    /**
     * 新增跑道信息
     *
     * @param rRunwayrvrres 跑道信息
     * @return 结果
     */
    @Override
    public int insertRRunwayrvrres(RRunwayrvrres rRunwayrvrres) {
        return rRunwayrvrresMapper.insertRRunwayrvrres(rRunwayrvrres);
    }

    /**
     * 修改跑道信息
     *
     * @param rRunwayrvrres 跑道信息
     * @return 结果
     */
    @Override
    public int updateRRunwayrvrres(RRunwayrvrres rRunwayrvrres) {
        return rRunwayrvrresMapper.updateRRunwayrvrres(rRunwayrvrres);
    }

    /**
     * 批量删除跑道信息
     *
     * @param ids 需要删除的跑道信息主键
     * @return 结果
     */
    @Override
    public int deleteRRunwayrvrresByIds(Long[] ids) {
        return rRunwayrvrresMapper.deleteRRunwayrvrresByIds(ids);
    }

    /**
     * 删除跑道信息信息
     *
     * @param id 跑道信息主键
     * @return 结果
     */
    @Override
    public int deleteRRunwayrvrresById(Long id) {
        return rRunwayrvrresMapper.deleteRRunwayrvrresById(id);
    }

    @Override
    public void insertRRunwayrvrresList(List<RRunwayrvrres> runwayRvrResDtoList) {
        if (runwayRvrResDtoList != null && runwayRvrResDtoList.size() > 0) {
            rRunwayrvrresMapper.insertRRunwayrvrresList(runwayRvrResDtoList);
        }

    }
}
