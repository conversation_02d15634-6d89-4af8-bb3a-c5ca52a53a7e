package com.ruoyi.system.service.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.WorkBasisLongLat;

/**
 * 作业区经纬度基础信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface IWorkBasisLongLatService 
{
    /**
     * 查询作业区经纬度基础信息
     * 
     * @param id 作业区经纬度基础信息主键
     * @return 作业区经纬度基础信息
     */
    public WorkBasisLongLat selectWorkBasisLongLatById(Long id);

    /**
     * 查询作业区经纬度基础信息列表
     * 
     * @param workBasisLongLat 作业区经纬度基础信息
     * @return 作业区经纬度基础信息集合
     */
    public List<WorkBasisLongLat> selectWorkBasisLongLatList(WorkBasisLongLat workBasisLongLat);

    /**
     * 新增作业区经纬度基础信息
     * 
     * @param workBasisLongLat 作业区经纬度基础信息
     * @return 结果
     */
    public int insertWorkBasisLongLat(WorkBasisLongLat workBasisLongLat);

    /**
     * 修改作业区经纬度基础信息
     * 
     * @param workBasisLongLat 作业区经纬度基础信息
     * @return 结果
     */
    public int updateWorkBasisLongLat(WorkBasisLongLat workBasisLongLat);

    /**
     * 批量删除作业区经纬度基础信息
     * 
     * @param ids 需要删除的作业区经纬度基础信息主键集合
     * @return 结果
     */
    public int deleteWorkBasisLongLatByIds(Long[] ids);

    /**
     * 删除作业区经纬度基础信息信息
     * 
     * @param id 作业区经纬度基础信息主键
     * @return 结果
     */
    public int deleteWorkBasisLongLatById(Long id);
}
