package com.ruoyi.system.service.mdmp.impl;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import com.ruoyi.system.mapper.mdmp.MapDataMapper;
import com.ruoyi.system.mapper.mdmp.MapDataObstacleMapper;
import com.ruoyi.system.service.mdmp.IAirspaceService;
import com.ruoyi.system.service.mdmp.IMapDataObstacleService;
import com.ruoyi.system.util.CircularUtil;
import com.ruoyi.system.util.MapDataFilter;
import com.ruoyi.system.util.Point3D;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;


/**
 * 长期飞行计划Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Service
public class MapDataObstacleServiceImpl implements IMapDataObstacleService {

    @Resource
    private MapDataMapper mapDataMapper;

    @Resource
    private MapDataObstacleMapper mapDataObstacleMapper;



    public static void main(String[] args) {
        MapData mapData = new MapData();
        mapData.setIndex(0);
        mapData.setLongitudeStart(BigDecimal.valueOf(84.80));
        mapData.setLongitudeEnd(BigDecimal.valueOf(84.81));
        mapData.setLatitudeStart(BigDecimal.valueOf(43.70));
        mapData.setLatitudeEnd(BigDecimal.valueOf(43.71));
        mapData.setPressureStart(950);
        mapData.setPressureEnd(950);
        mapData.setLayer(1);
        mapData.setHeightStart(354);
        mapData.setHeightEnd(750);


        //生成10*10*10
        BigDecimal pressure = BigDecimal.valueOf(mapData.getPressureStart());

        BigDecimal heightAdd = (BigDecimal.valueOf(mapData.getHeightEnd()).subtract(BigDecimal.valueOf(mapData.getHeightStart()))).divide(BigDecimal.valueOf(10));
        BigDecimal latitudeAdd = BigDecimal.valueOf(0.001);
        BigDecimal longitudeAdd = BigDecimal.valueOf(0.001);

        System.out.println(heightAdd);
        System.out.println(latitudeAdd);
        System.out.println(longitudeAdd);

        List<MapDataObstacle> mapDataList = new ArrayList<>();
        Integer time = 0;
        for (int i = 0; i < 10; i++) {
            BigDecimal heightStart = BigDecimal.valueOf(mapData.getHeightStart())
                    .add(heightAdd.multiply(BigDecimal.valueOf(i)));
            BigDecimal heightEnd = BigDecimal.valueOf(mapData.getHeightStart())
                    .add(heightAdd.multiply(BigDecimal.valueOf(i + 1)));
            for (int j = 0; j < 10; j++) {
                BigDecimal latStart = mapData.getLatitudeStart()
                        .add(latitudeAdd.multiply(BigDecimal.valueOf(j)));
                BigDecimal latEnd = mapData.getLatitudeStart()
                        .add(latitudeAdd.multiply(BigDecimal.valueOf(j + 1)));
                for (int k = 0; k < 10; k++) {
                    BigDecimal lonStart = mapData.getLongitudeStart()
                            .add(longitudeAdd.multiply(BigDecimal.valueOf(k)));
                    BigDecimal lonEnd = mapData.getLongitudeStart()
                            .add(longitudeAdd.multiply(BigDecimal.valueOf(k + 1)));

                    MapDataObstacle mapDataObstacle = new MapDataObstacle();
                    mapDataObstacle.setHeightStart(heightStart);
                    mapDataObstacle.setHeightEnd(heightEnd);
                    mapDataObstacle.setPressureStart(pressure);
                    mapDataObstacle.setPressureEnd(pressure);
                    mapDataObstacle.setLayer(i + 1);
                    mapDataObstacle.setIndex(time);
                    mapDataObstacle.setLongitudeStart(lonStart);
                    mapDataObstacle.setLongitudeEnd(lonEnd);
                    mapDataObstacle.setLatitudeStart(latStart);
                    mapDataObstacle.setLatitudeEnd(latEnd);
                    mapDataObstacle.setLongitudeIncrease(BigDecimal.valueOf(0.001));
                    mapDataObstacle.setLatitudeIncrease(BigDecimal.valueOf(0.001));
                    time++;
                    if (heightStart.compareTo(BigDecimal.valueOf(500)) < 0) {
                        mapDataObstacle.setStatusCode(55);
                    } else {
                        mapDataObstacle.setStatusCode(0);
                    }
                    mapDataList.add(mapDataObstacle);
                }
            }
        }

        System.out.println(mapDataList);
        System.out.println(mapDataList.size());

    }

    @Override
    public void saveObstacle(Airspace airspace, List<AirspaceMapData> airspaceMapDataList, AirspaceLongLat airspaceLongLat) {


        if (airspaceMapDataList != null && airspaceMapDataList.size() > 0) {
            for (AirspaceMapData airspaceMapData : airspaceMapDataList) {
                MapData mapData = mapDataMapper.selectMapDataByIndex(airspaceMapData.getIndex());
                if (mapData == null) {
                    throw new ServiceException("未找到对应的MapData");
                }
                //生成10*10*10
                BigDecimal pressure = BigDecimal.valueOf(mapData.getPressureStart());
                BigDecimal heightAdd = (BigDecimal.valueOf(mapData.getHeightEnd()).subtract(BigDecimal.valueOf(mapData.getHeightStart()))).divide(BigDecimal.valueOf(10));
                BigDecimal latitudeAdd = BigDecimal.valueOf(0.001);
                BigDecimal longitudeAdd = BigDecimal.valueOf(0.001);
                System.out.println(heightAdd);
                System.out.println(latitudeAdd);
                System.out.println(longitudeAdd);
                List<MapDataObstacle> mapDataList = new ArrayList<>();
                Integer time = 0;
                for (int i = 0; i < 10; i++) {
                    BigDecimal heightStart = BigDecimal.valueOf(mapData.getHeightStart())
                            .add(heightAdd.multiply(BigDecimal.valueOf(i)));
                    BigDecimal heightEnd = BigDecimal.valueOf(mapData.getHeightStart())
                            .add(heightAdd.multiply(BigDecimal.valueOf(i + 1)));
                    for (int j = 0; j < 10; j++) {
                        BigDecimal latStart = mapData.getLatitudeStart()
                                .add(latitudeAdd.multiply(BigDecimal.valueOf(j)));
                        BigDecimal latEnd = mapData.getLatitudeStart()
                                .add(latitudeAdd.multiply(BigDecimal.valueOf(j + 1)));
                        for (int k = 0; k < 10; k++) {
                            BigDecimal lonStart = mapData.getLongitudeStart()
                                    .add(longitudeAdd.multiply(BigDecimal.valueOf(k)));
                            BigDecimal lonEnd = mapData.getLongitudeStart()
                                    .add(longitudeAdd.multiply(BigDecimal.valueOf(k + 1)));

                            MapDataObstacle mapDataObstacle = new MapDataObstacle();
                            mapDataObstacle.setHeightStart(heightStart);
                            mapDataObstacle.setHeightEnd(heightEnd);
                            mapDataObstacle.setPressureStart(pressure);
                            mapDataObstacle.setPressureEnd(pressure);
                            mapDataObstacle.setLayer(i + 1);
                            mapDataObstacle.setIndex(time);
                            mapDataObstacle.setLongitudeStart(lonStart);
                            mapDataObstacle.setLongitudeEnd(lonEnd);
                            mapDataObstacle.setLatitudeStart(latStart);
                            mapDataObstacle.setAirspaceId(airspace.getId());
                            mapDataObstacle.setAirspaceMapDataId(airspaceMapData.getId());
                            mapDataObstacle.setMapDataIndex(airspaceMapData.getIndex());
                            mapDataObstacle.setLatitudeEnd(latEnd);
                            mapDataObstacle.setLongitudeIncrease(BigDecimal.valueOf(0.001));
                            mapDataObstacle.setLatitudeIncrease(BigDecimal.valueOf(0.001));

                            BigDecimal terminationHeight = BigDecimal.valueOf(airspace.getTerminationHeight());
                            // 方案1：严格区间判断
                            boolean isFullyBelow = heightStart.compareTo(terminationHeight) < 0
                                    && heightEnd.compareTo(terminationHeight) <= 0;
//                            mapDataObstacle.setStatusCode(isFullyBelow ? 55 : 0);

                            if (Objects.equals(airspace.getRadius(), new BigDecimal(0))) {
                                //判断点是否在小格子里面
                                Point3D point3D = new Point3D(airspaceLongLat.getDoubleLongitude(), airspaceLongLat.getDoubleLatitude(), terminationHeight.doubleValue());
                                MapData mapData1 = new MapData();
                                BeanUtils.copyProperties(mapDataObstacle, mapData1);
                                boolean flag = MapDataFilter.isPointInside(point3D, mapData1);
                                if (flag && isFullyBelow) {
                                    mapDataObstacle.setStatusCode(55);
                                } else {
                                    mapDataObstacle.setStatusCode(0);
                                }
                            } else {
                                //判断圆心到小格子的距离是否在半径内
                                // 四个对比点
                                double[][] others = {
                                        {mapDataObstacle.getLongitudeStart().doubleValue(), mapDataObstacle.getLatitudeStart().doubleValue()},
                                        {mapDataObstacle.getLongitudeEnd().doubleValue(), mapDataObstacle.getLatitudeStart().doubleValue()},  //
                                        {mapDataObstacle.getLongitudeEnd().doubleValue(), mapDataObstacle.getLatitudeEnd().doubleValue()},  //
                                        {mapDataObstacle.getLongitudeStart().doubleValue(), mapDataObstacle.getLatitudeEnd().doubleValue()}   //
                                };

                                boolean d = CircularUtil.isNearbyAnyPoint(airspaceLongLat.getDoubleLongitude(), airspaceLongLat.getDoubleLatitude(), others, airspace.getRadius().doubleValue());
                                if (d && isFullyBelow) {
                                    System.out.println("=============================");
                                    mapDataObstacle.setStatusCode(55);
                                } else {
                                    mapDataObstacle.setStatusCode(0);
                                }
                            }


                            time++;
                            mapDataList.add(mapDataObstacle);
                        }
                    }
                }
                List<MapDataObstacle> mapDataObstacles = mapDataList.stream().filter(e -> e.getStatusCode().equals(55)).collect(Collectors.toList());
                System.out.println(mapDataObstacles);
                int row = mapDataObstacleMapper.insertMapDataObstacle(mapDataList);
                if (row == 0) {
                    throw new ServiceException("保存障碍物地图数据失败");
                }
            }


        }
    }

    @Override
    public Integer selectObstacleByIndex(Long airspaceId, Integer index, WebsocketFlightData flightData) {
        return mapDataObstacleMapper.selectObstacleByIndex(airspaceId, index, flightData);
    }
}
