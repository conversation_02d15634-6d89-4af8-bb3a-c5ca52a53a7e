package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.param.mdmp.SingleFlightTaskListParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.ruoyi.system.mapper.mdmp.SingleFlightTaskMapper;
import com.ruoyi.system.domain.mdmp.SingleFlightTask;
import com.ruoyi.system.service.mdmp.ISingleFlightTaskService;

import javax.annotation.Resource;

/**
 * 单一飞行任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Service
public class SingleFlightTaskServiceImpl implements ISingleFlightTaskService {
    @Resource
    private SingleFlightTaskMapper singleFlightTaskMapper;

    @Resource
    private LongTermFlightPlanServiceImpl longTermFlightPlanService;

    /**
     * 查询单一飞行任务
     *
     * @param id 单一飞行任务主键
     * @return 单一飞行任务
     */
    @Override
    public SingleFlightTask selectSingleFlightTaskById(Long id) {
        return singleFlightTaskMapper.selectSingleFlightTaskById(id);
    }

    /**
     * 查询单一飞行任务列表
     *
     * @param singleFlightTask 单一飞行任务
     * @return 单一飞行任务
     */
    @Override
    public List<SingleFlightTask> selectSingleFlightTaskList(SingleFlightTaskListParam param) {
        return singleFlightTaskMapper.selectSingleFlightTaskList(param);
    }

    /**
     * 新增单一飞行任务
     *
     * @param singleFlightTask 单一飞行任务
     * @return 结果
     */
    @Override
    public int insertSingleFlightTask(SingleFlightTask singleFlightTask) {
        singleFlightTask.setPlanType(4);
        int row = singleFlightTaskMapper.insertSingleFlightTask(singleFlightTask);
        /**
         * 新增关联实体
         *
         */
        longTermFlightPlanService.addAssociatedEntitiesLongTermFlightPlan(4, singleFlightTask.getId(),
                singleFlightTask.getPlanModelAircraftList(),
                null,
                singleFlightTask.getFlightPlanRouteList(),
                null,
                null);

        return row;
    }

    /**
     * 修改单一飞行任务
     *
     * @param singleFlightTask 单一飞行任务
     * @return 结果
     */
    @Override
    public int updateSingleFlightTask(SingleFlightTask singleFlightTask) {


        SingleFlightTask singleFlightTask1 = singleFlightTaskMapper.selectSingleFlightTaskById(singleFlightTask.getId());
        if (singleFlightTask1 == null) {
            throw new ServiceException("该任务不存在");
        }

        //删除原有关联表
        longTermFlightPlanService.deleteExistingAssociated(singleFlightTask.getId(), 4);
        //更新航班计划
        int count = singleFlightTaskMapper.updateSingleFlightTask(singleFlightTask);
        //新增关联实体
        longTermFlightPlanService.addAssociatedEntitiesLongTermFlightPlan(4, singleFlightTask.getId(),
                singleFlightTask.getPlanModelAircraftList(),
                null,
                singleFlightTask.getFlightPlanRouteList(),
                null,
                null);
        return count;

    }

    /**
     * 批量删除单一飞行任务
     *
     * @param ids 需要删除的单一飞行任务主键
     * @return 结果
     */
    @Override
    public int deleteSingleFlightTaskByIds(Long[] ids) {
        return singleFlightTaskMapper.deleteSingleFlightTaskByIds(ids);
    }

    /**
     * 删除单一飞行任务信息
     *
     * @param id 单一飞行任务主键
     * @return 结果
     */
    @Override
    public int deleteSingleFlightTaskById(Long id) {
        return singleFlightTaskMapper.deleteSingleFlightTaskById(id);
    }
}
