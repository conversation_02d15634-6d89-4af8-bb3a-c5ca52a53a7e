package com.ruoyi.system.service.oc.impl;

import com.github.pagehelper.PageInfo;
import com.github.pagehelper.page.PageMethod;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;

import com.ruoyi.system.domain.oc.FlightPurpose;
import com.ruoyi.system.domain.oc.dto.AddFlightPurposeDTO;
import com.ruoyi.system.domain.oc.dto.QueryFlightPurposeDTO;
import com.ruoyi.system.domain.oc.dto.UpdateFlightPurposeDTO;
import com.ruoyi.system.mapper.oc.FlightPurposeMapper;
import com.ruoyi.system.service.oc.FlightPurposeService;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/24 11:10
 * @mood 功能
 */
@Service
public class FlightPurposeServiceImpl implements FlightPurposeService {
    @Resource
    private FlightPurposeMapper flightPurposeMapper;

    /**
     * 查询列表
     */
    @Override
    public PageCommonResult<List<FlightPurpose>> selectList(QueryFlightPurposeDTO dto, Integer pageNum, Integer pageSize, String companyCode) {
        FlightPurpose FlightPurpose = new FlightPurpose();
        BeanUtils.copyProperties(dto, FlightPurpose);
        FlightPurpose.setCompanyCode(companyCode);
        PageMethod.startPage(pageNum, pageSize);
        List<FlightPurpose> flightPurposeList = flightPurposeMapper.selectList(FlightPurpose);
        PageInfo<FlightPurpose> info = new PageInfo<>(flightPurposeList);
        return PageCommonResult.success(flightPurposeList, info.getTotal());
    }

    @Override
    public List<FlightPurpose> queryAll(String companyCode) {
        FlightPurpose flightPurpose = new FlightPurpose();
        flightPurpose.setCompanyCode(companyCode);
        List<FlightPurpose> list = flightPurposeMapper.selectList(flightPurpose);
        return list;
    }

    /**
     * 查询一条
     */
    @Override
    public CommonResult<FlightPurpose> selectOneById(Long id) {
        FlightPurpose flightPurpose = flightPurposeMapper.selectOneById(id);
        return CommonResult.success(flightPurpose);
    }

    /**
     * 新增一条
     */
    @Override
    public CommonResult<String> insertOne(AddFlightPurposeDTO dto, String companyCode) {
        FlightPurpose flightPurpose = new FlightPurpose();
        BeanUtils.copyProperties(dto, flightPurpose);
        flightPurpose.setCompanyCode(companyCode);
        int row = flightPurposeMapper.insertOne(flightPurpose);
        return CommonResult.toResult(row);
    }

    /**
     * 修改一条
     */
    @Override
    public CommonResult<String> updateOne(UpdateFlightPurposeDTO dto) {
        FlightPurpose flightPurpose = new FlightPurpose();
        BeanUtils.copyProperties(dto, flightPurpose);
        int row = flightPurposeMapper.updateOne(flightPurpose);
        return CommonResult.toResult(row);
    }

    /**
     * 删除一条
     */
    @Override
    public int deleteOneById(Long id) {
        return flightPurposeMapper.deleteOneById(id);
    }

    /**
     * 批量删除
     */
    @Override
    public CommonResult<String> deleteAllByIds(Long[] ids) {
        int rows = flightPurposeMapper.deleteAllByIds(ids);
        return CommonResult.toResult(rows);
    }
}

