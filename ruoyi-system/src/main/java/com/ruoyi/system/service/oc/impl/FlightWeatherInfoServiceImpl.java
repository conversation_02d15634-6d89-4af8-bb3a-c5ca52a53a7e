package com.ruoyi.system.service.oc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.oc.FlightTaskBook;
import com.ruoyi.system.domain.oc.FlightTaskInfo;
import com.ruoyi.system.domain.oc.dto.FlightWeatherDto;
import com.ruoyi.system.domain.oc.dto.FlightWeatherInfoDto;
import com.ruoyi.system.domain.oc.entity.FlightWeatherInfo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.mapper.oc.FlightDynamicInfoMapper;
import com.ruoyi.system.mapper.oc.FlightTaskBookMapper;
import com.ruoyi.system.mapper.oc.FlightTaskInfoMapper;
import com.ruoyi.system.mapper.oc.FlightWeatherInfoMapper;
import com.ruoyi.system.service.oc.IFlightDynamicInfoService;
import com.ruoyi.system.service.oc.IFlightWeatherInfoService;
import com.ruoyi.system.util.word.WeatherInfoWordUtils;
import com.ruoyi.system.util.word.WeatherInfoPdfUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FlightWeatherInfoServiceImpl extends ServiceImpl<FlightWeatherInfoMapper, FlightWeatherInfo> implements IFlightWeatherInfoService {
    @Resource
    private FlightWeatherInfoMapper flightWeatherInfoBizMapper;
    @Resource
    private FlightTaskBookMapper flightTaskBookMapper;
    @Resource
    private FlightTaskInfoMapper flightTaskInfoMapper;
    @Resource
    private IFlightWeatherInfoService iFlightWeatherInfoService;
    @Value("${template.weatherTemplatePath}")
    private String weatherTemplate;

    /**
     * 查询气象信息
     */
    @Override
    public FlightWeatherVo selectFlightWeatherInfoById(Long flightTaskBookId, String companyCode) {
        return flightWeatherInfoBizMapper.selectFlightWeatherInfoById(flightTaskBookId, companyCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveFlightWeatherInfo(String companyCode, FlightWeatherDto flightWeatherDto) {
        String taskBookNumber = getFlightTaskBook(companyCode, flightWeatherDto);
        if (StrUtil.isNotEmpty(taskBookNumber)) {
            List<FlightWeatherInfoDto> flightWeatherInfoDtos = flightWeatherDto.getFlightWeatherInfoDtos();
            if (CollUtil.isNotEmpty(flightWeatherInfoDtos)) {
                List<Long> flightTaskInfoIds = flightWeatherInfoDtos.stream().map(FlightWeatherInfoDto::getFlightTaskInfoId).collect(Collectors.toList());
                //先删除旧数据
                iFlightWeatherInfoService.lambdaUpdate().eq(FlightWeatherInfo::getTaskBookNumber, taskBookNumber).in(FlightWeatherInfo::getFlightTaskInfoId, flightTaskInfoIds).remove();
                //封装新增的数据
                saveWeatherInfo(companyCode, flightWeatherInfoDtos, taskBookNumber);
            }
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public void exportWeatherInfoWord(HttpServletResponse response, Integer flightTaskBookId, String companyCode) {
        log.info("开始导出气象信息Word文档，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
        try {
            // 查询气象信息数据
            FlightWeatherVo flightWeatherVo = this.selectFlightWeatherInfoById(flightTaskBookId.longValue(), companyCode);
            if (flightWeatherVo == null) {
                log.error("未找到对应的气象数据，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
                return;
            }
            // 转换为Word导出格式
            WordMeteorologicalVo wordMeteorologicalVo = convertToWeatherWordVo(flightWeatherVo);

            // 生成Word文档
            byte[] docBytes = WeatherInfoWordUtils.generateWeatherInfoDocument(weatherTemplate, wordMeteorologicalVo, null);

            // 设置响应头
            String fileName = generateFileName("气象信息记录表", "docx");
            setWordResponseHeaders(response, fileName);
            // 输出文件到前端
            streamWordDocumentToResponse(response, docBytes);
            log.info("气象信息Word文档导出成功，文件名: {}", fileName);
        } catch (Exception e) {
            log.error("导出气象信息Word文档失败，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode, e);
        }
    }


    @Override
    public void exportWeatherInfoPdf(HttpServletResponse response, Integer flightTaskBookId, String companyCode) {
        log.info("开始导出气象信息PDF文档，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
        try {
            // 查询气象信息数据
            FlightWeatherVo flightWeatherVo = this.selectFlightWeatherInfoById(flightTaskBookId.longValue(), companyCode);
            if (flightWeatherVo == null) {
                log.error("未找到对应的气象数据，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
                return;
            }

            // 转换为Word导出格式
            WordMeteorologicalVo wordMeteorologicalVo = convertToWeatherWordVo(flightWeatherVo);

            // 生成PDF文档
            byte[] pdfBytes = WeatherInfoPdfUtils.generateWeatherInfoPdf(weatherTemplate, wordMeteorologicalVo, null);

            // 设置响应头
            String fileName = generateFileName("气象信息记录表", "pdf");
            setPdfResponseHeaders(response, fileName);

            // 输出文件到前端
            streamPdfDocumentToResponse(response, pdfBytes);

            log.info("气象信息PDF文档导出成功，文件名: {}", fileName);
        } catch (Exception e) {
            log.error("导出气象信息PDF文档失败，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode, e);
        }
    }

    @Override
    public void exportWeatherInfoPdfBatch(HttpServletResponse response, List<Integer> flightTaskBookIds, String companyCode) {
        log.info("开始批量导出气象信息PDF文档，任务数量: {}, 公司代码: {}", flightTaskBookIds.size(), companyCode);
        try {
            // 准备数据列表
            List<WordMeteorologicalVo> dataList = new ArrayList<>();

            for (Integer flightTaskBookId : flightTaskBookIds) {
                try {
                    FlightWeatherVo flightWeatherVo = this.selectFlightWeatherInfoById(flightTaskBookId.longValue(), companyCode);
                    if (flightWeatherVo != null) {
                        WordMeteorologicalVo wordMeteorologicalVo = convertToWeatherWordVo(flightWeatherVo);
                        dataList.add(wordMeteorologicalVo);
                    } else {
                        log.warn("任务书ID: {} 未找到对应的气象数据", flightTaskBookId);
                    }
                } catch (Exception e) {
                    log.error("处理任务书ID: {} 时发生错误", flightTaskBookId, e);
                }
            }

            if (dataList.isEmpty()) {
                log.error("没有找到任何有效的气象数据");
                return;
            }

            // 生成批量PDF压缩包
            byte[] zipBytes = WeatherInfoPdfUtils.generateWeatherInfoPdfBatch(weatherTemplate, dataList, null, "气象信息记录表");

            // 设置响应头
            String fileName = generateFileName("气象信息记录表批量导出", "zip");
            setZipResponseHeaders(response, fileName);

            // 输出文件到前端
            streamZipDocumentToResponse(response, zipBytes);

            log.info("气象信息PDF批量导出成功，文件名: {}, 处理数量: {}", fileName, dataList.size());
        } catch (Exception e) {
            log.error("批量导出气象信息PDF文档失败，公司代码: {}", companyCode, e);
        }
    }

    private void saveWeatherInfo(String companyCode, List<FlightWeatherInfoDto> flightWeatherInfoDtos, String taskBookNumber) {
        List<FlightWeatherInfo> flightWeatherInfos = new ArrayList<>();
        List<FlightTaskInfo> flightTaskInfos = flightTaskInfoMapper.selectByTaskBookNumber(taskBookNumber, companyCode);
        if (CollUtil.isNotEmpty(flightTaskInfos)) {
            // 筛选taskInfoId和批次都匹配的数据
            List<FlightWeatherInfoDto> matchingWeatherDtos = new ArrayList<>();
            for (FlightWeatherInfoDto weatherDto : flightWeatherInfoDtos) {
                Optional<FlightTaskInfo> first = flightTaskInfos.stream()
                        .filter(taskInfo -> weatherDto.getFlightTaskInfoId().equals(taskInfo.getId()) && weatherDto.getBatch().equals(taskInfo.getBatch())).findFirst();
                if (first.isPresent()) {
                    matchingWeatherDtos.add(weatherDto);
                }
            }
            // 更新为匹配后的数据列表
            flightWeatherInfoDtos = matchingWeatherDtos;
        }
        flightWeatherInfoDtos.forEach(dto -> {
            FlightWeatherInfo flightWeatherInfo = new FlightWeatherInfo();
            BeanUtils.copyProperties(dto, flightWeatherInfo);
            flightWeatherInfo.setTaskBookNumber(taskBookNumber);
            flightWeatherInfo.setCompanyCode(companyCode);
            flightWeatherInfos.add(flightWeatherInfo);
        });
        iFlightWeatherInfoService.saveBatch(flightWeatherInfos);
    }

    private String getFlightTaskBook(String companyCode, FlightWeatherDto flightWeatherDto) {
        FlightTaskBook dbTaskBook = flightTaskBookMapper.selectByPrimaryKey(flightWeatherDto.getTaskBookId());
        if (dbTaskBook != null && dbTaskBook.getCompanyCode().equals(companyCode)) {
            return dbTaskBook.getTaskBookNumber();
        }
        return StrUtil.EMPTY;
    }


    /**
     * 转换气象信息为Word导出格式（仅气象信息，不包含动态信息）
     */
    private WordMeteorologicalVo convertToWeatherWordVo(FlightWeatherVo flightWeatherVo) {
        WordMeteorologicalVo wordMeteorologicalVo = new WordMeteorologicalVo();
        BeanUtils.copyProperties(flightWeatherVo, wordMeteorologicalVo);

        List<FlightWeatherInfoVo> flightWeatherInfoVos = flightWeatherVo.getFlightWeatherInfoVos();
        if (flightWeatherInfoVos != null) {
            flightWeatherInfoVos = flightWeatherInfoVos.stream()
                    .filter(flightWeatherInfoVo -> StrUtil.isNotEmpty(flightWeatherInfoVo.getLocationName()))
                    .collect(Collectors.toList());

            List<WordFlightWeatherInfoVo> departureWeatherInfoList = new ArrayList<>();
            List<WordFlightWeatherInfoVo> arrivalWeatherInfoList = new ArrayList<>();

            flightWeatherInfoVos.forEach(flightWeatherInfoVo -> {
                if (flightWeatherInfoVo.getLocationType() == 0) {
                    WordFlightWeatherInfoVo wordFlightWeatherInfoVo = new WordFlightWeatherInfoVo();
                    BeanUtils.copyProperties(flightWeatherInfoVo, wordFlightWeatherInfoVo);
                    departureWeatherInfoList.add(wordFlightWeatherInfoVo);
                } else if (flightWeatherInfoVo.getLocationType() == 1) {
                    WordFlightWeatherInfoVo arrivalWeatherInfo = new WordFlightWeatherInfoVo();
                    BeanUtils.copyProperties(flightWeatherInfoVo, arrivalWeatherInfo);
                    arrivalWeatherInfoList.add(arrivalWeatherInfo);
                }
            });

            wordMeteorologicalVo.setDepartureWeatherInfoList(departureWeatherInfoList);
            wordMeteorologicalVo.setArrivalWeatherInfoList(arrivalWeatherInfoList);
        }
        // 气象信息导出不包含动态信息
        wordMeteorologicalVo.setDynamicInfoList(new ArrayList<>());
        return wordMeteorologicalVo;
    }


    /**
     * 生成文件名
     */
    private String generateFileName(String prefix, String extension) {
        return WeatherInfoWordUtils.generateFileName(prefix, extension);
    }

    /**
     * 设置Word响应头
     */
    private void setWordResponseHeaders(HttpServletResponse response, String fileName) {
        WeatherInfoWordUtils.setWordResponseHeaders(response, fileName);
    }

    /**
     * 输出Word文档到响应流
     */
    private void streamWordDocumentToResponse(HttpServletResponse response, byte[] docBytes) {
        WeatherInfoWordUtils.streamWordDocumentToResponse(response, docBytes);
    }

    /**
     * 设置PDF响应头
     */
    private void setPdfResponseHeaders(HttpServletResponse response, String fileName) {
        WeatherInfoPdfUtils.setPdfResponseHeaders(response, fileName);
    }

    /**
     * 输出PDF文档到响应流
     */
    private void streamPdfDocumentToResponse(HttpServletResponse response, byte[] pdfBytes) {
        WeatherInfoPdfUtils.streamPdfDocumentToResponse(response, pdfBytes);
    }

    /**
     * 设置ZIP响应头
     */
    private void setZipResponseHeaders(HttpServletResponse response, String fileName) {
        WeatherInfoPdfUtils.setZipResponseHeaders(response, fileName);
    }

    /**
     * 输出ZIP文档到响应流
     */
    private void streamZipDocumentToResponse(HttpServletResponse response, byte[] zipBytes) {
        WeatherInfoPdfUtils.streamZipDocumentToResponse(response, zipBytes);
    }

}
