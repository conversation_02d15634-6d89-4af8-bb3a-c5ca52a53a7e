package com.ruoyi.system.service.oc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.oc.FlightTaskBook;
import com.ruoyi.system.domain.oc.FlightTaskInfo;
import com.ruoyi.system.domain.oc.dto.FlightWeatherDto;
import com.ruoyi.system.domain.oc.dto.FlightWeatherInfoDto;
import com.ruoyi.system.domain.oc.entity.FlightDynamicInfo;
import com.ruoyi.system.domain.oc.entity.FlightWeatherInfo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.mapper.oc.FlightTaskBookMapper;
import com.ruoyi.system.mapper.oc.FlightTaskInfoMapper;
import com.ruoyi.system.mapper.oc.FlightWeatherInfoMapper;
import com.ruoyi.system.service.oc.IFlightWeatherInfoService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FlightWeatherInfoServiceImpl extends ServiceImpl<FlightWeatherInfoMapper, FlightWeatherInfo> implements IFlightWeatherInfoService {
    @Resource
    private FlightWeatherInfoMapper flightWeatherInfoBizMapper;
    @Resource
    private FlightTaskBookMapper flightTaskBookMapper;
    @Resource
    private FlightTaskInfoMapper flightTaskInfoMapper;
    @Resource
    private IFlightWeatherInfoService iFlightWeatherInfoService;
    @Value("${template.weatherTemplatePath}")
    private String weatherTemplate;

    /**
     * 查询气象信息
     */
    @Override
    public FlightWeatherVo selectFlightWeatherInfoById(Long flightTaskBookId, String companyCode) {
        return flightWeatherInfoBizMapper.selectFlightWeatherInfoById(flightTaskBookId, companyCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveFlightWeatherInfo(String companyCode, FlightWeatherDto flightWeatherDto) {
        String taskBookNumber = getFlightTaskBook(companyCode, flightWeatherDto);
        if (StrUtil.isNotEmpty(taskBookNumber)) {
            List<FlightWeatherInfoDto> flightWeatherInfoDtos = flightWeatherDto.getFlightWeatherInfoDtos();
            if (CollUtil.isNotEmpty(flightWeatherInfoDtos)) {
                //先删除旧数据
                iFlightWeatherInfoService.lambdaUpdate().eq(FlightWeatherInfo::getTaskBookNumber, taskBookNumber).remove();
                //封装新增的数据
                saveWeatherInfo(companyCode, flightWeatherInfoDtos, taskBookNumber);
            }
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public void exportWeatherInfoWord(HttpServletResponse response, Integer flightTaskBookId, String companyCode) {

    }


    @Override
    public void exportWeatherInfoPdf(HttpServletResponse response, Integer flightTaskBookId, String companyCode) {

    }

    @Override
    public void exportWeatherInfoPdfBatch(HttpServletResponse response, List<Integer> flightTaskBookIds, String companyCode) {

    }

    private void saveWeatherInfo(String companyCode, List<FlightWeatherInfoDto> flightWeatherInfoDtos, String taskBookNumber) {
        List<FlightWeatherInfo> flightWeatherInfos = new ArrayList<>();
        List<FlightTaskInfo> flightTaskInfos = flightTaskInfoMapper.selectByTaskBookNumber(taskBookNumber, companyCode);
        if (CollUtil.isNotEmpty(flightTaskInfos)) {
            // 筛选taskInfoId和批次都匹配的数据
            List<FlightWeatherInfoDto> matchingWeatherDtos = new ArrayList<>();
            for (FlightWeatherInfoDto weatherDto : flightWeatherInfoDtos) {
                for (FlightTaskInfo taskInfo : flightTaskInfos) {
                    if (weatherDto.getFlightTaskInfoId() != null && weatherDto.getFlightTaskInfoId().equals(taskInfo.getId()) && StrUtil.equals(weatherDto.getBatch(), taskInfo.getBatch())) {
                        matchingWeatherDtos.add(weatherDto);
                        break; // 找到匹配项后退出内层循环
                    }
                }
            }
            // 更新为匹配后的数据列表
            flightWeatherInfoDtos = matchingWeatherDtos;
        }
        flightWeatherInfoDtos.forEach(dto -> {
            FlightWeatherInfo flightWeatherInfo = new FlightWeatherInfo();
            BeanUtils.copyProperties(dto, flightWeatherInfo);
            flightWeatherInfo.setTaskBookNumber(taskBookNumber);
            flightWeatherInfo.setCompanyCode(companyCode);
            flightWeatherInfos.add(flightWeatherInfo);
        });
        iFlightWeatherInfoService.saveBatch(flightWeatherInfos);
    }

    private String getFlightTaskBook(String companyCode, FlightWeatherDto flightWeatherDto) {
        FlightTaskBook dbTaskBook = flightTaskBookMapper.selectByPrimaryKey(flightWeatherDto.getTaskBookId());
        if (dbTaskBook != null && dbTaskBook.getCompanyCode().equals(companyCode)) {
            return dbTaskBook.getTaskBookNumber();
        }
        return StrUtil.EMPTY;
    }


    private WordMeteorologicalVo getMeteorologicalVo(FlightWeatherDynamicVo flightWeatherDynamicVo) {
        // 创建目标对象实例并拷贝属性
        WordMeteorologicalVo wordMeteorologicalVo = new WordMeteorologicalVo();
        BeanUtils.copyProperties(flightWeatherDynamicVo, wordMeteorologicalVo);
        List<FlightWeatherInfoVo> flightWeatherInfoVos = flightWeatherDynamicVo.getFlightWeatherInfoVos();
        flightWeatherInfoVos = flightWeatherInfoVos.stream().filter(flightWeatherInfoVo -> StrUtil.isNotEmpty(flightWeatherInfoVo.getLocationName())).collect(Collectors.toList());

        List<WordFlightWeatherInfoVo> departureWeatherInfoList = new ArrayList<>();
        List<WordFlightWeatherInfoVo> arrivalWeatherInfoList = new ArrayList<>();
        flightWeatherInfoVos.forEach(flightWeatherInfoVo -> {
            if (flightWeatherInfoVo.getLocationType() == 0) {
                WordFlightWeatherInfoVo wordFlightWeatherInfoVo = new WordFlightWeatherInfoVo();
                BeanUtils.copyProperties(flightWeatherInfoVo, wordFlightWeatherInfoVo);
                departureWeatherInfoList.add(wordFlightWeatherInfoVo);
            } else if (flightWeatherInfoVo.getLocationType() == 1) {
                WordFlightWeatherInfoVo arrivalWeatherInfo = new WordFlightWeatherInfoVo();
                BeanUtils.copyProperties(flightWeatherInfoVo, arrivalWeatherInfo);
                arrivalWeatherInfoList.add(arrivalWeatherInfo);
            }
        });
        wordMeteorologicalVo.setDepartureWeatherInfoList(departureWeatherInfoList);
        wordMeteorologicalVo.setArrivalWeatherInfoList(arrivalWeatherInfoList);
        return wordMeteorologicalVo;
    }

}
