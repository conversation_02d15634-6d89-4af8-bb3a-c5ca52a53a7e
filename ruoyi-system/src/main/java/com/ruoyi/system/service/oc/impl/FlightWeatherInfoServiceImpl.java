package com.ruoyi.system.service.oc.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.oc.FlightTaskBook;
import com.ruoyi.system.domain.oc.FlightTaskInfo;
import com.ruoyi.system.domain.oc.dto.FlightDynamicInfoDto;
import com.ruoyi.system.domain.oc.dto.FlightWeatherDto;
import com.ruoyi.system.domain.oc.dto.FlightWeatherDynamicDto;
import com.ruoyi.system.domain.oc.dto.FlightWeatherInfoDto;
import com.ruoyi.system.domain.oc.entity.FlightDynamicInfo;
import com.ruoyi.system.domain.oc.entity.FlightWeatherInfo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.mapper.oc.FlightDynamicInfoMapper;
import com.ruoyi.system.mapper.oc.FlightTaskBookMapper;
import com.ruoyi.system.mapper.oc.FlightTaskInfoMapper;
import com.ruoyi.system.mapper.oc.FlightWeatherInfoMapper;
import com.ruoyi.system.service.oc.IFlightDynamicInfoService;
import com.ruoyi.system.service.oc.IFlightWeatherInfoService;
import com.ruoyi.system.util.word.WeatherInfoWordUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@Slf4j
public class FlightWeatherInfoServiceImpl extends ServiceImpl<FlightWeatherInfoMapper, FlightWeatherInfo> implements IFlightWeatherInfoService {
    @Resource
    private FlightWeatherInfoMapper flightWeatherInfoBizMapper;
    @Resource
    private FlightDynamicInfoMapper flightDynamicInfoMapper;
    @Resource
    private FlightTaskBookMapper flightTaskBookMapper;
    @Resource
    private FlightTaskInfoMapper flightTaskInfoMapper;
    @Resource
    private IFlightWeatherInfoService iFlightWeatherInfoService;
    @Resource
    private IFlightDynamicInfoService iFlightDynamicInfoService;
    @Value("${template.weatherTemplatePath}")
    private String weatherTemplate;

    /**
     * 查询气象信息
     */
    @Override
    public FlightWeatherVo selectFlightWeatherInfoById(Long flightTaskBookId, String companyCode) {
        return flightWeatherInfoBizMapper.selectFlightWeatherInfoById(flightTaskBookId, companyCode);
    }

    /**
     * 查询气象信息和动态信息
     */
    @Override
    public FlightWeatherDynamicVo selectFlightWeatherDynamicInfoById(Long flightTaskBookId, String companyCode) {
        FlightWeatherDynamicVo flightWeatherDynamicVo = new FlightWeatherDynamicVo();
        FlightWeatherVo flightWeatherVo = flightWeatherInfoBizMapper.selectFlightWeatherInfoById(flightTaskBookId, companyCode);
        if (flightWeatherVo != null) {
            // 查询任务书信息查询动态信息详情
            List<FlightDynamicInfo> flightDynamicInfos = flightDynamicInfoMapper.selectFlightDynamicInfoByTaskBookNumber(flightWeatherVo.getTaskBookNumber(), companyCode);
            BeanUtils.copyProperties(flightWeatherVo, flightWeatherDynamicVo);
            flightWeatherDynamicVo.setFlightWeatherInfoVos(flightWeatherVo.getFlightWeatherInfoVos());
            flightWeatherDynamicVo.setFlightDynamicInfoList(flightDynamicInfos);
        }
        return flightWeatherDynamicVo;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public int saveFlightWeatherInfo(String companyCode, FlightWeatherDto flightWeatherDto) {
        String taskBookNumber = getFlightTaskBook(companyCode, flightWeatherDto);
        if (StrUtil.isNotEmpty(taskBookNumber)) {
            List<FlightWeatherInfoDto> flightWeatherInfoDtos = flightWeatherDto.getFlightWeatherInfoDtos();
            if (CollUtil.isNotEmpty(flightWeatherInfoDtos)) {
                //先删除旧数据
                iFlightWeatherInfoService.lambdaUpdate().eq(FlightWeatherInfo::getTaskBookNumber, taskBookNumber).remove();
                //封装新增的数据
                saveWeatherInfo(companyCode, flightWeatherInfoDtos, taskBookNumber);
            }
            return 1;
        } else {
            return 0;
        }
    }

    @Override
    public int insertFlightWeatherDynamic(String companyCode, FlightWeatherDynamicDto flightWeatherDynamicDto) {
        log.info("新增气象和动态信息，公司代码: {}, 任务书ID: {}", companyCode, flightWeatherDynamicDto.getTaskBookId());

        String taskBookNumber = getFlightTaskBook(companyCode, flightWeatherDynamicDto.getTaskBookId());
        if (StrUtil.isEmpty(taskBookNumber)) {
            log.error("任务书不存在或不属于该公司，任务书ID: {}, 公司代码: {}", flightWeatherDynamicDto.getTaskBookId(), companyCode);
            return 0;
        }

        try {
            // 保存气象信息
            if (CollUtil.isNotEmpty(flightWeatherDynamicDto.getFlightWeatherInfoDtos())) {
                saveWeatherInfo(companyCode, flightWeatherDynamicDto.getFlightWeatherInfoDtos(), taskBookNumber);
            }

            // 保存动态信息
            if (CollUtil.isNotEmpty(flightWeatherDynamicDto.getFlightDynamicInfoDtos())) {
                saveDynamicInfo(companyCode, flightWeatherDynamicDto.getFlightDynamicInfoDtos(), taskBookNumber);
            }

            return 1;
        } catch (Exception e) {
            log.error("新增气象和动态信息失败", e);
            return 0;
        }
    }

    @Override
    public int updateFlightWeatherDynamic(String companyCode, FlightWeatherDynamicDto flightWeatherDynamicDto) {
        log.info("更新气象和动态信息，公司代码: {}, 任务书ID: {}", companyCode, flightWeatherDynamicDto.getTaskBookId());

        String taskBookNumber = getFlightTaskBook(companyCode, flightWeatherDynamicDto.getTaskBookId());
        if (StrUtil.isEmpty(taskBookNumber)) {
            log.error("任务书不存在或不属于该公司，任务书ID: {}, 公司代码: {}", flightWeatherDynamicDto.getTaskBookId(), companyCode);
            return 0;
        }

        try {
            // 删除旧的气象信息
            iFlightWeatherInfoService.lambdaUpdate().eq(FlightWeatherInfo::getTaskBookNumber, taskBookNumber).remove();

            // 删除旧的动态信息
            iFlightDynamicInfoService.lambdaUpdate().eq(FlightDynamicInfo::getTaskBookNumber, taskBookNumber).remove();

            // 保存新的气象信息
            if (CollUtil.isNotEmpty(flightWeatherDynamicDto.getFlightWeatherInfoDtos())) {
                saveWeatherInfo(companyCode, flightWeatherDynamicDto.getFlightWeatherInfoDtos(), taskBookNumber);
            }

            // 保存新的动态信息
            if (CollUtil.isNotEmpty(flightWeatherDynamicDto.getFlightDynamicInfoDtos())) {
                saveDynamicInfo(companyCode, flightWeatherDynamicDto.getFlightDynamicInfoDtos(), taskBookNumber);
            }

            return 1;
        } catch (Exception e) {
            log.error("更新气象和动态信息失败", e);
            return 0;
        }
    }

    @Override
    public void exportWeatherInfoWord(HttpServletResponse response, Integer flightTaskBookId, String companyCode) {
        log.info("开始导出气象信息Word文档，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
        try {
            // 查询气象信息数据
            FlightWeatherVo flightWeatherVo = this.selectFlightWeatherInfoById(flightTaskBookId.longValue(), companyCode);
            if (flightWeatherVo == null) {
                log.error("未找到对应的气象数据，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode);
                return;
            }

            // 转换为Word导出格式
            WordMeteorologicalVo wordMeteorologicalVo = convertToWeatherWordVo(flightWeatherVo);

            // 生成Word文档
            byte[] docBytes = generateWordDocument(wordMeteorologicalVo);

            // 设置响应头
            String fileName = generateFileName("气象信息记录表", "docx");
            setWordResponseHeaders(response, fileName);

            // 输出文件到前端
            streamWordDocumentToResponse(response, docBytes);

            log.info("气象信息Word文档导出成功，文件名: {}", fileName);
        } catch (Exception e) {
            log.error("导出气象信息Word文档失败，任务ID: {}, 公司代码: {}", flightTaskBookId, companyCode, e);
        }
    }


    @Override
    public void exportWeatherInfoPdf(HttpServletResponse response, Integer flightTaskBookId, String companyCode) {

    }

    @Override
    public void exportWeatherInfoPdfBatch(HttpServletResponse response, List<Integer> flightTaskBookIds, String companyCode) {

    }

    private void saveWeatherInfo(String companyCode, List<FlightWeatherInfoDto> flightWeatherInfoDtos, String taskBookNumber) {
        List<FlightWeatherInfo> flightWeatherInfos = new ArrayList<>();
        List<FlightTaskInfo> flightTaskInfos = flightTaskInfoMapper.selectByTaskBookNumber(taskBookNumber, companyCode);
        if (CollUtil.isNotEmpty(flightTaskInfos)) {
            // 筛选taskInfoId和批次都匹配的数据
            List<FlightWeatherInfoDto> matchingWeatherDtos = new ArrayList<>();
            for (FlightWeatherInfoDto weatherDto : flightWeatherInfoDtos) {
                for (FlightTaskInfo taskInfo : flightTaskInfos) {
                    if (weatherDto.getFlightTaskInfoId() != null && weatherDto.getFlightTaskInfoId().equals(taskInfo.getId()) && StrUtil.equals(weatherDto.getBatch(), taskInfo.getBatch())) {
                        matchingWeatherDtos.add(weatherDto);
                        break; // 找到匹配项后退出内层循环
                    }
                }
            }
            // 更新为匹配后的数据列表
            flightWeatherInfoDtos = matchingWeatherDtos;
        }
        flightWeatherInfoDtos.forEach(dto -> {
            FlightWeatherInfo flightWeatherInfo = new FlightWeatherInfo();
            BeanUtils.copyProperties(dto, flightWeatherInfo);
            flightWeatherInfo.setTaskBookNumber(taskBookNumber);
            flightWeatherInfo.setCompanyCode(companyCode);
            flightWeatherInfos.add(flightWeatherInfo);
        });
        iFlightWeatherInfoService.saveBatch(flightWeatherInfos);
    }

    private String getFlightTaskBook(String companyCode, FlightWeatherDto flightWeatherDto) {
        FlightTaskBook dbTaskBook = flightTaskBookMapper.selectByPrimaryKey(flightWeatherDto.getTaskBookId());
        if (dbTaskBook != null && dbTaskBook.getCompanyCode().equals(companyCode)) {
            return dbTaskBook.getTaskBookNumber();
        }
        return StrUtil.EMPTY;
    }

    private String getFlightTaskBook(String companyCode, Long taskBookId) {
        FlightTaskBook dbTaskBook = flightTaskBookMapper.selectByPrimaryKey(taskBookId);
        if (dbTaskBook != null && dbTaskBook.getCompanyCode().equals(companyCode)) {
            return dbTaskBook.getTaskBookNumber();
        }
        return StrUtil.EMPTY;
    }

    /**
     * 保存动态信息
     */
    private void saveDynamicInfo(String companyCode, List<FlightDynamicInfoDto> flightDynamicInfoDtos, String taskBookNumber) {
        List<FlightDynamicInfo> flightDynamicInfos = new ArrayList<>();
        List<FlightTaskInfo> flightTaskInfos = flightTaskInfoMapper.selectByTaskBookNumber(taskBookNumber, companyCode);

        if (CollUtil.isNotEmpty(flightTaskInfos)) {
            // 筛选批次匹配的数据
            List<FlightDynamicInfoDto> matchingDynamicDtos = new ArrayList<>();
            for (FlightDynamicInfoDto dynamicDto : flightDynamicInfoDtos) {
                for (FlightTaskInfo taskInfo : flightTaskInfos) {
                    if (StrUtil.equals(dynamicDto.getBatch(), taskInfo.getBatch())) {
                        matchingDynamicDtos.add(dynamicDto);
                        break;
                    }
                }
            }
            flightDynamicInfoDtos = matchingDynamicDtos;
        }

        flightDynamicInfoDtos.forEach(dto -> {
            FlightDynamicInfo flightDynamicInfo = new FlightDynamicInfo();
            BeanUtils.copyProperties(dto, flightDynamicInfo);
            flightDynamicInfo.setTaskBookNumber(taskBookNumber);
            flightDynamicInfo.setCompanyCode(companyCode);
            flightDynamicInfos.add(flightDynamicInfo);
        });

        iFlightDynamicInfoService.saveBatch(flightDynamicInfos);
    }


    private WordMeteorologicalVo getMeteorologicalVo(FlightWeatherDynamicVo flightWeatherDynamicVo) {
        // 创建目标对象实例并拷贝属性
        WordMeteorologicalVo wordMeteorologicalVo = new WordMeteorologicalVo();
        BeanUtils.copyProperties(flightWeatherDynamicVo, wordMeteorologicalVo);
        List<FlightWeatherInfoVo> flightWeatherInfoVos = flightWeatherDynamicVo.getFlightWeatherInfoVos();
        flightWeatherInfoVos = flightWeatherInfoVos.stream().filter(flightWeatherInfoVo -> StrUtil.isNotEmpty(flightWeatherInfoVo.getLocationName())).collect(Collectors.toList());

        List<WordFlightWeatherInfoVo> departureWeatherInfoList = new ArrayList<>();
        List<WordFlightWeatherInfoVo> arrivalWeatherInfoList = new ArrayList<>();
        flightWeatherInfoVos.forEach(flightWeatherInfoVo -> {
            if (flightWeatherInfoVo.getLocationType() == 0) {
                WordFlightWeatherInfoVo wordFlightWeatherInfoVo = new WordFlightWeatherInfoVo();
                BeanUtils.copyProperties(flightWeatherInfoVo, wordFlightWeatherInfoVo);
                departureWeatherInfoList.add(wordFlightWeatherInfoVo);
            } else if (flightWeatherInfoVo.getLocationType() == 1) {
                WordFlightWeatherInfoVo arrivalWeatherInfo = new WordFlightWeatherInfoVo();
                BeanUtils.copyProperties(flightWeatherInfoVo, arrivalWeatherInfo);
                arrivalWeatherInfoList.add(arrivalWeatherInfo);
            }
        });
        wordMeteorologicalVo.setDepartureWeatherInfoList(departureWeatherInfoList);
        wordMeteorologicalVo.setArrivalWeatherInfoList(arrivalWeatherInfoList);
        return wordMeteorologicalVo;
    }

    /**
     * 转换气象信息为Word导出格式（仅气象信息，不包含动态信息）
     */
    private WordMeteorologicalVo convertToWeatherWordVo(FlightWeatherVo flightWeatherVo) {
        WordMeteorologicalVo wordMeteorologicalVo = new WordMeteorologicalVo();
        BeanUtils.copyProperties(flightWeatherVo, wordMeteorologicalVo);

        List<FlightWeatherInfoVo> flightWeatherInfoVos = flightWeatherVo.getFlightWeatherInfoVos();
        if (flightWeatherInfoVos != null) {
            flightWeatherInfoVos = flightWeatherInfoVos.stream()
                .filter(flightWeatherInfoVo -> StrUtil.isNotEmpty(flightWeatherInfoVo.getLocationName()))
                .collect(Collectors.toList());

            List<WordFlightWeatherInfoVo> departureWeatherInfoList = new ArrayList<>();
            List<WordFlightWeatherInfoVo> arrivalWeatherInfoList = new ArrayList<>();

            flightWeatherInfoVos.forEach(flightWeatherInfoVo -> {
                if (flightWeatherInfoVo.getLocationType() == 0) {
                    WordFlightWeatherInfoVo wordFlightWeatherInfoVo = new WordFlightWeatherInfoVo();
                    BeanUtils.copyProperties(flightWeatherInfoVo, wordFlightWeatherInfoVo);
                    departureWeatherInfoList.add(wordFlightWeatherInfoVo);
                } else if (flightWeatherInfoVo.getLocationType() == 1) {
                    WordFlightWeatherInfoVo arrivalWeatherInfo = new WordFlightWeatherInfoVo();
                    BeanUtils.copyProperties(flightWeatherInfoVo, arrivalWeatherInfo);
                    arrivalWeatherInfoList.add(arrivalWeatherInfo);
                }
            });

            wordMeteorologicalVo.setDepartureWeatherInfoList(departureWeatherInfoList);
            wordMeteorologicalVo.setArrivalWeatherInfoList(arrivalWeatherInfoList);
        }

        // 气象信息导出不包含动态信息
        wordMeteorologicalVo.setDynamicInfoList(new ArrayList<>());

        return wordMeteorologicalVo;
    }

    @Override
    public byte[] generateWordDocument(WordMeteorologicalVo wordMeteorologicalVo) {
        try {
            return WeatherInfoWordUtils.generateWeatherInfoDocument(weatherTemplate, wordMeteorologicalVo, null);
        } catch (Exception e) {
            log.error("生成气象信息Word文档失败", e);
            throw new RuntimeException("生成Word文档失败: " + e.getMessage(), e);
        }
    }

    /**
     * 生成文件名
     */
    private String generateFileName(String prefix, String extension) {
        return WeatherInfoWordUtils.generateFileName(prefix, extension);
    }

    /**
     * 设置Word响应头
     */
    private void setWordResponseHeaders(HttpServletResponse response, String fileName) {
        WeatherInfoWordUtils.setWordResponseHeaders(response, fileName);
    }

    /**
     * 输出Word文档到响应流
     */
    private void streamWordDocumentToResponse(HttpServletResponse response, byte[] docBytes) {
        WeatherInfoWordUtils.streamWordDocumentToResponse(response, docBytes);
    }

}
