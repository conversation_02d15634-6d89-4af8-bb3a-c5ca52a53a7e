package com.ruoyi.system.service.oc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.oc.dto.FlightWeatherDto;
import com.ruoyi.system.domain.oc.entity.FlightWeatherInfo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherVo;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface IFlightWeatherInfoService extends IService<FlightWeatherInfo> {

    /**
     * 查询气象信息
     *
     * @param flightTaskBookId 气象信息主键
     * @return 气象信息
     */
    public FlightWeatherVo selectFlightWeatherInfoById(Long flightTaskBookId, String companyCode);

    int saveFlightWeatherInfo(String companyCode, FlightWeatherDto flightWeatherDto);

    void exportWeatherInfoWord(HttpServletResponse response, Integer flightTaskBookId, String companyCode);

    void exportWeatherInfoPdf(HttpServletResponse response, Integer flightTaskBookId, String companyCode);

    void exportWeatherInfoPdfBatch(HttpServletResponse response, List<Integer> flightTaskBookIds, String companyCode);

}
