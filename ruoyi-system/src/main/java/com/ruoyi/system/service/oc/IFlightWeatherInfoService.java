package com.ruoyi.system.service.oc;

import com.baomidou.mybatisplus.extension.service.IService;
import com.ruoyi.system.domain.oc.dto.FlightWeatherDynamicDto;
import com.ruoyi.system.domain.oc.entity.FlightWeatherInfo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;

import javax.servlet.http.HttpServletResponse;

/**
 *
 */
public interface IFlightWeatherInfoService extends IService<FlightWeatherInfo> {

    /**
     * 查询气象信息
     *
     * @param flightTaskBookId 气象信息主键
     * @return 气象信息
     */
    public FlightWeatherDynamicVo selectFlightWeatherInfoById(Long flightTaskBookId, String companyCode);

    int insertFlightWeatherDynamic(String companyCode, FlightWeatherDynamicDto flightWeatherDynamicDto);

    int updateFlightWeatherDynamic(String companyCode, FlightWeatherDynamicDto flightWeatherDynamicDto);

    void exportWeatherInfoToWord(HttpServletResponse response, Integer flightTaskBookId, String companyCode);
    /**
     * 生成Word文档字节数组
     * @param wordMeteorologicalVo 导出数据
     * @return Word文档字节数组
     */
     byte[] generateWordDocument(WordMeteorologicalVo wordMeteorologicalVo);
}
