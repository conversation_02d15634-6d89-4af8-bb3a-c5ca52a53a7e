package com.ruoyi.system.service.oc.impl;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.oc.WxMenu;
import com.ruoyi.system.mapper.oc.WxMenuMapper;
import com.ruoyi.system.service.oc.WxMenuService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/6/4 15:16
 * @description：
 * @modified By：
 * @version: $
 */
@Service
@RequiredArgsConstructor
public class WxMenuServiceImpl  implements WxMenuService {

    @Resource
    private WxMenuMapper wxMenuMapper;

    @Override
    public CommonResult<List<WxMenu>> list() {
        List<WxMenu> wxMenus = wxMenuMapper.selectList();
        return CommonResult.success(wxMenus);
    }
}
