package com.ruoyi.system.service.mdmp.impl;

import java.util.List;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.PlanNoUtil;
import com.ruoyi.system.domain.mdmp.Aircraft;
import com.ruoyi.system.domain.mdmp.AircraftMp;
import com.ruoyi.system.domain.mdmp.DailyFlightPlan;
import com.ruoyi.system.domain.mdmp.LongTermFlightPlan;
import com.ruoyi.system.mapper.mdmp.DailyFlightPlanMapper;
import com.ruoyi.system.param.mdmp.DailyFlightPlanActualTimeParam;
import com.ruoyi.system.param.mdmp.DailyFlightPlanListParam;
import com.ruoyi.system.service.mdmp.IDailyFlightPlanService;
import com.ruoyi.system.service.mdmp.ILongTermFlightPlanService;
import com.ruoyi.system.service.mdmp.INextDayFlightPlanService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 当日计划飞行Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Service
public class DailyFlightPlanServiceImpl implements IDailyFlightPlanService {
    @Resource
    private DailyFlightPlanMapper dailyFlightPlanMapper;


    @Resource
    private ILongTermFlightPlanService longTermFlightPlanService;

    @Resource
    private INextDayFlightPlanService nextDayFlightPlanService;


    /**
     * 查询当日计划飞行
     *
     * @param id 当日计划飞行主键
     * @return 当日计划飞行
     */
    @Override
    public DailyFlightPlan selectDailyFlightPlanById(Long id) {
        DailyFlightPlan dailyFlightPlan = dailyFlightPlanMapper.selectDailyFlightPlanById(id);
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        if (deptCodeList.contains(dailyFlightPlan.getDeptCode())) {
            return dailyFlightPlan;
        } else {
            throw new ServiceException("无权限访问该数据");
        }
    }

    /**
     * 查询当日计划飞行列表
     *
     * @param param 当日计划飞行
     * @return 当日计划飞行
     */
    @Override
    public List<DailyFlightPlan> selectDailyFlightPlanList(DailyFlightPlanListParam param) {
        return dailyFlightPlanMapper.selectDailyFlightPlanList(param);
    }

    /**
     * 新增当日计划飞行
     *
     * @param dailyFlightPlan 当日计划飞行
     * @return 结果
     */
    @Override
    public int insertDailyFlightPlan(DailyFlightPlan dailyFlightPlan) {


        /**
         *  生成计划编号
         */
        dailyFlightPlan.setSerialNo(PlanNoUtil.generatePlanNo(dailyFlightPlan.getCompanyCode()));

        /**
         * 新增计划实体
         */
        int longTermFlightPlanCount = dailyFlightPlanMapper.insertDailyFlightPlan(dailyFlightPlan);


        /**
         * 新增关联实体
         *
         */
        longTermFlightPlanService.addAssociatedEntitiesLongTermFlightPlan(3, dailyFlightPlan.getId(),
                dailyFlightPlan.getPlanModelAircraftList(),
                dailyFlightPlan.getFlightPlanAirportList(),
                dailyFlightPlan.getFlightPlanRouteList(),
                dailyFlightPlan.getFlightPlanFileList(),
                dailyFlightPlan.getFlightPlanWorkList());


        return longTermFlightPlanCount;
    }


    /**
     * 修改当日计划飞行
     *
     * @param dailyFlightPlan 当日计划飞行
     * @return 结果
     */
    @Override
    public int updateDailyFlightPlan(DailyFlightPlan dailyFlightPlan) {
        selectDailyFlightPlanById(dailyFlightPlan.getId());
        //删除原有关联表
        longTermFlightPlanService.deleteExistingAssociated(dailyFlightPlan.getId(), 3);
        //更新航班计划
        int count = dailyFlightPlanMapper.updateDailyFlightPlan(dailyFlightPlan);
        //新增关联实体
        longTermFlightPlanService.addAssociatedEntitiesLongTermFlightPlan(3, dailyFlightPlan.getId(),
                dailyFlightPlan.getPlanModelAircraftList(),
                dailyFlightPlan.getFlightPlanAirportList(),
                dailyFlightPlan.getFlightPlanRouteList(),
                dailyFlightPlan.getFlightPlanFileList(),
                dailyFlightPlan.getFlightPlanWorkList());
        return count;
    }


    /**
     * 批量删除当日计划飞行
     *
     * @param ids 需要删除的当日计划飞行主键
     * @return 结果
     */
    @Override
    public int deleteDailyFlightPlanByIds(Long[] ids) {
        for (Long id : ids) {
            selectDailyFlightPlanById(id);
        }
        return dailyFlightPlanMapper.deleteDailyFlightPlanByIds(ids);
    }

    /**
     * 删除当日计划飞行信息
     *
     * @param id 当日计划飞行主键
     * @return 结果
     */
    @Override
    public int deleteDailyFlightPlanById(Long id) {
        return dailyFlightPlanMapper.deleteDailyFlightPlanById(id);
    }

    @Override
    public DailyFlightPlan verifyDailyFlightPlan(DailyFlightPlan dailyFlightPlan) {
        return (DailyFlightPlan) longTermFlightPlanService.verifyLongTermFlightPlan(dailyFlightPlan);
    }

    @Override
    public int auditing(DailyFlightPlan dailyFlightPlan) {
        selectDailyFlightPlanById(dailyFlightPlan.getId());
        //根据id查询非草稿状态下的飞行计划
        DailyFlightPlan dailyFlightPlan1 = dailyFlightPlanMapper.selectDailyFlightByIdAndStatus(dailyFlightPlan.getId());
        if (dailyFlightPlan1 != null) {
            int i = updateDailyFlightPlan(dailyFlightPlan);
            return i; // 如果更新成功，返回结果
        } else {
            // 处理未找到的情况，例如返回特定的错误代码或抛出异常
            throw new ServiceException("未查询到飞行计划");
        }
    }

    @Override
    public int updateDailyFlightPlanActualTime(DailyFlightPlanActualTimeParam param) {
        selectDailyFlightPlanById(param.getId());
        DailyFlightPlan dailyFlightPlan = dailyFlightPlanMapper.selectDailyFlightPlanById(param.getId());
        if (dailyFlightPlan == null || dailyFlightPlan.getIsDelete().equals(0)) {
            throw new ServiceException("未查询到飞行计划");
        } else {
            // 处理未找到的情况，例如返回特定的错误代码或抛出异常
            dailyFlightPlan.setActualDepartureTime(param.getActualDepartureTime());
            dailyFlightPlan.setActualArrivalTime(param.getActualArrivalTime());
            return dailyFlightPlanMapper.updateDailyFlightPlan(dailyFlightPlan); // 如果更新成功，返回结果
        }
    }
}
