package com.ruoyi.system.service.mdmp;


import com.ruoyi.system.domain.mdmp.Airspace;
import com.ruoyi.system.domain.mdmp.AirspaceLongLat;
import com.ruoyi.system.domain.mdmp.AirspaceMapData;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;

import java.util.List;

/**
 * 地图数据 Service接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface IMapDataObstacleService {


    /**
     * 保存障碍物地图数据
     *
     * @param airspaceMapDataList 障碍物
     */
    void saveObstacle(Airspace airspace, List<AirspaceMapData> airspaceMapDataList, AirspaceLongLat airspaceLongLat);

    Integer selectObstacleByIndex(Long airspaceId, Integer index, WebsocketFlightData flightData);
}
