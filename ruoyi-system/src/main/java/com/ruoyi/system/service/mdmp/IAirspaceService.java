package com.ruoyi.system.service.mdmp;

import com.ruoyi.common.config.rule.Obstacle;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.mdmp.Airspace;
import com.ruoyi.system.domain.mdmp.AirspaceLongLat;
import com.ruoyi.system.domain.mdmp.vo.AirspaceListVO;

import java.util.List;


/**
 * 空域Service接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface IAirspaceService
{
    /**
     * 查询空域
     *
     * @param id 空域主键
     * @return 空域
     */
    public Airspace selectAirspaceById(Long id);

    /**
     * 查询空域列表
     *
     * @param airspace 空域
     * @return 空域集合
     */
    public List<Airspace> selectAirspaceList(Airspace airspace);

    public List<AirspaceListVO> selectAirspaceListAndMap(Long[] ids);
    /**
     * 新增空域
     *
     * @param airspace 空域
     * @return 结果
     */
    public int insertAirspace(Airspace airspace);

    /**
     * 修改空域
     *
     * @param airspace 空域
     * @return 结果
     */
    public int updateAirspace(Airspace airspace,List<String> deptCodeList);

    /**
     * 批量删除空域
     *
     * @param ids 需要删除的空域主键集合
     * @return 结果
     */
    public int deleteAirspaceByIds(Long[] ids);

    /**
     * 删除空域信息
     *
     * @param id 空域主键
     * @return 结果
     */
    public int deleteAirspaceById(Long id,List<String> deptCodeList);


    /**
     * 获取圆的50个点
     * @param airspace
     * @param id
     * @return
     */
    public List<AirspaceLongLat> processPolygonAndCircularAirspace(Airspace airspace, long id);



}
