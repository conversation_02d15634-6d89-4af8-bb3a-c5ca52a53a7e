package com.ruoyi.system.service.mdmp.impl;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.config.websocket.UpdateCommandHandler;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.FlightCommandLog;
import com.ruoyi.system.domain.mdmp.dto.FlightCommandDTO;
import com.ruoyi.system.domain.mdmp.dto.PushToDroneSystemDTO;
import com.ruoyi.system.domain.mdmp.dto.QueryFlightCommandLogDTO;
import com.ruoyi.system.mapper.mdmp.FlightCommandLogMapper;
import com.ruoyi.system.service.mdmp.FlightCommandService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

@Service
@Slf4j
public class FlightCommandServiceImpl implements FlightCommandService {

    private final FlightCommandLogMapper flightCommandLogMapper;
    private final UpdateCommandHandler updateCommandHandler;
    @Autowired
    @Qualifier("sendCommandJmsTemplate")
    private JmsTemplate sendCommandJmsTemplate;

    public FlightCommandServiceImpl(FlightCommandLogMapper flightCommandLogMapper, UpdateCommandHandler updateCommandHandler) {
        this.flightCommandLogMapper = flightCommandLogMapper;
        this.updateCommandHandler = updateCommandHandler;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> executionResult(FlightCommandDTO dto) {
        log.info("接收无人机系统飞行指令执行结果：[{}]", JSON.toJSONString(dto));
        FlightCommandLog flightCommandLog = flightCommandLogMapper.queryById(dto.getCommandId());
        if (Objects.isNull(flightCommandLog)) {
            return CommonResult.error("当前消息不存在");
        }
        flightCommandLog.setViewed(dto.getQueryStatus() == 0 ? "未查看" : "已查看");
        flightCommandLog.setExecuted(dto.getExecutionStatus() == 0 ? "未执行" : "已执行");
        flightCommandLog.setUpdateTime(dto.getOperatingTime());
        flightCommandLogMapper.update(flightCommandLog);
        // socket发送给前端
        updateCommandHandler.sendMessage("更新电子进程单指令状态");
        return CommonResult.success();
    }

    @Override
    public CommonResult<List<FlightCommandLog>> query(QueryFlightCommandLogDTO dto) {
        List<FlightCommandLog> flightCommandLogs = flightCommandLogMapper.queryByDate(dto.getDate());
        return CommonResult.success(flightCommandLogs);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public CommonResult<String> pushToDroneSystem(PushToDroneSystemDTO dto) {
        String logMsg;
        if (dto.getType() == 1) {
            logMsg = "返航";
        }else{
            logMsg = "绕点盘旋"+"，经度："+dto.getLongitude()+"，纬度："+dto.getLatitude()+"，高度："+dto.getHeight();
        }
        FlightCommandLog flightCommandLog = new FlightCommandLog();
        flightCommandLog.setFlightId(null);
        flightCommandLog.setCommandLog(logMsg);
        flightCommandLog.setCallSign(dto.getAircraftReg());
        flightCommandLog.setDelivered("已送达");
        flightCommandLog.setCreateDate(DateUtils.getDate());
        String time = DateUtils.getTime();
        flightCommandLog.setCreateTime(time);
        flightCommandLog.setUpdateTime(time);
        flightCommandLogMapper.insert(flightCommandLog);
        //发送给无人机系统
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("commandId", flightCommandLog.getId());
        jsonObject.put("commandLog", "(" + flightCommandLog.getCallSign() + ")" + flightCommandLog.getCommandLog());
        jsonObject.put("createDate", flightCommandLog.getCreateDate());
        jsonObject.put("createTime", flightCommandLog.getCreateTime());
        sendCommandJmsTemplate.convertAndSend("Command", JSON.toJSONString(jsonObject));
        // socket发送给前端
        updateCommandHandler.sendMessage("更新电子进程单指令状态");
        return CommonResult.success();
    }
}
