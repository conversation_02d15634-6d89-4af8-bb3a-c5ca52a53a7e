package com.ruoyi.system.mapper.oc;

import com.ruoyi.system.domain.oc.entity.AircrewRecord;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface AircrewRecordMapper {

    /**
     * 查询机组人员记录
     *
     * @param aircrewRecordId 记录主键
     * @return 结果
     */
    AircrewRecord selectById(Long aircrewRecordId);

    /**
     * 查询机组人员记录集合
     *
     * @param userId 记录主键
     * @param firstFileType 记录主键
     * @return 结果
     */
    List<AircrewRecord> selectByUserIdAndFirstFileType(@Param("userId") Long userId, @Param("firstFileType") Integer firstFileType);

    /**
     * 新增机组人员记录
     *
     * @param aircrewRecord 机组人员记录
     * @return 结果
     */
    int insertAircrewRecord(AircrewRecord aircrewRecord);

    /**
     * 修改机组人员记录
     *
     * @param aircrewRecord 机组人员记录
     * @return 结果
     */
    int updateAircrewRecord(AircrewRecord aircrewRecord);

    /**
     * 修改机组人员记录
     *
     * @param aircrewRecordId 机组人员记录ID
     * @return 结果
     */
    int deleteById(Long aircrewRecordId);
}
