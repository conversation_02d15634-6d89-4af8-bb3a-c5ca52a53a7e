package com.ruoyi.system.mapper.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.MpData;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface MpDataMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public MpData selectMpDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param mpData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<MpData> selectMpDataList(MpData mpData);

    /**
     * 新增【请填写功能名称】
     *
     * @param mpData 【请填写功能名称】
     * @return 结果
     */
    public int insertMpData(MpData mpData);

    /**
     * 修改【请填写功能名称】
     *
     * @param mpData 【请填写功能名称】
     * @return 结果
     */
    public int updateMpData(MpData mpData);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteMpDataById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMpDataByIds(Long[] ids);

    void insertMpDataList(@Param("param") List<MpData> mpDataList);

    void deleteMpDataByFcstTimeSequence(@Param("time")Long time,@Param("altitude") Double altitude);

    MpData selectUMpDataByMeteInfo(@Param("param")MeteInfoParam meteInfoParam);

    List<Long> selectOldMpDataList();

    List<MpData> getList(@Param("param")MeteParam meteParam);

    int deleteOldMpDataList();
}
