package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.WorkMapData;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface WorkMapDataMapper {


    List<WorkMapData> selectAllByWorkId(@Param("workId") Long workId);


    int delByWorkId(@Param("workId") Long workId);


    int insertAllWorkMapData(@Param("workMapData") Set<WorkMapData> workMapData);

    WorkMapData selectByIndex(@Param("index") Integer index);

    WorkMapData selectWorkMap(@Param("param") WebsocketFlightData flightData, @Param("index") Integer index);
}
