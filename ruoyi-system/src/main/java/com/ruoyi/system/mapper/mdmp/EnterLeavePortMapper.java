package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.EnterLeavePort;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 */
public interface EnterLeavePortMapper {

    EnterLeavePort queryById(@Param("id") Integer id);

    EnterLeavePort selectByFlightId(@Param("flightId") Integer flightId);
    /**
     * 通过航班ID查询进离场详情信息
     *
     * @param flightId 主键
     * @return 实例对象
     */
    EnterLeavePort queryByFlightId(@Param("flightId") Integer flightId);

    /**
     * 新增数据
     *
     * @param enterLeavePort 实例对象
     * @return 影响行数
     */
    int insert(EnterLeavePort enterLeavePort);

    /**
     * 更新数据
     *
     * @param enterLeavePort 实例对象
     * @return 影响行数
     */
    int update(EnterLeavePort enterLeavePort);
}
