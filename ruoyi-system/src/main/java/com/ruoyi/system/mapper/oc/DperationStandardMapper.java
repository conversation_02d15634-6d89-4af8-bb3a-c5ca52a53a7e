package com.ruoyi.system.mapper.oc;


import com.ruoyi.system.domain.oc.entity.DperationStandard;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/24 11:06
 * @mood 功能
 */
public interface DperationStandardMapper {
    /**
     * 查询列表
     */
    public List<DperationStandard> selectList(DperationStandard dperationStandard);
    /**
     * 查询一条
     */
    public DperationStandard selectOneById(Long id);

    /**
     * 新增一条
     */
    public int insertOne(DperationStandard param);

    /**
     * 修改一条
     */
    public int updateOne(DperationStandard param);

    /**
     * 删除一条
     */
    public int deleteOneById(Long id);

    /**
     * 批量删除
     */
    public int deleteAllByIds(Long[] ids);

}
