package com.ruoyi.system.mapper.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.AircraftMp;
import com.ruoyi.system.domain.mdmp.TemperatureMpData;
import com.ruoyi.system.domain.mdmp.ThunderstormMpData;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import com.ruoyi.system.domain.mdmp.vo.MapDataVo;
import com.ruoyi.system.param.mdmp.MeteParam;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface ThunderstormMpDataMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public ThunderstormMpData selectThunderstormMpDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param thunderstormMpData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<ThunderstormMpData> selectThunderstormMpDataList(ThunderstormMpData thunderstormMpData);

    /**
     * 新增【请填写功能名称】
     *
     * @param thunderstormMpData 【请填写功能名称】
     * @return 结果
     */
    public int insertThunderstormMpData(ThunderstormMpData thunderstormMpData);

    /**
     * 修改【请填写功能名称】
     *
     * @param thunderstormMpData 【请填写功能名称】
     * @return 结果
     */
    public int updateThunderstormMpData(ThunderstormMpData thunderstormMpData);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteThunderstormMpDataById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteThunderstormMpDataByIds(Long[] ids);

    void deleteThunderstormMpDataByFcstTimeSequence(@Param("time")Long time, @Param("altitude") Double altitude);

    void deleteThunderstormMpDataByReatimeTimeSequence(@Param("time")Long time, @Param("altitude") Double altitude);

    void insertMpDataList(@Param("param")List<ThunderstormMpData> mpDataList);

    List<Long> selectOldVMpDataList();

    void deleteVMpDataByIds(Long[] batchIds);

    List<ThunderstormMpData> getList(@Param("param")MeteParam meteParam);

    int deleteOldMpDataList();

    List<MapDataVo> getThunderstormMapData(@Param("time")long time, @Param("param")AircraftMp aircraftMp);

    MapDataVo getThunderstormAlerts(@Param("time")long time,@Param("aircraftMp") AircraftMp aircraftMp,@Param("flightData") WebsocketFlightData flightData);
}
