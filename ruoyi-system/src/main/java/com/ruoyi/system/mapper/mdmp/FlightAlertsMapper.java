package com.ruoyi.system.mapper.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.FlightAlerts;
import com.ruoyi.system.domain.mdmp.MapData;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-21
 */
public interface FlightAlertsMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public FlightAlerts selectFlightAlertsById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param flightAlerts 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<FlightAlerts> selectFlightAlertsList(FlightAlerts flightAlerts);

    /**
     * 新增【请填写功能名称】
     *
     * @param flightAlerts 【请填写功能名称】
     * @return 结果
     */
    public int insertFlightAlerts(FlightAlerts flightAlerts);

    /**
     * 修改【请填写功能名称】
     *
     * @param flightAlerts 【请填写功能名称】
     * @return 结果
     */
    public int updateFlightAlerts(FlightAlerts flightAlerts);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteFlightAlertsById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFlightAlertsByIds(Long[] ids);


    List<FlightAlerts> selectFlightAlertsListByAircraftReg(@Param("aircraftReg") String aircraftReg, @Param("alertType") Long alertType);


    Long selectMaxVersion(@Param("aircraftReg") String aircraftReg);

    List<FlightAlerts> selectFlightAlertsLists(FlightAlerts flightAlerts);

    void batchUpdateSentStatus(@Param("ids") List<Long> ids);

    List<FlightAlerts> selectConnection(FlightAlerts flightAlerts);

    int insertBatchFlightAlerts(List<FlightAlerts> batchInsertList);


}
