package com.ruoyi.system.mapper.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.FlightPlanAircraftModel;
import com.ruoyi.system.domain.mdmp.FlightPlanAirport;
import org.apache.ibatis.annotations.Param;

/**
 * 航班计划关联机型Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface FlightPlanAircraftModelMapper {
    /**
     * 查询航班计划关联机型
     *
     * @param id 航班计划关联机型主键
     * @return 航班计划关联机型
     */
    public FlightPlanAircraftModel selectFlightPlanAircraftModelById(Long id);

    /**
     * 通过航班计划Id查询航班计划关联机型
     *
     * @param flightPlanId 航班计划Id
     * @return 航班计划关联机型
     */
    List<FlightPlanAircraftModel> selectByFlightPlanIdAndOPlanType(@Param("flightPlanId") Long flightPlanId, @Param("planType") Integer planType);

    /**
     * 查询航班计划关联机型列表
     *
     * @param flightPlanAircraftModel 航班计划关联机型
     * @return 航班计划关联机型集合
     */
    public List<FlightPlanAircraftModel> selectFlightPlanAircraftModelList(FlightPlanAircraftModel flightPlanAircraftModel);

    /**
     * 新增航班计划关联机型
     *
     * @param flightPlanAircraftModel 航班计划关联机型
     * @return 结果
     */
    public int insertFlightPlanAircraftModel(FlightPlanAircraftModel flightPlanAircraftModel);

    /**
     * 修改航班计划关联机型
     *
     * @param flightPlanAircraftModel 航班计划关联机型
     * @return 结果
     */
    public int updateFlightPlanAircraftModel(FlightPlanAircraftModel flightPlanAircraftModel);

    /**
     * 删除航班计划关联机型
     *
     * @param id 航班计划关联机型主键
     * @return 结果
     */
    public int deleteFlightPlanAircraftModelById(Long id);

    /**
     * 批量删除航班计划关联机型
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFlightPlanAircraftModelByIds(Long[] ids);

    /**
     * 批量新增航班计划关联机型
     *
     * @param flightPlanAircraftModelList 航班计划关联机型
     * @return 结果
     */
    int insertAllFlightPlanAircraftModel(@Param("flightPlanAircraftModelList") List<FlightPlanAircraftModel> flightPlanAircraftModelList);

    int deleteAllByFlightPlanId(@Param("flightPlanId") Long flightPlanId,@Param("planType") Integer planType);

    List<FlightPlanAircraftModel> selectAircraftByFlightPlanId(FlightPlanAircraftModel flightPlanAircraftModel);

    /**
     * 通过航班计划Id查询航班计划关联机型
     *
     * @param flightPlanId 航班计划Id
     * @param aircraftReg 机尾号
     * @return 航班计划关联机型
     */
    FlightPlanAircraftModel selectByFlightPlanIdAndTailNumber(@Param("flightPlanId") Long flightPlanId, @Param("aircraftReg") String aircraftReg);
}
