package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RouteLongLat;
import org.apache.ibatis.annotations.Param;

/**
 * 航线经纬度Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface RouteLongLatMapper
{
    /**
     * 查询航线经纬度
     *
     * @param id 航线经纬度主键
     * @return 航线经纬度
     */
    public RouteLongLat selectRouteLongLatById(Long id);

    /**
     * 通过航线id查询航线经纬度列表
     *
     * @param routeId 航线id
     * @return 航线经纬度集合
     */
    List<RouteLongLat> selectByRouteId(Long routeId);

    /**
     * 查询航线经纬度列表
     *
     * @param routeLongLat 航线经纬度
     * @return 航线经纬度集合
     */
    public List<RouteLongLat> selectRouteLongLatList(RouteLongLat routeLongLat);

    /**
     * 新增航线经纬度
     *
     * @param routeLongLat 航线经纬度
     * @return 结果
     */
    public int insertRouteLongLat(RouteLongLat routeLongLat);

    /**
     * 修改航线经纬度
     *
     * @param routeLongLat 航线经纬度
     * @return 结果
     */
    public int updateRouteLongLat(RouteLongLat routeLongLat);

    /**
     * 删除航线经纬度
     *
     * @param id 航线经纬度主键
     * @return 结果
     */
    public int deleteRouteLongLatById(Long id);

    /**
     * 批量删除航线经纬度
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRouteLongLatByIds(Long[] ids);


    //批量插入经纬度
    public int bulkInsertRouteLongLats(@Param("routeLongLatList") List<RouteLongLat> routeLongLatList);

    int deleteRouteLongLatByRouteId(Long id);
}
