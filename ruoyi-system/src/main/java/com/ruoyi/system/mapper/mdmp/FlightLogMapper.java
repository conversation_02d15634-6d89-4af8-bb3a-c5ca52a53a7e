package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.FlightLog;
import com.ruoyi.system.domain.mdmp.dto.QueryFlightLogListDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FlightLogMapper {

    /**
     * 查询当天所有航班操作日志
     *
     * @return 结果
     */
    List<FlightLog> queryAll();

    /**
     * 通过航班ID查询航班操作日志
     *
     * @param flightId 主键
     * @return 操作日志结婚
     */
    List<FlightLog> queryByFlightId(@Param("flightId") Integer flightId,@Param("deptCodeList") List<String> deptCodeList);

    List<FlightLog> selectFlightLogList(FlightLog flightLog);

    List<FlightLog> selectFlightLog(QueryFlightLogListDTO dto);

    /**
     * 新增数据
     *
     * @param flightLog 实例对象
     * @return 影响行数
     */
    int insert(FlightLog flightLog);

    /**
     * 批量新增数据
     *
     * @param entities List<FlightLog> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<FlightLog> entities);

    /**
     * 删除数据
     *
     * @param flightId 航班ID
     * @return 影响行数
     */
    int deleteByFlightId(@Param("flightId") Integer flightId);
}
