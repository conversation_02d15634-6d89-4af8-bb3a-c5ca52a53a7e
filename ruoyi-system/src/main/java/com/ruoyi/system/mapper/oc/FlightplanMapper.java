package com.ruoyi.system.mapper.oc;

import com.ruoyi.system.domain.oc.dto.QueryFlightPlanDTO;
import com.ruoyi.system.domain.oc.entity.Flightplan;
import com.ruoyi.system.domain.oc.vo.PilotVO;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 航班计划信息Mapper接口
 *
 * <AUTHOR>
 * @date 2021-09-22
 */
@Mapper
public interface FlightplanMapper
{
    /**
     * 查询航班计划信息
     *
     * @param flightplanId 航班计划信息主键
     * @return 航班计划信息
     */
    public Flightplan selectFlightplanByFlightplanId(Long flightplanId);

    /**
     * 查询航班计划信息列表
     *
     * @param flightplan 航班计划信息
     * @return 航班计划信息集合
     */
    public List<Flightplan> selectFlightplanList(Flightplan flightplan);

    /**
     * 查询航班计划列表
     *
     * @param dto 请求参数
     * @return 航班计划列表
     */
    List<Flightplan> queryFlightPlanList(QueryFlightPlanDTO dto);

    /**
     * 查询航班计划信息列表
     *
     * @param flightDate 航班日期
     * @param companyCode 航司代码
     * @return 航班计划信息集合
     */
    List<Flightplan> selectByFlightDate(@Param("flightDate") String flightDate, @Param("companyCode") String companyCode);

    /**
     * 新增航班计划信息
     *
     * @param flightplan 航班计划信息
     * @return 结果
     */
    public int insertFlightplan(Flightplan flightplan);
    public int batchInsertFlightplan(List<Flightplan> flightplanList);

    /**
     * 修改航班计划信息
     *
     * @param flightplan 航班计划信息
     * @return 结果
     */
     int updateFlightplan(Flightplan flightplan);

    /**
     * 删除航班计划信息
     *
     * @param flightplanId 航班计划信息主键
     * @return 结果
     */
    public int deleteFlightplanByFlightplanId(Long flightplanId);

    /**
     * 批量删除航班计划信息
     *
     * @param flightplanIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFlightplanByFlightplanIds(Long[] flightplanIds);

    /**
     * 批量删除飞行器信息
     *
     * @param flightplanIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAircraftByAircraftIds(Long[] flightplanIds);

    /**
     * 批量删除班次信息
     *
     * @param flightplanIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSortiesByflightplanIds(Long[] flightplanIds);


    /**
     * 通过航班计划信息主键删除飞行器信息信息
     *
     * @param flightplanId 航班计划信息ID
     * @return 结果
     */
    public int deleteAircraftByAircraftId(Long flightplanId);

    /**
     * 查询日期区间内，当前飞行员执飞过的航班
     *
     * @param userId 用户ID
     * @param startDate 生效日期
     * @param endDate 失效日期
     * @return 结果
     */
    List<Flightplan> selectByUserIdAndFlightDateBetween(@Param("userId") String userId, @Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询某个时间段的航班计划
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param companyCode 航司代码
     * @return 结果
     */
    List<Flightplan> selectByFlightDateBetween(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("companyCode") String companyCode);

    /**
     * 查询某个时间段的特定任务类型航班计划
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param flightPurpose 任务类型
     * @param companyCode 航司代码
     * @return 结果
     */
    List<Flightplan> selectByFlightPurpose(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("flightPurpose") String flightPurpose, @Param("companyCode") String companyCode);

    /**
     * 查询某个时间段的特定任务类型航班计划
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @param flightPurposes 任务类型集合
     * @param companyCode 航司代码
     * @return 结果
     */
    List<Flightplan> selectByFlightPurposes(@Param("startDate") String startDate, @Param("endDate") String endDate, @Param("flightPurposes") List<String> flightPurposes, @Param("companyCode") String companyCode);

    /**
     * 通过航班计划ID查找前15的飞行员信息
     * @param flightPlanIds 航班计划ID
     * @return 结果
     */
    List<PilotVO> selectPilotsByFlightPlanIds(List<Long> flightPlanIds);
}
