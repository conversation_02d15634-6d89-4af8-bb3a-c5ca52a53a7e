package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.FlightCommandLog;
import org.apache.ibatis.annotations.Param;

import java.util.List;


public interface FlightCommandLogMapper {

    FlightCommandLog queryById(@Param("id") Integer id);

    List<FlightCommandLog> queryByFlightId(@Param("flightId") Integer flightId);

    List<FlightCommandLog> queryByDate(@Param("date") String date);

    int insert(FlightCommandLog flightCommandLog);

    int update(FlightCommandLog FlightCommandLog);
}
