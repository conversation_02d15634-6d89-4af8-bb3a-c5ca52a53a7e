package com.ruoyi.system.mapper.oc;



import com.ruoyi.system.domain.oc.entity.RefuelingRecord;

import java.util.List;

/**
 * 加油记录信息Mapper接口
 *
 * <AUTHOR>
 * @date 2021-09-22
 */
public interface RefuelingRecordMapper {
    /**
     * 查询加油记录信息
     *
     * @param refuelingRecordId 加油记录信息主键
     * @return 加油记录信息
     */
    public RefuelingRecord selectRefuelingRecordByRefuelingRecordId(Long refuelingRecordId);

    /**
     * 查询加油记录信息列表
     *
     * @param refuelingRecord 加油记录信息
     * @return 加油记录信息集合
     */
    public List<RefuelingRecord> selectRefuelingRecordList(RefuelingRecord refuelingRecord);

    /**
     * 新增加油记录信息
     *
     * @param refuelingRecord 加油记录信息
     * @return 结果
     */
    public int insertRefuelingRecord(RefuelingRecord refuelingRecord);

    /**
     * 修改加油记录信息
     *
     * @param refuelingRecord 加油记录信息
     * @return 结果
     */
    public int updateRefuelingRecord(RefuelingRecord refuelingRecord);

    /**
     * 删除加油记录信息
     *
     * @param refuelingRecordId 加油记录信息主键
     * @return 结果
     */
    public int deleteRefuelingRecordByRefuelingRecordId(Long refuelingRecordId);

    /**
     * 批量删除加油记录信息
     *
     * @param refuelingRecordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRefuelingRecordByRefuelingRecordIds(Long[] refuelingRecordIds);
}
