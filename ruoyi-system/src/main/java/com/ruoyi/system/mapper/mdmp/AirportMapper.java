package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.Airport;
import org.apache.ibatis.annotations.Param;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;

/**
 * 机场Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface AirportMapper
{
    /**
     * 查询机场
     *
     * @param id 机场主键
     * @return 机场
     */
    public Airport selectAirportById(Long id);

    /**
     * 查询机场列表
     *
     * @param airport 机场
     * @return 机场集合
     */
    public List<Airport> selectAirportList(Airport airport);

    /**
     * 新增机场
     *
     * @param airport 机场
     * @return 结果
     */
    public int insertAirport(Airport airport);

    /**
     * 修改机场
     *
     * @param airport 机场
     * @return 结果
     */
    public int updateAirport(Airport airport);

    /**
     * 删除机场
     *
     * @param id 机场主键
     * @return 结果
     */
    public int deleteAirportById(Long id);

    /**
     * 批量删除机场
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAirportByIds(Long[] ids);
}
