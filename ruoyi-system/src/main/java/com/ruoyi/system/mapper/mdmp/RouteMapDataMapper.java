package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.RouteMapData;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Set;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface RouteMapDataMapper {


    List<RouteMapData> selectAllByRouteId(@Param("routeId") Long routeId);


    int delByRouteId(@Param("routeId") Long routeId);


    int insertAllRouteMapData(@Param("routeMapData") Set<RouteMapData> routeMapData);
}
