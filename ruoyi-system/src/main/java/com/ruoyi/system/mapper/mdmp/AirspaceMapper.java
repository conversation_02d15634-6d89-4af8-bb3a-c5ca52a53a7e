package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.common.config.rule.Obstacle;
import com.ruoyi.system.domain.mdmp.Airspace;
import com.ruoyi.system.domain.mdmp.vo.AirspaceListVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 空域Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface AirspaceMapper {
    /**
     * 查询空域
     *
     * @param id 空域主键
     * @return 空域
     */
    public Airspace selectAirspaceById(Long id);

    /**
     * 查询空域列表
     *
     * @param ids 空域主键
     * @return 空域列表
     */
    List<Airspace> selectAirspaceByIds(@Param("ids") List<Long> ids);

    /**
     * 查询空域列表
     *
     * @param airspace 空域
     * @return 空域集合
     */
    public List<Airspace> selectAirspaceList(Airspace airspace);


    List<Obstacle> selectAirspaceListByCodeAndType(@Param("deptCodeList") List<String> deptCodeList, @Param("airspaceType") Integer airspaceType);

    /**
     * 查询所有空域
     *
     * @param date 日期
     * @return 空域集合
     */
    List<Airspace> selectAll(String date);

    /**
     * 新增空域
     *
     * @param airspace 空域
     * @return 结果
     */
    public int insertAirspace(Airspace airspace);

    /**
     * 修改空域
     *
     * @param airspace 空域
     * @return 结果
     */
    public int updateAirspace(Airspace airspace);

    /**
     * 删除空域
     *
     * @param id 空域主键
     * @return 结果
     */
    public int deleteAirspaceById(Long id);

    /**
     * 批量删除空域
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAirspaceByIds(Long[] ids);

    List<AirspaceListVO> selectAirspaceListAndMap(@Param("ids") Long[] ids, @Param("deptCodeList") List<String> deptCodeList);
}
