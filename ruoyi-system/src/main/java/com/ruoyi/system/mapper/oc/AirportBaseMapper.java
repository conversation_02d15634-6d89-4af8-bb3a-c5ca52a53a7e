package com.ruoyi.system.mapper.oc;

import com.ruoyi.system.domain.oc.AirportBase;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/18 11:15
 * @mood 功能
 */
public interface AirportBaseMapper {
    /**
     * 查询列表
     */
    public List<AirportBase> selectList(AirportBase airportBase);

    /**
     * 查询一条
     */
    public AirportBase selectOneById(Long id);

    /**
     * 新增一条
     */
    public int insertOne(AirportBase param);

    /**
     * 修改一条
     */
    public int updateOne(AirportBase param);

    /**
     * 删除一条
     */
    public int deleteOneById(Long id);

    /**
     * 批量删除
     */
    public int deleteAllByIds(Long[] ids);
}
