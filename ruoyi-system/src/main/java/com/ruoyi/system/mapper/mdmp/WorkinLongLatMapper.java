package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.WorkinLongLat;
import org.apache.ibatis.annotations.Param;

/**
 * 作业区内经纬度Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface WorkinLongLatMapper
{
    /**
     * 查询作业区内经纬度
     *
     * @param id 作业区内经纬度主键
     * @return 作业区内经纬度
     */
    public WorkinLongLat selectWorkinLongLatById(Long id);

    /**
     * 查询作业区内经纬度列表
     *
     * @param workinLongLat 作业区内经纬度
     * @return 作业区内经纬度集合
     */
    public List<WorkinLongLat> selectWorkinLongLatList(WorkinLongLat workinLongLat);

    /**
     * 新增作业区内经纬度
     *
     * @param workinLongLat 作业区内经纬度
     * @return 结果
     */
    public int insertWorkinLongLat(WorkinLongLat workinLongLat);

    /**
     * 修改作业区内经纬度
     *
     * @param workinLongLat 作业区内经纬度
     * @return 结果
     */
    public int updateWorkinLongLat(WorkinLongLat workinLongLat);

    /**
     * 删除作业区内经纬度
     *
     * @param id 作业区内经纬度主键
     * @return 结果
     */
    public int deleteWorkinLongLatById(Long id);

    /**
     * 批量删除作业区内经纬度
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWorkinLongLatByIds(Long[] ids);

    /**
     * 批量新增作业区内经纬度
     * @param workinLongLatList
     * @return
     */
    int insertAllWorkinLongLat(@Param("workinLongLatList") List<WorkinLongLat> workinLongLatList);

    /**
     * 查询作业区内经纬度
     *
     * @param flightPlanWorkInId 作业区内关联表id
     * @return 作业区内经纬度
     */
    List<WorkinLongLat> selectByFlightPlanWorkInId(Long flightPlanWorkInId);
}
