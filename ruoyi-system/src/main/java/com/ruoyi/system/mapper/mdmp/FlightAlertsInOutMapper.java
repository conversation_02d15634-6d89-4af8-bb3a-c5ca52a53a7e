package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.AircraftFlightAlertsInOut;
import com.ruoyi.system.domain.mdmp.FlightAlertsInOut;
import org.apache.ibatis.annotations.Param;

/**
 * <AUTHOR>
 * @date ：Created in 2025/6/10 15:59
 * @description：
 * @modified By：
 * @version: $
 */
public interface FlightAlertsInOutMapper {
    FlightAlertsInOut selectByAircraftRegAndWorkId(@Param("aircraftReg")String aircraftReg);

    void insertFlightAlertsInOut(FlightAlertsInOut flightAlertsInOut);

    void updateFlightAlertsInOut(FlightAlertsInOut flightAlertsInOutAfter);

    AircraftFlightAlertsInOut selectFlightAlertsInOut(@Param("aircraftReg")String aircraftReg);
}
