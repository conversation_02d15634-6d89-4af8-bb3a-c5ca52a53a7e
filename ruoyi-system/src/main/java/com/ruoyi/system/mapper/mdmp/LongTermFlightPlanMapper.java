package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.LongTermFlightPlan;
import com.ruoyi.system.param.mdmp.LongTermFlightPlanListParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 长期飞行计划Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface LongTermFlightPlanMapper {
    /**
     * 查询长期飞行计划
     *
     * @param id 长期飞行计划主键
     * @return 长期飞行计划
     */
    public LongTermFlightPlan selectLongTermFlightPlanById(Long id);


    /**
     * 查询长期飞行计划
     *
     * @param id 长期飞行计划主键
     * @return 长期飞行计划
     */
    public LongTermFlightPlan selectLongTermFlightPlanByIdAndStatus(Long id);


    /**
     * 查询长期飞行计划列表
     *
     * @param longTermFlightPlan 长期飞行计划
     * @return 长期飞行计划集合
     */
    public List<LongTermFlightPlan> selectLongTermFlightPlanList(LongTermFlightPlanListParam longTermFlightPlan);

    /**
     * 新增长期飞行计划
     *
     * @param longTermFlightPlan 长期飞行计划
     * @return 结果
     */
    public int insertLongTermFlightPlan(LongTermFlightPlan longTermFlightPlan);

    /**
     * 修改长期飞行计划
     *
     * @param longTermFlightPlan 长期飞行计划
     * @return 结果
     */
    public int updateLongTermFlightPlan(LongTermFlightPlan longTermFlightPlan);

    /**
     * 删除长期飞行计划
     *
     * @param id 长期飞行计划主键
     * @return 结果
     */
    public int deleteLongTermFlightPlanById(Long id);

    /**
     * 批量删除长期飞行计划
     * 软删除
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteLongTermFlightPlanByIds(Long[] ids);

    /**
     * 查询所有 在改区间内的长期计划
     *
     * @param longTermFlightPlan
     * @return
     */
    List<LongTermFlightPlan> selectLongTermFlightPlanByInterval(@Param("longTermFlightPlan") LongTermFlightPlan longTermFlightPlan);
}
