package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.SingleFlightTask;
import com.ruoyi.system.param.mdmp.SingleFlightTaskListParam;

/**
 * 单一飞行任务Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface SingleFlightTaskMapper
{
    /**
     * 查询单一飞行任务
     *
     * @param id 单一飞行任务主键
     * @return 单一飞行任务
     */
    public SingleFlightTask selectSingleFlightTaskById(Long id);

    /**
     * 查询单一飞行任务列表
     *
     * @param singleFlightTask 单一飞行任务
     * @return 单一飞行任务集合
     */
    public List<SingleFlightTask> selectSingleFlightTaskList(SingleFlightTaskListParam singleFlightTask);

    /**
     * 新增单一飞行任务
     *
     * @param singleFlightTask 单一飞行任务
     * @return 结果
     */
    public int insertSingleFlightTask(SingleFlightTask singleFlightTask);

    /**
     * 修改单一飞行任务
     *
     * @param singleFlightTask 单一飞行任务
     * @return 结果
     */
    public int updateSingleFlightTask(SingleFlightTask singleFlightTask);

    /**
     * 删除单一飞行任务
     *
     * @param id 单一飞行任务主键
     * @return 结果
     */
    public int deleteSingleFlightTaskById(Long id);

    /**
     * 批量删除单一飞行任务
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSingleFlightTaskByIds(Long[] ids);
}
