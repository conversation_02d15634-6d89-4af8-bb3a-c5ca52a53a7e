package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.temporary.FlightDataTemporary;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface FlightDataTemporaryMapper {

    /**
     * 新增飞行数据
     *
     * @param flightDataTemporary 飞行数据
     * @return 结果
     */
    int insertFlightDataTemporary(FlightDataTemporary flightDataTemporary);

    /**
     * 通过机尾号查询
     * @param aircraftReg 机尾号
     * @return 结果
     */
    List<FlightDataTemporary> selectList(String aircraftReg);
    List<FlightDataTemporary> selectList3(String aircraftReg);
    List<FlightDataTemporary> selectList2(@Param("aircraftReg") String aircraftReg, @Param("callSign") String callSign);
}
