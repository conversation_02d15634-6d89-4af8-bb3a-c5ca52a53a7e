package com.ruoyi.system.mapper.oc;



import com.ruoyi.system.domain.oc.entity.MaintenanceRecord;

import java.util.List;

/**
 * 维修记录信息Mapper接口
 *
 * <AUTHOR>
 * @date 2021-09-22
 */
public interface MaintenanceRecordMapper {
    /**
     * 查询维修记录信息
     *
     * @param maintenanceRecordId 维修记录信息主键
     * @return 维修记录信息
     */
    public MaintenanceRecord selectMaintenanceRecordByMaintenanceRecordId(Long maintenanceRecordId);

    /**
     * 查询维修记录信息列表
     *
     * @param maintenanceRecord 维修记录信息
     * @return 维修记录信息集合
     */
    public List<MaintenanceRecord> selectMaintenanceRecordList(MaintenanceRecord maintenanceRecord);

    /**
     * 新增维修记录信息
     *
     * @param maintenanceRecord 维修记录信息
     * @return 结果
     */
    public int insertMaintenanceRecord(MaintenanceRecord maintenanceRecord);

    /**
     * 修改维修记录信息
     *
     * @param maintenanceRecord 维修记录信息
     * @return 结果
     */
    public int updateMaintenanceRecord(MaintenanceRecord maintenanceRecord);

    /**
     * 删除维修记录信息
     *
     * @param maintenanceRecordId 维修记录信息主键
     * @return 结果
     */
    public int deleteMaintenanceRecordByMaintenanceRecordId(Long maintenanceRecordId);

    /**
     * 批量删除维修记录信息
     *
     * @param maintenanceRecordIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMaintenanceRecordByMaintenanceRecordIds(Long[] maintenanceRecordIds);
}
