package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.AircraftMp;
import com.ruoyi.system.domain.mdmp.AirspaceLongLat;
import com.ruoyi.system.domain.mdmp.MapData;
import com.ruoyi.system.domain.mdmp.MapDataObstacle;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import com.ruoyi.system.domain.mdmp.vo.AddTopographicVo;
import com.ruoyi.system.domain.mdmp.vo.MapDataVo;
import com.ruoyi.system.domain.mdmp.vo.MeteInfo;
import com.ruoyi.system.domain.mdmp.vo.QueryMapDataLongLatVo;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 障碍物地图数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface MapDataObstacleMapper {


    int insertMapDataObstacle(@Param("mapDataObstacleList") List<MapDataObstacle> mapDataObstacleList);


    int deleteByAirspaceId(@Param("airspaceId") Long airspaceId);

    Integer selectObstacleByIndex(@Param("airspaceId")Long airspaceId,@Param("index") Integer index,@Param("param") WebsocketFlightData flightData);
}
