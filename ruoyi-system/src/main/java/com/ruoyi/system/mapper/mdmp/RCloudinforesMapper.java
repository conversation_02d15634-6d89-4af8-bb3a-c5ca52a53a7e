package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RCloudinfores;
import org.apache.ibatis.annotations.Param;

/**
 * 云Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface RCloudinforesMapper
{
    /**
     * 查询云
     *
     * @param id 云主键
     * @return 云
     */
    public RCloudinfores selectRCloudinforesById(Long id);

    /**
     * 查询云列表
     *
     * @param rCloudinfores 云
     * @return 云集合
     */
    public List<RCloudinfores> selectRCloudinforesList(RCloudinfores rCloudinfores);

    /**
     * 新增云
     *
     * @param rCloudinfores 云
     * @return 结果
     */
    public int insertRCloudinfores(RCloudinfores rCloudinfores);

    /**
     * 修改云
     *
     * @param rCloudinfores 云
     * @return 结果
     */
    public int updateRCloudinfores(RCloudinfores rCloudinfores);

    /**
     * 删除云
     *
     * @param id 云主键
     * @return 结果
     */
    public int deleteRCloudinforesById(Long id);

    /**
     * 批量删除云
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRCloudinforesByIds(Long[] ids);

    void insertRCloudinforesList(@Param("param") List<RCloudinfores> cloudResDtoList);
}
