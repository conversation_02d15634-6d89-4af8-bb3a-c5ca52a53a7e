package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.FlightPlanWorkLongLat;
import org.apache.ibatis.annotations.Param;

/**
 * 航班计划作业区经纬度Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface FlightPlanWorkLongLatMapper
{
    /**
     * 查询航班计划作业区经纬度
     *
     * @param id 航班计划作业区经纬度主键
     * @return 航班计划作业区经纬度
     */
    public FlightPlanWorkLongLat selectFlightPlanWorkLongLatById(Long id);

    /**
     * 查询航班计划作业区经纬度列表
     *
     * @param flightPlanWorkId 作业区id
     * @return 航班计划作业区经纬度列表
     */
    List<FlightPlanWorkLongLat> selectWorkLongLatByFlightPlanWorkId(Long flightPlanWorkId);

    /**
     * 查询航班计划作业区经纬度列表
     *
     * @param flightPlanWorkLongLat 航班计划作业区经纬度
     * @return 航班计划作业区经纬度集合
     */
    public List<FlightPlanWorkLongLat> selectFlightPlanWorkLongLatList(FlightPlanWorkLongLat flightPlanWorkLongLat);

    /**
     * 新增航班计划作业区经纬度
     *
     * @param flightPlanWorkLongLat 航班计划作业区经纬度
     * @return 结果
     */
    public int insertFlightPlanWorkLongLat(FlightPlanWorkLongLat flightPlanWorkLongLat);

    /**
     * 修改航班计划作业区经纬度
     *
     * @param flightPlanWorkLongLat 航班计划作业区经纬度
     * @return 结果
     */
    public int updateFlightPlanWorkLongLat(FlightPlanWorkLongLat flightPlanWorkLongLat);

    /**
     * 删除航班计划作业区经纬度
     *
     * @param id 航班计划作业区经纬度主键
     * @return 结果
     */
    public int deleteFlightPlanWorkLongLatById(Long id);

    /**
     * 批量删除航班计划作业区经纬度
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFlightPlanWorkLongLatByIds(Long[] ids);

    /**
     * 批量新增航班计划作业区经纬度
     *
     * @param flightPlanWorkLongLatList 航班计划作业区经纬度
     * @return 结果
     */
    int insertAllFlightPlanWorkLongLat(@Param("flightPlanWorkLongLatList") List<FlightPlanWorkLongLat> flightPlanWorkLongLatList);
}
