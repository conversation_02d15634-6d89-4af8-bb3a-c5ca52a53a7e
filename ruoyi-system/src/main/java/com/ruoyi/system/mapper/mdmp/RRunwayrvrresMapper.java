package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RRunwayrvrres;
import org.apache.ibatis.annotations.Param;

/**
 * 跑道信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface RRunwayrvrresMapper
{
    /**
     * 查询跑道信息
     *
     * @param id 跑道信息主键
     * @return 跑道信息
     */
    public RRunwayrvrres selectRRunwayrvrresById(Long id);

    /**
     * 查询跑道信息列表
     *
     * @param rRunwayrvrres 跑道信息
     * @return 跑道信息集合
     */
    public List<RRunwayrvrres> selectRRunwayrvrresList(RRunwayrvrres rRunwayrvrres);

    /**
     * 新增跑道信息
     *
     * @param rRunwayrvrres 跑道信息
     * @return 结果
     */
    public int insertRRunwayrvrres(RRunwayrvrres rRunwayrvrres);

    /**
     * 修改跑道信息
     *
     * @param rRunwayrvrres 跑道信息
     * @return 结果
     */
    public int updateRRunwayrvrres(RRunwayrvrres rRunwayrvrres);

    /**
     * 删除跑道信息
     *
     * @param id 跑道信息主键
     * @return 结果
     */
    public int deleteRRunwayrvrresById(Long id);

    /**
     * 批量删除跑道信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRRunwayrvrresByIds(Long[] ids);

    void insertRRunwayrvrresList(@Param("param") List<RRunwayrvrres> runwayRvrResDtoList);
}
