package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.AirspaceLongLat;
import org.apache.ibatis.annotations.Param;

/**
 * 空域经纬度Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface AirspaceLongLatMapper
{
    /**
     * 查询空域经纬度
     *
     * @param id 空域经纬度主键
     * @return 空域经纬度
     */
    public AirspaceLongLat selectAirspaceLongLatById(Long id);

    /**
     * 通过空域id查询空域经纬度列表
     *
     * @param airspaceId 空域id
     * @return 空域经纬度集合
     */
    List<AirspaceLongLat> selectByAirspaceId(Long airspaceId);

    /**
     * 查询空域经纬度列表
     *
     * @param airspaceLongLat 空域经纬度
     * @return 空域经纬度集合
     */
    public List<AirspaceLongLat> selectAirspaceLongLatList(AirspaceLongLat airspaceLongLat);

    /**
     * 新增空域经纬度
     *
     * @param airspaceLongLat 空域经纬度
     * @return 结果
     */
    public int insertAirspaceLongLat(AirspaceLongLat airspaceLongLat);

    /**
     * 修改空域经纬度
     *
     * @param airspaceLongLat 空域经纬度
     * @return 结果
     */
    public int updateAirspaceLongLat(AirspaceLongLat airspaceLongLat);

    /**
     * 删除空域经纬度
     *
     * @param id 空域经纬度主键
     * @return 结果
     */
    public int deleteAirspaceLongLatById(Long id);

    /**
     * 批量删除空域经纬度
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAirspaceLongLatByIds(Long[] ids);


    int insertAirspaceLongLatList(@Param("airspaceLongLatList") List<AirspaceLongLat> airspaceLongLatList);

    int deleteAirspaceLongLatByAirspaceId(Long id);
}
