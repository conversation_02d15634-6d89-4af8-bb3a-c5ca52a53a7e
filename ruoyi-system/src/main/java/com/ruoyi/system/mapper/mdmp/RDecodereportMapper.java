package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RDecodereport;
import com.ruoyi.system.domain.mdmp.vo.DecodeReportVo;

/**
 * 报文信息Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface RDecodereportMapper
{
    /**
     * 查询报文信息
     *
     * @param id 报文信息主键
     * @return 报文信息
     */
    public RDecodereport selectRDecodereportById(Long id);

    /**
     * 查询报文信息列表
     *
     * @param rDecodereport 报文信息
     * @return 报文信息集合
     */
    public List<RDecodereport> selectRDecodereportList(RDecodereport rDecodereport);

    /**
     * 新增报文信息
     *
     * @param rDecodereport 报文信息
     * @return 结果
     */
    public int insertRDecodereport(RDecodereport rDecodereport);

    /**
     * 修改报文信息
     *
     * @param rDecodereport 报文信息
     * @return 结果
     */
    public int updateRDecodereport(RDecodereport rDecodereport);

    /**
     * 删除报文信息
     *
     * @param id 报文信息主键
     * @return 结果
     */
    public int deleteRDecodereportById(Long id);

    /**
     * 批量删除报文信息
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRDecodereportByIds(Long[] ids);

    List<DecodeReportVo> selectRDecodereportListByTime();

    List<String> selectNewContentList();
}
