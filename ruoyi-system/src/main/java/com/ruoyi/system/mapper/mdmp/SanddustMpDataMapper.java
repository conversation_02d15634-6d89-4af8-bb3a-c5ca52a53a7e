package com.ruoyi.system.mapper.mdmp;

import java.math.BigDecimal;
import java.util.List;
import com.ruoyi.system.domain.mdmp.SanddustMpData;
import com.ruoyi.system.param.mdmp.MeteParam;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-22
 */
public interface SanddustMpDataMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param dataValue 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public SanddustMpData selectSanddustMpDataByDataValue(BigDecimal dataValue);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param sanddustMpData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<SanddustMpData> selectSanddustMpDataList(SanddustMpData sanddustMpData);

    /**
     * 新增【请填写功能名称】
     *
     * @param sanddustMpData 【请填写功能名称】
     * @return 结果
     */
    public int insertSanddustMpData(SanddustMpData sanddustMpData);

    /**
     * 修改【请填写功能名称】
     *
     * @param sanddustMpData 【请填写功能名称】
     * @return 结果
     */
    public int updateSanddustMpData(SanddustMpData sanddustMpData);

    /**
     * 删除【请填写功能名称】
     *
     * @param dataValue 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteSanddustMpDataByDataValue(BigDecimal dataValue);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param dataValues 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteSanddustMpDataByDataValues(BigDecimal[] dataValues);

    void insertsanddustMpDataList(@Param("param")List<SanddustMpData> batchData);

    List<Double> getList(@Param("param")MeteParam meteParam);

    void deleteAll();
}
