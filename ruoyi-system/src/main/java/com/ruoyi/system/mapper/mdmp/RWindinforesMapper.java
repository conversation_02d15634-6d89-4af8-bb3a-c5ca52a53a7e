package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.RWindinfores;

import java.util.List;


/**
 * 风Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface RWindinforesMapper
{
    /**
     * 查询风
     *
     * @param id 风主键
     * @return 风
     */
    public RWindinfores selectRWindinforesById(Long id);

    /**
     * 查询风列表
     *
     * @param rWindinfores 风
     * @return 风集合
     */
    public List<RWindinfores> selectRWindinforesList(RWindinfores rWindinfores);

    /**
     * 新增风
     *
     * @param rWindinfores 风
     * @return 结果
     */
    public int insertRWindinfores(RWindinfores rWindinfores);

    /**
     * 修改风
     *
     * @param rWindinfores 风
     * @return 结果
     */
    public int updateRWindinfores(RWindinfores rWindinfores);

    /**
     * 删除风
     *
     * @param id 风主键
     * @return 结果
     */
    public int deleteRWindinforesById(Long id);

    /**
     * 批量删除风
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRWindinforesByIds(Long[] ids);
}
