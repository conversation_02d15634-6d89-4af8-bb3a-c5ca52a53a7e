package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.MpTimeList;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface MpTimeListMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public MpTimeList selectMpTimeListById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param mpTimeList 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<MpTimeList> selectMpTimeListList(MpTimeList mpTimeList);

    /**
     * 新增【请填写功能名称】
     *
     * @param mpTimeList 【请填写功能名称】
     * @return 结果
     */
    public int insertMpTimeList(MpTimeList mpTimeList);

    /**
     * 修改【请填写功能名称】
     *
     * @param mpTimeList 【请填写功能名称】
     * @return 结果
     */
    public int updateMpTimeList(MpTimeList mpTimeList);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteMpTimeListById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMpTimeListByIds(Long[] ids);


    List<MpTimeList> selectMpTimeListByMpTimeLine(@Param("dataSource") Integer dataSource, @Param("dateType") Integer dateType,@Param("altitude")Double altitude);


}
