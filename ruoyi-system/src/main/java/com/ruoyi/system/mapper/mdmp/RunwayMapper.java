package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.Runway;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface RunwayMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    Runway queryById(Integer id);

    /**
     * 查询所有数据
     *
     * @return 对象列表
     */
    List<Runway> queryAll();
    /**
     * 更新数据
     *
     * @param runway 实例对象
     * @return 影响行数
     */
    int update(Runway runway);
}
