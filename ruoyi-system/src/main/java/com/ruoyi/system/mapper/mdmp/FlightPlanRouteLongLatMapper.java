package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.FlightPlanRouteLongLat;
import com.ruoyi.system.domain.mdmp.RouteLongLat;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 航班航线经纬度Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface FlightPlanRouteLongLatMapper {

    int insertAllFlightPlanRouteLongLat(@Param("flightPlanRouteLongLatList") List<FlightPlanRouteLongLat> flightPlanRouteLongLatList);

    List<FlightPlanRouteLongLat> selectByFlightPlanRouteId(@Param("flightPlanRouteId") Long flightPlanRouteId);

    int deleteAllByFlightPlanRouteId(@Param("flightPlanRouteId") Long flightPlanRouteId);
}
