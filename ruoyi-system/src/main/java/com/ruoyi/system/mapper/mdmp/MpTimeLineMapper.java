package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.MpTimeLine;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
public interface MpTimeLineMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public MpTimeLine selectMpTimeLineById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param mpTimeLine 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<MpTimeLine> selectMpTimeLineList(MpTimeLine mpTimeLine);

    /**
     * 新增【请填写功能名称】
     *
     * @param mpTimeLine 【请填写功能名称】
     * @return 结果
     */
    public int insertMpTimeLine(MpTimeLine mpTimeLine);

    /**
     * 修改【请填写功能名称】
     *
     * @param mpTimeLine 【请填写功能名称】
     * @return 结果
     */
    public int updateMpTimeLine(MpTimeLine mpTimeLine);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteMpTimeLineById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteMpTimeLineByIds(Long[] ids);
}
