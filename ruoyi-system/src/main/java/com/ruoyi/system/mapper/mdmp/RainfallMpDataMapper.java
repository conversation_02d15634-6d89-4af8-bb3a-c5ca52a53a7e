package com.ruoyi.system.mapper.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.AircraftMp;
import com.ruoyi.system.domain.mdmp.RainfallMpData;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import com.ruoyi.system.domain.mdmp.vo.MapDataVo;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface RainfallMpDataMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public RainfallMpData selectRainfallMpDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param rainfallMpData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<RainfallMpData> selectRainfallMpDataList(RainfallMpData rainfallMpData);

    /**
     * 新增【请填写功能名称】
     *
     * @param rainfallMpData 【请填写功能名称】
     * @return 结果
     */
    public int insertRainfallMpData(RainfallMpData rainfallMpData);

    /**
     * 修改【请填写功能名称】
     *
     * @param rainfallMpData 【请填写功能名称】
     * @return 结果
     */
    public int updateRainfallMpData(RainfallMpData rainfallMpData);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteRainfallMpDataById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRainfallMpDataByIds(Long[] ids);

    void insertVisibilityMpDataList(@Param("param") List<RainfallMpData> mpDataList);

    List<Long> selectOldVMpDataList();

    void deleteVMpDataByIds(Long[] batchIds);

    RainfallMpData selectRainfallMpDataByMeteInfo(@Param("param")MeteInfoParam meteInfoParam);

    List<Double> getList(@Param("param")MeteParam meteParam);

    void deleteRainfallMpDataByFcstTimeSequence(@Param("time")Long time);

    int deleteOldMpDataList();

    List<MapDataVo> getRainfallMpMapData(@Param("time")long time, @Param("param")AircraftMp aircraftMp);

    MapDataVo getRainfallAlerts(@Param("time")long time,@Param("aircraftMp") AircraftMp aircraftMp,@Param("flightData") WebsocketFlightData flightData);
}
