package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.Flight;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FlightMapper {

    /**
     * 通过ID查询单条数据
     *
     * @param id 主键
     * @return 实例对象
     */
    Flight queryById(@Param("id") Integer id);

    /**
     * 查询当天未删除的所有航班
     * @return 结果
     */
    List<Flight> queryAll(@Param("deptCodeList")List<String> deptCodeList);

    /**
     * 分页查询指定行数据
     *
     * @param flight 查询条件
     * @return 对象列表
     */
    List<Flight> selectFlightList(Flight flight);

    /**
     * 新增数据
     *
     * @param flight  实例对象
     * @return 影响行数
     */
    int insert(Flight flight);

    /**
     * 批量新增数据
     *
     * @param entities List<Flight> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<Flight> entities);
    /**
     * 批量新增或按主键更新数据
     *
     * @param entities List<Flight> 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<Flight> entities);

    /**
     * 修改数据
     *
     * @param flight  实例对象
     * @return 影响行数
     */
    int update(Flight flight);

    /**
     * 修改数据
     *
     * @param id  id
     * @return 影响行数
     */
    int delete(Integer id);
}
