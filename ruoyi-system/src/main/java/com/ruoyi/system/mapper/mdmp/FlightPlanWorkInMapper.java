package com.ruoyi.system.mapper.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.FlightPlanWorkIn;
import org.apache.ibatis.annotations.Param;

/**
 * 航班计划作业区内关联表Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface FlightPlanWorkInMapper {
    /**
     * 查询航班计划作业区内关联表
     *
     * @param id 航班计划作业区内关联表主键
     * @return 航班计划作业区内关联表
     */
    public FlightPlanWorkIn selectFlightPlanWorkInById(Long id);

    /**
     * 查询航班计划作业区内关联表列表
     *
     * @param flightPlanWorkIn 航班计划作业区内关联表
     * @return 航班计划作业区内关联表集合
     */
    public List<FlightPlanWorkIn> selectFlightPlanWorkInList(FlightPlanWorkIn flightPlanWorkIn);

    /**
     * 新增航班计划作业区内关联表
     *
     * @param flightPlanWorkIn 航班计划作业区内关联表
     * @return 结果
     */
    public int insertFlightPlanWorkIn(FlightPlanWorkIn flightPlanWorkIn);

    /**
     * 修改航班计划作业区内关联表
     *
     * @param flightPlanWorkIn 航班计划作业区内关联表
     * @return 结果
     */
    public int updateFlightPlanWorkIn(FlightPlanWorkIn flightPlanWorkIn);

    /**
     * 删除航班计划作业区内关联表
     *
     * @param id 航班计划作业区内关联表主键
     * @return 结果
     */
    public int deleteFlightPlanWorkInById(Long id);

    /**
     * 批量删除航班计划作业区内关联表
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFlightPlanWorkInByIds(Long[] ids);

    /**
     * 查询航班计划作业区内列表
     *
     * @param flightPlanWorkId 作业区Id
     * @return 航班计划作业区内关联表列表
     */
    List<FlightPlanWorkIn> selectByFlightPlanWorkId(Long flightPlanWorkId);
}
