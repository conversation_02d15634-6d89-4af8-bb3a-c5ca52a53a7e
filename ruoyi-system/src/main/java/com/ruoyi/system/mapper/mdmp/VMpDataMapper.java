package com.ruoyi.system.mapper.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.VMpData;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-12-30
 */
public interface VMpDataMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public VMpData selectVMpDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param vMpData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<VMpData> selectVMpDataList(VMpData vMpData);

    /**
     * 新增【请填写功能名称】
     *
     * @param vMpData 【请填写功能名称】
     * @return 结果
     */
    public int insertVMpData(VMpData vMpData);

    /**
     * 修改【请填写功能名称】
     *
     * @param vMpData 【请填写功能名称】
     * @return 结果
     */
    public int updateVMpData(VMpData vMpData);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteVMpDataById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVMpDataByIds(Long[] ids);

    void deleteMpDataByFcstTimeSequence(@Param("time") Long time, @Param("altitude") Double altitude);

    void insertMpDataList(@Param("param") List<VMpData> mpDataList);

    VMpData selectVMpDataByMeteInfo(@Param("param")MeteInfoParam meteInfoParam);

    List<Long> selectOldVMpDataList();

    List<VMpData> getList(@Param("param")MeteParam meteParam);

    int deleteOldVMpDataList();
}
