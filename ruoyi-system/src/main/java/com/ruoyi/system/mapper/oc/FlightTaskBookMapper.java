package com.ruoyi.system.mapper.oc;

import com.ruoyi.system.domain.oc.FlightTaskBook;
import com.ruoyi.system.domain.oc.FlightTaskBookExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FlightTaskBookMapper {
    long countByExample(FlightTaskBookExample example);

    int deleteByExample(FlightTaskBookExample example);

    int deleteByPrimaryKey(Long id);

    int insert(FlightTaskBook row);

    int insertSelective(FlightTaskBook row);

    List<FlightTaskBook> selectByExample(FlightTaskBookExample example);

    FlightTaskBook selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("row") FlightTaskBook row, @Param("example") FlightTaskBookExample example);

    int updateByExample(@Param("row") FlightTaskBook row, @Param("example") FlightTaskBookExample example);

    int updateByPrimaryKeySelective(FlightTaskBook row);

    int updateByPrimaryKey(FlightTaskBook row);
}