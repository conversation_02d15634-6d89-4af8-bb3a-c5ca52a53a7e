package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.AircraftMp;
import com.ruoyi.system.domain.mdmp.AirspaceLongLat;
import com.ruoyi.system.domain.mdmp.FlightPlanWork;
import com.ruoyi.system.domain.mdmp.MapData;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import com.ruoyi.system.domain.mdmp.vo.AddTopographicVo;
import com.ruoyi.system.domain.mdmp.vo.MapDataVo;
import com.ruoyi.system.domain.mdmp.vo.MeteInfo;
import com.ruoyi.system.domain.mdmp.vo.QueryMapDataLongLatVo;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import org.apache.ibatis.annotations.Param;

import java.math.BigDecimal;
import java.util.List;
import java.util.Map;


/**
 * 地图数据Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface MapDataMapper {


    List<MapData> selectMapDataList();

    MapData selectMapDataByIndex(@Param("index") Integer index);


    int insertMapData(@Param("mapDataList") List<MapData> mapDataList);

    int deleteAll();

    /**
     * 边界查询
     * @param startHeight
     * @param endHeight
     * @return
     */
    List<MapData> selectGridsInBoundary( @Param("minLon") BigDecimal minLon,
                                         @Param("maxLon") BigDecimal maxLon,
                                         @Param("minLat") BigDecimal minLat,
                                         @Param("maxLat") BigDecimal maxLat,
                                         @Param("startHeight") Integer startHeight,
                                         @Param("endHeight") Integer endHeight);


    List<MapData> selectMapDataByHeight(@Param("startHeight") Integer startHeight, @Param("endHeight") Integer endHeight);

    List<MapData> findIntersectingMapDataByMBR(@Param("mbr") Map<String, Object> mbr);

    List<MapData> selectMapDataByLongLatAndHeight(@Param("mapALong") BigDecimal MapALong, @Param("mapALat") BigDecimal MapALat,
                                                  @Param("mapAHeight") BigDecimal MapAHeight, @Param("mapBLong") BigDecimal MapBLong,
                                                  @Param("mapBLat") BigDecimal MapBLat, @Param("mapBHeight") BigDecimal MapBHeight);

    //    List<MapData> selectMapDataByHeightAndMaxAndMinAndLat(@Param("lat") BigDecimal lat, @Param("longitudeStart") BigDecimal longitudeStart, @Param("longitudeStart1") BigDecimal longitudeStart1, @Param("startHeight") Integer startHeight, @Param("endHeight") Integer endHeight);
    List<MapData> selectMapDataByHeightAndMaxAndMinAndLat(@Param("lat") BigDecimal lat, @Param("longitudeStart") BigDecimal longitudeStart, @Param("longitudeStart1") BigDecimal longitudeStart1, @Param("startHeight") Integer startHeight, @Param("endHeight") Integer endHeight);

    List<MapData> selectMapDataListById(@Param("airspaceId") Integer airspaceId, @Param("colorType") Integer colorType);

    List<MapData> mapData(@Param("param") QueryMapDataLongLatVo param);

    List<Long> queryMapDate(@Param("param") List<AddTopographicVo> param);


    int insertTopographic(@Param("indexList") List<Long> indexList);

    Long queryMapDateByOne(@Param("param") AddTopographicVo addTopographicVo);

    List<MapData> getTopographicMapDate(@Param("param") WebsocketFlightData flightData);

    List<MapData> mapDataList(@Param("param") AirspaceLongLat airspaceLongLat);

    List<MapData> getObstaclesList(@Param("param") WebsocketFlightData flightData);

    List<MapData> getControlZoneAlerts(@Param("param") WebsocketFlightData flightData);

    List<MapData> getMapData(@Param("param") QueryMapDataLongLatVo param);

    MeteInfo getMeteInfo(@Param("param") MeteInfoParam meteInfoParam);

    List<MapDataVo> getUAndVMapData(@Param("time") long time, @Param("param") AircraftMp aircraftMp);

    List<MapDataVo> getAirspaceAlarm(@Param("aircraftReg") String aircraftReg);

    MapDataVo getUAndVAlerts(@Param("time") long time, @Param("aircraftMp") AircraftMp aircraftMp, @Param("flightData") WebsocketFlightData flightData);

    List<MapData> getAlerts(@Param("param") WebsocketFlightData flightData);
}
