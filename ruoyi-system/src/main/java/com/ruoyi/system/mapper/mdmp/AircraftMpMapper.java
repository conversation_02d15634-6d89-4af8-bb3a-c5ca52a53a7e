package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.AircraftMp;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
public interface AircraftMpMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public AircraftMp selectAircraftMpById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param aircraftMp 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<AircraftMp> selectAircraftMpList(AircraftMp aircraftMp);

    /**
     * 新增【请填写功能名称】
     *
     * @param aircraftMp 【请填写功能名称】
     * @return 结果
     */
    public int insertAircraftMp(AircraftMp aircraftMp);

    /**
     * 修改【请填写功能名称】
     *
     * @param aircraftMp 【请填写功能名称】
     * @return 结果
     */
    public int updateAircraftMp(AircraftMp aircraftMp);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteAircraftMpById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAircraftMpByIds(Long[] ids);

    int insertAircraftMpList(@Param("param")List<AircraftMp> mpList);

    int deleteByAircraftId(Long id);

    List<AircraftMp> getAircraftMpListByAircraftReg(@Param("aircraftReg")String aircraftReg);
}
