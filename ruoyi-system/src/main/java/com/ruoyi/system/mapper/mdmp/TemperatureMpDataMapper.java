package com.ruoyi.system.mapper.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.AircraftMp;
import com.ruoyi.system.domain.mdmp.TemperatureMpData;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import com.ruoyi.system.domain.mdmp.vo.MapDataVo;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
public interface TemperatureMpDataMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public TemperatureMpData selectTemperatureMpDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param temperatureMpData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<TemperatureMpData> selectTemperatureMpDataList(TemperatureMpData temperatureMpData);

    /**
     * 新增【请填写功能名称】
     *
     * @param temperatureMpData 【请填写功能名称】
     * @return 结果
     */
    public int insertTemperatureMpData(TemperatureMpData temperatureMpData);

    /**
     * 修改【请填写功能名称】
     *
     * @param temperatureMpData 【请填写功能名称】
     * @return 结果
     */
    public int updateTemperatureMpData(TemperatureMpData temperatureMpData);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteTemperatureMpDataById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteTemperatureMpDataByIds(Long[] ids);

    void insertMpDataList(@Param("param") List<TemperatureMpData> mpDataList);

    List<Long> selectOldVMpDataList();

    void deleteVMpDataByIds(Long[] batchIds);

    TemperatureMpData selectTemperatureMpDataByMeteInfo(@Param("param")MeteInfoParam meteInfoParam);

    List<Double> getList(@Param("param")MeteParam meteParam);

    void deleteTemperatureMpDataByFcstTimeSequence(@Param("time")Long time,@Param("altitude") Double altitude);

    int deleteOldMpDataList();

    List<MapDataVo> getTemperatureMpMapData(@Param("time")long time, @Param("param")AircraftMp aircraftMp);

    MapDataVo getTemperatureAlerts(@Param("time")long time,@Param("aircraftMp") AircraftMp aircraftMp,@Param("flightData") WebsocketFlightData flightData);
}
