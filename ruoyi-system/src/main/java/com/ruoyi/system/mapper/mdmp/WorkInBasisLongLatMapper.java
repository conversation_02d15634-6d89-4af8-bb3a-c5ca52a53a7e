package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.WorkInBasisLongLat;
import org.apache.ibatis.annotations.Param;

/**
 * 作业区内经纬度基础信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-04-02
 */
public interface WorkInBasisLongLatMapper 
{
    /**
     * 查询作业区内经纬度基础信息
     * 
     * @param id 作业区内经纬度基础信息主键
     * @return 作业区内经纬度基础信息
     */
    public WorkInBasisLongLat selectWorkInBasisLongLatById(Long id);

    /**
     * 查询作业区内经纬度基础信息列表
     * 
     * @param workInBasisLongLat 作业区内经纬度基础信息
     * @return 作业区内经纬度基础信息集合
     */
    public List<WorkInBasisLongLat> selectWorkInBasisLongLatList(WorkInBasisLongLat workInBasisLongLat);

    /**
     * 新增作业区内经纬度基础信息
     * 
     * @param workInBasisLongLat 作业区内经纬度基础信息
     * @return 结果
     */
    public int insertWorkInBasisLongLat(WorkInBasisLongLat workInBasisLongLat);

    /**
     * 修改作业区内经纬度基础信息
     * 
     * @param workInBasisLongLat 作业区内经纬度基础信息
     * @return 结果
     */
    public int updateWorkInBasisLongLat(WorkInBasisLongLat workInBasisLongLat);

    /**
     * 删除作业区内经纬度基础信息
     * 
     * @param id 作业区内经纬度基础信息主键
     * @return 结果
     */
    public int deleteWorkInBasisLongLatById(Long id);

    /**
     * 批量删除作业区内经纬度基础信息
     * 
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWorkInBasisLongLatByIds(Long[] ids);

    int insertWorkBasisLongLatList(@Param("workInBasisLongLatList") List<WorkInBasisLongLat> workInBasisLongLatList);

    int deleteWorkInBasisLongLatByWorkInBasisIds(Long[] ids);
}
