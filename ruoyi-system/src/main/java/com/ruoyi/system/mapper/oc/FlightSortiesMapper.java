package com.ruoyi.system.mapper.oc;

import java.util.List;
import java.util.Map;

import com.ruoyi.system.domain.oc.entity.FlightSorties;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 航班架次Mapper接口
 *
 * <AUTHOR>
 * @date 2021-11-01
 */
@Mapper
public interface FlightSortiesMapper {

    /**
     * 查询出给出用户Id，及其执行状态 作为机长和副驾驶的航班班次
     */
    List<FlightSorties> selectFlightSortiesByUserIdAndStatus(@Param("userId") String userId, @Param("status") Integer status);

    List<FlightSorties> selectFlightSortiesByCaptainUserIdOrCopilotUserId(String userId);

    /**
     * 查询航班架次
     *
     * @param flightsortiesId 航班架次主键
     * @return 航班架次
     */
    public FlightSorties selectFlightSortiesByFlightsortiesId(Long flightsortiesId);

    public List<FlightSorties> selectFlightSortiesByFlighplanId(Long flightplanId);

    /**
     * 查询航班架次信息列表
     *
     * @param sortiesDate 班次日期
     * @return 航班架次信息集合
     */
    List<FlightSorties> selectBySortiesDate(String sortiesDate);

    /**
     * 查询航班架次列表
     *
     * @param flightSorties 航班架次
     * @return 航班架次集合
     */
    public List<FlightSorties> selectFlightSortiesList(FlightSorties flightSorties);

    /**
     * 新增航班架次
     *
     * @param flightSorties 航班架次
     * @return 结果
     */
    int insertFlightSorties(FlightSorties flightSorties);

    /**
     * 修改航班架次
     *
     * @param flightSorties 航班架次
     * @return 结果
     */
    int updateFlightSorties(FlightSorties flightSorties);

//    int updateFlightSortiesNestRunStatus(Long flightsortiesId);

    /**
     * 删除航班架次
     *
     * @param flightsortiesId 航班架次主键
     * @return 结果
     */
    int deleteFlightSortiesByFlightsortiesId(Long flightsortiesId);

    /**
     * 批量删除航班架次
     *
     * @param flightsortiesIds 需要删除的数据主键集合
     * @return 结果
     */
    int deleteFlightSortiesByFlightsortiesIds(Long[] flightsortiesIds);


    /**
     * 查询某个时间段的航班班次
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 结果
     */
    List<FlightSorties> selectBySortiesDateBetween(@Param("startDate") String startDate, @Param("endDate") String endDate);

    /**
     * 查询各个月份的执飞次数
     *
     * @param startDate 开始时间
     * @param endDate 结束时间
     * @return 结果
     */
    List<Map<String, Integer>> selectMonthlyFlightTimes(@Param("startDate") String startDate, @Param("endDate") String endDate);
}
