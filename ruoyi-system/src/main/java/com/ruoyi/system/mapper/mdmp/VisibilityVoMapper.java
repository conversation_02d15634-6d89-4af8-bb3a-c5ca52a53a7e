package com.ruoyi.system.mapper.mdmp;

import java.math.BigDecimal;
import java.util.List;
import com.ruoyi.system.domain.mdmp.VisibilityVo;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2025-03-25
 */
public interface VisibilityVoMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param dataValue 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public VisibilityVo selectVisibilityVoByDataValue(BigDecimal dataValue);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param visibilityVo 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<VisibilityVo> selectVisibilityVoList(VisibilityVo visibilityVo);

    /**
     * 新增【请填写功能名称】
     *
     * @param visibilityVo 【请填写功能名称】
     * @return 结果
     */
    public int insertVisibilityVo(VisibilityVo visibilityVo);

    /**
     * 修改【请填写功能名称】
     *
     * @param visibilityVo 【请填写功能名称】
     * @return 结果
     */
    public int updateVisibilityVo(VisibilityVo visibilityVo);

    /**
     * 删除【请填写功能名称】
     *
     * @param dataValue 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteVisibilityVoByDataValue(BigDecimal dataValue);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param dataValues 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteVisibilityVoByDataValues(BigDecimal[] dataValues);

    void insertVisibilityMpDataList(@Param("param")List<VisibilityVo> batchData);

    List<Double> getDoubleList();
}
