package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.AirspaceMapData;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface AirspaceMapDataMapper
{
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public AirspaceMapData selectAirspaceMapDataById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param airspaceMapData 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<AirspaceMapData> selectAirspaceMapDataList(AirspaceMapData airspaceMapData);

    /**
     * 新增【请填写功能名称】
     *
     * @param airspaceMapData 【请填写功能名称】
     * @return 结果
     */
    public int insertAirspaceMapData(AirspaceMapData airspaceMapData);


    public int insertAirspaceListMapData(@Param("airspaceMapDataList") List<AirspaceMapData> airspaceMapData);

    /**
     * 修改【请填写功能名称】
     *
     * @param airspaceMapData 【请填写功能名称】
     * @return 结果
     */
    public int updateAirspaceMapData(AirspaceMapData airspaceMapData);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteAirspaceMapDataById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAirspaceMapDataByIds(Long[] ids);


    int deleteAirspaceMapDataByAirspaceId(Long airspaceId);

    AirspaceMapData selectAirspaceMapData(@Param("param")MeteInfoParam meteInfoParam);
}
