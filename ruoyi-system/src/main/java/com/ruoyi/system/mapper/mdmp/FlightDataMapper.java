package com.ruoyi.system.mapper.mdmp;


import com.ruoyi.system.domain.mdmp.FlightAlerts;
import com.ruoyi.system.domain.mdmp.FlightData;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import com.ruoyi.system.param.mdmp.AircraftRegParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FlightDataMapper {

    /**
     * 新增飞行数据
     *
     * @param flightDate  航班日期
     * @param aircraftReg 飞机注册号
     * @return 结果
     */
    List<FlightData> selectByFlightDateAndTailNumber(@Param("flightDate") String flightDate, @Param("aircraftReg") String aircraftReg);

    List<FlightData> selectByFlightDateAndAircraftReg(@Param("flightDate") String flightDate,
                                                      @Param("aircraftReg") String aircraftReg,
                                                      @Param("dynamicTime") String dynamicTime);

    List<FlightData> getFlightAlerts(@Param("param") WebsocketFlightData flightData);

    /**
     * 新增飞行数据
     *
     * @param flightData 飞行数据
     * @return 结果
     */
    int insertFlightData(FlightData flightData);

    int updateFlightData(FlightData flightData);

    int deleteAll(@Param("flightDate") String flightDate, @Param("targetIdentification") String targetIdentification);

    List<FlightData> getConnectionInfoList(@Param("param") WebsocketFlightData websocketFlightData, @Param("height") Double height);

    List<FlightData> getFlightAlertsIsCivilTh(@Param("param") WebsocketFlightData websocketFlightData, @Param("height") Double height);


    List<AircraftRegParam> selectFlightDataListByOneMinute(@Param("aircraftReg") String aircraftReg);
}
