package com.ruoyi.system.mapper.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.FlightPlanFile;
import com.ruoyi.system.domain.mdmp.FlightPlanWork;
import org.apache.ibatis.annotations.Param;

/**
 * 航班计划附件Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface FlightPlanFileMapper {
    /**
     * 查询航班计划附件
     *
     * @param id 航班计划附件主键
     * @return 航班计划附件
     */
    public FlightPlanFile selectFlightPlanFileById(Long id);

    /**
     * 查询航班计划附件列表
     *
     * @param flightPlanFile 航班计划附件
     * @return 航班计划附件集合
     */
    public List<FlightPlanFile> selectFlightPlanFileList(FlightPlanFile flightPlanFile);

    /**
     * 新增航班计划附件
     *
     * @param flightPlanFile 航班计划附件
     * @return 结果
     */
    public int insertFlightPlanFile(FlightPlanFile flightPlanFile);

    /**
     * 修改航班计划附件
     *
     * @param flightPlanFile 航班计划附件
     * @return 结果
     */
    public int updateFlightPlanFile(FlightPlanFile flightPlanFile);

    /**
     * 删除航班计划附件
     *
     * @param id 航班计划附件主键
     * @return 结果
     */
    public int deleteFlightPlanFileById(Long id);

    /**
     * 批量删除航班计划附件
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFlightPlanFileByIds(Long[] ids);

    /**
     * 批量新增航班计划附件
     *
     * @param flightPlanFileList 航班计划附件
     * @return 结果
     */
    int insertAllFlightPlanFile(@Param("flightPlanFileList") List<FlightPlanFile> flightPlanFileList);

    /**
     * 删除航班计划附件
     *
     * @param flightPlanId 航班计划主键
     * @return 结果
     */
    int deleteAllByFlightPlanId(@Param("flightPlanId") Long flightPlanId, @Param("planType") Integer planType);

    public List<FlightPlanFile> selectFileByFlightPlanId(FlightPlanFile flightPlanFile);
}
