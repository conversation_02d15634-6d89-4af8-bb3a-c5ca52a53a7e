package com.ruoyi.system.mapper.oc;

import com.ruoyi.system.domain.oc.FlightTaskInfo;
import com.ruoyi.system.domain.oc.FlightTaskInfoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface FlightTaskInfoMapper {
    long countByExample(FlightTaskInfoExample example);

    int deleteByExample(FlightTaskInfoExample example);

    int deleteByPrimaryKey(Long id);

    int insert(FlightTaskInfo row);

    int insertSelective(FlightTaskInfo row);

    List<FlightTaskInfo> selectByExample(FlightTaskInfoExample example);

    FlightTaskInfo selectByPrimaryKey(Long id);

    List<FlightTaskInfo> selectByTaskBookNumber(@Param("taskBookNumber") String taskBookNumber, @Param("companyCode") String companyCode);

    int updateByExampleSelective(@Param("row") FlightTaskInfo row, @Param("example") FlightTaskInfoExample example);

    int updateByExample(@Param("row") FlightTaskInfo row, @Param("example") FlightTaskInfoExample example);

    int updateByPrimaryKeySelective(FlightTaskInfo row);

    int updateByPrimaryKey(FlightTaskInfo row);
}