package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.Route;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 航线Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface RouteMapper
{
    /**
     * 查询航线
     *
     * @param id 航线主键
     * @return 航线
     */
    public Route selectRouteById(Long id);

    /**
     * 查询航线
     *
     * @param routeCode 航线代号
     * @return 航线
     */
    Route selectRouteByrRuteCode(String routeCode);

    /**
     * 查询航线列表
     *
     * @param ids 航线主键
     * @return 航线列表
     */
    List<Route> selectRouteListByIds(@Param("ids") List<Long> ids);

    /**
     * 查询航线列表
     *
     * @param route 航线
     * @return 航线集合
     */
    public List<Route> selectRouteList(Route route);

    /**
     * 查询所有航线
     * @return 航线集合
     */
    List<Route> selectAll();

    /**
     * 新增航线
     *
     * @param route 航线
     * @return 结果
     */
    public int insertRoute(Route route);

    /**
     * 修改航线
     *
     * @param route 航线
     * @return 结果
     */
    public int updateRoute(Route route);

    /**
     * 删除航线
     *
     * @param id 航线主键
     * @return 结果
     */
    public int deleteRouteById(Long id);

    /**
     * 批量删除航线
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRouteByIds(Long[] ids);
}
