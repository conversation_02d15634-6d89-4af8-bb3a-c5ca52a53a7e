package com.ruoyi.system.mapper.oc;

import com.ruoyi.system.domain.oc.WxUser;
import com.ruoyi.system.domain.oc.dto.QueryPilotQualificationDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WxUserMapper {

    /**
     * 查询角色对应的微信用户
     *
     * @param roleId      角色ID
     * @param companyCode 航司代码
     * @return 结果
     */
    List<WxUser> selectWxUsersByRoleId(@Param("roleId") Long roleId, @Param("companyCode") String companyCode);

    /**
     * 查询微信用户列表
     *
     * @param userIds 微信用户Ids
     * @return 结果
     */
    List<WxUser> selectWxUserByIds(Long[] userIds);

    /**
     * 查询微信用户列表
     *
     * @param wxUser 用户信息
     * @return 结果
     */
    List<WxUser> selectList(WxUser wxUser);

    /**
     * 查询微信用户列表
     *
     * @param userId userId
     * @return 结果
     */
    WxUser selectByUserId(Long userId);

    /**
     * 查询飞行员用户列表
     *
     * @param queryQualificationDTO 查询飞行员资质请求参数
     * @return 用户信息集合信息
     */
    List<WxUser> selectPilotList(QueryPilotQualificationDTO queryQualificationDTO);

    /**
     * 修改微信用户
     *
     * @param wxUser 微信用户
     * @return 结果
     */
    int updateWxUser(WxUser wxUser);

}
