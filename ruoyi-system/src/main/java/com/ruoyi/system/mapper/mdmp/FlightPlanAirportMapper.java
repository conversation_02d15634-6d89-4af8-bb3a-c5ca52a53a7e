package com.ruoyi.system.mapper.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.FlightPlanAirport;
import org.apache.ibatis.annotations.Param;

/**
 * 航班计划机场Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface FlightPlanAirportMapper {
    /**
     * 查询航班计划机场
     *
     * @param id 航班计划机场主键
     * @return 航班计划机场
     */
    public FlightPlanAirport selectFlightPlanAirportByID(Long id);

    /**
     * 查询航班计划机场列表
     *
     * @param flightPlanAirport 航班计划机场
     * @return 航班计划机场集合
     */
    public List<FlightPlanAirport> selectFlightPlanAirportList(FlightPlanAirport flightPlanAirport);

    /**
     * 新增航班计划机场
     *
     * @param flightPlanAirport 航班计划机场
     * @return 结果
     */
    public int insertFlightPlanAirport(FlightPlanAirport flightPlanAirport);

    /**
     * 修改航班计划机场
     *
     * @param flightPlanAirport 航班计划机场
     * @return 结果
     */
    public int updateFlightPlanAirport(FlightPlanAirport flightPlanAirport);

    /**
     * 删除航班计划机场
     *
     * @param id 航班计划机场主键
     * @return 结果
     */
    public int deleteFlightPlanAirportByID(Long id);

    /**
     * 批量删除航班计划机场
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFlightPlanAirportByIDs(Long[] ids);


    /**
     * 批量新增航班计划机场
     *
     * @param flightPlanAirportList 航班计划机场
     * @return 结果
     */
    int insertAllFlightPlanAirport(@Param("flightPlanAirportList") List<FlightPlanAirport> flightPlanAirportList);


    /**
     * 删除航班计划机场
     *
     * @param flightPlanId 航班计划主键
     * @return 结果
     */
    int deleteAllByFlightPlanId(@Param("flightPlanId") Long flightPlanId, @Param("planType") Integer planType);

    public List<FlightPlanAirport> selectAirportByFlightPlanId(FlightPlanAirport flightPlanAirport);
}
