package com.ruoyi.system.mapper.oc;


import com.ruoyi.system.domain.oc.Company;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/18 11:16
 * @mood 功能
 */
public interface CompanyMapper {
    /**
     * 查询列表
     * @param company 查询条件
     * @return 结果
     */
    public List<Company> selectList(Company company);

    /**
     * 查询列表
     * @param id ID
     * @return 结果
     */
    Company selectById(Long id);

    /**
     * 查询列表
     * @param companyCode 航司代码
     * @return 结果
     */
    Company selectByCompanyCode(String companyCode);

    /**
     * 添加公司
     * @param company 公司
     * @return 结果
     */
    int insertCompany(Company company);

    /**
     * 修改公司
     * @param company 公司
     * @return 结果
     */
    int updateCompany(Company company);

    /**
     * 删除公司
     * @param id 公司ID
     * @return 结果
     */
    int deleteCompany(Long id);
}
