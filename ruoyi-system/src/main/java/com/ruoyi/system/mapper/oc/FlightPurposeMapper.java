package com.ruoyi.system.mapper.oc;


import com.ruoyi.system.domain.oc.FlightPurpose;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/24 11:06
 * @mood 功能
 */
public interface FlightPurposeMapper {
    /**
     * 查询列表
     */
    public List<FlightPurpose> selectList(FlightPurpose flightPurpose);

    /**
     * 查询一条
     */
    public FlightPurpose selectOneById(Long id);

    /**
     * 新增一条
     */
    public int insertOne(FlightPurpose param);

    /**
     * 修改一条
     */
    public int updateOne(FlightPurpose param);

    /**
     * 删除一条
     */
    public int deleteOneById(Long id);

    /**
     * 批量删除
     */
    public int deleteAllByIds(Long[] ids);
}
