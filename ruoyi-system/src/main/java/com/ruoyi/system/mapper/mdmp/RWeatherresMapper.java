package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RWeatherres;
import org.apache.ibatis.annotations.Param;

/**
 * 天气现象Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface RWeatherresMapper
{
    /**
     * 查询天气现象
     *
     * @param id 天气现象主键
     * @return 天气现象
     */
    public RWeatherres selectRWeatherresById(Long id);

    /**
     * 查询天气现象列表
     *
     * @param rWeatherres 天气现象
     * @return 天气现象集合
     */
    public List<RWeatherres> selectRWeatherresList(RWeatherres rWeatherres);

    /**
     * 新增天气现象
     *
     * @param rWeatherres 天气现象
     * @return 结果
     */
    public int insertRWeatherres(RWeatherres rWeatherres);

    /**
     * 修改天气现象
     *
     * @param rWeatherres 天气现象
     * @return 结果
     */
    public int updateRWeatherres(RWeatherres rWeatherres);

    /**
     * 删除天气现象
     *
     * @param id 天气现象主键
     * @return 结果
     */
    public int deleteRWeatherresById(Long id);

    /**
     * 批量删除天气现象
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRWeatherresByIds(Long[] ids);

    void insertRWeatherresList(@Param("param") List<RWeatherres> weatherResDtoList);
}
