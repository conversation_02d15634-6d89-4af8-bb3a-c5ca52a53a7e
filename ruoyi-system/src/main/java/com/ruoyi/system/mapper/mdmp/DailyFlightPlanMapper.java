package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.common.annotation.DataSource;
import com.ruoyi.common.enums.DataSourceType;
import com.ruoyi.system.domain.mdmp.DailyFlightPlan;
import com.ruoyi.system.domain.mdmp.LongTermFlightPlan;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import com.ruoyi.system.param.mdmp.DailyFlightPlanListParam;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 当日计划飞行Mapper接口
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
public interface DailyFlightPlanMapper
{
    /**
     * 查询当日计划飞行
     *
     * @param id 当日计划飞行主键
     * @return 当日计划飞行
     */
    public DailyFlightPlan selectDailyFlightPlanById(Long id);

    /**
     * 查询当日计划飞行列表
     *
     * @param dailyFlightPlan 当日计划飞行
     * @return 当日计划飞行集合
     */
    public List<DailyFlightPlan> selectDailyFlightPlanList(DailyFlightPlanListParam param);

    /**
     * 查询审核通过的当日计划飞行列表
     *
     * @param status 状态
     * @param flightDate 航班日期
     * @return 当日计划飞行集合
     */
    public List<DailyFlightPlan> selectListByStatus(@Param("status") Integer status, @Param("flightDate") String flightDate);

    /**
     * 通过日期查询当日航班计划
     *
     * @param flightDate 航班日期
     * @param aircraftReg 机型
     * @return 当日计划飞行集合
     */
    DailyFlightPlan selectByFlightDateAndTailNumber(@Param("flightDate") String flightDate, @Param("aircraftReg") String aircraftReg);

    /**
     * 新增当日计划飞行
     *
     * @param dailyFlightPlan 当日计划飞行
     * @return 结果
     */
    public int insertDailyFlightPlan(DailyFlightPlan dailyFlightPlan);

    /**
     * 修改当日计划飞行
     *
     * @param dailyFlightPlan 当日计划飞行
     * @return 结果
     */
    public int updateDailyFlightPlan(DailyFlightPlan dailyFlightPlan);

    /**
     * 删除当日计划飞行
     *
     * @param id 当日计划飞行主键
     * @return 结果
     */
    public int deleteDailyFlightPlanById(Long id);

    /**
     * 批量删除当日计划飞行
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteDailyFlightPlanByIds(Long[] ids);

    /**
     * 查询所有计划 根据 时间 状态
     * @param dailyFlightPlan
     * @return
     */
    List<DailyFlightPlan> selectDailyFlightPlanByInterval(@Param("dailyFlightPlan") DailyFlightPlan dailyFlightPlan);

    DailyFlightPlan selectDailyFlightByIdAndStatus(Long id);




    Long getRouteAlerts(@Param("param")WebsocketFlightData flightData);

// 根据传入的WebsocketFlightData对象，获取没有飞行计划的警报数量
    DailyFlightPlan getNoFlightPlanAlerts(@Param("param") WebsocketFlightData flightData);

    Long getWorAlerts(@Param("param")WebsocketFlightData flightData);
}
