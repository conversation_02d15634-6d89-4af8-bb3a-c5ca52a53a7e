package com.ruoyi.system.mapper.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.FlightPlanWork;
import org.apache.ibatis.annotations.Param;

/**
 * 航班计划作业区Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface FlightPlanWorkMapper {
    /**
     * 查询航班计划作业区
     *
     * @param id 航班计划作业区主键
     * @return 航班计划作业区
     */
    public FlightPlanWork selectFlightPlanWorkById(Long id);

    /**
     * 查询航班计划作业区列表
     *
     * @param flightPlanWork 航班计划作业区
     * @return 航班计划作业区集合
     */
    public List<FlightPlanWork> selectFlightPlanWorkList(FlightPlanWork flightPlanWork);

    /**
     * 新增航班计划作业区
     *
     * @param flightPlanWork 航班计划作业区
     * @return 结果
     */
    public int insertFlightPlanWork(FlightPlanWork flightPlanWork);

    /**
     * 修改航班计划作业区
     *
     * @param flightPlanWork 航班计划作业区
     * @return 结果
     */
    public int updateFlightPlanWork(FlightPlanWork flightPlanWork);

    /**
     * 删除航班计划作业区
     *
     * @param id 航班计划作业区主键
     * @return 结果
     */
    public int deleteFlightPlanWorkById(Long id);

    /**
     * 批量删除航班计划作业区
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFlightPlanWorkByIds(@Param("ids") Long[] ids, @Param("planType") Integer planType);

    /**
     * 查询航班计划作业区列表
     *
     * @param flightPlanId 航班计划主键
     * @return 航班计划作业区集合
     */
    List<FlightPlanWork> selectWorkByFlightPlanId(FlightPlanWork flightPlanWork);

    /**
     * 查询航班计划作业区列表
     *
     * @param flightPlanId 航班计划主键
     * @return 航班计划作业区集合
     */
    List<FlightPlanWork> selectByFlightPlanId(@Param("flightPlanId") Long flightPlanId, @Param("planType") Integer planType);

    FlightPlanWork selectByIndex(@Param("index") Integer index);
}
