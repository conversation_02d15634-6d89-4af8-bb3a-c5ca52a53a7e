package com.ruoyi.system.mapper.oc;


import com.ruoyi.system.domain.oc.FileDir;

import java.util.List;

/**
 * 文件目录Mapper接口
 * 
 * <AUTHOR>
 * @date 2021-12-23
 */
public interface FileDirMapper 
{
    /**
     * 查询文件目录
     * 
     * @param fileDirId 文件目录主键
     * @return 文件目录
     */
    public FileDir selectFileDirByFileDirId(Long fileDirId);
    public List<FileDir> selectFileDirByParentId(Long parentId);


    /**
     * 查询文件目录列表
     * 
     * @param fileDir 文件目录
     * @return 文件目录集合
     */
    public List<FileDir> selectFileDirList(FileDir fileDir);

    /**
     * 新增文件目录
     * 
     * @param fileDir 文件目录
     * @return 结果
     */
    public int insertFileDir(FileDir fileDir);

    /**
     * 修改文件目录
     * 
     * @param fileDir 文件目录
     * @return 结果
     */
    public int updateFileDir(FileDir fileDir);

    /**
     * 删除文件目录
     * 
     * @param fileDirId 文件目录主键
     * @return 结果
     */
    public int deleteFileDirByFileDirId(Long fileDirId);

    /**
     * 批量删除文件目录
     * 
     * @param fileDirIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFileDirByFileDirIds(Long[] fileDirIds);
}
