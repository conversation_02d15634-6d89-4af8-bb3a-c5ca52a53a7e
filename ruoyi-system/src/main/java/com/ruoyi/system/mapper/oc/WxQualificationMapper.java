package com.ruoyi.system.mapper.oc;

import com.ruoyi.system.domain.oc.WxQualification;
import org.apache.ibatis.annotations.Param;

/**
 * 微信用户资质
 * <AUTHOR>
 */
public interface WxQualificationMapper {

    /**
     * 查询微信用户的资质
     *
     * @param userId      微信用户ID
     * @return 结果
     */
    WxQualification selectByUserId(@Param("userId") Long userId);

    /**
     * 添加微信用户的资质
     *
     * @param wxQualification 微信用户的资质
     * @return 结果
     */
    int insertWxQualification(WxQualification wxQualification);

    /**
     * 修改微信用户的资质
     *
     * @param wxQualification      微信用户的资质
     * @return 结果
     */
    int updateWxQualification(WxQualification wxQualification);
}
