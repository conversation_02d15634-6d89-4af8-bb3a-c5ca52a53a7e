package com.ruoyi.system.mapper.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.FlightPlanAirport;
import com.ruoyi.system.domain.mdmp.FlightPlanRoute;
import org.apache.ibatis.annotations.Param;

/**
 * 航班计划航线Mapper接口
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
public interface FlightPlanRouteMapper {
    /**
     * 查询航班计划航线
     *
     * @param id 航班计划航线主键
     * @return 航班计划航线
     */
    public FlightPlanRoute selectFlightPlanRouteById(String id);

    /**
     * 通过航班计划Id查询航班计划航线
     *
     * @param flightPlanId 航班计划Id
     * @return 航班计划航线
     */
    List<FlightPlanRoute> selectFlightPlanRouteByFlightPlanId(@Param("flightPlanId") Long flightPlanId, @Param("planType") Integer planType);

    /**
     * 通过航班计划Id查询航班计划航线
     *
     * @param flightPlanId 航班计划Id
     * @param routeCode    航线代号
     * @return 航班计划航线
     */
    FlightPlanRoute selectByFlightPlanIdAndRouteCode(@Param("flightPlanId") Long flightPlanId, @Param("routeCode") String routeCode);

    /**
     * 查询航班计划航线列表
     *
     * @param flightPlanRoute 航班计划航线
     * @return 航班计划航线集合
     */
    public List<FlightPlanRoute> selectFlightPlanRouteList(FlightPlanRoute flightPlanRoute);

    /**
     * 新增航班计划航线
     *
     * @param flightPlanRoute 航班计划航线
     * @return 结果
     */
    public int insertFlightPlanRoute(FlightPlanRoute flightPlanRoute);

    /**
     * 修改航班计划航线
     *
     * @param flightPlanRoute 航班计划航线
     * @return 结果
     */
    public int updateFlightPlanRoute(FlightPlanRoute flightPlanRoute);

    /**
     * 删除航班计划航线
     *
     * @param id 航班计划航线主键
     * @return 结果
     */
    public int deleteFlightPlanRouteById(String id);

    /**
     * 批量删除航班计划航线
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteFlightPlanRouteByIds(String[] ids);

    /**
     * 批量新增航班计划航线
     *
     * @param flightPlanRouteList 航班计划航线
     * @return 结果
     */
    int insertAllFlightPlanRoute(@Param("flightPlanRouteList") List<FlightPlanRoute> flightPlanRouteList);

    /**
     * 删除航班计划航线
     *
     * @param flightPlanId 航班计划主键
     * @return 结果
     */
    int deleteAllByFlightPlanId(@Param("flightPlanId") Long flightPlanId, @Param("planType") Integer planType);

    public List<FlightPlanRoute> selectRouteByFlightPlanId(FlightPlanRoute route);

}
