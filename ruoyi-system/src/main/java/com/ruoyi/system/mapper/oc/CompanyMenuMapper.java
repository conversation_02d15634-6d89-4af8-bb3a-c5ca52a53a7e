package com.ruoyi.system.mapper.oc;

import com.ruoyi.system.domain.oc.CompanyMenu;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
@Mapper
public interface CompanyMenuMapper {

    /**
     * 通过航司代码查询公司菜单关联
     * @param companyCode 航司代码
     * @return 结果
     */
    List<CompanyMenu> selectByCompanyCode(@Param("companyCode") String companyCode);

    /**
     * 批量新增公司菜单关联信息
     *
     * @param companyMenuList 公司菜单列表
     * @return 结果
     */
    int batchInsertCompanyMenu(List<CompanyMenu> companyMenuList);

    /**
     * 通过航司代码查询公司菜单关联
     * @param companyCode 航司代码
     * @return 结果
     */
    int deleteByCompanyCode(@Param("companyCode") String companyCode);
}
