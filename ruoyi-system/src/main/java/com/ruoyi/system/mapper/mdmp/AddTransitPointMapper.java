package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.AddTransitPoint;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AddTransitPointMapper {
    /**
     * 通过ID查询单条数据
     *
     * @param enterLeaveId 进离场信息表ID
     * @return 实例对象
     */
    List<AddTransitPoint> queryByEnterLeaveId(@Param("enterLeaveId") Integer enterLeaveId);
    /**
     * 批量新增数据
     *
     * @param entities List<AddTransitPoint> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AddTransitPoint> entities);
    /**
     * 批量新增或按主键更新数据
     *
     * @param entities List<AddTransitPoint> 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<AddTransitPoint> entities);
    /**
     * 通过主键删除数据
     *
     * @param enterLeaveId 进离场信息表ID
     * @return 影响行数
     */
    int deleteByEnterLeaveId(@Param("enterLeaveId") Integer enterLeaveId);
}
