package com.ruoyi.system.mapper.oc;

import com.ruoyi.system.domain.oc.entity.OcAircraft;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 飞行器信息Mapper接口
 *
 * <AUTHOR>
 * @date 2021-09-22
 */
@Mapper
public interface OcAircraftMapper
{

    /**
     * 查询飞行器信息列表
     *
     * @param ocAircraft 飞行器信息
     * @return 飞行器信息集合
     */
    public List<OcAircraft> selectAircraftList(OcAircraft ocAircraft);

}
