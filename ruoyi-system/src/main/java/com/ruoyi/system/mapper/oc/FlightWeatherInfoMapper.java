package com.ruoyi.system.mapper.oc;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.oc.entity.FlightWeatherInfo;
import com.ruoyi.system.domain.oc.vo.FlightWeatherDynamicVo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 气象信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface FlightWeatherInfoMapper extends BaseMapper<FlightWeatherInfo> {
    /**
     * 查询气象信息
     *
     * @param id 气象信息主键
     * @return 气象信息
     */
    public FlightWeatherDynamicVo selectFlightWeatherInfoById(@Param("id") Long id, @Param("companyCode") String companyCode);

}