package com.ruoyi.system.mapper.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.Aircraft;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import org.apache.ibatis.annotations.Param;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
public interface AircraftMapper {
    /**
     * 查询【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 【请填写功能名称】
     */
    public Aircraft selectAircraftById(Long id);

    /**
     * 查询【请填写功能名称】列表
     *
     * @param aircraft 【请填写功能名称】
     * @return 【请填写功能名称】集合
     */
    public List<Aircraft> selectAircraftList(Aircraft aircraft);

    /**
     * 新增【请填写功能名称】
     *
     * @param aircraft 【请填写功能名称】
     * @return 结果
     */
    public int insertAircraft(Aircraft aircraft);

    public List<Aircraft> checkAircraftOverlap(Aircraft aircraft);

    /**
     * 修改【请填写功能名称】
     *
     * @param aircraft 【请填写功能名称】
     * @return 结果
     */
    public int updateAircraft(Aircraft aircraft);

    /**
     * 删除【请填写功能名称】
     *
     * @param id 【请填写功能名称】主键
     * @return 结果
     */
    public int deleteAircraftById(Long id);

    /**
     * 批量删除【请填写功能名称】
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteAircraftByIds(Long[] ids);

    List<Aircraft> selectByAircraftId(Long aircraftId);

    Aircraft checkAircraftUnique(Aircraft aircraft);

    Aircraft selectAircraft(@Param("aircraftReg") String aircraftReg);
}
