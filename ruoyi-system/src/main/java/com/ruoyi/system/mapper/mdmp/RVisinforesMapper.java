package com.ruoyi.system.mapper.mdmp;

import java.util.List;
import com.ruoyi.system.domain.mdmp.RVisinfores;

/**
 * 能见度Mapper接口
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
public interface RVisinforesMapper
{
    /**
     * 查询能见度
     *
     * @param id 能见度主键
     * @return 能见度
     */
    public RVisinfores selectRVisinforesById(Long id);

    /**
     * 查询能见度列表
     *
     * @param rVisinfores 能见度
     * @return 能见度集合
     */
    public List<RVisinfores> selectRVisinforesList(RVisinfores rVisinfores);

    /**
     * 新增能见度
     *
     * @param rVisinfores 能见度
     * @return 结果
     */
    public int insertRVisinfores(RVisinfores rVisinfores);

    /**
     * 修改能见度
     *
     * @param rVisinfores 能见度
     * @return 结果
     */
    public int updateRVisinfores(RVisinfores rVisinfores);

    /**
     * 删除能见度
     *
     * @param id 能见度主键
     * @return 结果
     */
    public int deleteRVisinforesById(Long id);

    /**
     * 批量删除能见度
     *
     * @param ids 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteRVisinforesByIds(Long[] ids);
}
