package com.ruoyi.system.mapper.oc;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.ruoyi.system.domain.oc.entity.FlightDynamicInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 动态信息Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@Mapper
public interface FlightDynamicInfoMapper extends BaseMapper<FlightDynamicInfo> {

    public List<FlightDynamicInfo> selectFlightDynamicInfoByTaskBookNumber(@Param("taskBookNumber") String taskBookNumber, @Param("companyCode") String companyCode);



}