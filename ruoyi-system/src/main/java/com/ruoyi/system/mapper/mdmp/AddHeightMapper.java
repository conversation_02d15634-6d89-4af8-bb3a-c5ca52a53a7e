package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.AddHeight;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface AddHeightMapper {

    /**
     * 通过ID查询多条数据
     *
     * @param enterLeaveId 进离场信息表ID
     * @return 实例对象
     */
    List<AddHeight> queryByEnterLeaveId(@Param("enterLeaveId") Integer enterLeaveId);
    /**
     * 批量新增数据
     *
     * @param addHeight 实例对象
     * @return 影响行数
     */
    int insert(AddHeight addHeight);
    /**
     * 批量新增数据
     *
     * @param entities List<AddHeight> 实例对象列表
     * @return 影响行数
     */
    int insertBatch(@Param("entities") List<AddHeight> entities);
    /**
     * 批量新增或按主键更新数据
     *
     * @param entities List<AddHeight> 实例对象列表
     * @return 影响行数
     */
    int insertOrUpdateBatch(@Param("entities") List<AddHeight> entities);
    /**
     * 通过ID删除数据
     *
     * @param enterLeaveId 进离场信息表ID
     * @return 影响行数
     */
    int deleteByEnterLeaveId(@Param("enterLeaveId") Integer enterLeaveId);
    /**
     * 更新数据
     *
     * @param addHeight 实例对象
     * @return 影响行数
     */
    int update(AddHeight addHeight);
}
