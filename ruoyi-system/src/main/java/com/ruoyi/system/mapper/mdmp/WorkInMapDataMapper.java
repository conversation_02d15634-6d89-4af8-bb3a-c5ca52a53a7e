package com.ruoyi.system.mapper.mdmp;

import com.ruoyi.system.domain.mdmp.WorkInMapData;
import com.ruoyi.system.domain.mdmp.WorkMapData;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
public interface WorkInMapDataMapper {


    List<WorkInMapData> selectAllByWorkInId(@Param("workInId") Long workInId);


    int delByWorkInId(@Param("workId") Long[] workId);


    int insertAllWorkInMapData(@Param("workInMapData") List<WorkInMapData> workInMapData);
}
