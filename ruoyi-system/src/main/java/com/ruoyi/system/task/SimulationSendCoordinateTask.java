package com.ruoyi.system.task;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.FlightData;
import com.ruoyi.system.domain.mdmp.temporary.FlightDataTemporary;
import com.ruoyi.system.mapper.mdmp.FlightDataMapper;
import com.ruoyi.system.mapper.mdmp.FlightDataTemporaryMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Component
@EnableScheduling
@RequiredArgsConstructor
public class SimulationSendCoordinateTask {

    private final FlightDataTemporaryMapper flightDataTemporaryMapper;
    private final JmsTemplate jmsTemplate;
    private static List<FlightDataTemporary> temporaries = new ArrayList<>();
    private int index = 0;
    private boolean taskRunning = false;

    @PostConstruct
    public void init() {
        temporaries = flightDataTemporaryMapper.selectList("B10KN");
//        temporaries = flightDataTemporaryMapper.selectList("B3118");
//        temporaries = temporaries.stream().sorted(Comparator.comparing(FlightDataTemporary::getDynamicTime)).collect(Collectors.toList());
    }

    @Scheduled(fixedRate = 1000)
    public void performTask2() {
        if (taskRunning==false) {
            return;
        }
        if (index == temporaries.size()) {
//            flightDataMapper.deleteAll("2025-01-14", "B3118");
//            index = 0;
            return;
        }
        FlightDataTemporary temporary = temporaries.get(index);
        FlightData flightData = convertFlightData(temporary);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("flightData", flightData);
        String jsonString = JSON.toJSONString(jsonObject);
        jmsTemplate.convertAndSend("insertDatabase.queue", jsonString);

        index++;
    }

    private static FlightData convertFlightData(FlightDataTemporary temporary) {
        FlightData flightData = new FlightData();
        BeanUtils.copyProperties(temporary, flightData);
        flightData.setId(null);
        flightData.setFlightDate(DateUtils.getDate());
        flightData.setDynamicTime(DateUtils.getHms());
        return flightData;
    }

    public void startTask() {
        taskRunning = true;
        System.out.println("Task started.");
    }

    public void stopTask() {
        taskRunning = false;
        index = 0;
        System.out.println("Task stopped.");
    }
}
