package com.ruoyi.system.domain.mdmp.vo;


import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.math.BigDecimal;
import java.util.List;

@Data
@ApiModel(description = "查询地图数据")
public class QueryMapDataVo {

    @ApiModelProperty("高度起始")
    @Excel(name = "高度起始")
    @NotNull(message = "高度起始不能为空")
    private Integer startHeight;


    @ApiModelProperty("高度截止")
    @Excel(name = "高度截止")
    @NotNull(message = "高度截止不能为空")
    private Integer endHeight;


    @ApiModelProperty("查询地图数据经纬度")
    @Excel(name = "查询地图数据经纬度")
    @NotNull(message = "地图数据经纬度不能为空")
    @Size(min = 2,message = "地图数据经纬度最小2个点")
    @Valid
    private List<MapDataLongLatVo> mapDataLongLatVoList;
}
