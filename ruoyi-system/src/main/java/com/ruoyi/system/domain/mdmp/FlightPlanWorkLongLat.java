package com.ruoyi.system.domain.mdmp;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 航班计划作业区经纬度对象 work_long_lat
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@Data
@ApiModel(value = "航班计划作业区经纬度")
public class FlightPlanWorkLongLat extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 作业区Id
     */
    @Excel(name = "作业区Id")
    @ApiModelProperty(value = "作业区Id", example = "132", dataType = "Long")
    private Long flightPlanWorkId;

    /**
     * 经度
     */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度", example = "132.23131321", dataType = "String")
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty(value = "纬度", example = "132.23131321", dataType = "String")
    private String latitude;

    /**
     * 高度
     */
    @Excel(name = "高度")
    @ApiModelProperty(value = "高度", example = "132.00", dataType = "BigDecimal")
    private BigDecimal height;

    /**
     * 排序
     */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序", example = "2", dataType = "Integer")
    private Integer sortNumber;

    /**
     * 转换后的经度
     */
    @Excel(name = "转换后的经度")
    @ApiModelProperty(value = "转换后的经度", example = "2", dataType = "BigDecimal")
    private BigDecimal doubleLongitude;

    /**
     * 转换后的纬度
     */
    @Excel(name = "转换后的纬度")
    @ApiModelProperty(value = "转换后的纬度", example = "2", dataType = "BigDecimal")
    private BigDecimal doubleLatitude;

    @ApiModelProperty("组别号 0作业区  1缓冲区")
    @Excel(name = "组别号")
    private Integer groupNumber;


    /**
     * 点名称
     */
    @ApiModelProperty("点名称")
    @Excel(name = "点名称")
    private String coordinateName;

}
