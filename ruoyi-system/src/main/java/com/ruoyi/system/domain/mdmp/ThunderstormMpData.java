package com.ruoyi.system.domain.mdmp;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 thunderstorm_mp_data
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Data
public class ThunderstormMpData extends BaseEntity
{
    private static final long serialVersionUID = 1L;


    /** 数据值 */
    @Excel(name = "数据值")
    private Integer dataValue;

    /** 预计时间 */
    @Excel(name = "预计时间")
    private Long fcstTimeSequence;

    /** 实时时间 */
    @Excel(name = "实时时间")
    private Long reatimeTimeSequence;

    /** 经度 */
    @Excel(name = "经度")
    private BigDecimal longitude;

    /** 纬度 */
    @Excel(name = "纬度")
    private BigDecimal latitude;

    /** 高度 */
    @Excel(name = "高度")
    private BigDecimal altitude;

    private Date addTime;
}
