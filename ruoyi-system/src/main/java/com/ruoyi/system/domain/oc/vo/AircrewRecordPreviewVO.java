package com.ruoyi.system.domain.oc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "AircrewRecordPreviewVO", description = "文件预览信息返回")
public class AircrewRecordPreviewVO {

    /**
     * 文件类型
     */
    @ApiModelProperty("文件类型")
    private String fileType;

    /**
     * 文件虚拟网址
     */
    @ApiModelProperty("文件虚拟网址")
    private String virtualUrl;

    public static AircrewRecordPreviewVO setting(String fileType, String virtualUrl) {
        AircrewRecordPreviewVO previewVO = new AircrewRecordPreviewVO();
        previewVO.setFileType(fileType);
        previewVO.setVirtualUrl(virtualUrl);
        return previewVO;
    }
}
