package com.ruoyi.system.domain.mdmp;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 天气现象对象 r_weatherres
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
public class RWeatherres {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 报文表ID
     */
    @Excel(name = "报文表ID")
    private Long decodeReportId;

    /**
     * 强度
     */
    @Excel(name = "强度")
    private String intensity;

    /**
     * 描述
     */
    @Excel(name = "描述")
    private String description;

    /**
     * 现象
     */
    @Excel(name = "现象")
    private String phenomenon;


    private Integer colourType;

    /**
     * 其他
     */
    @Excel(name = "其他")
    private String other;
}

