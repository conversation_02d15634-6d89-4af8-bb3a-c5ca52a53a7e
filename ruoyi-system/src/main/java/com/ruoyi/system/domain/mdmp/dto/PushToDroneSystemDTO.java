package com.ruoyi.system.domain.mdmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "推送消息给无人机系统入参")
public class PushToDroneSystemDTO {

    @ApiModelProperty(value = "类型(1:返航, 2:绕点盘旋)", example = "1", required = true)
    @NotNull(message = "指令消息ID为空")
    private Integer type ;

    @ApiModelProperty(value = "机尾号", example = "", required = true)
    @NotBlank(message = "机尾号不能为空")
    private String aircraftReg;

    @ApiModelProperty(value = "纬度", example = "25.37")
    private Double latitude;
    @ApiModelProperty(value = "经度", example = "116.48")
    private Double longitude;
    @ApiModelProperty(value = "高度", example = "6889.0")
    private Double height;
}
