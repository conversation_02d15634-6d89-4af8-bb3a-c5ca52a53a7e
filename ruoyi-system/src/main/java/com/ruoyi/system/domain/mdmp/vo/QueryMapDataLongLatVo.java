package com.ruoyi.system.domain.mdmp.vo;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2024/10/9 9:42
 * @description：根据经纬度查询在哪个格子
 * @modified By：
 * @version: $
 */
@Data
@ApiModel(description = "根据经纬度查询在哪个格子")
public class QueryMapDataLongLatVo {
    /**
     * 经度
     */
    @ApiModelProperty("经度122.20")
    @Excel(name = "经度")
    @NotNull(message = "经度不能为空")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度122.20")
    @Excel(name = "纬度")
    @NotNull(message = "纬度不能为空")
    private BigDecimal latitude;


    @ApiModelProperty("高度")
    @Excel(name = "高度")
    @NotNull(message = "高度不能为空")
    private Double height;

}
