package com.ruoyi.system.domain.mdmp;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 云对象 r_cloudinfores
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
public class RCloudinfores {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 报文表ID
     */
    @Excel(name = "报文表ID")
    private Long decodeReportId;

    /**
     * 云
     */
    @Excel(name = "云")
    private String cloudQuantity;

    /**
     * 云高度
     */
    @Excel(name = "云高度")
    private Integer cloudHeight;

    /**
     * 云状
     */
    @Excel(name = "云状")
    private String cloudShape;

    private Integer colourType;

}
