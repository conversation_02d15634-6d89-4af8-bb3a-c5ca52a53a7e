package com.ruoyi.system.domain.oc.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * 飞行器信息对象 oc_aircraft
 *
 * <AUTHOR>
 * @date 2021-09-22
 */
@ApiModel(value = "OcAircraft", description = "运控管理-飞行器实体")
@Data
public class OcAircraft extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 飞行器编号
     */
    @ApiModelProperty("飞行器编号")
    private Long aircraftId;

    /**
     * 机型
     */
    @ApiModelProperty("机型")
    @Excel(name = "机型")
    private String aircraftStyle;

    /**
     * 机尾号
     */
    @ApiModelProperty("机尾号")
    @Excel(name = "机尾号")
    private String aircraftTailNo;

    /**
     * 飞行器信息
     */
    @ApiModelProperty("飞行器信息")
    @Excel(name = "飞行器信息")
    private String aircraftInfo;

    /**
     * 机号
     */
    @ApiModelProperty("机号")
    @Excel(name = "机号")
    private String machineNumber;

    /**
     * 飞行器制造公司
     */
    @ApiModelProperty("飞行器制造公司")
    @Excel(name = "飞行器制造公司")
    private String aircraftCompany;

    /**
     * 飞行器可供座位数
     */
    @ApiModelProperty("飞行器可供座位数")
    @Excel(name = "飞行器可供座位数")
    private Long aircraftSeat;

    /**
     * 飞行器长度
     */
    @ApiModelProperty("飞行器长度")
    @Excel(name = "飞行器长度")
    private Long aircraftLength;

    /**
     * 飞行器高度
     */
    @ApiModelProperty("飞行器高度")
    @Excel(name = "飞行器高度")
    private Long aircraftHeight;

    /**
     * 飞行器宽度
     */
    @ApiModelProperty("飞行器宽度")
    @Excel(name = "飞行器宽度")
    private Long aircraftWidth;

    /**
     * 飞行器最大航程距离
     */
    @ApiModelProperty("飞行器最大航程距离")
    @Excel(name = "飞行器最大航程距离")
    private Long aircraftMfRange;

    /**
     * 加油记录
     */
    @ApiModelProperty("加油记录集合")
    private List<RefuelingRecord> refuelingRecords;

    /**
     * 维修记录
     */
    @ApiModelProperty("维修记录集合")
    private List<MaintenanceRecord> maintenanceRecords;

    /**
     * 加油次数
     */
    @ApiModelProperty("加油次数")
    private Integer refuelingTimes;

    /**
     * 加油总油量
     */
    @ApiModelProperty("加油总油量")
    private Long totalOilVolume;

    /**
     * 航司代码
     */
    @ApiModelProperty("航司代码")
    private String companyCode;
}
