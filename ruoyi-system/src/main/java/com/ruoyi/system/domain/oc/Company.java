package com.ruoyi.system.domain.oc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2022/4/18 10:43
 * @mood 功能
 */
@Data
@ApiModel(description = "公司")
public class Company {

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 航司名称
     */
    @ApiModelProperty("航司名称")
    private String companyName;

    /**
     * 航司代码
     */
    @ApiModelProperty("航司代码")
    private String companyCode;

    /**
     * 公司类型（0:管理类公司, 1:通航公司）
     */
    @ApiModelProperty("公司类型（0:管理类公司, 1:通航公司）")
    private Integer companyType;

    /**
     * 是否存在管理员
     */
    @ApiModelProperty("是否存在管理员(0:不存在, 1:存在)")
    private Integer existAdmin;
}
