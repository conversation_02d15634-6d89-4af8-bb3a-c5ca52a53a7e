package com.ruoyi.system.domain.oc.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 航班计划信息对象 oc_flightplan
 *
 * <AUTHOR>
 * @date 2021-09-22
 */
@Data
@ApiModel(value = "Flightplan", description = "航班计划实体")
public class Flightplan extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 飞行器编号
     */
    @ApiModelProperty("飞行器编号")
    private Long flightplanId;

    /**
     * 航司代码
     */
    @ApiModelProperty("航司代码")
    private String companyCode;

    @ApiModelProperty(value = "航班类型（1：航线，2：空域）", required = true)
    private Integer flightType;

    /**
     * 任务类型（1:短途运输,2:航空护林,3:调机飞行,4:人工影响天气,5:飞行训练,6:航拍航摄）
     */
    @ApiModelProperty("任务类型")
    @Excel(name = "任务类型")
    private String flightPurpose;

    /**
     * 航线类型(0:单程,1:往返)
     */
    @ApiModelProperty("航线类型(0:单程,1:往返)")
    private Integer routeType;

    /**
     * 航班状态(1:计划中,2:已执行,3:已取消)
     */
    @ApiModelProperty("航班状态(1:计划中,2:已执行,3:已取消)")
    private Integer flightStatus;

    /**
     * 航班飞行状态
     */
    @ApiModelProperty(value = "航班飞行状态(1:飞行前,2:飞行中,3:飞行后)")
    private Integer flyStatus;

    /**
     * 任务进展
     */
    @ApiModelProperty(value = "任务进展(1:预先准备,2:直接准备,3:飞行实施,4:飞行讲评,5:资料存档)")
    private Integer taskProgress;

    /**
     * 航班日期
     */
    @ApiModelProperty("航班日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "航班日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date flightDate;

    /**
     * 开始航班日期
     */
    @ApiModelProperty("开始航班日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDateBatchStart;

    /**
     * 结束航班日期
     */
    @ApiModelProperty("结束航班日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDateBatchEnd;

    /**
     * 航班号
     */
    @ApiModelProperty("航班号")
    @Excel(name = "航班号")
    private String flightNo;

    /**
     * 呼号
     */
    @ApiModelProperty("呼号")
    @Excel(name = "呼号")
    private String callSign;

    @ApiModelProperty("运行号")
    private String runNo;

    /**
     * 起始机场三字码、起始城市
     */
    @ApiModelProperty("起始机场三字码")
    @Excel(name = "起始机场三字码")
    private String departAirportCode;

    @ApiModelProperty("起始城市")
    @Excel(name = "起始城市")
    private String departCity;

    /**
     * 到达城市三字码、到达城市
     */
    @ApiModelProperty("到达城市三字码")
    @Excel(name = "到达城市三字码")
    private String arriveAirportCode;

    @ApiModelProperty("到达城市")
    @Excel(name = "到达城市")
    private String arriveCity;

    /**
     * 备降城市三字码、到达城市
     */
    @ApiModelProperty("备降城市三字码")
    @Excel(name = "备降城市三字码")
    private String alternateAirportCode;

    @ApiModelProperty("备降城市")
    @Excel(name = "备降城市")
    private String alternateCity;

    @ApiModelProperty("是否发送给局方")
    private Integer isSend;

    /**
     * 航班班次
     */
    @ApiModelProperty("航班班次集合")
    private List<FlightSorties> flightSorties;


    /**
     * 总值班领导的用户id
     */
    @ApiModelProperty("总值班领导的用户id")
    private Long generalleaderUserId;

    /**
     * 值班领导的用户id
     */
    @ApiModelProperty("值班领导的用户id")
    private Long leaderUserId;

    /**
     * 预计总航时间（分钟）
     */
    @ApiModelProperty("预计总航时间(分钟)")
    private Long estimatedTotalSailingTime;

    /**
     * 运行种类（1：，2：，3：，）
     */
    @ApiModelProperty("运行种类（1：，2：，3：，）")
    private Integer dperationType;

    /**
     * 运行标准（1：，2：，3：，）
     */
    @ApiModelProperty("运行标准（1：，2：，3：，）")
    private Integer dperationStandard;

    /**
     * 预先准备审批状态（0：未审批，1：同意，2：拒绝，3：其他）
     */
    @ApiModelProperty("预先准备审批状态（0：未审批，1：同意，2：拒绝，3：其他）")
    private Integer preApprovalStatus;

    /**
     * 预先准备审批用户ID
     */
    @ApiModelProperty("预先准备审批用户ID")
    private long preApprovalUserId;

    /**
     * 预先准备审批用户名
     */
    @ApiModelProperty("预先准备审批用户名")
    private String preApprovalUserName;

    /**
     * 预先准备审批评语
     */
    @ApiModelProperty("预先准备审批评语")
    private String approvalComments;

    /**
     * 计划起飞时间
     */
    @ApiModelProperty("计划起飞时间(HH:mm)")
    @Excel(name = "计划起飞时间")
    private String planDepartTime;

    /**
     * 计划到达时间
     */
    @ApiModelProperty("计划到达时间(HH:mm)")
    @Excel(name = "计划到达时间")
    private String planArriveTime;

    /**
     * 机长的用户id
     */
    @ApiModelProperty("机长的用户id，用,隔开")
    private String captainUserId;

    /**
     * 副驾驶的用户id
     */
    @ApiModelProperty("副驾驶的用户id，用,隔开")
    private String copilotUserId;

    @ApiModelProperty("机长类型(1:机长，2：实习机长，3：教员)")
    private Integer pilotType;

    /**
     * 副驾驶类型(0:副驾驶，1：第二机长)
     */
    @ApiModelProperty("副驾驶类型(1:副驾驶，2：学员，3：同乘)")
    private Integer copilotType;

    /**
     * 放行机务的用户id
     */
    @ApiModelProperty("机务人员id，用,隔开")
    private String maintenanceId;

    /**
     * 安全员机务的用户id
     */
    @ApiModelProperty("安全员id，用,隔开")
    private String safetyOfficerId;

    /**
     * 运控值班的用户id
     */
    @ApiModelProperty("运控值班的用户id，用,隔开")
    private String ocUserId;

    @ApiModelProperty("机械员的用户id，用,隔开")
    private String mechanicId;
    @ApiModelProperty("机械师")
    private String mechanicMasterId;
    @ApiModelProperty("现场组织人员的用户id，用,隔开")
    private String organizationId;
    @ApiModelProperty("安检员的用户id，用,隔开")
    private String inspectorId;
    @ApiModelProperty("售票员的用户id，用,隔开")
    private String conductorId;
    @ApiModelProperty(value = "值班经理")
    private String dutyManagerId;

    /**
     * 飞行器id
     */
    @ApiModelProperty("飞行器id")
    private Long aircraftId;

    /**
     * 机尾号
     */
    @ApiModelProperty("机尾号")
    @Excel(name = "机尾号")
    private String aircraftTailNo;

    /**
     * 飞行架次
     */
    @ApiModelProperty(value = "飞行架次")
    private Integer flightFrequency;

    /**
     * 滑行开始时间
     */
    @ApiModelProperty("滑行开始时间(yyyy-MM-dd HH:mm)")
    private String slideStartTime;

    /**
     * 起飞时间
     */
    @ApiModelProperty("起飞时间(yyyy-MM-dd HH:mm)")
    private String flyStartTime;

    /**
     * 着陆时间
     */
    @ApiModelProperty("着陆时间(yyyy-MM-dd HH:mm)")
    private String flyEndTime;

    /**
     * 滑行结束时间
     */
    @ApiModelProperty("滑行结束时间(yyyy-MM-dd HH:mm)")
    private String slideEndTime;

    /**
     * 空中时间 （落地时间-起飞时间） 时刻 计算得到
     */
    @ApiModelProperty("空中飞行时间(落地时间-起飞时间）")
    private Integer flyTime;

    /**
     * 飞行含滑行时间（滑行结束-滑行开始）时刻 计算得到
     */
    @ApiModelProperty("飞行含滑行时间（滑行结束-滑行开始）")
    private Integer slideTime;

    /**
     * 夜航时间(HH:mm)
     */
    @ApiModelProperty(value = "夜航时间(HH:mm)")
    private String nightFlyTime;

    /**
     * 燃油消耗
     */
    @ApiModelProperty("燃油消耗")
    private Integer fuelExpend;

    /**
     * 旅客人数
     */
    @ApiModelProperty("旅客人数")
    private Integer passengerNumber;

    /**
     * 返程滑行开始时间
     */
    @ApiModelProperty("返程滑行开始时间(yyyy-MM-dd HH:mm)")
    private String secondSlideStartTime;

    /**
     * 返程起飞时间
     */
    @ApiModelProperty("返程起飞时间(yyyy-MM-dd HH:mm)")
    private String secondFlyStartTime;

    /**
     * 返程着陆时间
     */
    @ApiModelProperty("返程着陆时间(yyyy-MM-dd HH:mm)")
    private String secondFlyEndTime;

    /**
     * 返程滑行结束时间
     */
    @ApiModelProperty("返程滑行结束时间(yyyy-MM-dd HH:mm)")
    private String secondSlideEndTime;

    /**
     * 返程空中时间 （落地时间-起飞时间）
     */
    @ApiModelProperty("返程空中飞行时间")
    private Integer secondFlyTime;

    /**
     * 返程飞行时间（滑行结束-滑行开始）
     */
    @ApiModelProperty("返程飞行时间")
    private Integer secondSlideTime;

    /**
     * 返程夜航时间
     */
    @ApiModelProperty("返程夜航时间(HH:mm)")
    private String secondNightFlyTime;

    /**
     * 返程燃油消耗
     */
    @ApiModelProperty("返程燃油消耗")
    private Integer secondFuelExpend;

    /**
     * 返程旅客人数
     */
    @ApiModelProperty("返程旅客人数")
    private Integer secondPassengerNumber;

    /**
     * 航班延误(0:未延误, 1:延误)
     */
    @ApiModelProperty("航班延误(0:未延误, 1:延误)")
    private Integer flightDelay;

    /**
     * 延误原因
     */
    @ApiModelProperty("延误原因")
    private String delayReason;

    @ApiModelProperty("返程航班延误(0:未延误, 1:延误)")
    private Integer secondFlightDelay;

    @ApiModelProperty(value = "返程延误原因", example = "天气原因")
    private String secondDelayReason;

    /**
     * 航班取消原因
     */
    @ApiModelProperty(value = "航班取消原因")
    private String flightCancelReason;

    @ApiModelProperty(value = "机型")
    private String aircraftStyle;


    @ApiModelProperty(value = "航线-空域")
    private String route;

    @ApiModelProperty(value = "营业时间")
    private String businessHours;



    public Flightplan() {

    }

    public Flightplan(Long flightplanId) {
        this.flightplanId = flightplanId;
    }


    public String getCallSign() {
        return callSign;
    }

    public void setCallSign(String callSign) {
        this.callSign = callSign;
    }
}
