package com.ruoyi.system.domain.mdmp.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.system.domain.mdmp.FlightPlanWorkLongLat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/5/6 10:02
 * @description：
 * @modified By：
 * @version: $
 */
@Data
@ApiModel(value = "冲突作业区对象")
public class ConflictWorkVo {

    private static final long serialVersionUID = 1L;


    /**
     * 航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划
     */
    @Excel(name = "航班计划类型;1 长期计划 2 次日计划 3 当日放行计划 4 单一飞行计划")
    @ApiModelProperty(value = "航班计划类型(1,长期计划;2, 次日计划;3,当日放行计划 4 单一飞行计划)", required = true, allowableValues = "1,2,3")
    private Integer planType;

    /**
     * 航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划
     */
    @Excel(name = "作业类型;0区域内作业 1沿线作业,2不作业航线")
    @ApiModelProperty(value = "作业类型(0,区域内作业;2, 沿线作业;3,不作业航线)", required = true, allowableValues = "0,1,2")
    @NotNull(message = "作业类型 不能为空")
    private Integer workType;


    /**
     * 图形类型;0圆  1点
     */
    @Excel(name = "图形类型;0圆  1点")
    @ApiModelProperty(value = "图形类型(0,圆;1, 点)", required = true, allowableValues = "0,1")
    @NotNull(message = "图形类型不能为空")
    private Integer graphType;


    /**
     * 作业区偏移量;默认5000米 图形内
     */
    @Excel(name = "作业区偏移量;默认5000米 图形内")
    @ApiModelProperty(value = "作业区偏移量", example = "5000.00", dataType = "BigDecimal")
    private BigDecimal workOffset;

    /**
     * 圆心经度
     */
    @Excel(name = "圆心经度")
    @ApiModelProperty(value = "圆心经度", example = "112.23223", dataType = "String")
    private String circleCenterLong;

    /**
     * 圆心纬度
     */
    @Excel(name = "圆心纬度")
    @ApiModelProperty(value = "圆心纬度", example = "22.22123", dataType = "String")
    private String circleCenterLat;

    /**
     * 半径;单位米
     */
    @Excel(name = "半径;单位米")
    @ApiModelProperty(value = "半径", example = "500", dataType = "String")
    private String radius;

    /**
     * 作业区名称
     */
    @Excel(name = "作业区名称")
    @ApiModelProperty(value = "作业区名称", example = "作业区", dataType = "String")
    private String workName;


    /**
     * 最低高度;单位米
     */
    @ApiModelProperty("半径;单位米")
    @Excel(name = "最低高度;单位米")
    private BigDecimal minHeight;

    /**
     * 最高高度;单位米
     */
    @ApiModelProperty("半径;单位米")
    @Excel(name = "最高高度;单位米")
    private BigDecimal maxHeight;

    /**
     * 作业区经纬度关联表
     * 作业区内表
     */
    @Excel(name = "作业区经纬度关联表")
    @ApiModelProperty(value = "作业区经纬度关联表")
    private List<FlightPlanWorkLongLat> flightPlanWorkLongLatList;


    @Excel(name = "交点经度")
    @ApiModelProperty(value = "交点经度", example = "22.22123", dataType = "Double")
    private Double intersectionLong;

    @Excel(name = "交点纬度")
    @ApiModelProperty(value = "交点纬度", example = "22.22123", dataType = "Double")
    private Double intersectionLat;

    @Excel(name = "交点高度")
    @ApiModelProperty(value = "交点高度", example = "22.22123", dataType = "Double")
    private Double intersectionHeight;

    @ApiModelProperty(value = "计划编号", example = "1231231", dataType = "String")
    private String serialNo;
    @ApiModelProperty(value = "id", example = "id", dataType = "Long")
    private Long id;
}
