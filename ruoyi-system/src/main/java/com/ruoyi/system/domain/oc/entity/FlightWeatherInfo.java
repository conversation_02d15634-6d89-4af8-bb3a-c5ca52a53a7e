package com.ruoyi.system.domain.oc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonIgnore;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Map;

/**
 * 气象信息对象 oc_flight_weather_info
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@ApiModel(value = "FlightWeatherInfo", description = "气象信息")
@Data
@TableName("oc_flight_weather_info")
public class FlightWeatherInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 任务书编号
     */
    @ApiModelProperty("任务书编号")
    @Excel(name = "任务书编号")
    private String taskBookNumber;

    /**
     * flight_task_info表主键id
     */
    @ApiModelProperty("flight_task_info表主键id")
    private Long flightTaskInfoId;

    /**
     * 批次
     */
    @ApiModelProperty("批次")
    @Excel(name = "批次")
    private String batch;

    /**
     * 位置类型（0-始发地，1-目的地）
     */
    @ApiModelProperty("位置类型（0-始发地，1-目的地）")
    @Excel(name = "位置类型", readConverterExp = "0=始发地,1=目的地")
    private Integer locationType;

    /**
     * 位置名称
     */
    @ApiModelProperty("位置名称")
    @Excel(name = "位置名称")
    private String locationName;

    /**
     * 天气
     */
    @ApiModelProperty("天气")
    @Excel(name = "天气")
    private String weather;

    /**
     * 云高（m）
     */
    @ApiModelProperty("云高（m）")
    @Excel(name = "云高（m）")
    private String cloudHeight;

    /**
     * 温度（C）
     */
    @ApiModelProperty("温度（C）")
    @Excel(name = "温度（C）")
    private String temperature;

    /**
     * 风向
     */
    @ApiModelProperty("风向")
    @Excel(name = "风向")
    private String windDirection;

    /**
     * 风速（m/s）
     */
    @ApiModelProperty("风速（m/s）")
    @Excel(name = "风速（m/s）")
    private String windSpeed;

    /**
     * 能见度（m）
     */
    @ApiModelProperty("能见度（m）")
    @Excel(name = "能见度（m）")
    private String visibility;

    /**
     * QNH(hPa)
     */
    @ApiModelProperty("QNH(hPa)")
    @Excel(name = "QNH(hPa)")
    private String qnh;

    /**
     * 所属航司代码
     */
    @ApiModelProperty("所属航司代码")
    private String companyCode;

    /**
     * 公司部门code
     */
    @ApiModelProperty("公司部门code")
    private String deptCode;
    @JsonIgnore
    private Map<String, Object> params;
}