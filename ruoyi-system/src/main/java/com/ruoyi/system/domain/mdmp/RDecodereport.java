package com.ruoyi.system.domain.mdmp;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import java.util.List;

/**
 * 报文信息对象 r_decodereport
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
public class RDecodereport
{
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    private Long id;

    /** 原始报文ID */
    @Excel(name = "原始报文ID")
    private String rawReportId;

    /** 报文内容 */
    @Excel(name = "报文内容")
    private String content;

    /** 报文发布时间时间戳 */
    @Excel(name = "报文发布时间时间戳")
    private Long publishTime;

    /** 报文发布时间类型;0:整点，1：半点报，2：非整非半点 */
    @Excel(name = "报文发布时间类型;0:整点，1：半点报，2：非整非半点")
    private Integer publishTimeType;

    /** 报文类型;SA、SP、FT、FC，其中SA、SP为 METAR，FT、FC为TAF */
    @Excel(name = "报文类型;SA、SP、FT、FC，其中SA、SP为 METAR，FT、FC为TAF")
    private String type;

    /** 趋势类型;MAIN为主报文，BECMG、TEMPO、FM趋势报文 */
    @Excel(name = "趋势类型;MAIN为主报文，BECMG、TEMPO、FM趋势报文")
    private String trendType;

    /** 有效开始时间戳（UTC） */
    @Excel(name = "有效开始时间戳", readConverterExp = "U=TC")
    private Long validTimeBegin;

    /** 有效结束时间戳（UTC） */
    @Excel(name = "有效结束时间戳", readConverterExp = "U=TC")
    private Long validTimeEnd;

    /** 机场代码 */
    @Excel(name = "机场代码")
    private String airportCode;

    /** 是否自动发布;1：自动，0：非自动 */
    @Excel(name = "是否自动发布;1：自动，0：非自动")
    private Integer isAuto;

    /** 是否为更正报文;1：更正，0：否 */
    @Excel(name = "是否为更正报文;1：更正，0：否")
    private Integer isCor;

    /** CVA */
    @Excel(name = "CVA")
    private Boolean cavOk;

    private RMintemperatureres minTemperatureResDTO;

    private RMaxtemperatureres maxTemperatureResDTO;

    private RWindinfores windInfoResDto;

    private RVisinfores visInfoResDto;


    /** 垂直能见度 */
    @Excel(name = "垂直能见度")
    private Integer verticalVisibility;

    /** 气温温度;metar报文时有值，taf报文为空 */
    @Excel(name = "气温温度;metar报文时有值，taf报文为空")
    private Integer temperature;

    /** 露点温度;metar报文时有值，taf报文为空 */
    @Excel(name = "露点温度;metar报文时有值，taf报文为空")
    private Integer dpTemperature;

    /** 气压;metar报文时有值，taf报文为空 */
    @Excel(name = "气压;metar报文时有值，taf报文为空")
    private Integer altimeter;

    /** 风切变;metar报文时有值，taf报文为空 */
    @Excel(name = "风切变;metar报文时有值，taf报文为空")
    private Integer windShear;

    /** 风切变原文;metar报文时有值，taf报文为空 */
    @Excel(name = "风切变原文;metar报文时有值，taf报文为空")
    private String windShearContent;

    /** 风切变跑道;metar报文时有值，taf报文为空 */
    @Excel(name = "风切变跑道;metar报文时有值，taf报文为空")
    private String windShearRunway;

    private List<RRunwayrvrres> runwayRvrResDtoList;

    private List<RCloudinfores> cloudResDtoList;

    private List<RWeatherres> weatherResDtoList;

    /** 云简码;metar报文时有值，taf报文为空 */
    @Excel(name = "云简码;metar报文时有值，taf报文为空")
    private String cloudCode;

    /** 近时天气;metar报文时有值，taf报文为空 */
    @Excel(name = "近时天气;metar报文时有值，taf报文为空")
    private String recentWeather;

    /** 是否为取消报;taf报文时有值，metar报文为空 */
    @Excel(name = "是否为取消报;taf报文时有值，metar报文为空")
    private Boolean isCancel;

    /** 无云;taf报文时有值，metar报文为空 */
    @Excel(name = "无云;taf报文时有值，metar报文为空")
    private Boolean nsc;

    /** 无天气;taf报文时有值，metar报文为空 */
    @Excel(name = "无天气;taf报文时有值，metar报文为空")
    private Boolean nsw;

    private String remark;

    private Integer colourType;
}
