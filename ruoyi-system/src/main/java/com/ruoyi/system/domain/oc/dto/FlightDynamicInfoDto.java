package com.ruoyi.system.domain.oc.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> by yaodan
 **/
@Data
public class FlightDynamicInfoDto {
    /**
     * 批次
     */
    @ApiModelProperty("批次")
    private String batch;

    /**
     * 始发地
     */
    @ApiModelProperty("始发地")
    private String departureLocation;

    /**
     * 目的地
     */
    @ApiModelProperty("目的地")
    private String arrivalLocation;

    /**
     * 开车时刻
     */
    @ApiModelProperty("开车时刻")
    private String carStartTime;

    /**
     * 起飞时刻
     */
    @ApiModelProperty("起飞时刻")
    private String takeOffTime;

    /**
     * 着陆时刻
     */
    @ApiModelProperty("着陆时刻")
    private String landingTime;

    /**
     * 关车时刻
     */
    @ApiModelProperty("关车时刻")
    private String carStopTime;

    /**
     * 地面时间（分钟）
     */
    @ApiModelProperty("地面时间（分钟）")
    private Integer groundTimeMin;

    /**
     * 空中时间（分钟）
     */
    @ApiModelProperty("空中时间（分钟）")
    private Integer airTimeMin;

    /**
     * 时间小计（分钟）
     */
    @ApiModelProperty("时间小计（分钟）")
    private Integer totalTimeMin;

    /**
     * 架次
     */
    @ApiModelProperty("架次")
    private Integer sortieCount;
}
