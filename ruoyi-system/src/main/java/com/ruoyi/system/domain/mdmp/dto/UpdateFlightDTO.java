package com.ruoyi.system.domain.mdmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "修改航班入参")
public class UpdateFlightDTO {

    /** 航班ID */
    @ApiModelProperty(value = "航班ID", required = true)
    @NotNull(message = "航班ID不能为空")
    private Integer id ;

    /** 航班日期 */
    @ApiModelProperty(value = "航班日期" ,required = true)
    @NotBlank(message = "航班日期不能为空")
    private String flightDate ;
    /** 航班状态;(1:正常, 2:异常, 3:已取消) */
    @ApiModelProperty(value = "航班状态(1:正常, 2:异常, 3:已取消)" ,required = true)
    @NotNull(message = "航班状态不能为空")
    private Integer flightStatus ;
    /** 航空器呼号 */
    @ApiModelProperty(value = "航空器呼号" ,required = true)
    @NotBlank(message = "航空器呼号不能为空")
    private String callSign ;
    /** 起始城市 */
    @ApiModelProperty(value = "起始城市" ,required = true)
    @NotBlank(message = "起始城市不能为空")
    private String departCity ;
    /** 起始机场四字码 */
    @ApiModelProperty(value = "起始机场四字码" ,required = true)
    @NotBlank(message = "起始机场四字码不能为空")
    private String departAirportCode ;
    /** 到达城市 */
    @ApiModelProperty(value = "到达城市" ,required = true)
    @NotBlank(message = "到达城市不能为空")
    private String arriveCity ;
    /** 到达机场四字码 */
    @ApiModelProperty(value = "到达机场四字码" ,required = true)
    @NotBlank(message = "到达机场四字码不能为空")
    private String arriveAirportCode ;
    /** 计划起飞时间 */
    @ApiModelProperty(value = "计划起飞时间" ,required = true)
    @NotBlank(message = "计划起飞时间不能为空")
    private String planDepartTime ;
    /** 计划到达时间 */
    @ApiModelProperty(value = "计划到达时间", required = true)
    @NotBlank(message = "计划到达时间不能为空")
    private String planArriveTime ;
    /** 机型 */
    @ApiModelProperty(value = "机型" ,required = true)
    @NotBlank(message = "机型不能为空")
    private String aircraftType ;
    /** 尾流标志 */
    @ApiModelProperty(value = "尾流标志" ,required = true)
    @NotBlank(message = "尾流标志不能为空")
    private String tailSign ;
    /** 二次雷达应答机模式及编码 */
    @ApiModelProperty(value = "二次雷达应答机模式及编码" ,required = true)
    @NotBlank(message = "二次雷达应答机模式及编码不能为空")
    private String radarTransponderMode ;
    /** 实际起飞时间 */
    @ApiModelProperty(value = "实际起飞时间")
    private String actualDepartTime ;
    /** 实际到达时间 */
    @ApiModelProperty(value = "实际到达时间")
    private String actualArriveTime ;
    /** 进出港标识;0:进港, 1:离港 */
    @ApiModelProperty(value = "进出港标识(0:进港, 1:离港)", required = true)
    @NotNull(message = "进出港标识不能为空")
    private Integer ioSign ;

    @ApiModelProperty(value = "默认高度属性(0:标准海平面气压, A:修正海平面气压, H:场面气压)", example = "A")
    private String heightAttribute;
    /** 高度值 */
    @ApiModelProperty(value = "默认高度", example = "0480")
    private String defaultHeight ;
    /** 放行备注 */
    @ApiModelProperty(value = "放行备注")
    private String releaseRemark ;
}
