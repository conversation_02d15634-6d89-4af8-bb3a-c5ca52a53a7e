package com.ruoyi.system.domain.mdmp.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.EnterLeavePort;
import com.ruoyi.system.domain.mdmp.Flight;
import com.ruoyi.system.domain.mdmp.FlightLog;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "电子进程单操作日志返回参数")
public class FlightLogVO {
    /**
     * ID
     */
    @ApiModelProperty(value = "ID", example = "1")
    private Integer id;
    /**
     * 航班ID
     */
    @Excel(name = "航班ID")
    @ApiModelProperty(value = "航班ID", example = "1")
    private Integer flightId;
    /**
     * 航空器呼号
     */
    @Excel(name = "航空器呼号")
    @ApiModelProperty(value = "航空器呼号", example = "CES1111")
    private String callSign;
    /**
     * 日志消息
     */
    @Excel(name = "日志消息")
    @ApiModelProperty(value = "日志消息", example = "日志消息")
    private String logMessage;
    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    @ApiModelProperty(value = "创建时间", example = "")
    private String createTime;

    /**
     * 航班日期
     */
    @Excel(name = "航班日期")
    @ApiModelProperty(value = "航班日期", example = "2025-02-13")
    private String flightDate;
    /**
     * 航班状态
     */
    @Excel(name = "航班状态")
    @ApiModelProperty(value = "航班状态", example = "正常")
    private String flightStatus;
    /**
     * 起始城市
     */
    @Excel(name = "起始城市")
    @ApiModelProperty(value = "起始城市", example = "阿达")
    private String departCity;
    /**
     * 起始机场四字码
     */
    @Excel(name = "起始机场四字码")
    @ApiModelProperty(value = "起始机场四字码", example = "TECV")
    private String departAirportCode;
    /**
     * 到达城市
     */
    @Excel(name = "到达城市")
    @ApiModelProperty(value = "到达城市", example = "石河子")
    private String arriveCity;
    /**
     * 到达机场四字码
     */
    @Excel(name = "到达机场四字码")
    @ApiModelProperty(value = "到达机场四字码", example = "DJSS")
    private String arriveAirportCode;
    /**
     * 计划起飞时间
     */
    @Excel(name = "计划起飞时间")
    @ApiModelProperty(value = "计划起飞时间", example = "1634")
    private String planDepartTime;
    /**
     * 计划到达时间
     */
    @Excel(name = "计划到达时间")
    @ApiModelProperty(value = "计划到达时间", example = "1638")
    private String planArriveTime;
    /**
     * 机型
     */
    @Excel(name = "机型")
    @ApiModelProperty(value = "机型", example = "A320")
    private String aircraftType;
    /**
     * 尾流标志
     */
    @Excel(name = "尾流标志")
    @ApiModelProperty(value = "尾流标志", example = "M")
    private String tailSign;
    /**
     * 二次雷达应答机模式及编码
     */
    @Excel(name = "二次雷达应答机模式及编码")
    @ApiModelProperty(value = "二次雷达应答机模式及编码", example = "ADS2")
    private String radarTransponderMode;
    /**
     * 实际起飞时间
     */
    @Excel(name = "实际起飞时间")
    @ApiModelProperty(value = "实际起飞时间", example = "1634")
    private String actualDepartTime;
    /**
     * 实际到达时间
     */
    @Excel(name = "实际到达时间")
    @ApiModelProperty(value = "实际到达时间", example = "1638")
    private String actualArriveTime;
    /**
     * 进出港标识
     */
    @Excel(name = "进出港标识")
    @ApiModelProperty(value = "进出港标识", example = "进港")
    private String ioSign;


    /**
     * 使用跑道
     */
    @Excel(name = "使用跑道")
    @ApiModelProperty(value = "使用跑道", example = "09")
    private String runway;
    /**
     * 滑行指令
     */
    @Excel(name = "滑行指令")
    @ApiModelProperty(value = "滑行指令", example = "A")
    private String taxiInstruction;

    /**
     * 飞行日期
     */
    @Excel(name = "飞行日期")
    @ApiModelProperty(value = "飞行日期", example = "2025-02-14")
    private String flyDate;
    /**
     * 停机位
     */
    @Excel(name = "停机位")
    @ApiModelProperty(value = "停机位", example = "101")
    private String parkingGate;

    public static FlightLogVO setting(FlightLog log, Flight flight, EnterLeavePort enterLeavePort) {
        FlightLogVO logVO = new FlightLogVO();
        BeanUtils.copyProperties(log, logVO);
        BeanUtils.copyProperties(flight, logVO);
        if (enterLeavePort != null) {
            BeanUtils.copyProperties(enterLeavePort, logVO);
        }
        logVO.setCreateTime(log.getCreateTime());
        logVO.setFlightDate(DateUtils.convertDate(flight.getFlightDate()));
        logVO.setFlightStatus(flight.getFlightStatus() == 1 ? "正常" : flight.getFlightStatus() == 2 ? "异常" : flight.getFlightStatus() == 3?"已取消":"已删除");
        logVO.setIoSign(flight.getIoSign()==0?"进港":"离港");
        return logVO;
    }
}
