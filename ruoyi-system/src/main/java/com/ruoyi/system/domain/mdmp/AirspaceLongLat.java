package com.ruoyi.system.domain.mdmp;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;

/**
 * 空域经纬度对象 airspace_long_lat
 *
 * <AUTHOR>
 * @date 2024-04-01
 */

@ApiModel(value = "AirspaceLongLat", description = "空域经纬度实体")
@Data
public class AirspaceLongLat
{
    private static final long serialVersionUID = 1L;

    /** 自增ID */

    @ApiModelProperty("ID")
    private Long id;

    /** 空域id表 */
    @ApiModelProperty("空域id表")
    @Excel(name = "空域id表")
    private Long airspaceId;

    /** 排序号 */
    @ApiModelProperty("排序号")
    @Excel(name = "排序号")
    private Integer sortNumber;

    /** 经度 */
    @ApiModelProperty("经度")
    @Excel(name = "经度")
    private String longitude;

    /** 纬度 */
    @ApiModelProperty("纬度")
    @Excel(name = "纬度")
    private String latitude;

    /** 经度 */
    @ApiModelProperty("转换后的经度123.456789")
    @Excel(name = "转换后的经度")
    private Double doubleLongitude;

    /** 纬度 */
    @ApiModelProperty("转换后的纬度123.456789")
    @Excel(name = "转换后的纬度")
    private Double doubleLatitude;

    /** 高度 */
    @ApiModelProperty("高度")
    @Excel(name = "高度")
    private Integer height;

    @ApiModelProperty("组别号")
    @Excel(name = "组别号")
    private Integer groupNumber;

    /** 点名称 */
    @ApiModelProperty("点名称")
    @Excel(name = "点名称")
    private String coordinateName;

}
