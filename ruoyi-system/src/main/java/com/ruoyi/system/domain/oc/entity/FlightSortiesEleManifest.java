package com.ruoyi.system.domain.oc.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 记录班次下电子舱单对象 oc_flight_sorties_ele_manifest
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Data
public class FlightSortiesEleManifest extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 航班班次编号
     */
    private Long id;

    /**
     * 航班计划编号
     */
    @Excel(name = "航班计划编号")
    private Long flightSortiesId;

    /**
     * 上传电子舱单时间
     */
    @Excel(name = "上传电子舱单时间")
    private String uploadEleManifestTime;

    /**
     * 电子舱单拒绝时间
     */
    @Excel(name = "电子舱单拒绝时间")
    private String eleManifestRefuseTime;

    /**
     * 电子舱单通过时间
     */
    @Excel(name = "电子舱单通过时间")
    private String eleManifestPassTime;

    /**
     * 电子舱单拒绝理由
     */
    @Excel(name = "电子舱单拒绝理由")
    private String eleManifestRefuse;

    @ApiModelProperty("是否当前使用")
    private Integer useing;
}
