package com.ruoyi.system.domain.mdmp;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 aircraft_airspace
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
public class AircraftAirspace extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** 机型ID */
    @Excel(name = "机型ID")
    private Long aircraftId;

    /** 空域ID */
    @Excel(name = "空域ID")
    private Long airspaceId;

    /** 飞机注册号 */
    @Excel(name = "飞机注册号")
    private String aircraftReg;

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setAircraftId(Long aircraftId)
    {
        this.aircraftId = aircraftId;
    }

    public Long getAircraftId()
    {
        return aircraftId;
    }
    public void setAirspaceId(Long airspaceId)
    {
        this.airspaceId = airspaceId;
    }

    public Long getAirspaceId()
    {
        return airspaceId;
    }
    public void setAircraftReg(String aircraftReg)
    {
        this.aircraftReg = aircraftReg;
    }

    public String getAircraftReg()
    {
        return aircraftReg;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("aircraftId", getAircraftId())
            .append("airspaceId", getAirspaceId())
            .append("aircraftReg", getAircraftReg())
            .toString();
    }
}
