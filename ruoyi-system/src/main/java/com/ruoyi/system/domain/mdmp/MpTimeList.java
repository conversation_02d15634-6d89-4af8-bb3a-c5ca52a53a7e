package com.ruoyi.system.domain.mdmp;

import java.util.Date;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 mp_time_list
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@Data
public class MpTimeList extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**  */
    private Long id;

    /** 预计时间 */
    private Long fcstTimeSequence;

    /** 实时时间 */
    private Long reatimeTimeSequence;


    private Long mpTimeLineId;


}
