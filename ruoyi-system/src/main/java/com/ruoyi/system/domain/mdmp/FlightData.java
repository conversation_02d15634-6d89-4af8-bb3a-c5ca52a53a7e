package com.ruoyi.system.domain.mdmp;

import com.ruoyi.system.domain.mdmp.vo.QueryMapDataLongLatVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * 飞行数据
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行数据")
public class FlightData {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", example = "1")
    private Long id;

    /**
     * 唯一ID(用于告警信息关联)
     */
    @ApiModelProperty("唯一ID(用于告警信息关联)")
    private Long uniqueId;
    /**
     * 航班计划ID
     */
    @ApiModelProperty(value = "航班计划ID", example = "5")
    private Long flightPlanId;

    /**
     * 目标识别 呼号或机尾号
     */
    @ApiModelProperty("目标识别（呼号或机尾号）")
    private String targetIdentification;
    /**
     * 呼号
     */
    @ApiModelProperty(value = "呼号", example = "FF9999")
    private String callSign;
    @ApiModelProperty(value = "飞机注册号", example = "B-7962")
    private String aircraftReg;
    @ApiModelProperty(value = "运营机构", example = "中信海洋直升机")
    private String operationAgency;
    @ApiModelProperty(value = "设备编号", example = "10001")
    private String deviceId;
    @ApiModelProperty(value = "UTC 年", example = "2024")
    private Integer utcYear;
    @ApiModelProperty(value = "UTC 月", example = "4")
    private Integer utcMonth;
    @ApiModelProperty(value = "UTC 日", example = "1")
    private Integer utcDay;
    @ApiModelProperty(value = "UTC 时", example = "12")
    private Integer utcHour;
    @ApiModelProperty(value = "UTC 分", example = "33")
    private Integer utcMinute;
    @ApiModelProperty(value = "UTC 秒", example = "54")
    private Integer utcSecond;
    /**
     * 日期
     */
    @ApiModelProperty(value = "日期(yyyy-MM-dd)", example = "2024-04-22")
    private String flightDate;
    /**
     * 飞行坐标的动态时间
     */
    @ApiModelProperty(value = "飞行坐标的动态时间(HH:mm:ss)", example = "17:37:30")
    private String dynamicTime;
    @ApiModelProperty("实时时间(yyyy-MM-dd HH:mm:ss)")
    private String realTime;
    @ApiModelProperty(value = "定位指示（0=无效解，1=单点解，2=差分解，4=固定解，5=浮点解，6=惯导解）", example = "0")
    private Integer gpsFlag;
    @ApiModelProperty(value = "纬度", example = "25.37")
    private BigDecimal latitude;
    @ApiModelProperty(value = "经度", example = "116.48")
    private BigDecimal longitude;
    @ApiModelProperty(value = "海拔高度(气压)", example = "6889.0")
    private Double elevation;
    @ApiModelProperty(value = "修正海拔高度", example = "6889.0")
    private Double correctElevation;
    @ApiModelProperty(value = "几何高度", example = "6889.0")
    private Double geometricHeight;
    @ApiModelProperty(value = "选定高度", example = "6889.0")
    private Double selectedHeight;
    @ApiModelProperty(value = "给定高度", example = "S0689")
    private String givenHeight;
    @ApiModelProperty(value = "航速", example = "202154")
    private Double speed;
    @ApiModelProperty(value = "给定速度", example = "K689")
    private String givenSpeed;
    @ApiModelProperty(value = "北向速度(米/秒)", example = "12.2")
    private Float northSpeed;
    @ApiModelProperty(value = "东向速度(米/秒)", example = "9.3")
    private Float eastSpeed;
    @ApiModelProperty(value = "天顶方向速度(米/秒)", example = "2.4")
    private Float verticalSpeed;
    @ApiModelProperty(value = "航向角度", example = "34.2")
    private Float headingAngle;
    @ApiModelProperty(value = "俯仰角度", example = "12.3")
    private Float pitchAngle;
    @ApiModelProperty(value = "横滚角度", example = "1.2")
    private Float rollAngle;
    @ApiModelProperty(value = "在50公里范围内(0:否, 1:是)", example = "1")
    private Integer withinTheScope;


    @ApiModelProperty(value = "机型", example = "C208")
    private String aircraftStyle;

    @ApiModelProperty(value = "起飞机场", example = "乌鲁木齐")
    public String departAirport;

    @ApiModelProperty(value = "到达机场", example = "克拉玛依")
    public String arriveAirport;

    @ApiModelProperty(value = "应答机编码", example = "202154")
    private String answeringMachineCode;

    @ApiModelProperty(value = "高度(基准面符号 AHO)", example = "202154")
    private Float datumHeight;

    @ApiModelProperty(value = "预计下降/上升(1上升 0下降)", example = "1")
    private Integer pitchStatus;

    @ApiModelProperty(value = "格子信息")
    private MapData mapData;

    @ApiModelProperty(value = "告警信息")
    private List<FlightAlerts> flightAlertsList;

    @ApiModelProperty(value = "连线信息")
    private List<FlightAlerts> connectionInfoList;

    private int isCivil;

}
