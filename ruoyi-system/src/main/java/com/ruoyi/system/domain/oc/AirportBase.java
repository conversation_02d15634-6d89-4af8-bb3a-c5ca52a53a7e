package com.ruoyi.system.domain.oc;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/4/18 10:41
 * @mood 功能
 */
@ApiModel(value = "AirportBase", description = "基地实体")
@Data
public class AirportBase implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("名称")
    @Excel(name = "名称")
    private String name;

    /**
     * 三字码
     */
    @ApiModelProperty("三字码")
    @Excel(name = "三字码")
    private String threeAirportCode;

    /**
     * 四字码
     */
    @ApiModelProperty("四字码")
    @Excel(name = "四字码")
    private String fourAirportCode;

    @ApiModelProperty("公司代码")
    private String companyCode;
}
