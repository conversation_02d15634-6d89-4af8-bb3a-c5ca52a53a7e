package com.ruoyi.system.domain.mdmp.temporary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 飞机实时坐标与计划航线的距离
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "飞机实时坐标与计划航线的距离")
public class FlightDataRouteDistance {


    /**
     * 飞机坐标ID
     */
    @ApiModelProperty(value = "飞机坐标ID", example = "5")
    private Long flightDataId;

    /**
     * 与计划航线的距离
     */
    @ApiModelProperty(value = "与计划航线的距离", example = "348")
    private Double distance;

}
