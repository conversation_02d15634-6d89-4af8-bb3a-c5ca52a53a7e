package com.ruoyi.system.domain.oc.dto;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "查询航班计划列表入参")
public class QueryFlightPlanDTO {

    @ApiModelProperty(value = "航班类型（1：航线，2：空域）", required = true)
    private Integer flightType;
    @ApiModelProperty("任务类型（1:短途运输,2:航空护林,3:调机飞行,4:人工影响天气,5:飞行训练,6:航拍航摄）")
    @Excel(name = "任务类型")
    private String flightPurpose;

    @ApiModelProperty("航线类型(0:单程,1:往返)")
    private Integer routeType;

    @ApiModelProperty("起始城市")
    private String departCity;

    @ApiModelProperty("到达城市")
    private String arriveCity;

    @ApiModelProperty("航班号")
    private String flightNo;

    @ApiModelProperty("呼号")
    private String callSign;

    @ApiModelProperty("机尾号")
    private String aircraftTailNo;

    @ApiModelProperty("生效日期")
    private String validDate;

    @ApiModelProperty("失效日期")
    private String expireDate;

    /**
     * 航司代码
     */
    private String companyCode;
}
