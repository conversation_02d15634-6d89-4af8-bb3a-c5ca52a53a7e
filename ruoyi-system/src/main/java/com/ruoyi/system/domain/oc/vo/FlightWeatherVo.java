package com.ruoyi.system.domain.oc.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> by yaodan
 **/
@Data
public class FlightWeatherVo {
    @ApiModelProperty("机型")
    private String aircraftType;
    @ApiModelProperty("注册号")
    private String registrationNumber;
    @ApiModelProperty("航班日期")
    private String flightDate;
    @ApiModelProperty("任务书编号")
    private String taskBookNumber;
    @ApiModelProperty("气象信息列表")
    private List<FlightWeatherInfoVo> flightWeatherInfoVos;

}
