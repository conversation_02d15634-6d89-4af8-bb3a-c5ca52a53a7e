package com.ruoyi.system.domain.mdmp.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.system.domain.mdmp.FlightPlanRouteLongLat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/5/6 10:13
 * @description：
 * @modified By：
 * @version: $
 */
@Data
@ApiModel(value = "冲突航线对象")
public class ConflictRoute {

    private static final long serialVersionUID = 1L;

    /**
     * 航线代号
     */
    @Excel(name = "航线代号")
    @ApiModelProperty(value = "航线代号", example = "GH001", dataType = "String")
    private String routeCode;
    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "备注", dataType = "String")
    private String remarks;

    @Excel(name = "交点经度")
    @ApiModelProperty(value = "交点经度", example = "22.22123", dataType = "Double")
    private Double intersectionLong;

    @Excel(name = "交点纬度")
    @ApiModelProperty(value = "交点纬度", example = "22.22123", dataType = "Double")
    private Double intersectionLat;

    @Excel(name = "交点高度")
    @ApiModelProperty(value = "交点高度", example = "22.22123", dataType = "Double")
    private Double intersectionHeight;

    @ApiModelProperty(value = "计划编号", example = "1231231", dataType = "String")
    private String serialNo;

    @ApiModelProperty(value = "id", example = "id", dataType = "Long")
    private Long id;

    @ApiModelProperty("航班计划航线经纬度")
    private List<FlightPlanRouteLongLat> flightPlanRouteLongLatList;

}
