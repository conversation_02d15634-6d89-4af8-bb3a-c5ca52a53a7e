package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 topographic_map_data
 *
 * <AUTHOR>
 * @date 2024-10-14
 */
@Data
public class TopographicMapData
{
    private static final long serialVersionUID = 1L;

    /** $column.columnComment */
    private Long id;

    /** $column.columnComment */
    @ApiModelProperty(value = "格子状态", example = "0", dataType = "Long")
    private Long statusCode;

    /** 排序 */
    @ApiModelProperty(value = "排序", example = "0", dataType = "String")
    private Long index;


}
