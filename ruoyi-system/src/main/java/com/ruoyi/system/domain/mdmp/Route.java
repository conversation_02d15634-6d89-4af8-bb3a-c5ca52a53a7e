package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 航线对象 route
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@ApiModel(value = "Route", description = "航线实体")
@Data
public class Route
{
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    @ApiModelProperty("ID")
    private Long id;

    /** 航线代号 */
    @ApiModelProperty("航线代号")
    @Excel(name = "航线代号")
    @NotBlank(message = "航线代号不能为空")
    private String routeCode;


    /** 备注 */
    @ApiModelProperty("备注")
    @Excel(name = "备注")
    private String remarks;

    @ApiModelProperty("航线经纬度")
    private List<RouteLongLat> routeLongLatList;

    @ApiModelProperty("航线地图数据")
    private List<RouteMapData> routeMapDataList;

    private String deptCode;

    private List<String> deptCodeList;
}
