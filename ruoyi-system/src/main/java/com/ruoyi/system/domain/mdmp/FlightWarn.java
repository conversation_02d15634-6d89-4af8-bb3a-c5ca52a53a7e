package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 飞行告警信息
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行告警信息")
public class FlightWarn {

    /**
     * ID
     */
    @ApiModelProperty(value = "ID", example = "1")
    private Long id;

    /**
     * 飞行坐标ID
     */
    @ApiModelProperty(value = "飞行坐标ID", example = "14")
    private Long flightDataId;

    /**
     * 告警日期
     */
    @ApiModelProperty(value = "告警日期", example = "2024-06-12")
    private String warnDate;

    /**
     * 告警时间
     */
    @ApiModelProperty(value = "告警时间", example = "2024-06-12 10:58:17")
    private String warnTime;

    @ApiModelProperty(value = "正在告警的飞机", example = "B-7962")
    private String warnAircraft;

    @ApiModelProperty(value = "另一个飞机（进入了当前飞机安全距离内的飞机）", example = "B-8962")
    private String anotherAircraft;

    @ApiModelProperty(value = "告警信息", example = "告警信息")
    private String warnMessage;
}
