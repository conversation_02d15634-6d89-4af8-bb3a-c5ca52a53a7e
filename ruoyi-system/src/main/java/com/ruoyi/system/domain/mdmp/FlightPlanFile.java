package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 航班计划附件对象 flight_plan_file
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@ApiModel(value = "航班计划附件对象")
@Data
public class FlightPlanFile extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划
     */
    @Excel(name = "航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划")
    @ApiModelProperty(value = "航班计划类型(1,长期计划;2, 次日计划;3,当日放行计划 4 单一飞行计划)", required = true, allowableValues = "1,2,3")
    private Integer planType;

    /**
     * 飞行计划id
     */
    @Excel(name = "飞行计划id")
    @ApiModelProperty(value = "飞行计划id", example = "13", dataType = "Long")
    private Long flightPlanId;

    /**
     * 文件名
     */
    @Excel(name = "文件名")
    @ApiModelProperty(value = "文件名", example = "13", dataType = "String")
    @NotBlank(message = "文件名不能为空")
    private String fileName;

    /**
     * 文件地址
     */
    @Excel(name = "文件地址")
    @ApiModelProperty(value = "文件地址", example = "13", dataType = "String")
    @NotBlank(message = "文件地址不能为空")
    private String filePath;

    /**
     * 空军批件号
     */
    @Excel(name = "空军批件号")
    @ApiModelProperty(value = "空军批件号", example = "13", dataType = "String")
    @NotBlank(message = "空军批件号不能为空")
    private String approvalNumber;


}
