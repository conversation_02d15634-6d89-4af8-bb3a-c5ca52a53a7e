package com.ruoyi.system.domain.mdmp;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 跑道信息对象 r_runwayrvrres
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
public class RRunwayrvrres {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 报文表ID
     */
    @Excel(name = "报文表ID")
    private Long decodeReportId;

    /**
     * 跑道编号
     */
    @Excel(name = "跑道编号")
    private String runwayNO;

    /**
     * RVR 最小值
     */
    @Excel(name = "RVR 最小值")
    private Long rvrMin;

    /**
     * RVR 最大值
     */
    @Excel(name = "RVR 最大值")
    private Long rvrMax;

    /**
     * 趋势
     */
    @Excel(name = "趋势")
    private String trend;


    private Integer colourType;
}
