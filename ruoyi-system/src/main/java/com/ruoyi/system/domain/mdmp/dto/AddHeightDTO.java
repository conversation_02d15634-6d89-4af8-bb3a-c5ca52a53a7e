package com.ruoyi.system.domain.mdmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "添加高度入参")
public class AddHeightDTO {

    @ApiModelProperty(value = "ID(修改必传, 新增不传)", example = "1")
    private Integer id;

    @ApiModelProperty(value = "属性(0:标准海平面气压, A:修正海平面气压, H:场面气压)", example = "A", required = true)
    @NotBlank(message = "高度属性不能为空")
    private String attribute;
    /** 高度值 */
    @ApiModelProperty(value = "高度值", example = "0480", required = true)
    @NotBlank(message = "高度不能为空")
    private String height ;
    /** 高度类型(上升/下降);0:下降, 1:上升 */
    @ApiModelProperty(value = "高度类型(0:下降, 1:上升)", example = "1", required = true)
    @NotNull(message = "高度类型不能为空")
    private Integer heightType ;
    /** 高度是否保持;0:否, 1:保持 */
    @ApiModelProperty(value = "高度是否保持(0:否, 1:保持)", example = "0", required = true)
    @NotNull(message = "高度是否保持不能为空")
    private Integer keep ;
}
