package com.ruoyi.system.domain.mdmp.vo;


import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;


import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(description = "查询地图数据")
public class QueryRoutePlanningVo {

    @ApiModelProperty("起始点经度")
    @NotNull(message = "起始点经度不能为空")
    private BigDecimal startLongitude;
    @NotNull(message = "起始点纬度不能为空")
    @ApiModelProperty("起始点纬度")
    private BigDecimal startLatitude;
    @ApiModelProperty("结束点经度")
    @NotNull(message = "结束点经度不能为空")
    private BigDecimal endLongitude;
    @ApiModelProperty("结束点纬度")
    @NotNull(message = "结束点纬度不能为空")
    private BigDecimal endLatitude;
    @ApiModelProperty("缓冲距离")
    private BigDecimal bufferDistance;

}
