package com.ruoyi.system.domain.mdmp;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 作业区经纬度基础信息对象 work_basis_long_lat
 * 
 * <AUTHOR>
 * @date 2024-04-02
 */
@Data
@ApiModel(value = "WorkBasisLongLat", description = "作业区经纬度实体")
public class WorkBasisLongLat
{
    private static final long serialVersionUID = 1L;

    /** id */
    @ApiModelProperty("ID")
    private Long id;

    /** 作业区基础信息表Id */
    @ApiModelProperty("作业区基础信息表Id")
    @Excel(name = "作业区基础信息表Id")
    private Long workBasisId;

    /** 经度 */
    @ApiModelProperty("经度")
    @Excel(name = "经度")
    private String longitude;

    /** 纬度 */
    @ApiModelProperty("纬度")
    @Excel(name = "纬度")
    private String latitude;

    /** 高度 */
    @ApiModelProperty("高度")
    @Excel(name = "高度")
    private BigDecimal height;

    /** 排序 */
    @ApiModelProperty("排序")
    @Excel(name = "排序")
    private Integer sortNumber;

    /** 转换后的经度 */
    @ApiModelProperty("转换后的经度")
    @Excel(name = "转换后的经度")
    private Double doubleLongitude;

    /** 转换后的纬度 */
    @ApiModelProperty("转换后的纬度")
    @Excel(name = "转换后的纬度")
    private Double doubleLatitude;


    @ApiModelProperty("组别号")
    @Excel(name = "组别号")
    private Integer groupNumber;

    /** 点名称 */
    @ApiModelProperty("点名称")
    @Excel(name = "点名称")
    private String coordinateName;

}
