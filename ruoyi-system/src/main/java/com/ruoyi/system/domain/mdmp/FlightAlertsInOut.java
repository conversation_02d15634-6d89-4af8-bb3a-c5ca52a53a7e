package com.ruoyi.system.domain.mdmp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2025/6/10 15:56
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class FlightAlertsInOut {

    private Long id;

    private String aircraftReg;

    private String addTime;

    private Integer circleId;

    private Integer statusCode;

    private Integer oddOrEven;

    private Long workId;
}
