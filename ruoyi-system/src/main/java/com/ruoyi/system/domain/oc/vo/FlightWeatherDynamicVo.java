package com.ruoyi.system.domain.oc.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.system.domain.oc.entity.FlightDynamicInfo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR> by yaodan
 **/
@Data
public class FlightWeatherDynamicVo {
    @ApiModelProperty("机型")
    private String aircraftType;
    @ApiModelProperty("注册号")
    private String registrationNumber;
    @ApiModelProperty("航班日期")
    private String flightDate;
    @ApiModelProperty("任务书编号")
    private String taskBookNumber;
    @ApiModelProperty("总计地面时间（分钟）")
    private String groundTimeMinTotal;
    @ApiModelProperty("总计空中时间（分钟）")
    private String airTimeMinTotal;
    @ApiModelProperty("总计时间小计（分钟）")
    private String totalTimeMinTotal;
    @ApiModelProperty("总计架次")
    private String sortieCountTotal;
    @ApiModelProperty("气象信息列表")
    private List<FlightWeatherInfoVo> flightWeatherInfoVos;
    @ApiModelProperty("动态信息列表")
    private List<FlightDynamicInfo> flightDynamicInfoList;
}
