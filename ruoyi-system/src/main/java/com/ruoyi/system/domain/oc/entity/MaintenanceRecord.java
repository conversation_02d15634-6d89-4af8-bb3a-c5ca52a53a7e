package com.ruoyi.system.domain.oc.entity;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 维修记录信息对象 oc_maintenance_record
 *
 * <AUTHOR>
 * @date 2021-09-22
 */
@ApiModel(value = "MaintenanceRecord", description = "维修记录实体")
@Data
public class MaintenanceRecord {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("维修记录编号")
    private Long maintenanceRecordId;

    @ApiModelProperty("飞行器编号")
    @Excel(name = "飞行器编号")
    private Long aircraftId;

    @ApiModelProperty("维修员")
    @Excel(name = "维修员")
    private String repairman;

    @ApiModelProperty("维修时间")
    @Excel(name = "维修时间")
    private String maintenanceTime;

    @ApiModelProperty("维修机场")
    @Excel(name = "维修机场")
    private String maintenanceAirport;

    @ApiModelProperty("备注")
    @Excel(name = "备注")
    private String remarks;
}
