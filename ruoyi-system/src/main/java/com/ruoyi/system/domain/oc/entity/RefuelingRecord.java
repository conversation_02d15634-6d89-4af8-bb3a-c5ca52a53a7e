package com.ruoyi.system.domain.oc.entity;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 加油记录信息对象 oc_refueling_record
 *
 * <AUTHOR>
 * @date 2021-09-22
 */
@ApiModel(value = "RefuelingRecord", description = "加油记录实体")
@Data
public class RefuelingRecord {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("加油记录编号")
    private Long refuelingRecordId;

    @ApiModelProperty("飞行器编号")
    @Excel(name = "飞行器编号")
    private Long aircraftId;

    @ApiModelProperty("加油员")
    @Excel(name = "加油员")
    private String oiler;

    @ApiModelProperty("油量（L）")
    @Excel(name = "油量（L）")
    private Long oilQuantity;

    @ApiModelProperty("加油时间")
    @Excel(name = "加油时间")
    private String refuelingTime;

    @ApiModelProperty("加油机场")
    @Excel(name = "加油机场")
    private String refuelingAirport;

    @ApiModelProperty("备注")
    @Excel(name = "备注")
    private String remarks;
}
