package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "跑道")
public class Runway {
    /** ID */
    @ApiModelProperty(value = "ID")
    private Integer id ;
    /** 跑道名称 */
    @ApiModelProperty(value = "跑道名称")
    private String runwayName ;

    @ApiModelProperty(value = "飞机占用(0:空闲, 1:占用)")
    private Integer planeOccupy ;

    @ApiModelProperty(value = "人员占用(0:空闲, 1:占用)")
    private Integer personOccupy ;
}
