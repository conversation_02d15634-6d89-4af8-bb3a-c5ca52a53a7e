package com.ruoyi.system.domain.mdmp;

import java.math.BigDecimal;
import java.util.Date;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【能见度】对象 visibility_mp_data
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
public class VisibilityMpData extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /**
     * 数据值
     */
    @Excel(name = "数据值")
    private double dataValue;

    /**
     * 预计时间
     */
    private Long fcstTimeSequence;

    /**
     * 实时时间
     */
    private Long reatimeTimeSequence;



    /**
     * 经度
     */
    private Double longitudeMin;

    private Double longitudeMax;
    /**
     * 纬度
     */
    @Excel(name = "纬度")
    private Double latitude;

    /**
     * 高度
     */
    @Excel(name = "高度")
    private Double altitude;

    private Date addTime;


    private Double latitudeMin;

    private Double latitudeMax;

}
