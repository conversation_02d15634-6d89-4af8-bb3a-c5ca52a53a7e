package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "电子行程单的航班操作日志")
public class FlightLog {
    /** ID */
    @ApiModelProperty(value = "ID")
    private Integer id ;
    /** 航班ID */
    @ApiModelProperty(value = "航班ID")
    private Integer flightId ;
    /** 航空器呼号 */
    @ApiModelProperty(value = "航空器呼号")
    private String callSign ;
    /** 日志消息 */
    @ApiModelProperty(value = "日志消息")
    private String logMessage ;
    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private String createTime ;

    private String deptCode;

    private List<String> deptCodeList;
}
