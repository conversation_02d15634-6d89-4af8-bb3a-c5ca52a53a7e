package com.ruoyi.system.domain.mdmp;

import com.ruoyi.system.domain.mdmp.dto.AddTransitPointDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "经过点（进离场信息表中添加）")
public class AddTransitPoint {
    /** ID */
    @ApiModelProperty(value = "ID")
    private Integer id ;
    /** 进离场信息表ID */
    @ApiModelProperty(value = "进离场信息表ID")
    private Integer enterLeaveId ;
    /** 位置点名称 */
    @ApiModelProperty(value = "位置点名称")
    private String positionName ;
    /** 预计到达时间 */
    @ApiModelProperty(value = "预计到达时间", example = "0915")
    private String predictArriveTime ;
    /** 实际到达时间 */
    @ApiModelProperty(value = "实际到达时间", example = "0920")
    private String actualArriveTime ;

    public static List<AddTransitPoint> setting(List<AddTransitPointDTO> params) {
        List<AddTransitPoint> pointList = new ArrayList<>();
        for (AddTransitPointDTO param : params) {
            AddTransitPoint point = new AddTransitPoint();
            BeanUtils.copyProperties(param, point);
            pointList.add(point);
        }
        return pointList;
    }
}
