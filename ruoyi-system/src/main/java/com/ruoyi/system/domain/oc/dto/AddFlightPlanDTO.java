package com.ruoyi.system.domain.oc.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "新增飞行任务入参")
public class AddFlightPlanDTO {

    @ApiModelProperty(value = "任务类型", required = true)
    @NotBlank(message = "任务类型不能为空")
    private String flightPurpose;
    @ApiModelProperty(value = "航班类型（1：航线，2：空域）", required = true)
    private Integer flightType;

    /**
     * 航班日期
     */
    @ApiModelProperty(value = "航班日期", required = true)
    @JsonFormat(pattern = "yyyy-MM-dd")
    @NotEmpty(message = "航班日期不能为空")
    private Date flightDate;

    /**
     * 开始航班日期
     */
    @ApiModelProperty("开始航班日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDateBatchStart;

    /**
     * 结束航班日期
     */
    @ApiModelProperty("结束航班日期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date flightDateBatchEnd;

    @ApiModelProperty(value = "起飞点三字码", required = true)
    @NotBlank(message = "起飞点三字码不能为空")
    private String departAirportCode;

    @ApiModelProperty(value = "起飞点", required = true)
    @NotBlank(message = "起飞点不能为空")
    private String departCity;

    @ApiModelProperty(value = "降落点三字码")
    private String arriveAirportCode;

    @ApiModelProperty(value = "降落点")
    @NotBlank(message = "降落点不能为空")
    private String arriveCity;

    @ApiModelProperty("备降城市三字码")
    private String alternateAirportCode;

    @ApiModelProperty("备降城市")
    private String alternateCity;

    /**
     * 运行标准（1：，2：，3：，）
     */
    @ApiModelProperty(value = "运行标准", required = true)
    @NotNull(message = "运行标准不能为空")
    private Integer dperationStandard;

    /**
     * 计划起飞时间
     */
    @ApiModelProperty(value = "计划起飞时间(HH:mm)", required = true)
    @NotBlank(message = "降落点不能为空")
    private String planDepartTime;

    /**
     * 计划到达时间
     */
    @ApiModelProperty(value = "计划到达时间(HH:mm)", required = true)
    private String planArriveTime;
    @ApiModelProperty("机长的用户id，用,隔开")
    private String captainUserId;
    @ApiModelProperty("副驾驶的用户id，用,隔开")
    private String copilotUserId;
    @ApiModelProperty("机长类型(1:机长，2：实习机长，3：教员)")
    private Integer pilotType;
    @ApiModelProperty("副驾驶类型(1:副驾驶， 2：副驾驶A，3：学员，4：同乘)")
    private Integer copilotType;
    @ApiModelProperty("放行机务人员id，用,隔开")
    private String maintenanceId;
    @ApiModelProperty("安全员id，用,隔开")
    private String safetyOfficerId;
    @ApiModelProperty("运控值班的用户id，用,隔开")
    private String ocUserId;
    @ApiModelProperty("机械员的用户id，用,隔开")
    private String mechanicId;
    @ApiModelProperty("机械师")
    private String mechanicMasterId;
    @ApiModelProperty("现场组织人员的用户id，用,隔开")
    private String organizationId;
    @ApiModelProperty("安检员的用户id，用,隔开")
    private String inspectorId;
    @ApiModelProperty("售票员的用户id，用,隔开")
    private String conductorId;
    @ApiModelProperty(value = "值班经理")
    private String dutyManagerId;

    @ApiModelProperty(value = "飞行器id", required = true)
    private Long aircraftId;

    @ApiModelProperty(value = "机尾号", required = true)
    private String aircraftTailNo;

    @ApiModelProperty(value = "机型", required = true)
    private String aircraftStyle;

    @ApiModelProperty(value = "航线-空域")
    private String route;

    @ApiModelProperty(value = "营业时间")
    private String businessHours;
}
