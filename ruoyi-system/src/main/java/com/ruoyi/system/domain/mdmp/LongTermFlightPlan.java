package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.hibernate.validator.constraints.Range;
import org.springframework.validation.annotation.Validated;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.util.List;

/**
 * 长期飞行计划对象 long_term_flight_plan
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Data
@ApiModel(value = "长期飞行计划对象")
public class LongTermFlightPlan extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;


    /**
     * 计划名称
     */
    @Excel(name = "计划名称")
    @ApiModelProperty(value = "计划名称", example = "计划名称", dataType = "String")
    @NotBlank(message = "计划名称不能为空")
    private String name;


    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    @ApiModelProperty(value = "公司名称", example = "凯亚", dataType = "String")
    private String companyName;

    /**
     * 公司代码
     */
    @Excel(name = "公司代码")
    @ApiModelProperty(value = "公司代码", example = "KY", dataType = "String")
    private String companyCode;

    /**
     * 任务类型;枚举
     */
    @Excel(name = "任务类型;枚举")
    @ApiModelProperty(value = "任务类型", example = "KY", dataType = "String")
    @NotBlank(message = "任务类型不能为空")
    @Size(min = 0, max = 50, message = "任务类型不能超过50个字符")
    private String taskType;

    /**
     * 开始日期（长期计划）
     */
    @Excel(name = "开始日期", readConverterExp = "长=期计划")
    @ApiModelProperty(value = "开始日期", example = "2024-01-01", dataType = "String")
    @NotBlank(message = "开始日期不能为空")
    @Size(min = 0, max = 10, message = "开始日期不能超过10个字符")
    private String startDate;

    /**
     * 截止日期（长期计划）
     */
    @Excel(name = "截止日期", readConverterExp = "长=期计划")
    @ApiModelProperty(value = "截止日期", example = "2024-01-01", dataType = "String")
    @NotBlank(message = "截止日期不能为空")
    @Size(min = 0, max = 10, message = "截止日期不能超过10个字符")
    private String endDate;


    /**
     * 拒绝说明
     */
    @Excel(name = "拒绝说明")
    @ApiModelProperty(value = "拒绝说明", example = "拒绝说明", dataType = "String")
    private String refusalExplain;

    /**
     * 是否删除;0 未删除 1已删除
     */
    @Excel(name = "是否删除;0 未删除 1已删除")
    @ApiModelProperty(value = "是否删除( 0,未删除;1, 已删除)", required = true, allowableValues = "0,1")
    @Range(min = 0, max = 1, message = "是否删除必须为 0、1")
    private Integer isDelete;

    /**
     * 审核人
     */
    @Excel(name = "审核人")
    @ApiModelProperty(value = "审核人", example = "审核人", dataType = "String")
    private String auditor;

    /**
     * 审核时间
     */
    @Excel(name = "审核时间")
    @ApiModelProperty(value = "审核时间", example = "2024-01-01 12:00:00", dataType = "String")
    private String auditTime;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人", example = "创建人", dataType = "String")
    private String creator;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    @ApiModelProperty(value = "审核时间", example = "2024-01-01 12:00:00", dataType = "String")
    private String creationTime;

    /**
     * 联系人姓名
     */
    @Excel(name = "联系人姓名")
    @ApiModelProperty(value = "联系人姓名", example = "zs", dataType = "String")
    @NotBlank(message = "联系人姓名不能为空")
    @Size(min = 0, max = 255, message = "联系人姓名不能超过255个字符")
    private String contactName;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话")
    @ApiModelProperty(value = "联系人电话", example = "15333333333", dataType = "String")
    @NotBlank(message = "联系人电话不能为空")
    @Size(min = 0, max = 255, message = "联系人电话不能超过255个字符")
    private String contactPhone;

    /**
     * 编号
     */
    @Excel(name = "编号")
    @ApiModelProperty(value = "编号", example = "1232", dataType = "String")
    private String serialNo;

    /**
     * 状态;0草稿/1 已提交/ 2 已通过/ 3审核中/ 4拒绝
     */
    @Excel(name = "状态;0草稿/1 已提交/ 2 已通过/ 3审核中/ 4拒绝")
    @ApiModelProperty(value = "状态( 0,草稿;1, 已提交;2, 已通过;3, 审核中;4, 拒绝)", required = true, allowableValues = "0,1,2,3,4")
    @NotNull(message = "状态不能为空")
    @Range(min = 0, max = 4, message = "状态类型必须为 0、1、2、3、4")
    private Integer status;


    /**
     * 是否作业;1作业 0 航线（ 不作业）
     */
    @Excel(name = "是否作业;1作业 0 航线", readConverterExp = "不=作业")
    @NotNull(message = "是否作业不能为空")
    @Range(min = 0, max = 1, message = "是否作业类型必须为 0、1")
    private Integer isWork;


    /**
     * 关联表
     * <p>
     * 计划 机型机号关联表
     * 计划 机场关联表
     * 计划 航线关联表
     * 计划 作业区关联表
     * 计划 附件关联表
     */

    @Excel(name = "机型机号关联")
    @ApiModelProperty(required = true, value = "机型机号关联")
    @Valid
    @NotEmpty(message = "机型机号不能为空")
    private List<FlightPlanAircraftModel> planModelAircraftList;


    @Excel(name = "机场关联表")
    @ApiModelProperty(required = true, value = "机场关联表")
    @Valid
    @NotEmpty(message = "机场不能为空")
    private List<FlightPlanAirport> flightPlanAirportList;


    @Excel(name = "航线关联表")
    @ApiModelProperty(value = "航线关联表")
    @Valid
    private List<FlightPlanRoute> flightPlanRouteList;


    @Excel(name = "附件关联表")
    @ApiModelProperty(value = "附件关联表")
    @Valid
    @NotEmpty(message = "附件不能为空")
    private List<FlightPlanFile> flightPlanFileList;


    @Excel(name = "作业区关联表")
    @ApiModelProperty(value = "作业区关联表")
    @Valid
    private List<FlightPlanWork> flightPlanWorkList;

    @ApiModelProperty(value = "新增修改审核冲突验证")
    private boolean verify = false;

    @ApiModelProperty(value = "航线冲突说明", example = "航线冲突说明", dataType = "String")
    private String routeConflictDescription;

    @ApiModelProperty(value = "作业区冲突说明", example = "作业区冲突说明", dataType = "String")
    private String workConflictDescription;

    private Integer planType;

    private String deptCode;

    private List<String> deptCodeList;
}
