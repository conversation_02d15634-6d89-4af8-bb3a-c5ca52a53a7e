package com.ruoyi.system.domain.oc.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR> by yaodan
 **/
@Data
public class FlightWeatherInfoDto {
    /**
     * flight_task_info表主键id
     */
    @ApiModelProperty("flight_task_info表主键id")
    private Long flightTaskInfoId;

    /**
     * 批次
     */
    @ApiModelProperty("批次")
    private String batch;

    /**
     * 位置类型（0-始发地，1-目的地）
     */
    @ApiModelProperty("位置类型（0-始发地，1-目的地）")
    private Integer locationType;

    /**
     * 位置名称
     */
    @ApiModelProperty("位置名称")
    private String locationName;

    /**
     * 天气
     */
    @ApiModelProperty("天气")
    private String weather;

    /**
     * 云高（m）
     */
    @ApiModelProperty("云高（m）")
    private String cloudHeight;

    /**
     * 温度（C）
     */
    @ApiModelProperty("温度（C）")
    private String temperature;

    /**
     * 风向
     */
    @ApiModelProperty("风向")
    private String windDirection;

    /**
     * 风速（m/s）
     */
    @ApiModelProperty("风速（m/s）")
    private String windSpeed;

    /**
     * 能见度（m）
     */
    @ApiModelProperty("能见度（m）")
    private String visibility;

    /**
     * QNH(hPa)
     */
    @ApiModelProperty("QNH(hPa)")
    private String qnh;
}
