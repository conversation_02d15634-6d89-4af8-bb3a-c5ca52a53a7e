package com.ruoyi.system.domain.oc.vo;

import com.ruoyi.system.domain.oc.WxMenu;
import com.ruoyi.system.domain.oc.WxUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 微信用户详情信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "微信用户详情信息返回")
public class WxUserInfoVO {

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    private String userName;

    /**
     * 航司名称
     */
    @ApiModelProperty("航司名称")
    private String companyName;

    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String phoneNumber;

    /**
     * 用户状态(0:禁用, 1:可用)
     */
    @ApiModelProperty("用户状态(0:禁用, 1:可用)")
    private Integer userStatus;

    /**
     * 飞行时间
     */
    @ApiModelProperty("飞行时间")
    private Integer flyTime;

    /**
     * 飞行次数
     */
    @ApiModelProperty("飞行次数")
    private Integer flyNumber;

    /**
     * 角色Id
     */
    @ApiModelProperty("角色Id")
    private List<WxRoleVO> wxRoles;

    @ApiModelProperty("权限")
    private List<WxMenu> wxMenus;

    public static WxUserInfoVO setting(WxUser wxUser, List<WxRoleVO> wxRoles, List<WxMenu> wxMenus) {
        WxUserInfoVO wxUserVO = new WxUserInfoVO();
        wxUserVO.setUserId(wxUser.getUserId());
        wxUserVO.setUserName(wxUser.getUserName());
        wxUserVO.setCompanyName(wxUser.getCompanyName());
        wxUserVO.setPhoneNumber(wxUser.getPhoneNumber());
        wxUserVO.setUserStatus(wxUser.getUserStatus());
        wxUserVO.setFlyTime(wxUser.getFlyTime() / 60);
        wxUserVO.setFlyNumber(wxUser.getFlyNumber());
        wxUserVO.setWxRoles(wxRoles);
        wxUserVO.setWxMenus(wxMenus);
        return wxUserVO;
    }
}
