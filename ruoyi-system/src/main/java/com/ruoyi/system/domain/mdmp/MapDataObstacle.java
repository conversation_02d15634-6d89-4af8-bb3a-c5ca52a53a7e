package com.ruoyi.system.domain.mdmp;


import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "障碍物地图数据")
public class MapDataObstacle extends BaseEntity {

    private Long id;

    @ApiModelProperty(value = "地图数据index", example = "111", dataType = "Long")
    private Integer mapDataIndex;

    @ApiModelProperty(value = "障碍物空域关联ID", example = "111", dataType = "Long")
    private Long airspaceId;
    @ApiModelProperty(value = "空域地图数据表关联ID", example = "111", dataType = "Long")
    private Long airspaceMapDataId;

    @Excel(name = "经度起始点")
    @ApiModelProperty(value = "经度起始点", example = "85.25", dataType = "String")
    private BigDecimal longitudeStart;

    @Excel(name = "经度截止点")
    @ApiModelProperty(value = "经度截止点", example = "85.25", dataType = "BigDecimal")
    private BigDecimal longitudeEnd;

    @Excel(name = "纬度起始")
    @ApiModelProperty(value = "纬度起始", example = "22.25", dataType = "BigDecimal")
    private BigDecimal latitudeStart;
    @Excel(name = "纬度截止")
    @ApiModelProperty(value = "纬度截止", example = "22.25", dataType = "BigDecimal")
    private BigDecimal latitudeEnd;
    @Excel(name = "高度起始")
    @ApiModelProperty(value = "高度起始", example = "1000", dataType = "Integer")
    private BigDecimal heightStart;
    @Excel(name = "高度截止")
    @ApiModelProperty(value = "高度截止", example = "2000", dataType = "Integer")
    private BigDecimal heightEnd;
    @Excel(name = "大气压起始")
    @ApiModelProperty(value = "大气压起始", example = "1000", dataType = "Integer")
    private BigDecimal pressureStart;
    @Excel(name = "大气压截止")
    @ApiModelProperty(value = "大气压截止", example = "1100", dataType = "Integer")
    private BigDecimal pressureEnd;
    @Excel(name = "第几层")
    @ApiModelProperty(value = "第几层", example = "1", dataType = "Integer")
    private Integer layer;
    @Excel(name = "经度递增")
    @ApiModelProperty(value = "经度递增", example = "0.05", dataType = "BigDecimal")
    private BigDecimal longitudeIncrease;
    @Excel(name = "纬度递增")
    @ApiModelProperty(value = "纬度递增", example = "0.05", dataType = "BigDecimal")
    private BigDecimal latitudeIncrease;
    @Excel(name = "状态码")
    @ApiModelProperty(value = "状态码 55 红色 0蓝色", example = "1", dataType = "Integer")
    private Integer statusCode;
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序", example = "1", dataType = "Integer")
    private Integer index;



}


