package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "电子行程单的航班指令消息")
public class FlightCommandLog {

    @ApiModelProperty(value = "ID")
    private Integer id ;
    @ApiModelProperty(value = "航班ID")
    private Integer flightId ;
    @ApiModelProperty(value = "指令消息内容")
    private String commandLog ;
    @ApiModelProperty(value = "航空器呼号")
    private String callSign ;
    @ApiModelProperty(value = "已送达")
    private String delivered ;
    @ApiModelProperty(value = "已查看")
    private String viewed ;
    @ApiModelProperty(value = "已执行")
    private String executed ;
    @ApiModelProperty(value = "日期")
    private String createDate ;
    @ApiModelProperty(value = "创建时间")
    private String createTime ;
    @ApiModelProperty(value = "修改时间")
    private String updateTime;
}
