package com.ruoyi.system.domain.oc;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.io.Serializable;

/**
 * 任务类型 oc_flight_purpose
 *
 * <AUTHOR>
 * @date 2022/4/24 9:55
 * @mood 功能
 */
@ApiModel(value = "FlightPurpose", description = "任务类型实体")
@Data
public class FlightPurpose implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("名称")
    @Excel(name = "名称")
    private String name;

    @ApiModelProperty("公司代码")
    private String companyCode;
}
