package com.ruoyi.system.domain.mdmp.vo;


import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;

@Data
@ApiModel(description = "查询地图数据经纬度")
public class MapDataLongLatVo {

    /**
     * 经度
     */
    @ApiModelProperty("经度122.20")
    @Excel(name = "经度")
    @NotNull(message = "经度不能为空")
    private BigDecimal longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度122.20")
    @Excel(name = "纬度")
    @NotNull(message = "纬度不能为空")
    private BigDecimal latitude;


    /**
     * 排序号
     */
    @ApiModelProperty("排序号")
    @Excel(name = "排序号")
    @NotNull(message = "排序号不能为空")
    private Integer sortNumber;

    /**
     * 高度
     */
    @ApiModelProperty("高度")
    @Excel(name = "高度")
    private BigDecimal height;
}
