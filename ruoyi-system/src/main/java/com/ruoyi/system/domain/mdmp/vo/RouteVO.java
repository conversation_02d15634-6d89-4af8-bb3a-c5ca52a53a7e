package com.ruoyi.system.domain.mdmp.vo;

import com.ruoyi.system.domain.mdmp.FlightPlanRoute;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "当日在飞的航线信息返回参数")
public class RouteVO {

    /**
     * 航班计划id
     */
    @ApiModelProperty(value = "航班计划id", example = "1", dataType = "Long")
    private Long flightPlanId;

    /**
     * 航线代号
     */
    @ApiModelProperty(value = "航线代号", example = "GH001", dataType = "String")
    private String routeCode;

    public static List<RouteVO> setting(Long flightPlanId, List<FlightPlanRoute> routeList2) {
        List<RouteVO> voList = new ArrayList<>();
        for (FlightPlanRoute flightPlanRoute : routeList2) {
            RouteVO vo = new RouteVO();
            vo.setFlightPlanId(flightPlanId);
            vo.setRouteCode(flightPlanRoute.getRouteCode());
            voList.add(vo);
        }
        return voList;
    }
}
