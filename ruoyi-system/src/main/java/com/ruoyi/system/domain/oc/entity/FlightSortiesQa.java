package com.ruoyi.system.domain.oc.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 记录班次下飞行问答对象 oc_flight_sorties_qa
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Data
public class FlightSortiesQa extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 航班班次编号
     */
    private Long id;

    /**
     * 航班计划编号
     */
    @Excel(name = "航班计划编号")
    private Long flightSortiesId;

    /**
     * 获得分数
     */
    @Excel(name = "获得分数")
    private Double grade;

    /**
     * 飞行问答时间
     */
    @Excel(name = "飞行问答时间")
    private String flyQaTime;

    // 题 用,隔开
    private String qa;

}
