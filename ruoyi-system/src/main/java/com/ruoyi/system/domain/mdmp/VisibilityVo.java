package com.ruoyi.system.domain.mdmp;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import lombok.Data;

import java.math.BigDecimal;
import java.util.Date;

/**
 * <AUTHOR>
 * @date ：Created in 2025/3/25 13:33
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class VisibilityVo {


    /** 数据值 */
    @Excel(name = "数据值")
    private double dataValue;

    /** 预计时间 */
    @Excel(name = "预计时间")
    private Long fcstTimeSequence;

    /** 实时时间 */
    @Excel(name = "实时时间")
    private Long reatimeTimeSequence;

    /** 开始经度 */
    @Excel(name = "开始经度")
    private double longitudeMin;

    /** 纬度 */
    @Excel(name = "纬度")
    private double latitude;

    /** 高度 */
    @Excel(name = "高度")
    private double altitude;

    /** 时间戳 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "时间戳", width = 30, dateFormat = "yyyy-MM-dd")
    private Date addTime;

    /** 最大经度 */
    @Excel(name = "最大经度")
    private double longitudeMax;

    /** 最小纬度 */
    @Excel(name = "最小纬度")
    private double latitudeMin;

    /** 最大纬度 */
    @Excel(name = "最大纬度")
    private double latitudeMax;

    private int windType;

    public VisibilityVo(double minLon, double maxLon, double minLat, double maxLat, double value,Long fcstTimeSequence,Double altitude,Date addTime, int windType) {
        this.longitudeMin = minLon;
        this.longitudeMax = maxLon;
        this.latitudeMin = minLat;
        this.latitudeMax = maxLat;
        this.dataValue = value;
        this.fcstTimeSequence = fcstTimeSequence/1000;
        this.altitude = altitude;
        this.addTime = addTime;
        this.windType=windType;
    }
}
