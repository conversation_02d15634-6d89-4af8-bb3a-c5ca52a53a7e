package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2025/1/23 10:51
 * @description：
 * @modified By：
 * @version: $
 */
@Data
public class RadarContent {

    @ApiModelProperty(value = "预报时间点")
    private List<Long> fcstTimeSequence;


    @ApiModelProperty(value = "实时时间点")
    private List<Long> reatimeTimeSequence;

    public RadarContent(List<Long> fcstTimeSequence, List<Long> reatimeTimeSequence) {
        this.fcstTimeSequence = fcstTimeSequence;
        this.reatimeTimeSequence = reatimeTimeSequence;
    }
}
