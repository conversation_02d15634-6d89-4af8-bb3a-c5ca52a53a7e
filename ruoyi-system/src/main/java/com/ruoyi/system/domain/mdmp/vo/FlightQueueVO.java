package com.ruoyi.system.domain.mdmp.vo;

import com.ruoyi.system.domain.mdmp.Flight;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "航班队列返回参数")
public class FlightQueueVO {

    /** 进港航班 */
    @ApiModelProperty(value = "进港航班")
    private List<Flight> inboundFlight ;

    /** 出港航班 */
    @ApiModelProperty(value = "出港航班")
    private List<Flight> outboundFlight ;
}
