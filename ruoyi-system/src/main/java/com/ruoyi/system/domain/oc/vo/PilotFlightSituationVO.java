package com.ruoyi.system.domain.oc.vo;

import com.ruoyi.common.utils.ArithmeticUtils;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行员飞行情况")
public class PilotFlightSituationVO {

    @ApiModelProperty("七天飞行时间")
    private Double weekTime;
    @ApiModelProperty("七天飞行架次")
    private Integer weekFlightFrequency;

    @ApiModelProperty("本月飞行时间")
    private Double monthTime;
    @ApiModelProperty("本月飞行架次")
    private Integer monthFlightFrequency;

    @ApiModelProperty("年度飞行时间")
    private Double yearTime;
    @ApiModelProperty("年度飞行架次")
    private Integer yearFlightFrequency;

    @ApiModelProperty("累计飞行时间")
    private Double addUpTime;
    @ApiModelProperty("累计飞行架次")
    private Integer addUpFlightFrequency;

    public static void setting(PilotFlightSituationVO flightSituation, int flightFrequency, int minutes, String type) {
        switch (type) {
            case "week":
                flightSituation.setWeekFlightFrequency(flightFrequency);
                flightSituation.setWeekTime(ArithmeticUtils.minutesToHour(minutes));
                break;
            case "month":
                flightSituation.setMonthFlightFrequency(flightFrequency);
                flightSituation.setMonthTime(ArithmeticUtils.minutesToHour(minutes));
                break;
            case "year":
                flightSituation.setYearFlightFrequency(flightFrequency);
                flightSituation.setYearTime(ArithmeticUtils.minutesToHour(minutes));
                break;
            default:
                break;
        }
    }
}
