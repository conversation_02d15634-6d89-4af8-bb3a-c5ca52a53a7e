package com.ruoyi.system.domain.mdmp;

import com.fasterxml.jackson.annotation.JsonIgnore;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import javax.persistence.Transient;
import lombok.experimental.Accessors;

/**
 * <AUTHOR>
 */
@Data
@Accessors(chain = true)
@ApiModel(description = "航班信息(由机场发送的报文获取)")
public class Flight {
    /** 航班ID */
    @ApiModelProperty(value = "航班ID")
    private Integer id ;
    /** 航班日期 */
    @ApiModelProperty(value = "航班日期")
    private String flightDate ;
    /** 航班状态;1:执行中, 2:已执行, 3:已取消 */
    @ApiModelProperty(value = "航班状态(1:正常, 2:异常, 3:已取消, 4:已删除)")
    private Integer flightStatus ;
    /** 航空器呼号 */
    @ApiModelProperty(value = "航空器呼号")
    private String callSign ;
    /** 起始城市 */
    @ApiModelProperty(value = "起始城市")
    private String departCity ;
    /** 起始机场四字码 */
    @ApiModelProperty(value = "起始机场四字码")
    private String departAirportCode ;
    /** 到达城市 */
    @ApiModelProperty(value = "到达城市")
    private String arriveCity ;
    /** 到达机场四字码 */
    @ApiModelProperty(value = "到达机场四字码")
    private String arriveAirportCode ;
    /** 计划起飞时间 */
    @ApiModelProperty(value = "计划起飞时间")
    private String planDepartTime ;
    /** 计划到达时间 */
    @ApiModelProperty(value = "计划到达时间")
    private String planArriveTime ;
    /** 机型 */
    @ApiModelProperty(value = "机型")
    private String aircraftType ;
    /** 尾流标志 */
    @ApiModelProperty(value = "尾流标志")
    private String tailSign ;
    /** 二次雷达应答机模式及编码 */
    @ApiModelProperty(value = "二次雷达应答机模式及编码")
    private String radarTransponderMode ;
    /** 实际起飞时间 */
    @ApiModelProperty(value = "实际起飞时间")
    private String actualDepartTime ;
    /** 实际到达时间 */
    @ApiModelProperty(value = "实际到达时间")
    private String actualArriveTime ;
    /** 进出港标识;0:进港, 1:离港 */
    @ApiModelProperty(value = "进出港标识(0:进港, 1:离港)")
    private Integer ioSign ;
    /** 进或离场状态;(等待区/进离场区/跑道/地面/完成) */
    @ApiModelProperty(value = "进或离场状态(等待区/进离场区/跑道/地面/完成)")
    private String progress ;
    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private String createTime ;
    /** 修改时间 */
    @ApiModelProperty(value = "修改时间")
    private String updateTime ;
    @ApiModelProperty(value = "默认高度属性(0:标准海平面气压, A:修正海平面气压, H:场面气压)")
    private String heightAttribute;
    /** 默认高度 */
    @ApiModelProperty(value = "默认高度")
    private String defaultHeight ;
    /** 放行备注 */
    @ApiModelProperty(value = "放行备注")
    private String releaseRemark ;

    private String deptCode;

    @ApiModelProperty(value = "进离场信息")
    private EnterLeavePort enterLeavePort ;

    /**
     * 用于排序的时间
     */
    @JsonIgnore
    @Transient
    private String sortTime;
}
