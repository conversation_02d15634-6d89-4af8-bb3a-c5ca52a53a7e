package com.ruoyi.system.domain.mdmp;

import com.ruoyi.system.domain.mdmp.dto.AddCommandDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "指令（进离场信息表中添加）")
public class AddCommand {

    @ApiModelProperty(value = "ID")
    private Integer id ;
    /** 进离场信息表ID */
    @ApiModelProperty(value = "进离场信息表ID")
    private Integer enterLeaveId ;
    /** 指令;例:盲降、高于3000米 */
    @ApiModelProperty(value = "指令(高于;盲降)", example = "高于")
    private String command ;
    /** 指令值 */
    @ApiModelProperty(value = "指令值", example = "3000")
    private Integer commandValue ;

    public static List<AddCommand> setting(List<AddCommandDTO> params) {
        List<AddCommand> commandList = new ArrayList<>();
        for (AddCommandDTO param : params) {
            AddCommand command = new AddCommand();
            BeanUtils.copyProperties(param, command);
            commandList.add(command);
        }
        return commandList;
    }
}
