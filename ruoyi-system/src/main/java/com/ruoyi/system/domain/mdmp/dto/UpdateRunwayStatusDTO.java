package com.ruoyi.system.domain.mdmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "修改跑道状态入参")
public class UpdateRunwayStatusDTO {

    /** ID */
    @ApiModelProperty(value = "跑道ID", example = "1", required = true)
    @NotNull(message = "跑道ID不能为空")
    private Integer id ;

    /** 人员占用(0:空闲, 1:占用) */
    @ApiModelProperty(value = "人员占用(0:空闲, 1:占用)", example = "1", required = true)
    private Integer personOccupy ;
}
