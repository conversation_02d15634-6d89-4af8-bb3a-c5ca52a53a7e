package com.ruoyi.system.domain.mdmp.vo;

import com.ruoyi.system.domain.mdmp.*;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2025/5/20 9:16
 * @description：
 * @modified By：
 * @version: $
 */
@Data
@ApiModel(description = "分解报文数据")
public class DecodeReportVo {

    @ApiModelProperty("ID")
    private Long id;

    @ApiModelProperty("报文内容")
    private String content;

    @ApiModelProperty("报文类型;SA、SP、FT、FC，其中SA、SP为 METAR，FT、FC为TAF")
    private String type;

    @ApiModelProperty("时间戳")
    private Long validTimeBegin;

    @ApiModelProperty("转换后的时间")
    private Integer hour;

    @ApiModelProperty("颜色类型 0绿色，1黄色，2红色")
    private Integer colourType;

//    @ApiModelProperty("温度，metar报文时有值，taf报文为空")
//    private Integer temperature;
//
//    @ApiModelProperty("最小温度：taf报文时有值，metar报文为空")
//    private RMintemperatureres minTemperatureResDTO;
//
//    @ApiModelProperty("最大温度：taf报文时有值，metar报文为空")
//    private RMaxtemperatureres maxTemperatureResDTO;
//
//    @ApiModelProperty("风")
//    private RWindinfores windInfoResDto;
//
//    @ApiModelProperty("能见度")
//    private RVisinfores visInfoResDto;
//
//    @ApiModelProperty("跑道视程")
//    private List<RRunwayrvrres> runwayRvrResDtoList;
//
//    @ApiModelProperty("云")
//    private List<RCloudinfores> cloudResDtoList;
//
//    @ApiModelProperty("强度")
//    private List<RWeatherres> weatherResDtoList;


}
