package com.ruoyi.system.domain.oc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "修改微信用户信息入参")
public class UpdateWxUserDTO {

    /**
     * 用户ID
     */
    @ApiModelProperty(value = "用户ID", required = true)
    private Long userId;

    /**
     * 用户状态(0:禁用, 1:可用)
     */
    @ApiModelProperty("用户状态(0:禁用, 1:可用)")
    private Integer userStatus;

    /**
     * 飞行时间
     */
    @ApiModelProperty("飞行时间(小时)")
    private Integer flyTime;

    /**
     * 飞行次数
     */
    @ApiModelProperty("飞行次数")
    private Integer flyNumber;

    /**
     * 微信用户角色ID
     */
    @ApiModelProperty("微信用户角色ID")
    private Long[] roleIds;

    /**
     * 菜单权限ID
     */
    @ApiModelProperty("菜单权限ID")
    private Long[] menuIds;
}
