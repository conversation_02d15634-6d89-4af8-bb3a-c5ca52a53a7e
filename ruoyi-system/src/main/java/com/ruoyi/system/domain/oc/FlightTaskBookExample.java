package com.ruoyi.system.domain.oc;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class FlightTaskBookExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public FlightTaskBookExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeIsNull() {
            addCriterion("aircraft_type is null");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeIsNotNull() {
            addCriterion("aircraft_type is not null");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeEqualTo(String value) {
            addCriterion("aircraft_type =", value, "aircraftType");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeNotEqualTo(String value) {
            addCriterion("aircraft_type <>", value, "aircraftType");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeGreaterThan(String value) {
            addCriterion("aircraft_type >", value, "aircraftType");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeGreaterThanOrEqualTo(String value) {
            addCriterion("aircraft_type >=", value, "aircraftType");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeLessThan(String value) {
            addCriterion("aircraft_type <", value, "aircraftType");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeLessThanOrEqualTo(String value) {
            addCriterion("aircraft_type <=", value, "aircraftType");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeLike(String value) {
            addCriterion("aircraft_type like", value, "aircraftType");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeNotLike(String value) {
            addCriterion("aircraft_type not like", value, "aircraftType");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeIn(List<String> values) {
            addCriterion("aircraft_type in", values, "aircraftType");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeNotIn(List<String> values) {
            addCriterion("aircraft_type not in", values, "aircraftType");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeBetween(String value1, String value2) {
            addCriterion("aircraft_type between", value1, value2, "aircraftType");
            return (Criteria) this;
        }

        public Criteria andAircraftTypeNotBetween(String value1, String value2) {
            addCriterion("aircraft_type not between", value1, value2, "aircraftType");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIsNull() {
            addCriterion("registration_number is null");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIsNotNull() {
            addCriterion("registration_number is not null");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberEqualTo(String value) {
            addCriterion("registration_number =", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberNotEqualTo(String value) {
            addCriterion("registration_number <>", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberGreaterThan(String value) {
            addCriterion("registration_number >", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberGreaterThanOrEqualTo(String value) {
            addCriterion("registration_number >=", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberLessThan(String value) {
            addCriterion("registration_number <", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberLessThanOrEqualTo(String value) {
            addCriterion("registration_number <=", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberLike(String value) {
            addCriterion("registration_number like", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberNotLike(String value) {
            addCriterion("registration_number not like", value, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberIn(List<String> values) {
            addCriterion("registration_number in", values, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberNotIn(List<String> values) {
            addCriterion("registration_number not in", values, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberBetween(String value1, String value2) {
            addCriterion("registration_number between", value1, value2, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andRegistrationNumberNotBetween(String value1, String value2) {
            addCriterion("registration_number not between", value1, value2, "registrationNumber");
            return (Criteria) this;
        }

        public Criteria andCallSignIsNull() {
            addCriterion("call_sign is null");
            return (Criteria) this;
        }

        public Criteria andCallSignIsNotNull() {
            addCriterion("call_sign is not null");
            return (Criteria) this;
        }

        public Criteria andCallSignEqualTo(String value) {
            addCriterion("call_sign =", value, "callSign");
            return (Criteria) this;
        }

        public Criteria andCallSignNotEqualTo(String value) {
            addCriterion("call_sign <>", value, "callSign");
            return (Criteria) this;
        }

        public Criteria andCallSignGreaterThan(String value) {
            addCriterion("call_sign >", value, "callSign");
            return (Criteria) this;
        }

        public Criteria andCallSignGreaterThanOrEqualTo(String value) {
            addCriterion("call_sign >=", value, "callSign");
            return (Criteria) this;
        }

        public Criteria andCallSignLessThan(String value) {
            addCriterion("call_sign <", value, "callSign");
            return (Criteria) this;
        }

        public Criteria andCallSignLessThanOrEqualTo(String value) {
            addCriterion("call_sign <=", value, "callSign");
            return (Criteria) this;
        }

        public Criteria andCallSignLike(String value) {
            addCriterion("call_sign like", value, "callSign");
            return (Criteria) this;
        }

        public Criteria andCallSignNotLike(String value) {
            addCriterion("call_sign not like", value, "callSign");
            return (Criteria) this;
        }

        public Criteria andCallSignIn(List<String> values) {
            addCriterion("call_sign in", values, "callSign");
            return (Criteria) this;
        }

        public Criteria andCallSignNotIn(List<String> values) {
            addCriterion("call_sign not in", values, "callSign");
            return (Criteria) this;
        }

        public Criteria andCallSignBetween(String value1, String value2) {
            addCriterion("call_sign between", value1, value2, "callSign");
            return (Criteria) this;
        }

        public Criteria andCallSignNotBetween(String value1, String value2) {
            addCriterion("call_sign not between", value1, value2, "callSign");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeIsNull() {
            addCriterion("transponder_code is null");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeIsNotNull() {
            addCriterion("transponder_code is not null");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeEqualTo(String value) {
            addCriterion("transponder_code =", value, "transponderCode");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeNotEqualTo(String value) {
            addCriterion("transponder_code <>", value, "transponderCode");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeGreaterThan(String value) {
            addCriterion("transponder_code >", value, "transponderCode");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeGreaterThanOrEqualTo(String value) {
            addCriterion("transponder_code >=", value, "transponderCode");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeLessThan(String value) {
            addCriterion("transponder_code <", value, "transponderCode");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeLessThanOrEqualTo(String value) {
            addCriterion("transponder_code <=", value, "transponderCode");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeLike(String value) {
            addCriterion("transponder_code like", value, "transponderCode");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeNotLike(String value) {
            addCriterion("transponder_code not like", value, "transponderCode");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeIn(List<String> values) {
            addCriterion("transponder_code in", values, "transponderCode");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeNotIn(List<String> values) {
            addCriterion("transponder_code not in", values, "transponderCode");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeBetween(String value1, String value2) {
            addCriterion("transponder_code between", value1, value2, "transponderCode");
            return (Criteria) this;
        }

        public Criteria andTransponderCodeNotBetween(String value1, String value2) {
            addCriterion("transponder_code not between", value1, value2, "transponderCode");
            return (Criteria) this;
        }

        public Criteria andFlightDateIsNull() {
            addCriterion("flight_date is null");
            return (Criteria) this;
        }

        public Criteria andFlightDateIsNotNull() {
            addCriterion("flight_date is not null");
            return (Criteria) this;
        }

        public Criteria andFlightDateEqualTo(String value) {
            addCriterion("flight_date =", value, "flightDate");
            return (Criteria) this;
        }

        public Criteria andFlightDateNotEqualTo(String value) {
            addCriterion("flight_date <>", value, "flightDate");
            return (Criteria) this;
        }

        public Criteria andFlightDateGreaterThan(String value) {
            addCriterion("flight_date >", value, "flightDate");
            return (Criteria) this;
        }

        public Criteria andFlightDateGreaterThanOrEqualTo(String value) {
            addCriterion("flight_date >=", value, "flightDate");
            return (Criteria) this;
        }

        public Criteria andFlightDateLessThan(String value) {
            addCriterion("flight_date <", value, "flightDate");
            return (Criteria) this;
        }

        public Criteria andFlightDateLessThanOrEqualTo(String value) {
            addCriterion("flight_date <=", value, "flightDate");
            return (Criteria) this;
        }

        public Criteria andFlightDateLike(String value) {
            addCriterion("flight_date like", value, "flightDate");
            return (Criteria) this;
        }

        public Criteria andFlightDateNotLike(String value) {
            addCriterion("flight_date not like", value, "flightDate");
            return (Criteria) this;
        }

        public Criteria andFlightDateIn(List<String> values) {
            addCriterion("flight_date in", values, "flightDate");
            return (Criteria) this;
        }

        public Criteria andFlightDateNotIn(List<String> values) {
            addCriterion("flight_date not in", values, "flightDate");
            return (Criteria) this;
        }

        public Criteria andFlightDateBetween(String value1, String value2) {
            addCriterion("flight_date between", value1, value2, "flightDate");
            return (Criteria) this;
        }

        public Criteria andFlightDateNotBetween(String value1, String value2) {
            addCriterion("flight_date not between", value1, value2, "flightDate");
            return (Criteria) this;
        }

        public Criteria andDepartureIsNull() {
            addCriterion("departure is null");
            return (Criteria) this;
        }

        public Criteria andDepartureIsNotNull() {
            addCriterion("departure is not null");
            return (Criteria) this;
        }

        public Criteria andDepartureEqualTo(String value) {
            addCriterion("departure =", value, "departure");
            return (Criteria) this;
        }

        public Criteria andDepartureNotEqualTo(String value) {
            addCriterion("departure <>", value, "departure");
            return (Criteria) this;
        }

        public Criteria andDepartureGreaterThan(String value) {
            addCriterion("departure >", value, "departure");
            return (Criteria) this;
        }

        public Criteria andDepartureGreaterThanOrEqualTo(String value) {
            addCriterion("departure >=", value, "departure");
            return (Criteria) this;
        }

        public Criteria andDepartureLessThan(String value) {
            addCriterion("departure <", value, "departure");
            return (Criteria) this;
        }

        public Criteria andDepartureLessThanOrEqualTo(String value) {
            addCriterion("departure <=", value, "departure");
            return (Criteria) this;
        }

        public Criteria andDepartureLike(String value) {
            addCriterion("departure like", value, "departure");
            return (Criteria) this;
        }

        public Criteria andDepartureNotLike(String value) {
            addCriterion("departure not like", value, "departure");
            return (Criteria) this;
        }

        public Criteria andDepartureIn(List<String> values) {
            addCriterion("departure in", values, "departure");
            return (Criteria) this;
        }

        public Criteria andDepartureNotIn(List<String> values) {
            addCriterion("departure not in", values, "departure");
            return (Criteria) this;
        }

        public Criteria andDepartureBetween(String value1, String value2) {
            addCriterion("departure between", value1, value2, "departure");
            return (Criteria) this;
        }

        public Criteria andDepartureNotBetween(String value1, String value2) {
            addCriterion("departure not between", value1, value2, "departure");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeIsNull() {
            addCriterion("planned_time is null");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeIsNotNull() {
            addCriterion("planned_time is not null");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeEqualTo(String value) {
            addCriterion("planned_time =", value, "plannedTime");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeNotEqualTo(String value) {
            addCriterion("planned_time <>", value, "plannedTime");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeGreaterThan(String value) {
            addCriterion("planned_time >", value, "plannedTime");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeGreaterThanOrEqualTo(String value) {
            addCriterion("planned_time >=", value, "plannedTime");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeLessThan(String value) {
            addCriterion("planned_time <", value, "plannedTime");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeLessThanOrEqualTo(String value) {
            addCriterion("planned_time <=", value, "plannedTime");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeLike(String value) {
            addCriterion("planned_time like", value, "plannedTime");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeNotLike(String value) {
            addCriterion("planned_time not like", value, "plannedTime");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeIn(List<String> values) {
            addCriterion("planned_time in", values, "plannedTime");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeNotIn(List<String> values) {
            addCriterion("planned_time not in", values, "plannedTime");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeBetween(String value1, String value2) {
            addCriterion("planned_time between", value1, value2, "plannedTime");
            return (Criteria) this;
        }

        public Criteria andPlannedTimeNotBetween(String value1, String value2) {
            addCriterion("planned_time not between", value1, value2, "plannedTime");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointIsNull() {
            addCriterion("first_landing_point is null");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointIsNotNull() {
            addCriterion("first_landing_point is not null");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointEqualTo(String value) {
            addCriterion("first_landing_point =", value, "firstLandingPoint");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointNotEqualTo(String value) {
            addCriterion("first_landing_point <>", value, "firstLandingPoint");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointGreaterThan(String value) {
            addCriterion("first_landing_point >", value, "firstLandingPoint");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointGreaterThanOrEqualTo(String value) {
            addCriterion("first_landing_point >=", value, "firstLandingPoint");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointLessThan(String value) {
            addCriterion("first_landing_point <", value, "firstLandingPoint");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointLessThanOrEqualTo(String value) {
            addCriterion("first_landing_point <=", value, "firstLandingPoint");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointLike(String value) {
            addCriterion("first_landing_point like", value, "firstLandingPoint");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointNotLike(String value) {
            addCriterion("first_landing_point not like", value, "firstLandingPoint");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointIn(List<String> values) {
            addCriterion("first_landing_point in", values, "firstLandingPoint");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointNotIn(List<String> values) {
            addCriterion("first_landing_point not in", values, "firstLandingPoint");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointBetween(String value1, String value2) {
            addCriterion("first_landing_point between", value1, value2, "firstLandingPoint");
            return (Criteria) this;
        }

        public Criteria andFirstLandingPointNotBetween(String value1, String value2) {
            addCriterion("first_landing_point not between", value1, value2, "firstLandingPoint");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeIsNull() {
            addCriterion("estimated_time is null");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeIsNotNull() {
            addCriterion("estimated_time is not null");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeEqualTo(String value) {
            addCriterion("estimated_time =", value, "estimatedTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeNotEqualTo(String value) {
            addCriterion("estimated_time <>", value, "estimatedTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeGreaterThan(String value) {
            addCriterion("estimated_time >", value, "estimatedTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeGreaterThanOrEqualTo(String value) {
            addCriterion("estimated_time >=", value, "estimatedTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeLessThan(String value) {
            addCriterion("estimated_time <", value, "estimatedTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeLessThanOrEqualTo(String value) {
            addCriterion("estimated_time <=", value, "estimatedTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeLike(String value) {
            addCriterion("estimated_time like", value, "estimatedTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeNotLike(String value) {
            addCriterion("estimated_time not like", value, "estimatedTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeIn(List<String> values) {
            addCriterion("estimated_time in", values, "estimatedTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeNotIn(List<String> values) {
            addCriterion("estimated_time not in", values, "estimatedTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeBetween(String value1, String value2) {
            addCriterion("estimated_time between", value1, value2, "estimatedTime");
            return (Criteria) this;
        }

        public Criteria andEstimatedTimeNotBetween(String value1, String value2) {
            addCriterion("estimated_time not between", value1, value2, "estimatedTime");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceIsNull() {
            addCriterion("fuel_endurance is null");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceIsNotNull() {
            addCriterion("fuel_endurance is not null");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceEqualTo(String value) {
            addCriterion("fuel_endurance =", value, "fuelEndurance");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceNotEqualTo(String value) {
            addCriterion("fuel_endurance <>", value, "fuelEndurance");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceGreaterThan(String value) {
            addCriterion("fuel_endurance >", value, "fuelEndurance");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceGreaterThanOrEqualTo(String value) {
            addCriterion("fuel_endurance >=", value, "fuelEndurance");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceLessThan(String value) {
            addCriterion("fuel_endurance <", value, "fuelEndurance");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceLessThanOrEqualTo(String value) {
            addCriterion("fuel_endurance <=", value, "fuelEndurance");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceLike(String value) {
            addCriterion("fuel_endurance like", value, "fuelEndurance");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceNotLike(String value) {
            addCriterion("fuel_endurance not like", value, "fuelEndurance");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceIn(List<String> values) {
            addCriterion("fuel_endurance in", values, "fuelEndurance");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceNotIn(List<String> values) {
            addCriterion("fuel_endurance not in", values, "fuelEndurance");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceBetween(String value1, String value2) {
            addCriterion("fuel_endurance between", value1, value2, "fuelEndurance");
            return (Criteria) this;
        }

        public Criteria andFuelEnduranceNotBetween(String value1, String value2) {
            addCriterion("fuel_endurance not between", value1, value2, "fuelEndurance");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedIsNull() {
            addCriterion("circuit_speed is null");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedIsNotNull() {
            addCriterion("circuit_speed is not null");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedEqualTo(String value) {
            addCriterion("circuit_speed =", value, "circuitSpeed");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedNotEqualTo(String value) {
            addCriterion("circuit_speed <>", value, "circuitSpeed");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedGreaterThan(String value) {
            addCriterion("circuit_speed >", value, "circuitSpeed");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedGreaterThanOrEqualTo(String value) {
            addCriterion("circuit_speed >=", value, "circuitSpeed");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedLessThan(String value) {
            addCriterion("circuit_speed <", value, "circuitSpeed");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedLessThanOrEqualTo(String value) {
            addCriterion("circuit_speed <=", value, "circuitSpeed");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedLike(String value) {
            addCriterion("circuit_speed like", value, "circuitSpeed");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedNotLike(String value) {
            addCriterion("circuit_speed not like", value, "circuitSpeed");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedIn(List<String> values) {
            addCriterion("circuit_speed in", values, "circuitSpeed");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedNotIn(List<String> values) {
            addCriterion("circuit_speed not in", values, "circuitSpeed");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedBetween(String value1, String value2) {
            addCriterion("circuit_speed between", value1, value2, "circuitSpeed");
            return (Criteria) this;
        }

        public Criteria andCircuitSpeedNotBetween(String value1, String value2) {
            addCriterion("circuit_speed not between", value1, value2, "circuitSpeed");
            return (Criteria) this;
        }

        public Criteria andCaptainIsNull() {
            addCriterion("captain is null");
            return (Criteria) this;
        }

        public Criteria andCaptainIsNotNull() {
            addCriterion("captain is not null");
            return (Criteria) this;
        }

        public Criteria andCaptainEqualTo(String value) {
            addCriterion("captain =", value, "captain");
            return (Criteria) this;
        }

        public Criteria andCaptainNotEqualTo(String value) {
            addCriterion("captain <>", value, "captain");
            return (Criteria) this;
        }

        public Criteria andCaptainGreaterThan(String value) {
            addCriterion("captain >", value, "captain");
            return (Criteria) this;
        }

        public Criteria andCaptainGreaterThanOrEqualTo(String value) {
            addCriterion("captain >=", value, "captain");
            return (Criteria) this;
        }

        public Criteria andCaptainLessThan(String value) {
            addCriterion("captain <", value, "captain");
            return (Criteria) this;
        }

        public Criteria andCaptainLessThanOrEqualTo(String value) {
            addCriterion("captain <=", value, "captain");
            return (Criteria) this;
        }

        public Criteria andCaptainLike(String value) {
            addCriterion("captain like", value, "captain");
            return (Criteria) this;
        }

        public Criteria andCaptainNotLike(String value) {
            addCriterion("captain not like", value, "captain");
            return (Criteria) this;
        }

        public Criteria andCaptainIn(List<String> values) {
            addCriterion("captain in", values, "captain");
            return (Criteria) this;
        }

        public Criteria andCaptainNotIn(List<String> values) {
            addCriterion("captain not in", values, "captain");
            return (Criteria) this;
        }

        public Criteria andCaptainBetween(String value1, String value2) {
            addCriterion("captain between", value1, value2, "captain");
            return (Criteria) this;
        }

        public Criteria andCaptainNotBetween(String value1, String value2) {
            addCriterion("captain not between", value1, value2, "captain");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNull() {
            addCriterion("phone is null");
            return (Criteria) this;
        }

        public Criteria andPhoneIsNotNull() {
            addCriterion("phone is not null");
            return (Criteria) this;
        }

        public Criteria andPhoneEqualTo(String value) {
            addCriterion("phone =", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotEqualTo(String value) {
            addCriterion("phone <>", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThan(String value) {
            addCriterion("phone >", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneGreaterThanOrEqualTo(String value) {
            addCriterion("phone >=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThan(String value) {
            addCriterion("phone <", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLessThanOrEqualTo(String value) {
            addCriterion("phone <=", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneLike(String value) {
            addCriterion("phone like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotLike(String value) {
            addCriterion("phone not like", value, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneIn(List<String> values) {
            addCriterion("phone in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotIn(List<String> values) {
            addCriterion("phone not in", values, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneBetween(String value1, String value2) {
            addCriterion("phone between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andPhoneNotBetween(String value1, String value2) {
            addCriterion("phone not between", value1, value2, "phone");
            return (Criteria) this;
        }

        public Criteria andAddressIsNull() {
            addCriterion("address is null");
            return (Criteria) this;
        }

        public Criteria andAddressIsNotNull() {
            addCriterion("address is not null");
            return (Criteria) this;
        }

        public Criteria andAddressEqualTo(String value) {
            addCriterion("address =", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotEqualTo(String value) {
            addCriterion("address <>", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThan(String value) {
            addCriterion("address >", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThanOrEqualTo(String value) {
            addCriterion("address >=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThan(String value) {
            addCriterion("address <", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThanOrEqualTo(String value) {
            addCriterion("address <=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLike(String value) {
            addCriterion("address like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotLike(String value) {
            addCriterion("address not like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressIn(List<String> values) {
            addCriterion("address in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotIn(List<String> values) {
            addCriterion("address not in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressBetween(String value1, String value2) {
            addCriterion("address between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotBetween(String value1, String value2) {
            addCriterion("address not between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andIssueDateIsNull() {
            addCriterion("issue_date is null");
            return (Criteria) this;
        }

        public Criteria andIssueDateIsNotNull() {
            addCriterion("issue_date is not null");
            return (Criteria) this;
        }

        public Criteria andIssueDateEqualTo(String value) {
            addCriterion("issue_date =", value, "issueDate");
            return (Criteria) this;
        }

        public Criteria andIssueDateNotEqualTo(String value) {
            addCriterion("issue_date <>", value, "issueDate");
            return (Criteria) this;
        }

        public Criteria andIssueDateGreaterThan(String value) {
            addCriterion("issue_date >", value, "issueDate");
            return (Criteria) this;
        }

        public Criteria andIssueDateGreaterThanOrEqualTo(String value) {
            addCriterion("issue_date >=", value, "issueDate");
            return (Criteria) this;
        }

        public Criteria andIssueDateLessThan(String value) {
            addCriterion("issue_date <", value, "issueDate");
            return (Criteria) this;
        }

        public Criteria andIssueDateLessThanOrEqualTo(String value) {
            addCriterion("issue_date <=", value, "issueDate");
            return (Criteria) this;
        }

        public Criteria andIssueDateLike(String value) {
            addCriterion("issue_date like", value, "issueDate");
            return (Criteria) this;
        }

        public Criteria andIssueDateNotLike(String value) {
            addCriterion("issue_date not like", value, "issueDate");
            return (Criteria) this;
        }

        public Criteria andIssueDateIn(List<String> values) {
            addCriterion("issue_date in", values, "issueDate");
            return (Criteria) this;
        }

        public Criteria andIssueDateNotIn(List<String> values) {
            addCriterion("issue_date not in", values, "issueDate");
            return (Criteria) this;
        }

        public Criteria andIssueDateBetween(String value1, String value2) {
            addCriterion("issue_date between", value1, value2, "issueDate");
            return (Criteria) this;
        }

        public Criteria andIssueDateNotBetween(String value1, String value2) {
            addCriterion("issue_date not between", value1, value2, "issueDate");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberIsNull() {
            addCriterion("task_book_number is null");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberIsNotNull() {
            addCriterion("task_book_number is not null");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberEqualTo(String value) {
            addCriterion("task_book_number =", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberNotEqualTo(String value) {
            addCriterion("task_book_number <>", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberGreaterThan(String value) {
            addCriterion("task_book_number >", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberGreaterThanOrEqualTo(String value) {
            addCriterion("task_book_number >=", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberLessThan(String value) {
            addCriterion("task_book_number <", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberLessThanOrEqualTo(String value) {
            addCriterion("task_book_number <=", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberLike(String value) {
            addCriterion("task_book_number like", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberNotLike(String value) {
            addCriterion("task_book_number not like", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberIn(List<String> values) {
            addCriterion("task_book_number in", values, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberNotIn(List<String> values) {
            addCriterion("task_book_number not in", values, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberBetween(String value1, String value2) {
            addCriterion("task_book_number between", value1, value2, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberNotBetween(String value1, String value2) {
            addCriterion("task_book_number not between", value1, value2, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNull() {
            addCriterion("task_status is null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIsNotNull() {
            addCriterion("task_status is not null");
            return (Criteria) this;
        }

        public Criteria andTaskStatusEqualTo(Integer value) {
            addCriterion("task_status =", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotEqualTo(Integer value) {
            addCriterion("task_status <>", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThan(Integer value) {
            addCriterion("task_status >", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("task_status >=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThan(Integer value) {
            addCriterion("task_status <", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusLessThanOrEqualTo(Integer value) {
            addCriterion("task_status <=", value, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusIn(List<Integer> values) {
            addCriterion("task_status in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotIn(List<Integer> values) {
            addCriterion("task_status not in", values, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusBetween(Integer value1, Integer value2) {
            addCriterion("task_status between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andTaskStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("task_status not between", value1, value2, "taskStatus");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andVersionNumberIsNull() {
            addCriterion("version_number is null");
            return (Criteria) this;
        }

        public Criteria andVersionNumberIsNotNull() {
            addCriterion("version_number is not null");
            return (Criteria) this;
        }

        public Criteria andVersionNumberEqualTo(String value) {
            addCriterion("version_number =", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberNotEqualTo(String value) {
            addCriterion("version_number <>", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberGreaterThan(String value) {
            addCriterion("version_number >", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberGreaterThanOrEqualTo(String value) {
            addCriterion("version_number >=", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberLessThan(String value) {
            addCriterion("version_number <", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberLessThanOrEqualTo(String value) {
            addCriterion("version_number <=", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberLike(String value) {
            addCriterion("version_number like", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberNotLike(String value) {
            addCriterion("version_number not like", value, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberIn(List<String> values) {
            addCriterion("version_number in", values, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberNotIn(List<String> values) {
            addCriterion("version_number not in", values, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberBetween(String value1, String value2) {
            addCriterion("version_number between", value1, value2, "versionNumber");
            return (Criteria) this;
        }

        public Criteria andVersionNumberNotBetween(String value1, String value2) {
            addCriterion("version_number not between", value1, value2, "versionNumber");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}