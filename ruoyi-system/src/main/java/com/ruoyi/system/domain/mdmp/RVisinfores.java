package com.ruoyi.system.domain.mdmp;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 能见度对象 r_visinfores
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
public class RVisinfores {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 报文表ID
     */
    @Excel(name = "报文表ID")
    private Long decodeReportId;

    /**
     * 能见度
     */
    @Excel(name = "能见度")
    private String vis;

    /**
     * 最小能见度
     */
    @Excel(name = "最小能见度")
    private Integer visMin;

    /**
     * 最小能见度方向
     */
    @Excel(name = "最小能见度方向")
    private String visMinDirection;

    private Integer colourType;
}
