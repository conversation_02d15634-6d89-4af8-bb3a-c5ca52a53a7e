package com.ruoyi.system.domain.oc;

import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@Data
public class FlightTaskInfoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    public FlightTaskInfoExample() {
        oredCriteria = new ArrayList<>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andBatchIsNull() {
            addCriterion("batch is null");
            return (Criteria) this;
        }

        public Criteria andBatchIsNotNull() {
            addCriterion("batch is not null");
            return (Criteria) this;
        }

        public Criteria andBatchEqualTo(String value) {
            addCriterion("batch =", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchNotEqualTo(String value) {
            addCriterion("batch <>", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchGreaterThan(String value) {
            addCriterion("batch >", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchGreaterThanOrEqualTo(String value) {
            addCriterion("batch >=", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchLessThan(String value) {
            addCriterion("batch <", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchLessThanOrEqualTo(String value) {
            addCriterion("batch <=", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchLike(String value) {
            addCriterion("batch like", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchNotLike(String value) {
            addCriterion("batch not like", value, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchIn(List<String> values) {
            addCriterion("batch in", values, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchNotIn(List<String> values) {
            addCriterion("batch not in", values, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchBetween(String value1, String value2) {
            addCriterion("batch between", value1, value2, "batch");
            return (Criteria) this;
        }

        public Criteria andBatchNotBetween(String value1, String value2) {
            addCriterion("batch not between", value1, value2, "batch");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNull() {
            addCriterion("task_type is null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIsNotNull() {
            addCriterion("task_type is not null");
            return (Criteria) this;
        }

        public Criteria andTaskTypeEqualTo(String value) {
            addCriterion("task_type =", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotEqualTo(String value) {
            addCriterion("task_type <>", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThan(String value) {
            addCriterion("task_type >", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeGreaterThanOrEqualTo(String value) {
            addCriterion("task_type >=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThan(String value) {
            addCriterion("task_type <", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLessThanOrEqualTo(String value) {
            addCriterion("task_type <=", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeLike(String value) {
            addCriterion("task_type like", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotLike(String value) {
            addCriterion("task_type not like", value, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeIn(List<String> values) {
            addCriterion("task_type in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotIn(List<String> values) {
            addCriterion("task_type not in", values, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeBetween(String value1, String value2) {
            addCriterion("task_type between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andTaskTypeNotBetween(String value1, String value2) {
            addCriterion("task_type not between", value1, value2, "taskType");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceIsNull() {
            addCriterion("route_or_airspace is null");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceIsNotNull() {
            addCriterion("route_or_airspace is not null");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceEqualTo(String value) {
            addCriterion("route_or_airspace =", value, "routeOrAirspace");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceNotEqualTo(String value) {
            addCriterion("route_or_airspace <>", value, "routeOrAirspace");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceGreaterThan(String value) {
            addCriterion("route_or_airspace >", value, "routeOrAirspace");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceGreaterThanOrEqualTo(String value) {
            addCriterion("route_or_airspace >=", value, "routeOrAirspace");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceLessThan(String value) {
            addCriterion("route_or_airspace <", value, "routeOrAirspace");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceLessThanOrEqualTo(String value) {
            addCriterion("route_or_airspace <=", value, "routeOrAirspace");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceLike(String value) {
            addCriterion("route_or_airspace like", value, "routeOrAirspace");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceNotLike(String value) {
            addCriterion("route_or_airspace not like", value, "routeOrAirspace");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceIn(List<String> values) {
            addCriterion("route_or_airspace in", values, "routeOrAirspace");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceNotIn(List<String> values) {
            addCriterion("route_or_airspace not in", values, "routeOrAirspace");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceBetween(String value1, String value2) {
            addCriterion("route_or_airspace between", value1, value2, "routeOrAirspace");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceNotBetween(String value1, String value2) {
            addCriterion("route_or_airspace not between", value1, value2, "routeOrAirspace");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeIsNull() {
            addCriterion("route_or_airspace_code is null");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeIsNotNull() {
            addCriterion("route_or_airspace_code is not null");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeEqualTo(String value) {
            addCriterion("route_or_airspace_code =", value, "routeOrAirspaceCode");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeNotEqualTo(String value) {
            addCriterion("route_or_airspace_code <>", value, "routeOrAirspaceCode");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeGreaterThan(String value) {
            addCriterion("route_or_airspace_code >", value, "routeOrAirspaceCode");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("route_or_airspace_code >=", value, "routeOrAirspaceCode");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeLessThan(String value) {
            addCriterion("route_or_airspace_code <", value, "routeOrAirspaceCode");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeLessThanOrEqualTo(String value) {
            addCriterion("route_or_airspace_code <=", value, "routeOrAirspaceCode");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeLike(String value) {
            addCriterion("route_or_airspace_code like", value, "routeOrAirspaceCode");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeNotLike(String value) {
            addCriterion("route_or_airspace_code not like", value, "routeOrAirspaceCode");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeIn(List<String> values) {
            addCriterion("route_or_airspace_code in", values, "routeOrAirspaceCode");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeNotIn(List<String> values) {
            addCriterion("route_or_airspace_code not in", values, "routeOrAirspaceCode");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeBetween(String value1, String value2) {
            addCriterion("route_or_airspace_code between", value1, value2, "routeOrAirspaceCode");
            return (Criteria) this;
        }

        public Criteria andRouteOrAirspaceCodeNotBetween(String value1, String value2) {
            addCriterion("route_or_airspace_code not between", value1, value2, "routeOrAirspaceCode");
            return (Criteria) this;
        }

        public Criteria andAltitudeIsNull() {
            addCriterion("altitude is null");
            return (Criteria) this;
        }

        public Criteria andAltitudeIsNotNull() {
            addCriterion("altitude is not null");
            return (Criteria) this;
        }

        public Criteria andAltitudeEqualTo(String value) {
            addCriterion("altitude =", value, "altitude");
            return (Criteria) this;
        }

        public Criteria andAltitudeNotEqualTo(String value) {
            addCriterion("altitude <>", value, "altitude");
            return (Criteria) this;
        }

        public Criteria andAltitudeGreaterThan(String value) {
            addCriterion("altitude >", value, "altitude");
            return (Criteria) this;
        }

        public Criteria andAltitudeGreaterThanOrEqualTo(String value) {
            addCriterion("altitude >=", value, "altitude");
            return (Criteria) this;
        }

        public Criteria andAltitudeLessThan(String value) {
            addCriterion("altitude <", value, "altitude");
            return (Criteria) this;
        }

        public Criteria andAltitudeLessThanOrEqualTo(String value) {
            addCriterion("altitude <=", value, "altitude");
            return (Criteria) this;
        }

        public Criteria andAltitudeLike(String value) {
            addCriterion("altitude like", value, "altitude");
            return (Criteria) this;
        }

        public Criteria andAltitudeNotLike(String value) {
            addCriterion("altitude not like", value, "altitude");
            return (Criteria) this;
        }

        public Criteria andAltitudeIn(List<String> values) {
            addCriterion("altitude in", values, "altitude");
            return (Criteria) this;
        }

        public Criteria andAltitudeNotIn(List<String> values) {
            addCriterion("altitude not in", values, "altitude");
            return (Criteria) this;
        }

        public Criteria andAltitudeBetween(String value1, String value2) {
            addCriterion("altitude between", value1, value2, "altitude");
            return (Criteria) this;
        }

        public Criteria andAltitudeNotBetween(String value1, String value2) {
            addCriterion("altitude not between", value1, value2, "altitude");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportIsNull() {
            addCriterion("alternate_airport is null");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportIsNotNull() {
            addCriterion("alternate_airport is not null");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportEqualTo(String value) {
            addCriterion("alternate_airport =", value, "alternateAirport");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportNotEqualTo(String value) {
            addCriterion("alternate_airport <>", value, "alternateAirport");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportGreaterThan(String value) {
            addCriterion("alternate_airport >", value, "alternateAirport");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportGreaterThanOrEqualTo(String value) {
            addCriterion("alternate_airport >=", value, "alternateAirport");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportLessThan(String value) {
            addCriterion("alternate_airport <", value, "alternateAirport");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportLessThanOrEqualTo(String value) {
            addCriterion("alternate_airport <=", value, "alternateAirport");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportLike(String value) {
            addCriterion("alternate_airport like", value, "alternateAirport");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportNotLike(String value) {
            addCriterion("alternate_airport not like", value, "alternateAirport");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportIn(List<String> values) {
            addCriterion("alternate_airport in", values, "alternateAirport");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportNotIn(List<String> values) {
            addCriterion("alternate_airport not in", values, "alternateAirport");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportBetween(String value1, String value2) {
            addCriterion("alternate_airport between", value1, value2, "alternateAirport");
            return (Criteria) this;
        }

        public Criteria andAlternateAirportNotBetween(String value1, String value2) {
            addCriterion("alternate_airport not between", value1, value2, "alternateAirport");
            return (Criteria) this;
        }

        public Criteria andFlightRulesIsNull() {
            addCriterion("flight_rules is null");
            return (Criteria) this;
        }

        public Criteria andFlightRulesIsNotNull() {
            addCriterion("flight_rules is not null");
            return (Criteria) this;
        }

        public Criteria andFlightRulesEqualTo(String value) {
            addCriterion("flight_rules =", value, "flightRules");
            return (Criteria) this;
        }

        public Criteria andFlightRulesNotEqualTo(String value) {
            addCriterion("flight_rules <>", value, "flightRules");
            return (Criteria) this;
        }

        public Criteria andFlightRulesGreaterThan(String value) {
            addCriterion("flight_rules >", value, "flightRules");
            return (Criteria) this;
        }

        public Criteria andFlightRulesGreaterThanOrEqualTo(String value) {
            addCriterion("flight_rules >=", value, "flightRules");
            return (Criteria) this;
        }

        public Criteria andFlightRulesLessThan(String value) {
            addCriterion("flight_rules <", value, "flightRules");
            return (Criteria) this;
        }

        public Criteria andFlightRulesLessThanOrEqualTo(String value) {
            addCriterion("flight_rules <=", value, "flightRules");
            return (Criteria) this;
        }

        public Criteria andFlightRulesLike(String value) {
            addCriterion("flight_rules like", value, "flightRules");
            return (Criteria) this;
        }

        public Criteria andFlightRulesNotLike(String value) {
            addCriterion("flight_rules not like", value, "flightRules");
            return (Criteria) this;
        }

        public Criteria andFlightRulesIn(List<String> values) {
            addCriterion("flight_rules in", values, "flightRules");
            return (Criteria) this;
        }

        public Criteria andFlightRulesNotIn(List<String> values) {
            addCriterion("flight_rules not in", values, "flightRules");
            return (Criteria) this;
        }

        public Criteria andFlightRulesBetween(String value1, String value2) {
            addCriterion("flight_rules between", value1, value2, "flightRules");
            return (Criteria) this;
        }

        public Criteria andFlightRulesNotBetween(String value1, String value2) {
            addCriterion("flight_rules not between", value1, value2, "flightRules");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardIsNull() {
            addCriterion("captain_weather_standard is null");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardIsNotNull() {
            addCriterion("captain_weather_standard is not null");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardEqualTo(String value) {
            addCriterion("captain_weather_standard =", value, "captainWeatherStandard");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardNotEqualTo(String value) {
            addCriterion("captain_weather_standard <>", value, "captainWeatherStandard");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardGreaterThan(String value) {
            addCriterion("captain_weather_standard >", value, "captainWeatherStandard");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardGreaterThanOrEqualTo(String value) {
            addCriterion("captain_weather_standard >=", value, "captainWeatherStandard");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardLessThan(String value) {
            addCriterion("captain_weather_standard <", value, "captainWeatherStandard");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardLessThanOrEqualTo(String value) {
            addCriterion("captain_weather_standard <=", value, "captainWeatherStandard");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardLike(String value) {
            addCriterion("captain_weather_standard like", value, "captainWeatherStandard");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardNotLike(String value) {
            addCriterion("captain_weather_standard not like", value, "captainWeatherStandard");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardIn(List<String> values) {
            addCriterion("captain_weather_standard in", values, "captainWeatherStandard");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardNotIn(List<String> values) {
            addCriterion("captain_weather_standard not in", values, "captainWeatherStandard");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardBetween(String value1, String value2) {
            addCriterion("captain_weather_standard between", value1, value2, "captainWeatherStandard");
            return (Criteria) this;
        }

        public Criteria andCaptainWeatherStandardNotBetween(String value1, String value2) {
            addCriterion("captain_weather_standard not between", value1, value2, "captainWeatherStandard");
            return (Criteria) this;
        }

        public Criteria andCrewRole1IsNull() {
            addCriterion("crew_role_1 is null");
            return (Criteria) this;
        }

        public Criteria andCrewRole1IsNotNull() {
            addCriterion("crew_role_1 is not null");
            return (Criteria) this;
        }

        public Criteria andCrewRole1EqualTo(String value) {
            addCriterion("crew_role_1 =", value, "crewRole1");
            return (Criteria) this;
        }

        public Criteria andCrewRole1NotEqualTo(String value) {
            addCriterion("crew_role_1 <>", value, "crewRole1");
            return (Criteria) this;
        }

        public Criteria andCrewRole1GreaterThan(String value) {
            addCriterion("crew_role_1 >", value, "crewRole1");
            return (Criteria) this;
        }

        public Criteria andCrewRole1GreaterThanOrEqualTo(String value) {
            addCriterion("crew_role_1 >=", value, "crewRole1");
            return (Criteria) this;
        }

        public Criteria andCrewRole1LessThan(String value) {
            addCriterion("crew_role_1 <", value, "crewRole1");
            return (Criteria) this;
        }

        public Criteria andCrewRole1LessThanOrEqualTo(String value) {
            addCriterion("crew_role_1 <=", value, "crewRole1");
            return (Criteria) this;
        }

        public Criteria andCrewRole1Like(String value) {
            addCriterion("crew_role_1 like", value, "crewRole1");
            return (Criteria) this;
        }

        public Criteria andCrewRole1NotLike(String value) {
            addCriterion("crew_role_1 not like", value, "crewRole1");
            return (Criteria) this;
        }

        public Criteria andCrewRole1In(List<String> values) {
            addCriterion("crew_role_1 in", values, "crewRole1");
            return (Criteria) this;
        }

        public Criteria andCrewRole1NotIn(List<String> values) {
            addCriterion("crew_role_1 not in", values, "crewRole1");
            return (Criteria) this;
        }

        public Criteria andCrewRole1Between(String value1, String value2) {
            addCriterion("crew_role_1 between", value1, value2, "crewRole1");
            return (Criteria) this;
        }

        public Criteria andCrewRole1NotBetween(String value1, String value2) {
            addCriterion("crew_role_1 not between", value1, value2, "crewRole1");
            return (Criteria) this;
        }

        public Criteria andCrewName1IsNull() {
            addCriterion("crew_name_1 is null");
            return (Criteria) this;
        }

        public Criteria andCrewName1IsNotNull() {
            addCriterion("crew_name_1 is not null");
            return (Criteria) this;
        }

        public Criteria andCrewName1EqualTo(String value) {
            addCriterion("crew_name_1 =", value, "crewName1");
            return (Criteria) this;
        }

        public Criteria andCrewName1NotEqualTo(String value) {
            addCriterion("crew_name_1 <>", value, "crewName1");
            return (Criteria) this;
        }

        public Criteria andCrewName1GreaterThan(String value) {
            addCriterion("crew_name_1 >", value, "crewName1");
            return (Criteria) this;
        }

        public Criteria andCrewName1GreaterThanOrEqualTo(String value) {
            addCriterion("crew_name_1 >=", value, "crewName1");
            return (Criteria) this;
        }

        public Criteria andCrewName1LessThan(String value) {
            addCriterion("crew_name_1 <", value, "crewName1");
            return (Criteria) this;
        }

        public Criteria andCrewName1LessThanOrEqualTo(String value) {
            addCriterion("crew_name_1 <=", value, "crewName1");
            return (Criteria) this;
        }

        public Criteria andCrewName1Like(String value) {
            addCriterion("crew_name_1 like", value, "crewName1");
            return (Criteria) this;
        }

        public Criteria andCrewName1NotLike(String value) {
            addCriterion("crew_name_1 not like", value, "crewName1");
            return (Criteria) this;
        }

        public Criteria andCrewName1In(List<String> values) {
            addCriterion("crew_name_1 in", values, "crewName1");
            return (Criteria) this;
        }

        public Criteria andCrewName1NotIn(List<String> values) {
            addCriterion("crew_name_1 not in", values, "crewName1");
            return (Criteria) this;
        }

        public Criteria andCrewName1Between(String value1, String value2) {
            addCriterion("crew_name_1 between", value1, value2, "crewName1");
            return (Criteria) this;
        }

        public Criteria andCrewName1NotBetween(String value1, String value2) {
            addCriterion("crew_name_1 not between", value1, value2, "crewName1");
            return (Criteria) this;
        }

        public Criteria andCrewRole2IsNull() {
            addCriterion("crew_role_2 is null");
            return (Criteria) this;
        }

        public Criteria andCrewRole2IsNotNull() {
            addCriterion("crew_role_2 is not null");
            return (Criteria) this;
        }

        public Criteria andCrewRole2EqualTo(String value) {
            addCriterion("crew_role_2 =", value, "crewRole2");
            return (Criteria) this;
        }

        public Criteria andCrewRole2NotEqualTo(String value) {
            addCriterion("crew_role_2 <>", value, "crewRole2");
            return (Criteria) this;
        }

        public Criteria andCrewRole2GreaterThan(String value) {
            addCriterion("crew_role_2 >", value, "crewRole2");
            return (Criteria) this;
        }

        public Criteria andCrewRole2GreaterThanOrEqualTo(String value) {
            addCriterion("crew_role_2 >=", value, "crewRole2");
            return (Criteria) this;
        }

        public Criteria andCrewRole2LessThan(String value) {
            addCriterion("crew_role_2 <", value, "crewRole2");
            return (Criteria) this;
        }

        public Criteria andCrewRole2LessThanOrEqualTo(String value) {
            addCriterion("crew_role_2 <=", value, "crewRole2");
            return (Criteria) this;
        }

        public Criteria andCrewRole2Like(String value) {
            addCriterion("crew_role_2 like", value, "crewRole2");
            return (Criteria) this;
        }

        public Criteria andCrewRole2NotLike(String value) {
            addCriterion("crew_role_2 not like", value, "crewRole2");
            return (Criteria) this;
        }

        public Criteria andCrewRole2In(List<String> values) {
            addCriterion("crew_role_2 in", values, "crewRole2");
            return (Criteria) this;
        }

        public Criteria andCrewRole2NotIn(List<String> values) {
            addCriterion("crew_role_2 not in", values, "crewRole2");
            return (Criteria) this;
        }

        public Criteria andCrewRole2Between(String value1, String value2) {
            addCriterion("crew_role_2 between", value1, value2, "crewRole2");
            return (Criteria) this;
        }

        public Criteria andCrewRole2NotBetween(String value1, String value2) {
            addCriterion("crew_role_2 not between", value1, value2, "crewRole2");
            return (Criteria) this;
        }

        public Criteria andCrewName2IsNull() {
            addCriterion("crew_name_2 is null");
            return (Criteria) this;
        }

        public Criteria andCrewName2IsNotNull() {
            addCriterion("crew_name_2 is not null");
            return (Criteria) this;
        }

        public Criteria andCrewName2EqualTo(String value) {
            addCriterion("crew_name_2 =", value, "crewName2");
            return (Criteria) this;
        }

        public Criteria andCrewName2NotEqualTo(String value) {
            addCriterion("crew_name_2 <>", value, "crewName2");
            return (Criteria) this;
        }

        public Criteria andCrewName2GreaterThan(String value) {
            addCriterion("crew_name_2 >", value, "crewName2");
            return (Criteria) this;
        }

        public Criteria andCrewName2GreaterThanOrEqualTo(String value) {
            addCriterion("crew_name_2 >=", value, "crewName2");
            return (Criteria) this;
        }

        public Criteria andCrewName2LessThan(String value) {
            addCriterion("crew_name_2 <", value, "crewName2");
            return (Criteria) this;
        }

        public Criteria andCrewName2LessThanOrEqualTo(String value) {
            addCriterion("crew_name_2 <=", value, "crewName2");
            return (Criteria) this;
        }

        public Criteria andCrewName2Like(String value) {
            addCriterion("crew_name_2 like", value, "crewName2");
            return (Criteria) this;
        }

        public Criteria andCrewName2NotLike(String value) {
            addCriterion("crew_name_2 not like", value, "crewName2");
            return (Criteria) this;
        }

        public Criteria andCrewName2In(List<String> values) {
            addCriterion("crew_name_2 in", values, "crewName2");
            return (Criteria) this;
        }

        public Criteria andCrewName2NotIn(List<String> values) {
            addCriterion("crew_name_2 not in", values, "crewName2");
            return (Criteria) this;
        }

        public Criteria andCrewName2Between(String value1, String value2) {
            addCriterion("crew_name_2 between", value1, value2, "crewName2");
            return (Criteria) this;
        }

        public Criteria andCrewName2NotBetween(String value1, String value2) {
            addCriterion("crew_name_2 not between", value1, value2, "crewName2");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountIsNull() {
            addCriterion("crew_passenger_count is null");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountIsNotNull() {
            addCriterion("crew_passenger_count is not null");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountEqualTo(String value) {
            addCriterion("crew_passenger_count =", value, "crewPassengerCount");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountNotEqualTo(String value) {
            addCriterion("crew_passenger_count <>", value, "crewPassengerCount");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountGreaterThan(String value) {
            addCriterion("crew_passenger_count >", value, "crewPassengerCount");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountGreaterThanOrEqualTo(String value) {
            addCriterion("crew_passenger_count >=", value, "crewPassengerCount");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountLessThan(String value) {
            addCriterion("crew_passenger_count <", value, "crewPassengerCount");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountLessThanOrEqualTo(String value) {
            addCriterion("crew_passenger_count <=", value, "crewPassengerCount");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountLike(String value) {
            addCriterion("crew_passenger_count like", value, "crewPassengerCount");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountNotLike(String value) {
            addCriterion("crew_passenger_count not like", value, "crewPassengerCount");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountIn(List<String> values) {
            addCriterion("crew_passenger_count in", values, "crewPassengerCount");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountNotIn(List<String> values) {
            addCriterion("crew_passenger_count not in", values, "crewPassengerCount");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountBetween(String value1, String value2) {
            addCriterion("crew_passenger_count between", value1, value2, "crewPassengerCount");
            return (Criteria) this;
        }

        public Criteria andCrewPassengerCountNotBetween(String value1, String value2) {
            addCriterion("crew_passenger_count not between", value1, value2, "crewPassengerCount");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberIsNull() {
            addCriterion("task_book_number is null");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberIsNotNull() {
            addCriterion("task_book_number is not null");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberEqualTo(String value) {
            addCriterion("task_book_number =", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberNotEqualTo(String value) {
            addCriterion("task_book_number <>", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberGreaterThan(String value) {
            addCriterion("task_book_number >", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberGreaterThanOrEqualTo(String value) {
            addCriterion("task_book_number >=", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberLessThan(String value) {
            addCriterion("task_book_number <", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberLessThanOrEqualTo(String value) {
            addCriterion("task_book_number <=", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberLike(String value) {
            addCriterion("task_book_number like", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberNotLike(String value) {
            addCriterion("task_book_number not like", value, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberIn(List<String> values) {
            addCriterion("task_book_number in", values, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberNotIn(List<String> values) {
            addCriterion("task_book_number not in", values, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberBetween(String value1, String value2) {
            addCriterion("task_book_number between", value1, value2, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andTaskBookNumberNotBetween(String value1, String value2) {
            addCriterion("task_book_number not between", value1, value2, "taskBookNumber");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNull() {
            addCriterion("created_at is null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIsNotNull() {
            addCriterion("created_at is not null");
            return (Criteria) this;
        }

        public Criteria andCreatedAtEqualTo(Date value) {
            addCriterion("created_at =", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotEqualTo(Date value) {
            addCriterion("created_at <>", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThan(Date value) {
            addCriterion("created_at >", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("created_at >=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThan(Date value) {
            addCriterion("created_at <", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtLessThanOrEqualTo(Date value) {
            addCriterion("created_at <=", value, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtIn(List<Date> values) {
            addCriterion("created_at in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotIn(List<Date> values) {
            addCriterion("created_at not in", values, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtBetween(Date value1, Date value2) {
            addCriterion("created_at between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andCreatedAtNotBetween(Date value1, Date value2) {
            addCriterion("created_at not between", value1, value2, "createdAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNull() {
            addCriterion("updated_at is null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIsNotNull() {
            addCriterion("updated_at is not null");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtEqualTo(Date value) {
            addCriterion("updated_at =", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotEqualTo(Date value) {
            addCriterion("updated_at <>", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThan(Date value) {
            addCriterion("updated_at >", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtGreaterThanOrEqualTo(Date value) {
            addCriterion("updated_at >=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThan(Date value) {
            addCriterion("updated_at <", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtLessThanOrEqualTo(Date value) {
            addCriterion("updated_at <=", value, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtIn(List<Date> values) {
            addCriterion("updated_at in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotIn(List<Date> values) {
            addCriterion("updated_at not in", values, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtBetween(Date value1, Date value2) {
            addCriterion("updated_at between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andUpdatedAtNotBetween(Date value1, Date value2) {
            addCriterion("updated_at not between", value1, value2, "updatedAt");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNull() {
            addCriterion("company_code is null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIsNotNull() {
            addCriterion("company_code is not null");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeEqualTo(String value) {
            addCriterion("company_code =", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotEqualTo(String value) {
            addCriterion("company_code <>", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThan(String value) {
            addCriterion("company_code >", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeGreaterThanOrEqualTo(String value) {
            addCriterion("company_code >=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThan(String value) {
            addCriterion("company_code <", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLessThanOrEqualTo(String value) {
            addCriterion("company_code <=", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeLike(String value) {
            addCriterion("company_code like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotLike(String value) {
            addCriterion("company_code not like", value, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeIn(List<String> values) {
            addCriterion("company_code in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotIn(List<String> values) {
            addCriterion("company_code not in", values, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeBetween(String value1, String value2) {
            addCriterion("company_code between", value1, value2, "companyCode");
            return (Criteria) this;
        }

        public Criteria andCompanyCodeNotBetween(String value1, String value2) {
            addCriterion("company_code not between", value1, value2, "companyCode");
            return (Criteria) this;
        }
    }

    public static class Criteria extends GeneratedCriteria {
        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}