package com.ruoyi.system.domain.oc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "飞行员详细信息返回")
public class PilotDetailVO {

    @ApiModelProperty("头像")
    private String avatar;

    @ApiModelProperty("飞行员个人信息")
    private PilotInfoVO pilotInfo;

    @ApiModelProperty("飞行员飞行情况")
    private PilotFlightSituationVO flightSituation;




}
