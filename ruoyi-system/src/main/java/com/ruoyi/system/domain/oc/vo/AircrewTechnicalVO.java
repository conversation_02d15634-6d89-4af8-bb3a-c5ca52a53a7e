package com.ruoyi.system.domain.oc.vo;

import com.ruoyi.common.enums.runcontrol.AircrewRecordType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "AircrewTechnicalVO", description = "技术文档记录返回")
public class AircrewTechnicalVO {

    @ApiModelProperty("飞行记录簿")
    private List<AircrewRecordInfoVO> flightRecords;
    @ApiModelProperty("飞行训练和检查记录")
    private List<AircrewRecordInfoVO> trainingInspectRecords;
    @ApiModelProperty("事故征候结论")
    private List<AircrewRecordInfoVO> accidentConclusionRecords;
    @ApiModelProperty("奖励记录")
    private List<AircrewRecordInfoVO> rewardRecords;
    @ApiModelProperty("惩罚记录")
    private List<AircrewRecordInfoVO> punishRecords;

    public static AircrewTechnicalVO setting(Map<Integer, List<AircrewRecordInfoVO>> map) {
        AircrewTechnicalVO vo = new AircrewTechnicalVO();
        vo.setFlightRecords(map.get(AircrewRecordType.SECOND_FILETYPE_1.getCode()) == null ? new ArrayList<>() : map.get(AircrewRecordType.SECOND_FILETYPE_1.getCode()));
        vo.setTrainingInspectRecords(map.get(AircrewRecordType.SECOND_FILETYPE_2.getCode()) == null ? new ArrayList<>() : map.get(AircrewRecordType.SECOND_FILETYPE_2.getCode()));
        vo.setAccidentConclusionRecords(map.get(AircrewRecordType.SECOND_FILETYPE_3.getCode()) == null ? new ArrayList<>() : map.get(AircrewRecordType.SECOND_FILETYPE_3.getCode()));
        vo.setRewardRecords(map.get(AircrewRecordType.SECOND_FILETYPE_4.getCode()) == null ? new ArrayList<>() : map.get(AircrewRecordType.SECOND_FILETYPE_4.getCode()));
        vo.setPunishRecords(map.get(AircrewRecordType.SECOND_FILETYPE_5.getCode()) == null ? new ArrayList<>() : map.get(AircrewRecordType.SECOND_FILETYPE_5.getCode()));
        return vo;
    }
}
