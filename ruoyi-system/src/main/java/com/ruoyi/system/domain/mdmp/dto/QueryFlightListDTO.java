package com.ruoyi.system.domain.mdmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "查询航班列表入参")
public class QueryFlightListDTO {

    /** 航班日期 */
    @ApiModelProperty(value = "航班日期", example = "2024-11-27", required = true)
    @NotBlank(message = "航班日期不能为空")
    private String flightDate ;

    /** 进出港标识;0:进港, 1:离港 */
    @ApiModelProperty(value = "进出港标识(0:进港, 1:离港)", example = "0")
    private Integer ioSign ;

    /** 起始机场四字码 */
    @ApiModelProperty(value = "起始机场四字码", example = "2LCC")
    private String departAirportCode ;

    /** 到达机场四字码 */
    @ApiModelProperty(value = "到达机场四字码", example = "2WHZ")
    private String arriveAirportCode ;

    /** 航班状态 */
    @ApiModelProperty(value = "航班状态(1:正常, 2:异常, 3:已取消)", example = "1")
    private String flightStatus ;

    /** 航空器呼号 */
    @ApiModelProperty(value = "航空器呼号", example = "FL9999")
    private String callSign ;

    /** 机型 */
    @ApiModelProperty(value = "机型", example = "A320")
    private String aircraftType ;

    @ApiModelProperty(value = "页数", example = "1", required = true)
    private Integer pageNum;

    @ApiModelProperty(value = "条数", example = "10", required = true)
    private Integer pageSize;

    private String deptCode;

    List<String> deptCodeList;

}
