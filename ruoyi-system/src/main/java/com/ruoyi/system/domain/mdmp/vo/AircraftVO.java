package com.ruoyi.system.domain.mdmp.vo;

import com.ruoyi.system.domain.mdmp.FlightPlanAircraftModel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "当日在飞的航空器信息返回参数")
public class AircraftVO {

    /**
     * 航班计划id
     */
    @ApiModelProperty(value = "航班计划id", example = "1", dataType = "Long")
    private Long flightPlanId;

    /**
     * 航空器名称
     */
    @ApiModelProperty(value = "航空器名称", example = "787", dataType = "String")
    private String aircraftName;

    /**
     * 机尾号
     */
    @ApiModelProperty(value = "机尾号", example = "TN-21", dataType = "String")
    private String tailNumber;

    public static List<AircraftVO> setting(Long flightPlanId, List<FlightPlanAircraftModel> aircraftList) {
        List<AircraftVO> voList = new ArrayList<>();
        for (FlightPlanAircraftModel flightPlanAircraftModel : aircraftList) {
            AircraftVO vo = new AircraftVO();
            vo.setFlightPlanId(flightPlanId);
            vo.setAircraftName(flightPlanAircraftModel.getAircraftName());
            vo.setTailNumber(flightPlanAircraftModel.getTailNumber());
            voList.add(vo);
        }
        return voList;
    }
}
