package com.ruoyi.system.domain.mdmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "查询航班操作日志入参")
public class QueryFlightLogDTO {

    /** 航班ID */
    @ApiModelProperty(value = "航班ID", required = true)
    @NotNull(message = "航班ID不能为空")
    private Integer flightId ;
}
