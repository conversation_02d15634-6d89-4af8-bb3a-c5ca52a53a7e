package com.ruoyi.system.domain.oc.vo;

import com.ruoyi.system.domain.oc.WxUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.ArrayList;
import java.util.List;

/**
 * 微信用户详情信息
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "查询微信用户列表返回")
public class WxUserVO {

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    private String userName;

    /**
     * 用户状态(0:禁用, 1:可用)
     */
    @ApiModelProperty("用户状态(0:禁用, 1:可用)")
    private Integer userStatus;

    /**
     * 所属航司名称
     */
    @ApiModelProperty("所属航司名称")
    private String companyName;

    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String phoneNumber;

    /**
     * 飞行时间
     */
    @ApiModelProperty("飞行时间(小时)")
    private Integer flyTime;

    /**
     * 飞行次数
     */
    @ApiModelProperty("飞行次数")
    private Integer flyNumber;

    public static List<WxUserVO> setting(List<WxUser> wxUserList) {
        List<WxUserVO> voList = new ArrayList<>();
        for (WxUser wxUser : wxUserList) {
            WxUserVO vo = new WxUserVO();
            vo.setUserId(wxUser.getUserId());
            vo.setUserName(wxUser.getUserName());
            vo.setUserStatus(wxUser.getUserStatus());
            vo.setCompanyName(wxUser.getCompanyName());
            vo.setPhoneNumber(wxUser.getPhoneNumber());
            vo.setFlyTime(wxUser.getFlyTime()/60);
            vo.setFlyNumber(wxUser.getFlyNumber());
            voList.add(vo);
        }
        return voList;
    }
}
