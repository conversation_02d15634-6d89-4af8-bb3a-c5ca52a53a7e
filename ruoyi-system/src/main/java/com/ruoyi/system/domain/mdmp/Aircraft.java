package com.ruoyi.system.domain.mdmp;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 【请填写功能名称】对象 aircraft
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
@Data
public class Aircraft extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 机型
     */
    @Excel(name = "机型")
    @NotBlank(message = "机型不能为空")
    private String aircraftType;

    /**
     * 飞机注册号
     */
    @Excel(name = "飞机注册号")
    private String aircraftReg;

    /**
     * 呼号
     */
    @Excel(name = "呼号")
    private String callSign;
    /**是否民航飞机（1:是, 0:否）*/
    @NotNull(message = "有效日期不能为空")
    private Integer isCivil;
    /**有效期*/
    @NotBlank(message = "有效日期不能为空")
    private String validityDate;
    /**失效期*/
    @NotBlank(message = "失效日期不能为空")
    private String expirationDate;

    private String deptCode;

    List<String> deptCodeList;

    @Valid
    List<AircraftMp> aircraftMpList;
}
