package com.ruoyi.system.domain.mdmp.vo;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2024/10/14 10:27
 * @description：
 * @modified By：
 * @version: $
 */
@Data
@ApiModel(description = "新增地形")
public class AddTopographicVo {

    @Excel(name = "经度起始点")
    @ApiModelProperty(value = "经度起始点", example = "85.25", dataType = "String")
    private BigDecimal longitudeStart;

    @Excel(name = "经度截止点")
    @ApiModelProperty(value = "经度截止点", example = "85.25", dataType = "BigDecimal")
    private BigDecimal longitudeEnd;

    @Excel(name = "纬度起始")
    @ApiModelProperty(value = "纬度起始", example = "22.25", dataType = "BigDecimal")
    private BigDecimal latitudeStart;
    @Excel(name = "纬度截止")
    @ApiModelProperty(value = "纬度截止", example = "22.25", dataType = "BigDecimal")
    private BigDecimal latitudeEnd;

    @Excel(name = "海拔高度")
    @ApiModelProperty(value = "海拔高度", example = "1000", dataType = "Integer")
    private Integer height;
}
