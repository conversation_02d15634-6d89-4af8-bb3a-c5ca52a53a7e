package com.ruoyi.system.domain.oc.entity;

import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * 机组人员记录
 *
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
@ApiModel(value = "AircrewRecord", description = "飞行器实体")
public class AircrewRecord extends BaseEntity {

    /**
     * 记录编号
     */
    @ApiModelProperty("记录编号")
    private Long aircrewRecordId;

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Long userId;

    /**
     * 文件第一类型
     * 1:技术文档，2:满足条款要求记录，3:措施记录
     */
    @ApiModelProperty("文件第一类型(1:技术文档, 2:满足条款要求记录, 3:对不合格飞行员采取的措施记录(此时该记录没有文件第二类型))")
    private Integer firstFileType;
    /**
     * 文件第二类型
     * 技术文档：1:飞行记录簿, 2:训练和检查记录, 3:事故征候结论, 4:奖励记录, 5:惩罚记录;
     * 满足条款要求记录：1:航路检查, 2:飞机和航路资格审定, 3:体检鉴定和疾病治疗, 4:飞行执勤休息时间记录;
     * 措施记录：0:无第二类型
     */
    @ApiModelProperty("文件第二类型(技术文档：1:飞行记录簿, 2:训练和检查记录, 3:事故征候结论, 4:奖励记录, 5:惩罚记录;" +
            "满足条款要求记录：1:航路检查, 2:飞机和航路资格审定, 3:体检鉴定和疾病治疗, 4:飞行执勤休息时间记录;" +
            "措施记录：0:无第二类型" + ")")
    private Integer secondFileType;

    /**
     * 文件名
     */
    @ApiModelProperty("文件名")
    private String fileName;

    /**
     * 文件后缀
     */
    @ApiModelProperty("文件后缀")
    private String fileSuffix;

    /**
     * 文件地址
     */
    @ApiModelProperty("文件地址")
    private String saveUrl;

    public static AircrewRecord setting(Long userId, Integer firstFileType, Integer secondFileType,String remark, String realFileName, String saveUrl, String createBy) {
        String fileName = realFileName.substring(0, realFileName.lastIndexOf(".")).toLowerCase();
        String fileSuffix = realFileName.substring(realFileName.lastIndexOf(".") + 1).toLowerCase();
        AircrewRecord record = new AircrewRecord();
        record.setUserId(userId);
        record.setFirstFileType(firstFileType);
        record.setSecondFileType(secondFileType);
        record.setFileName(fileName);
        record.setFileSuffix(fileSuffix);
        record.setSaveUrl(saveUrl);
        record.setRemark(remark);
        Date createTime = new Date();
        record.setCreateTime(createTime);
        record.setUpdateTime(createTime);
        record.setCreateBy(createBy);
        return record;
    }
}
