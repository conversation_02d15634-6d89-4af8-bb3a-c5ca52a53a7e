package com.ruoyi.system.domain.oc;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 微信小程序用户
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "微信小程序用户")
public class WxUser {
    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Long userId;

    /**
     * 微信登录openId
     */
    @ApiModelProperty("微信登录openId")
    private String openId;

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    private String userName;

    /**
     * 头像
     */
    @ApiModelProperty("头像")
    private String avatar;

    /**
     * 用户状态(0:禁用, 1:可用)
     */
    @ApiModelProperty("用户状态(0:禁用, 1:可用)")
    private Integer userStatus;

    /**
     * 所属航司二字码
     */
    @ApiModelProperty("所属航司二字码")
    private String companyCode;

    /**
     * 所属航司名称
     */
    @ApiModelProperty("所属航司名称")
    private String companyName;

    /**
     * 手机号码
     */
    @ApiModelProperty("手机号码")
    private String phoneNumber;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 飞行时间
     */
    @ApiModelProperty("飞行时间(分钟数)")
    private Integer flyTime;

    /**
     * 飞行次数
     */
    @ApiModelProperty("飞行次数")
    private Integer flyNumber;

    /**
     * 角色标识（第一次注册时，手动添加用户时用,后续没用了）
     */
    @ApiModelProperty("角色标识（第一次注册时，手动添加用户时用,后续没用了）")
    private String roleKey;

}
