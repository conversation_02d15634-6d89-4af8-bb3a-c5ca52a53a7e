package com.ruoyi.system.domain.mdmp;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 航班计划作业区内关联表对象 work_in
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@Data
@ApiModel(value = "航班计划作业区内关联表对象")
public class FlightPlanWorkIn extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 作业区Id
     */
    @Excel(name = "作业区Id")
    @ApiModelProperty(value = "作业区Id", example = "123", dataType = "Long")
    private Long flightPlanWorkId;

    /**
     * 区域类型;0等待隔离区
     */
    @Excel(name = "区域类型;0等待隔离区")
    private Integer regionType;

    /**
     * 类型;0圆  1点连线
     */
    @Excel(name = "类型;0圆  1点连线")
    @ApiModelProperty(value = "类型(0,圆;1,点连线)", required = true, allowableValues = "0,1")
    private Integer graphType;

    /**
     * 半径;单位米
     */
    @Excel(name = "半径;单位米")
    @ApiModelProperty(value = "半径", example = "123.00", dataType = "BigDecimal")
    private BigDecimal radius;

    /**
     * 圆心;经度
     */
    @Excel(name = "圆心;经度")
    @ApiModelProperty(value = "圆心;经度", example = "123", dataType = "String")
    private String circleCenterLong;

    /**
     * 圆心;纬度
     */
    @Excel(name = "圆心;纬度")
    @ApiModelProperty(value = "圆心;纬度", example = "123", dataType = "String")
    private String circleCenterLat;


    /**
     * 作业区名称
     */
    @Excel(name = "作业区名称")
    @ApiModelProperty(value = "作业区名称", example = "作业区", dataType = "String")
    private String workInName;


    /**
     * 最低高度;单位米
     */
    @ApiModelProperty("半径;单位米")
    @Excel(name = "最低高度;单位米")
    private BigDecimal minHeight;

    /**
     * 最高高度;单位米
     */
    @ApiModelProperty("半径;单位米")
    @Excel(name = "最高高度;单位米")
    private BigDecimal maxHeight;


    /**
     * 作业区内经纬度关联表
     */
    @Excel(name = "作业区内表经纬度关联表")
    @ApiModelProperty(value = "作业区内表经纬度关联表")
    private List<WorkinLongLat> workinLongLatList;


}
