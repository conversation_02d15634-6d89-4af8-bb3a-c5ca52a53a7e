package com.ruoyi.system.domain.oc.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.domain.oc.WxQualification;
import com.ruoyi.system.domain.oc.WxUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "PilotQualificationVO", description = "查询飞行员资质返回")
public class PilotQualificationVO {

    @ApiModelProperty("用户ID")
    private Long userId;

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("执照有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityOfLicense;

    @ApiModelProperty("体检合格证有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityPeriodOfPhysicalExamination;

    @ApiModelProperty("熟练检查基准月")
    private String proficiencyCheckStandardMonth;

    @ApiModelProperty("熟练检查到期日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date proficiencyCheckValidity;

    @ApiModelProperty("机长航线练检查到期日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityPeriodOfRouteTrainingInspection;

    @ApiModelProperty("危险品训练有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dangerTrainingInspection;

    @ApiModelProperty("汉语言有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date chineseLanguageValidity;

    @ApiModelProperty("空勤登记证有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityOfRegistrationCertificate;

    @ApiModelProperty("Y12放行授权有效期(yyyy-MM-dd)")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date y12Inspection;

    @ApiModelProperty("C208放行授权有效期(yyyy-MM-dd)")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date c208Inspection;

    @ApiModelProperty("B300放行授权有效期(yyyy-MM-dd)")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date b300Inspection;

    @ApiModelProperty("用户(机长：pilot, 副机长：copilot, 机务：maintenance)")
    private String roleKey;

    public static PilotQualificationVO setting(WxUser user, WxQualification wxQualification, String roleKey) {
        PilotQualificationVO qualification = new PilotQualificationVO();
        qualification.setUserId(user.getUserId());
        qualification.setUserName(user.getUserName());
        if (Objects.nonNull(wxQualification)) {
            qualification.setValidityOfLicense(wxQualification.getValidityOfLicense());
            qualification.setValidityPeriodOfPhysicalExamination(wxQualification.getValidityPeriodOfPhysicalExamination());
            qualification.setProficiencyCheckStandardMonth(wxQualification.getProficiencyCheckStandardMonth());
            qualification.setProficiencyCheckValidity(wxQualification.getProficiencyCheckValidity());
            qualification.setValidityPeriodOfRouteTrainingInspection(wxQualification.getValidityPeriodOfRouteTrainingInspection());
            qualification.setDangerTrainingInspection(wxQualification.getDangerTrainingInspection());
            qualification.setChineseLanguageValidity(wxQualification.getChineseLanguageValidity());
            qualification.setValidityOfRegistrationCertificate(wxQualification.getValidityOfRegistrationCertificate());
            qualification.setY12Inspection(wxQualification.getY12Inspection());
            qualification.setC208Inspection(wxQualification.getC208Inspection());
            qualification.setB300Inspection(wxQualification.getB300Inspection());
        }
        qualification.setRoleKey(roleKey);
        return qualification;
    }
}
