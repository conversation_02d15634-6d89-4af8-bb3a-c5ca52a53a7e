package com.ruoyi.system.domain.mdmp;


import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

@Data
@ApiModel(value = "地图数据")
public class MapData extends BaseEntity {

    private Long id;
    @Excel(name = "经度起始点")
    @ApiModelProperty(value = "经度起始点", example = "85.25", dataType = "String")
    private BigDecimal longitudeStart;

    @Excel(name = "经度截止点")
    @ApiModelProperty(value = "经度截止点", example = "85.25", dataType = "BigDecimal")
    private BigDecimal longitudeEnd;

    @Excel(name = "纬度起始")
    @ApiModelProperty(value = "纬度起始", example = "22.25", dataType = "BigDecimal")
    private BigDecimal latitudeStart;
    @Excel(name = "纬度截止")
    @ApiModelProperty(value = "纬度截止", example = "22.25", dataType = "BigDecimal")
    private BigDecimal latitudeEnd;
    @Excel(name = "高度起始")
    @ApiModelProperty(value = "高度起始", example = "1000", dataType = "Integer")
    private Integer heightStart;
    @Excel(name = "高度截止")
    @ApiModelProperty(value = "高度截止", example = "2000", dataType = "Integer")
    private Integer heightEnd;
    @Excel(name = "大气压起始")
    @ApiModelProperty(value = "大气压起始", example = "1000", dataType = "Integer")
    private Integer pressureStart;
    @Excel(name = "大气压截止")
    @ApiModelProperty(value = "大气压截止", example = "1100", dataType = "Integer")
    private Integer pressureEnd;
    @Excel(name = "第几层")
    @ApiModelProperty(value = "第几层", example = "1", dataType = "Integer")
    private Integer layer;
    @Excel(name = "经度递增")
    @ApiModelProperty(value = "经度递增", example = "0.05", dataType = "BigDecimal")
    private BigDecimal longitudeIncrease;
    @Excel(name = "纬度递增")
    @ApiModelProperty(value = "纬度递增", example = "0.05", dataType = "BigDecimal")
    private BigDecimal latitudeIncrease;
    @Excel(name = "状态码")
    @ApiModelProperty(value = "状态码", example = "1", dataType = "Integer")
    private Integer statusCode;
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序", example = "1", dataType = "Integer")
    private Integer index;

    @ApiModelProperty(value = "名称", example = "1", dataType = "Integer")
    private String airspaceName;

    @ApiModelProperty(value = "状态码", example = "1", dataType = "Integer")
    private Integer statusCodes;

    private Long type;

    private Long airspaceId;
}


