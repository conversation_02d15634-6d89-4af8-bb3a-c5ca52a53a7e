package com.ruoyi.system.domain.oc.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 记录班次下放行单对象 oc_flight_sorties_releasenote
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Data
public class FlightSortiesReleasenote extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 航班班次编号
     */
    private Long id;

    /**
     * 航班计划编号
     */
    @Excel(name = "航班计划编号")
    private Long flightSortiesId;

    /**
     * 上传放行单时间
     */
    @Excel(name = "上传放行单时间")
    private String uploadReleasenoteTime;

    /**
     * 放行单拒绝时间
     */
    @Excel(name = "放行单拒绝时间")
    private String releasenoteRefuseTime;

    /**
     * 放行单通过时间
     */
    @Excel(name = "放行单通过时间")
    private String releasenotePassTime;

    /**
     * 拒绝理由
     */
    @Excel(name = "拒绝理由")
    private String releasenoteRefuse;

    @ApiModelProperty("是否当前使用")
    private Integer useing;

}
