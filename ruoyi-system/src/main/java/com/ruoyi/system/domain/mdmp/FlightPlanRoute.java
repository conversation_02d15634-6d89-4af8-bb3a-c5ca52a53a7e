package com.ruoyi.system.domain.mdmp;

import java.util.List;

import com.ruoyi.system.domain.mdmp.vo.ConflictRoute;
import com.ruoyi.system.domain.mdmp.vo.ConflictWorkVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;


/**
 * 航班计划航线对象 flight_plan_route
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@ApiModel(value = "航班计划航线对象")
public class FlightPlanRoute extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 租户号
     */
    private Long id;

    /**
     * 航班计划id
     */
    @Excel(name = "航班计划id")
    @ApiModelProperty(value = "航班计划id", example = "1", dataType = "Long")
    private Long flightPlanId;

    /**
     * 航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划
     */
    @Excel(name = "航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划")
    @ApiModelProperty(value = "航班计划类型(1,长期计划;2, 次日计划;3,当日放行计划 4 单一飞行计划)", required = true, allowableValues = "1,2,3")
    private Integer planType;


    @Excel(name = "航线id")
    @ApiModelProperty(value = "航线id", example = "1", dataType = "Long")
    private Long routeId;

    /**
     * 航线代号
     */
    @Excel(name = "航线代号")
    @ApiModelProperty(value = "航线代号", example = "GH001", dataType = "String")
    @NotBlank(message = "航线代号不能为空")
    private String routeCode;

    /**
     * 备注
     */
    @Excel(name = "备注")
    @ApiModelProperty(value = "备注", example = "备注", dataType = "String")
    private String remarks;

    @ApiModelProperty("航班计划航线经纬度")
    @Valid
    private List<FlightPlanRouteLongLat> flightPlanRouteLongLatList;


    @ApiModelProperty("冲突作业区")
    private List<ConflictWorkVo> conflictWorkVoList;

    @ApiModelProperty("冲突航线")
    private List<ConflictRoute> conflictRoute;

    @ApiModelProperty(value = "航线冲突说明", example = "航线冲突说明", dataType = "String")
    private String routeConflictDescription;


    @ApiModelProperty("航线地图数据")
    private List<RouteMapData> routeMapDataList;
}
