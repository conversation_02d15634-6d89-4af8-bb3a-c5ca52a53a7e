package com.ruoyi.system.domain.oc.entity;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 记录班次下气象资料对象 oc_flight_sorties_mete_review
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@ApiModel(value = "FlightSortiesMeteReview", description = "记录班次下气象资料对象")
@Data
public class FlightSortiesMeteReview extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 航班班次编号
     */
    private Long id;

    /**
     * 航班计划编号
     */
    @Excel(name = "航班计划编号")
    private Long flightSortiesId;

    /**
     * 气象资料上传时间
     */
    @Excel(name = "气象资料上传时间")
    private String uploadMeteReviewTime;


    private String meteReviewData;

    /**
     * 气象资料检核拒绝
     */
    @Excel(name = "气象资料检核拒绝")
    private String meteReviewRefuseTime;

    /**
     * 气象资料检核同该国时间
     */
    @Excel(name = "气象资料检核同该国时间")
    private String meteReviewPassTime;

    /**
     * 气象资料拒绝理由
     */
    @Excel(name = "气象资料拒绝理由")
    private String meteReviewRefuse;

    @ApiModelProperty("是否当前使用")
    private Integer useing;

}
