package com.ruoyi.system.domain.mdmp;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.Valid;
import javax.validation.constraints.Digits;
import javax.validation.constraints.NotBlank;

/**
 * 作业区基础信息对象 work_basis
 *
 * <AUTHOR>
 * @date 2024-04-02
 */

@Data
@ApiModel(value = "WorkBasis", description = "作业区实体")
public class WorkBasis {
    private static final long serialVersionUID = 1L;

    /**
     * 租户号
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 图形类型;0圆  1点
     */
    @ApiModelProperty("图形类型;0圆  1点")
    @Excel(name = "图形类型;0圆  1点")
    private Integer graphType;

    /**
     * 作业类型;0区域内作业 1沿线作业 2不作业航线
     */
    @ApiModelProperty("作业类型;0区域内作业 1沿线作业 2不作业航线")
    @Excel(name = "作业类型;0区域内作业 1沿线作业 2不作业航线")
    private Integer workType;

    /**
     * 作业区偏移量;默认5000米 图形内
     */
    @ApiModelProperty("作业区偏移量默认;5000米")
    @Excel(name = "作业区偏移量;默认5000米 图形内")
    @Digits(integer = 8, fraction = 2, message = "半径最多8位整数和2位小数")
    private BigDecimal workOffset;


    @ApiModelProperty("0是向内偏移，1是向外偏移")
    private Integer positiveOrNegative;

    /**
     * 圆心;经度
     */
    @ApiModelProperty("圆心;经度")
    @Excel(name = "圆心;经度")
    private String circleCenterLong;

    /**
     * 半径;单位米
     */
    @ApiModelProperty("半径;单位米")
    @Excel(name = "半径;单位米")
    private BigDecimal radius;

    /**
     * 作业区名称
     */
    @ApiModelProperty("作业区名称")
    @Excel(name = "作业区名称")
    @NotBlank(message = "作业区名称不能为空")
    private String workName;

    /**
     * 圆心;纬度
     */
    @ApiModelProperty("圆心;纬度")
    @Excel(name = "圆心;纬度")
    private String circleCenterLat;

    /**
     * 最低高度;单位米
     */
    @ApiModelProperty("半径;单位米")
    @Excel(name = "最低高度;单位米")
    private BigDecimal minHeight;

    /**
     * 最高高度;单位米
     */
    @ApiModelProperty("半径;单位米")
    @Excel(name = "最高高度;单位米")
    private BigDecimal maxHeight;


    @ApiModelProperty("作业区内集合")
    @Valid
    private List<WorkInBasis> workInBasisList;

    @ApiModelProperty("经纬度集合")
    private List<WorkBasisLongLat> workBasisLongLatList;


    @ApiModelProperty("作业区地图数据集合")
    private List<WorkMapData> workMapDataList;

    private String deptCode;

    private List<String> deptCodeList;
}
