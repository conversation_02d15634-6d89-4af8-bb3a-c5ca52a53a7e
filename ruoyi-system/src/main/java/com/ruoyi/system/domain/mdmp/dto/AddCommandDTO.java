package com.ruoyi.system.domain.mdmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "添加命令入参")
public class AddCommandDTO {

    @ApiModelProperty(value = "ID(修改必传, 新增不传)", example = "1")
    private Integer id;

    /** 指令;例:盲降、高于3000米 */
    @ApiModelProperty(value = "指令(高于;盲降)", example = "高于", required = true)
    @NotBlank(message = "指令不能为空")
    private String command ;
    /** 指令值 */
    @ApiModelProperty(value = "指令值", example = "3000")
    private Integer commandValue ;
}
