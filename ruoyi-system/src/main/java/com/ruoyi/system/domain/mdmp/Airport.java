package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import java.util.List;

/**
 * 机场对象 airport
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@ApiModel(value = "Airport", description = "机场实体")
public class Airport
{
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    @ApiModelProperty("ID")
    private Long id;

    /** 机场名称 */
    @ApiModelProperty("机场名称")
    @Excel(name = "机场名称")
    @NotBlank(message = "机场名称不能为空")
    private String airportName;

    /** 机场三字码 */
    @ApiModelProperty("机场三字码")
    @Excel(name = "机场三字码")
    private String threeCode;

    /** 四字码 */
    @ApiModelProperty("四字码")
    @Excel(name = "四字码")
    private String tetradCode;

    /** 经度 */
    @ApiModelProperty("经度")
    @Excel(name = "经度")
    private String longitude;

    /** 纬度 */
    @ApiModelProperty("纬度")
    @Excel(name = "纬度")
    private String latitude;

    /** 备注 */
    @ApiModelProperty("备注")
    @Excel(name = "备注")
    private String remarks;

    private String deptCode;

    private List<String> deptCodeList;

    public List<String> getDeptCodeList() {
        return deptCodeList;
    }

    public void setDeptCodeList(List<String> deptCodeList) {
        this.deptCodeList = deptCodeList;
    }

    public String getDeptCode() {
        return deptCode;
    }

    public void setDeptCode(String deptCode) {
        this.deptCode = deptCode;
    }

    public void setId(Long id)
    {
        this.id = id;
    }

    public Long getId()
    {
        return id;
    }
    public void setAirportName(String airportName)
    {
        this.airportName = airportName;
    }

    public String getAirportName()
    {
        return airportName;
    }
    public void setThreeCode(String threeCode)
    {
        this.threeCode = threeCode;
    }

    public String getThreeCode()
    {
        return threeCode;
    }
    public void setTetradCode(String tetradCode)
    {
        this.tetradCode = tetradCode;
    }

    public String getTetradCode()
    {
        return tetradCode;
    }
    public void setLongitude(String longitude)
    {
        this.longitude = longitude;
    }

    public String getLongitude()
    {
        return longitude;
    }
    public void setLatitude(String latitude)
    {
        this.latitude = latitude;
    }

    public String getLatitude()
    {
        return latitude;
    }
    public void setRemarks(String remarks)
    {
        this.remarks = remarks;
    }

    public String getRemarks()
    {
        return remarks;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("id", getId())
            .append("airportName", getAirportName())
            .append("threeCode", getThreeCode())
            .append("tetradCode", getTetradCode())
            .append("longitude", getLongitude())
            .append("latitude", getLatitude())
            .append("remarks", getRemarks())
            .toString();
    }
}
