package com.ruoyi.system.domain.oc.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import lombok.Data;

/**
 * 记录班次下飞行时刻对象 oc_flight_sorties_fly_moment
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
@Data
public class FlightSortiesFlyMoment extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 航班班次编号
     */
    private Long id;

    /**
     * 航班计划编号
     */
    @Excel(name = "航班计划编号")
    private Long flightSortiesId;

    /**
     * 飞行时刻录入时间
     */
    @Excel(name = "飞行时刻录入时间")
    private String flyMomentTime;

    /**
     * 滑行开始时间
     */
    @Excel(name = "滑行开始时间")
    private String slideStartTime;

    /**
     * 起飞时间
     */
    @Excel(name = "起飞时间")
    private String flyStartTime;

    /**
     * 落地时间
     */
    @Excel(name = "落地时间")
    private String flyEndTime;

    /**
     * 滑行结束
     */
    @Excel(name = "滑行结束")
    private String slideEndTime;


}
