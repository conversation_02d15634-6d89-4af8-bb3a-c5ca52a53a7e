package com.ruoyi.system.domain.mdmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

@Data
@ApiModel(description = "查询电子行程单操作日志列表入参")
public class QueryFlightLogListDTO {

    /** 创建时间 */
    @ApiModelProperty(value = "创建时间")
    private String createTime ;

    /** 呼号 */
    @ApiModelProperty(value = "呼号")
    private String callSign ;

    /** 航班状态 */
    @ApiModelProperty(value = "航班状态(1:正常, 2:异常, 3:已取消, 4:已删除)")
    private Integer flightStatus ;

    private Integer pageNum;
    private Integer pageSize;


    private String deptCode;

    List<String> deptCodeList;
}
