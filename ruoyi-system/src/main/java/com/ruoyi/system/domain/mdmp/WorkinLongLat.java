package com.ruoyi.system.domain.mdmp;

import java.math.BigDecimal;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 作业区内经纬度对象 workin_long_lat
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@Data
@ApiModel(value = "作业区内经纬度对象")
public class WorkinLongLat extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 作业区经纬度关联表ID
     */
    @Excel(name = "作业区经纬度关联表ID")
    @ApiModelProperty(value = "作业区经纬度关联表ID", example = "123", dataType = "Long")
    private Long workInId;

    /**
     * 经度
     */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度", example = "123", dataType = "String")
    private String longitude;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度", example = "123", dataType = "String")
    private String latitude;

    /**
     * 高度
     */
    @Excel(name = "高度")
    @ApiModelProperty(value = "高度", example = "123.00", dataType = "BigDecimal")
    private BigDecimal height;

    /**
     * 排序
     */
    @Excel(name = "排序")
    @ApiModelProperty(value = "排序", example = "12", dataType = "Integer")
    private Integer sortNumber;

    /**
     * 转换后的经度
     */
    @Excel(name = "转换后的经度")
    @ApiModelProperty(value = "转换后的经度", example = "12", dataType = "BigDecimal")
    private BigDecimal doubleLongitude;

    /**
     * 转换后的纬度
     */
    @Excel(name = "转换后的纬度")
    @ApiModelProperty(value = "转换后的纬度", example = "12", dataType = "BigDecimal")
    private BigDecimal doubleLatitude;

    /** 点名称 */
    @ApiModelProperty("点名称")
    @Excel(name = "点名称")
    private String coordinateName;


}
