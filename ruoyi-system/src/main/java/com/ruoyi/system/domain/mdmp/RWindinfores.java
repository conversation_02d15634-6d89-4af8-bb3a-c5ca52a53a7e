package com.ruoyi.system.domain.mdmp;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 风对象 r_windinfores
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
public class RWindinfores {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 报文表ID
     */
    @Excel(name = "报文表ID")
    private Long decodeReportId;

    /**
     * 风向
     */
    @Excel(name = "风向")
    private Integer windDirection;

    /**
     * 风速
     */
    @Excel(name = "风速")
    private Double windSpeed;

    /**
     * 开始风向
     */
    @Excel(name = "开始风向")
    private Integer windDirectionBegin;

    /**
     * 结束风向
     */
    @Excel(name = "结束风向")
    private Integer windDirectionEnd;

    /**
     * 阵风
     */
    @Excel(name = "阵风")
    private Double gust;

    /**
     * 风向代码
     */
    @Excel(name = "风向代码")
    private String windDirectionCode;

    private Integer colourType;
}
