package com.ruoyi.system.domain.mdmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "无人机指令入参")
public class FlightCommandDTO {

    @ApiModelProperty(value = "指令消息id", example = "1", required = true)
    @NotNull(message = "指令消息ID为空")
    private Integer commandId ;
    @ApiModelProperty(value = "是否查看", example = "已查看",required = true)
    @NotNull(message = "查看状态不能为空")
    private Integer queryStatus ;
    @ApiModelProperty(value = "是否执行", example = "未执行", required = true)
    @NotNull(message = "执行状态不能为空")
    private Integer executionStatus;
    @ApiModelProperty(value = "操作时间", example = "", required = true)
    @NotBlank(message = "操作时间不能为空")
    private String operatingTime;
}
