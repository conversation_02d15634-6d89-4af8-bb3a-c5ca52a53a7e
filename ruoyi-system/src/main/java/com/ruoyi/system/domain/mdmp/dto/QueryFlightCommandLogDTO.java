package com.ruoyi.system.domain.mdmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "查询进程单指令消息入参")
public class QueryFlightCommandLogDTO {

    @ApiModelProperty(value = "日期", example = "2025-06-07", required = true)
    @NotBlank(message = "日期不能为空")
    private String date ;
}
