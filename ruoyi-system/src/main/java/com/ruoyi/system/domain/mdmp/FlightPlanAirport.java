package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;

/**
 * 航班计划机场对象 flight_plan_airport
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@ApiModel(value = "航班计划机场对象")
public class FlightPlanAirport extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划
     */
    @Excel(name = "航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划")
    @ApiModelProperty(value = "航班计划类型(1,长期计划;2, 次日计划;3,当日放行计划 4 单一飞行计划)", required = true, allowableValues = "1,2,3")
    private Integer planType;

    /**
     * 航班计划id
     */
    @Excel(name = "航班计划id")
    @ApiModelProperty(value = "航班计划id", example = "131", dataType = "Long")
    private Long flightPlanId;

    /**
     * 机场名称
     */
    @Excel(name = "机场名称")
    @ApiModelProperty(value = "机场名称", example = "重庆机场", dataType = "String")
    @NotBlank(message = "机场名称不能为空")
    private String airportName;

    /**
     * 机场三字码
     */
    @Excel(name = "机场三字码")
    @ApiModelProperty(value = "机场三字码", example = "CKG", dataType = "String")
    @NotBlank(message = "机场三字码不能为空")
    private String threeCode;

    /**
     * 四字码
     */
    @Excel(name = "四字码")
    @ApiModelProperty(value = "机场四字码", example = "CKGW", dataType = "String")
    private String tetradCode;

    /**
     * 经度
     */
    @Excel(name = "经度")
    @ApiModelProperty(value = "经度", example = "1111.213131", dataType = "String")
    @NotBlank(message = "经度不能为空")
    @Pattern(
            regexp = "^[+-]?(?:180(?:\\.0{0,8})?|(?:(?:1[0-7]\\d|\\d{1,2})(?:\\.\\d{1,8})?))°(?:(?:[0-5]\\d|\\d|0)(?:\\.\\d{1,8})?)'(?:(?:60(?:\\.0{0,8})?|(?:[0-5]\\d|\\d|0)(?:\\.\\d{1,8})?))$",
            message = "经纬度格式无效，正确示例：0°0'0\" 或 37.2°0.0'60.00000000"
    )
    private String longitude;

    /**
     * 纬度
     */
    @Excel(name = "纬度")
    @ApiModelProperty(value = "纬度", example = "1111.213131", dataType = "String")
    @NotBlank(message = "纬度不能为空")
    @Pattern(
            regexp = "^[+-]?(?:180(?:\\.0{0,8})?|(?:(?:1[0-7]\\d|\\d{1,2})(?:\\.\\d{1,8})?))°(?:(?:[0-5]\\d|\\d|0)(?:\\.\\d{1,8})?)'(?:(?:60(?:\\.0{0,8})?|(?:[0-5]\\d|\\d|0)(?:\\.\\d{1,8})?))$",
            message = "经纬度格式无效，正确示例：0°0'0\" 或 37.2°0.0'60.00000000"
    )
    private String latitude;

    /**
     * 类型;1 起飞、2 到达、3 临时点
     */
    @Excel(name = "类型;1 起飞、2 到达、3 临时点")
    @ApiModelProperty(value = "类型(1,起飞;2, 到达;3,临时点)", required = true, allowableValues = "0,1,2")
    @NotNull(message = "机场类型不能为空")
    @Range(min = 1, max = 3, message = "机场类型必须为 1、2、3")
    private Long airportType;

    /**
     * 排序;排序
     */
    @Excel(name = "排序;排序")
    @ApiModelProperty(value = "排序", example = "2", dataType = "Integer")
    private Integer sortNumber;

}
