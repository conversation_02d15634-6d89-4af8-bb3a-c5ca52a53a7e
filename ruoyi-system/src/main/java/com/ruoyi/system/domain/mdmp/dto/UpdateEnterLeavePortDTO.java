package com.ruoyi.system.domain.mdmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "修改进离港信息入参")
public class UpdateEnterLeavePortDTO {

    /** 航班ID */
    @ApiModelProperty(value = "航班ID")
    @NotNull(message = "航班ID不能为空")
    private Integer flightId ;
    /** ID */
    @ApiModelProperty(value = "进离场信息ID(新增时无，修改时传)")
    private Integer id ;
    /** 实际起飞时间 */
    @ApiModelProperty(value = "实际起飞时间")
    private String actualDepartTime ;
    /** 实际到达时间 */
    @ApiModelProperty(value = "实际到达时间")
    private String actualArriveTime ;
    /** 使用跑道 */
    @ApiModelProperty(value = "使用跑道", example = "05")
    private String runway ;
    /**跑道ID */
    @ApiModelProperty(value = "跑道ID", example = "1")
    private Integer runwayId ;
    /** 滑行指令 */
    @ApiModelProperty(value = "滑行指令", example = "G")
    private String taxiInstruction ;
    /** 进/离场方向;A-:进场方向，D-:离场方向 */
    @ApiModelProperty(value = "进/离场方向", example = "09")
    private String direction ;
    /** 推出时间(离场专有) */
    @ApiModelProperty(value = "推出时间(离场专有)", example = "0900")
    private String pushTime ;
    /** 开车时间(离场专有) */
    @ApiModelProperty(value = "开车时间(离场专有)", example = "0910")
    private String startUpTime ;
    /** 是否重要客人飞行;0:否, 1:是 */
    @ApiModelProperty(value = "是否重要客人飞行(0:否, 1:是)", example = "0", required = true)
    @NotNull(message = "是否重要客人飞行不能为空")
    private Integer vip ;
    /** 是否空中潜在冲突;0:否, 1:是 */
    @ApiModelProperty(value = "是否空中潜在冲突(0:否, 1:是)", example = "0", required = true)
    @NotNull(message = "是否空中潜在冲突不能为空")
    private Integer airConflicts ;
    /** 是否备降;0:否, 1:是 */
    @ApiModelProperty(value = "是否备降(0:否, 1:是)", example = "0", required = true)
    @NotNull(message = "是否备降不能为空")
    private Integer alternate ;
    /** 是否返航;0:否, 1:是 */
    @ApiModelProperty(value = "是否返航(0:否, 1:是)", example = "0", required = true)
    @NotNull(message = "是否返航不能为空")
    private Integer turnBack ;
    /** 是否收到ATIS信息;0:否, 1:是 */
    @ApiModelProperty(value = "是否收到ATIS信息(0:否, 1:是)", example = "0", required = true)
    @NotNull(message = "是否收到ATIS信息不能为空")
    private Integer receivedMessage ;
    /** 过境航班拍发EST报;0:否, 1:是 */
    @ApiModelProperty(value = "过境航班拍发EST报(0:否, 1:是)", example = "0", required = true)
    @NotNull(message = "过境航班拍发EST报不能为空")
    private Integer estReport ;
    /** 是否与空军协调完毕;0:否, 1:是 */
    @ApiModelProperty(value = "是否与空军协调完毕(0:否, 1:是)", example = "0", required = true)
    @NotNull(message = "是否与空军协调完毕不能为空")
    private Integer airForceCoordination ;
    /** 管制员名称 */
    @ApiModelProperty(value = "管制员名称", example = "张三")
    private String controllerName ;
    /** 飞行日期 */
    @ApiModelProperty(value = "飞行日期", example = "1119")
    private String flyDate ;
    /** 停机位 */
    @ApiModelProperty(value = "停机位", example = "101")
    private String parkingGate ;
    /** 进近值 */
    @ApiModelProperty(value = "进近值", example = "")
    private String approachCriteria ;

    @ApiModelProperty(value = "高度")
    @Valid
    private List<AddHeightDTO> heightList ;

    @ApiModelProperty(value = "指令")
    @Valid
    private List<AddCommandDTO> commandList ;

    @ApiModelProperty(value = "经过点")
    @Valid
    private List<AddTransitPointDTO> pointList ;

    @ApiModelProperty(value = "是否向无人机系统发送请求", example = "true", required = true)
    private Boolean sendFlag ;


}
