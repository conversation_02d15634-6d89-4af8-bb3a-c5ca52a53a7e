package com.ruoyi.system.domain.oc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "初始化公司管理员入参")
public class InitCompanyAdminDTO {

    /**
     * 用户名
     */
    @ApiModelProperty(value = "用户名", required = true)
    private String userName;

    /**
     * 密码
     */
    @ApiModelProperty(value = "密码", required = true)
    private String passWord;

    /**
     * 航司代码
     */
    @ApiModelProperty(value = "航司代码", required = true)
    private String companyCode;
}
