package com.ruoyi.system.domain.mdmp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
@Data
@ApiModel(description = "空域地图数据信息返回参数")
public class AirspaceMapDataListVO {


    @ApiModelProperty(value = "状态码", example = "1", dataType = "Integer")
    private Integer statusCode;

    @ApiModelProperty(value = "排序", example = "1", dataType = "Integer")
    private Integer index;

}
