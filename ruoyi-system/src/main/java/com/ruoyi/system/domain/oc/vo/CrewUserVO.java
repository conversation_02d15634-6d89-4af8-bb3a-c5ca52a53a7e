package com.ruoyi.system.domain.oc.vo;

import com.ruoyi.common.utils.ArithmeticUtils;
import com.ruoyi.system.domain.oc.WxUser;
import com.ruoyi.system.domain.oc.entity.Flightplan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "建立航班时查询机组人员返回")
public class CrewUserVO {

    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("用户名")
    private String userName;
    @ApiModelProperty("用户昵称")
    private String nickName;
    @ApiModelProperty("用户七天飞行时间")
    private Double weekFlyTime;
    @ApiModelProperty("用户月飞行时间")
    private Double monthFlyTime;

    public static CrewUserVO setting(WxUser user, List<Flightplan> monthFlightPlans, List<Flightplan> weekFlightPlans) {
        CrewUserVO crewUser = new CrewUserVO();
        crewUser.setUserId(user.getUserId());
        crewUser.setUserName(user.getUserName());
        crewUser.setNickName(user.getUserName());
        //用户月飞行时间
        if (CollectionUtils.isEmpty(monthFlightPlans)) {
            crewUser.setMonthFlyTime(0.0);
            crewUser.setWeekFlyTime(0.0);
            return crewUser;
        }
        int monthMinutes = 0;
        for (Flightplan flightPlan : monthFlightPlans) {
            Integer routeType = flightPlan.getRouteType();
            int slideTime = routeType == 0 ? flightPlan.getSlideTime() : flightPlan.getSlideTime() + flightPlan.getSecondSlideTime();
            monthMinutes += slideTime;
        }
        crewUser.setMonthFlyTime(ArithmeticUtils.minutesToHour(monthMinutes));
        //用户七天飞行时间
        if (CollectionUtils.isEmpty(weekFlightPlans)) {
            crewUser.setWeekFlyTime(0.0);
            return crewUser;
        }
        int weekMinutes = 0;
        for (Flightplan flightPlan : weekFlightPlans) {
            Integer routeType = flightPlan.getRouteType();
            int slideTime = routeType == 0 ? flightPlan.getSlideTime() : flightPlan.getSlideTime() + flightPlan.getSecondSlideTime();
            weekMinutes += slideTime;
        }
        crewUser.setWeekFlyTime(ArithmeticUtils.minutesToHour(weekMinutes));
        return crewUser;
    }
}
