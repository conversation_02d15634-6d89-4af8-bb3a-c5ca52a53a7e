package com.ruoyi.system.domain.oc.entity;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 运行标准 oc_dperation_standard
 *
 * <AUTHOR>
 * @date 2022/4/24 9:55
 * @mood 功能
 */
@ApiModel(value = "DperationStandard", description = "运行标准实体")
@Data
public class DperationStandard extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("id")
    private Long id;

    @ApiModelProperty("名称")
    @Excel(name = "名称")
    private String name;
}
