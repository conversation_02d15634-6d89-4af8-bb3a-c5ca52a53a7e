package com.ruoyi.system.domain.mdmp;

import com.ruoyi.common.core.domain.AjaxResult;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.ValidationException;
import javax.validation.constraints.*;
import java.math.BigDecimal;

/**
 * 【请填写功能名称】对象 aircraft_mp
 *
 * <AUTHOR>
 * @date 2025-03-04
 */
@Data
public class AircraftMp extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 1风，2降雨量，3能见度，4温度，5雷暴
     */
    @Excel(name = "1风，2降雨量，3能见度，4温度，5雷暴")
    private Integer alarmType;

    /**
     * 机型ID
     */
    @Excel(name = "机型ID")
    private Long aircraftId;

    /**
     * 1一级，2二级，3三级
     */
    @Excel(name = "1一级，2二级，3三级")
    private Long alarmLevel;

    /**
     * 最小值
     */
    @Excel(name = "最小值")
    @Pattern(regexp = "^(?:[-+]?\\d{1,8}(\\.\\d{1,2})?)?$", message = "必须为有效数字（可含正负号），最小值最多8位整数和2位小数")
    private String mpMin;

    /**
     * 最大值
     */
    @Excel(name = "最大值")
    @Pattern(regexp = "^(?:[-+]?\\d{1,8}(\\.\\d{1,2})?)?$", message = "必须为有效数字（可含正负号），最大值最多8位整数和2位小数")
    private String mpMax;

}
