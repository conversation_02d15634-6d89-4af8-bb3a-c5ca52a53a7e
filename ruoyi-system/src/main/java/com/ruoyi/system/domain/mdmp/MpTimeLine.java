package com.ruoyi.system.domain.mdmp;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 mp_time_line
 *
 * <AUTHOR>
 * @date 2024-12-04
 */
@Data
public class MpTimeLine extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     *
     */
    private Long id;

    /**
     * 高度
     */
    @Excel(name = "高度")
    private Double altitude;

    /**
     * 类型1U高空分量,2V高空分量,3降雨量
     */
    @Excel(name = "类型1U高空分量,2V高空分量,3降雨量")
    private Integer dateType;

    /**
     * 数据源1欧洲中心预报，2美国预报
     */
    @Excel(name = "数据源1欧洲中心预报，2美国预报")
    private Integer dataSource;

    /**
     * 添加时间
     */
    private Date addTime;

    public MpTimeLine(Double altitude, Integer dateType, Integer dataSource, Date addTime) {
        this.altitude = altitude;
        this.dateType = dateType;
        this.dataSource = dataSource;
        this.addTime = addTime;
    }


}
