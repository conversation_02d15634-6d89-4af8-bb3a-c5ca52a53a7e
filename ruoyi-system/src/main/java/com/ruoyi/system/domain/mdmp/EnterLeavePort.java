package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "进离场信息表")
public class EnterLeavePort {
    /** ID */
    @ApiModelProperty(value = "ID")
    private Integer id ;
    /** 航班ID */
    @ApiModelProperty(value = "航班ID")
    private Integer flightId ;
    /** 使用跑道 */
    @ApiModelProperty(value = "使用跑道")
    private String runway ;
    /** 跑道ID */
    @ApiModelProperty(value = "跑道ID")
    private Integer runwayId ;
    /** 滑行指令 */
    @ApiModelProperty(value = "滑行指令")
    private String taxiInstruction ;
    /** 进/离场方向;A-:进场方向，D-:离场方向 */
    @ApiModelProperty(value = "进/离场方向")
    private String direction ;
    /** 推出时间(离场专有) */
    @ApiModelProperty(value = "推出时间(离场专有)")
    private String pushTime ;
    /** 开车时间(离场专有) */
    @ApiModelProperty(value = "开车时间(离场专有)")
    private String startUpTime ;
    /** 是否重要客人飞行;0:否, 1:是 */
    @ApiModelProperty(value = "是否重要客人飞行(0:否, 1:是)")
    private Integer vip ;
    /** 是否空中潜在冲突;0:否, 1:是 */
    @ApiModelProperty(value = "是否空中潜在冲突(0:否, 1:是)")
    private Integer airConflicts ;
    /** 是否备降;0:否, 1:是 */
    @ApiModelProperty(value = "是否备降(0:否, 1:是)")
    private Integer alternate ;
    /** 是否返航;0:否, 1:是 */
    @ApiModelProperty(value = "是否返航(0:否, 1:是)")
    private Integer turnBack ;
    /** 是否收到ATIS信息;0:否, 1:是 */
    @ApiModelProperty(value = "是否收到ATIS信息(0:否, 1:是)")
    private Integer receivedMessage ;
    /** 过境航班拍发EST报;0:否, 1:是 */
    @ApiModelProperty(value = "过境航班拍发EST报(0:否, 1:是)")
    private Integer estReport ;
    /** 是否与空军协调完毕;0:否, 1:是 */
    @ApiModelProperty(value = "是否与空军协调完毕(0:否, 1:是)")
    private Integer airForceCoordination ;
    /** 管制员名称 */
    @ApiModelProperty(value = "管制员名称")
    private String controllerName ;
    /** 飞行日期 */
    @ApiModelProperty(value = "飞行日期")
    private String flyDate ;
    /** 停机位 */
    @ApiModelProperty(value = "停机位")
    private String parkingGate ;
    /** 进近值 */
    @ApiModelProperty(value = "进近值")
    private String approachCriteria ;

    @ApiModelProperty(value = "高度")
    private List<AddHeight> heightList ;

    @ApiModelProperty(value = "指令")
    private List<AddCommand> commandList ;

    @ApiModelProperty(value = "经过点")
    private List<AddTransitPoint> pointList ;


    public static EnterLeavePort setting(Integer flightId) {
        EnterLeavePort enterLeavePort = new EnterLeavePort();
        enterLeavePort.setFlightId(flightId);
        enterLeavePort.setVip(0);
        enterLeavePort.setAirConflicts(0);
        enterLeavePort.setAlternate(0);
        enterLeavePort.setTurnBack(0);
        enterLeavePort.setReceivedMessage(0);
        enterLeavePort.setEstReport(0);
        enterLeavePort.setAirForceCoordination(0);
        return enterLeavePort;
    }
}
