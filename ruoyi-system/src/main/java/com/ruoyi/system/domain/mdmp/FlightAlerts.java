package com.ruoyi.system.domain.mdmp;

import java.util.Date;
import java.util.UUID;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 【请填写功能名称】对象 flight_alerts
 *
 * <AUTHOR>
 * @date 2024-10-21
 */

@Data
public class FlightAlerts extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 机尾号
     */
    @Excel(name = "机尾号")
    private String aircraftReg;

    /**
     * 告警名称
     */
    @Excel(name = "告警名称")
    private String alarmName;

    /**
     * 0已解除，1告警
     */
    @Excel(name = "0已解除，1告警,2连线")
    private Long alertStatus;

    /**
     * 时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date alertTime;

    /**
     * 0近地接近告警,1障碍物接近告警,2飞行计划偏离告警,3管制区违规侵入告警
     */
    @Excel(name = "0近地接近告警,1障碍物接近告警,2飞行计划偏离告警,3管制区违规侵入告警")
    private Long alertType;

    /**
     * 版本号
     */
    @Excel(name = "版本号")
    private Long version;

    /**
     * 地图数据排序号
     */
    @Excel(name = "地图数据排序号")
    private Integer index;

    private Long flightDataId;

    @ApiModelProperty(value = "状态码", example = "1", dataType = "Integer")
    private Integer statusCode;

    private Integer color;

    private String targetIdentification;

    private Integer isSent;

    private Integer subIndex;

    private Long uniqueId;

    private String relatedAircraftReg;


    private String x;

    private String uuid = UUID.randomUUID().toString();
}
