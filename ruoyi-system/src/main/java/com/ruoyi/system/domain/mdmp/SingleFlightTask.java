package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 单一飞行任务对象 single_flight_task
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@Data
@ApiModel(value = "单一飞行任务对象")
public class SingleFlightTask extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * $column.columnComment
     */
    private Long id;

    /**
     * 计划名称
     */
    @Excel(name = "计划名称")
    @ApiModelProperty(value = "计划名称")
    @NotBlank(message = "计划名称不能为空")
    private String planName;

    /**
     * 计划日期
     */
    @Excel(name = "计划日期")
    @ApiModelProperty(value = "计划日期")
    @NotBlank(message = "计划日期不能为空")
    private String planDate;

    /**
     * 计划起飞时间
     */
    @Excel(name = "计划起飞时间")
    @ApiModelProperty(value = "计划起飞时间")
    @NotBlank(message = "计划起飞时间不能为空")
    private String planDepTime;

    /**
     * 计划到达时间
     */
    @Excel(name = "计划到达时间")
    @ApiModelProperty(value = "计划到达时间")
    @NotBlank(message = "计划到达时间不能为空")
    private String planArrTime;

    /**
     * 航司代码
     */
    @Excel(name = "航司代码")
    @ApiModelProperty(value = "航司代码")
    @NotBlank(message = "航司代码不能为空")
    private String companyCode;

    /**
     * 航司名称
     */
    @Excel(name = "航司名称")
    @ApiModelProperty(value = "航司名称")
    @NotBlank(message = "航司名称不能为空")
    private String companyName;

    /**
     * 联系人姓名
     */
    @Excel(name = "联系人姓名")
    @ApiModelProperty(value = "联系人姓名")
    @NotBlank(message = "联系人姓名不能为空")
    private String contactName;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话")
    @ApiModelProperty(value = "联系人电话")
    @NotBlank(message = "联系人电话不能为空")
    private String contactPhone;

    /**
     * 计划状态
     */
    @Excel(name = "计划状态")
    @ApiModelProperty(value = "状态( 0,草稿;1, 已提交;2, 已通过;3, 审核中;4, 拒绝)", required = true, allowableValues = "0,1,2,3,4")
//    @NotNull(message = "计划状态不能为空")
    private Long planStatus;

    /**
     * 计划类型
     */
    @Excel(name = "计划类型")
    @ApiModelProperty(value = "计划类型")
//    @NotNull(message = "计划类型不能为空")
    private Integer planType;


    @Excel(name = "机型机号关联")
    @ApiModelProperty(required = true, value = "机型机号关联")
    @NotBlank(message = "机型不能为空")
    private List<FlightPlanAircraftModel> planModelAircraftList;


    @Excel(name = "航线关联表")
    @ApiModelProperty(value = "航线关联表")
    @NotBlank(message = "航线不能为空")
    private List<FlightPlanRoute> flightPlanRouteList;


}
