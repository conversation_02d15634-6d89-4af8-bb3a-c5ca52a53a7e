package com.ruoyi.system.domain.mdmp;

import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Pattern;
import java.math.BigDecimal;
import java.util.List;

/**
 * 航班计划航线对象 FlightPlanRouteLongLat
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@ApiModel(value = "FlightPlanRouteLongLat", description = "航班计划航线对象")
@Data
public class FlightPlanRouteLongLat {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 航班计划航线id
     */
    @ApiModelProperty("flightPlanRouteId")
    private Long flightPlanRouteId;

    /**
     * 经度
     */
    @ApiModelProperty("经度111°12′34")
    @Excel(name = "经度")
    @NotBlank(message = "经度不能为空")
    @Pattern(
            regexp = "^[+-]?(?:180(?:\\.0{0,8})?|(?:(?:1[0-7]\\d|\\d{1,2})(?:\\.\\d{1,8})?))°(?:(?:[0-5]\\d|\\d|0)(?:\\.\\d{1,8})?)'(?:(?:60(?:\\.0{0,8})?|(?:[0-5]\\d|\\d|0)(?:\\.\\d{1,8})?))$",
            message = "经纬度格式无效，正确示例：0°0'0\" 或 37.2°0.0'60.00000000"
    )
    private String longitude;

    /**
     * 纬度
     */
    @ApiModelProperty("纬度111°12′34")
    @Excel(name = "纬度")
    @NotBlank(message = "纬度不能为空")
    @Pattern(
            regexp = "^[+-]?(?:180(?:\\.0{0,8})?|(?:(?:1[0-7]\\d|\\d{1,2})(?:\\.\\d{1,8})?))°(?:(?:[0-5]\\d|\\d|0)(?:\\.\\d{1,8})?)'(?:(?:60(?:\\.0{0,8})?|(?:[0-5]\\d|\\d|0)(?:\\.\\d{1,8})?))$",
            message = "经纬度格式无效，正确示例：0°0'0\" 或 37.2°0.0'60.00000000"
    )
    private String latitude;


    @Excel(name = "经纬度类型", readConverterExp = "0=度分秒,1=数字")
    @ApiModelProperty("经纬度类型 0：度分秒 1：数字")
    @NotNull(message = "经纬度类型不能为空")
    @Range(min = 0, max = 1, message = "经纬度类型为 0、1")
    private Integer pointType;

    /**
     * 经度
     */
    @ApiModelProperty("转换后的经度123.456789")
    @Excel(name = "转换后的经度")
    private BigDecimal doubleLongitude;

    /**
     * 纬度
     */
    @ApiModelProperty("转换后的纬度123.456789")
    @Excel(name = "转换后的纬度")
    private BigDecimal doubleLatitude;

    /**
     * 高度
     */
    @ApiModelProperty("高度")
    @Excel(name = "高度")
    @NotNull(message = "高度不能为空")
    private BigDecimal height;

    /**
     * 排序号
     */
    @ApiModelProperty("排序号")
    @Excel(name = "排序号")
    private Integer sortNumber;

    /**
     * 点名称
     */
    @ApiModelProperty("点名称")
    @Excel(name = "点名称")
    @NotBlank(message = "点名称不能为空")
    private String coordinateName;
}
