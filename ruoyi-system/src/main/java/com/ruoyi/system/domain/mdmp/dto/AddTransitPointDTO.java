package com.ruoyi.system.domain.mdmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "经过点入参（进离场信息表中添加）")
public class AddTransitPointDTO {
    @ApiModelProperty(value = "ID(修改必传, 新增不传)", example = "1")
    private Integer id;
    /** 位置点名称 */
    @ApiModelProperty(value = "位置点名称", example = "P221", required = true)
    @NotBlank(message = "位置点名称不能为空")
    private String positionName ;
    /** 预计到达时间 */
    @ApiModelProperty(value = "预计到达时间", example = "0915", required = true)
//    @NotBlank(message = "预计到达时间不能为空")
    private String predictArriveTime ;
    /** 实际到达时间 */
    @ApiModelProperty(value = "实际到达时间", example = "0920")
    private String actualArriveTime ;
}
