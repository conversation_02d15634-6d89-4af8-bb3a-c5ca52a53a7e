package com.ruoyi.system.domain.mdmp;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 作业区内基础信息对象 work_in_basis
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@Data
@ApiModel(value = "WorkInBasis", description = "作业区内实体")
public class WorkInBasis {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 作业区基础信息表Id
     */
    @ApiModelProperty("作业区基础信息表Id")
    @Excel(name = "作业区基础信息表Id")
    private Long workBasisId;

    /**
     * 区域类型;0等待隔离区
     */
    @ApiModelProperty("区域类型:0等待隔离区")
    @Excel(name = "区域类型;0等待隔离区")
    private Integer regionType;

    /**
     * 类型;0圆  1点连线
     */
    @ApiModelProperty("类型;0圆  1点连线")
    @Excel(name = "类型;0圆  1点连线")
    private Integer graphType;

    /**
     * 半径;单位米
     */
    @ApiModelProperty("半径;单位米")
    @Excel(name = "半径;单位米")
    private BigDecimal radius;

    /**
     * 圆心;经度
     */
    @ApiModelProperty("圆心;经度")
    @Excel(name = "圆心;经度")
    private String circleCenterLong;

    /**
     * 圆心;纬度
     */
    @ApiModelProperty("圆心;纬度 ")
    @Excel(name = "圆心;纬度")
    private String circleCenterLat;

    /**
     * 最低高度;单位米
     */
    @ApiModelProperty("半径;单位米")
    @Excel(name = "最低高度;单位米")
    @NotNull(message = "最低高度不能为空")
    private BigDecimal minHeight;

    /**
     * 最高高度;单位米
     */
    @ApiModelProperty("半径;单位米")
    @Excel(name = "最高高度;单位米")
    @NotNull(message = "最高高度不能为空")
    private BigDecimal maxHeight;

    /**
     * 作业区名称
     */
    @ApiModelProperty("作业区名称")
    @Excel(name = "作业区名称")
    @NotBlank(message="作业区名称不能为空")
    private String workInName;


    @ApiModelProperty("作业区内经纬度集合")
    private List<WorkInBasisLongLat> workInBasisLongLatList;


    @ApiModelProperty("作业区内地图数据集合")
    private List<WorkInMapData> workInMapDataList;
}
