package com.ruoyi.system.domain.mdmp.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "修改航班进场或离场顺序入参")
public class UpdateProgressDTO {

    /** 航班ID */
    @ApiModelProperty(value = "航班ID", required = true)
    @NotNull(message = "航班ID不能为空")
    private Integer id ;

    /** 进出港标识;0:进港, 1:离港 */
    @ApiModelProperty(value = "进出港标识(0:进港, 1:离港)", example = "0", required = true)
    @NotNull(message = "进出港标识不能为空")
    private Integer ioSign ;

    /** 当前航班进或离场的顺序 */
    @ApiModelProperty(value = "当前航班进或离场的顺序(等待区/进离场区/跑道/地面/完成)", example = "等待区", required = true)
    @NotBlank(message = "当前航班进离顺序不能为空")
    private String progress ;

    /** 跑道ID */
    @ApiModelProperty(value = "跑道ID(飞机进入或离开跑道时传)", example = "09")
    private Integer runwayId ;

    @ApiModelProperty(value = "是否向无人机系统发送请求", example = "true", required = true)
    private Boolean sendFlag ;
}
