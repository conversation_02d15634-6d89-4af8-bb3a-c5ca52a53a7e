package com.ruoyi.system.domain.oc.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.domain.oc.WxQualification;
import com.ruoyi.system.domain.oc.WxUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.Objects;

/**
 * 微信用户和用户资质
 *
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "微信用户和用户资质返回")
public class CrewVO {

    /**
     * 用户ID
     */
    @ApiModelProperty("用户ID")
    private Long userId;

    /**
     * 用户名称
     */
    @ApiModelProperty("用户名称")
    private String userName;

    @ApiModelProperty("执照有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityOfLicense;

    @ApiModelProperty("体检合格证有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityPeriodOfPhysicalExamination;

    @ApiModelProperty("熟练检查基准月")
    private String proficiencyCheckStandardMonth;

    @ApiModelProperty("熟练检查到期日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date proficiencyCheckValidity;

    @ApiModelProperty("机长航线练检查到期日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityPeriodOfRouteTrainingInspection;

    @ApiModelProperty("危险品训练有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dangerTrainingInspection;

    @ApiModelProperty("汉语言有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date chineseLanguageValidity;

    @ApiModelProperty("空勤登记证有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityOfRegistrationCertificate;

    @ApiModelProperty("Y12放行授权有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date y12Inspection;

    @ApiModelProperty("C208放行授权有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date c208Inspection;

    @ApiModelProperty("B300放行授权有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date b300Inspection;

    @ApiModelProperty("机型")
    private String aircraftStyle;

    @ApiModelProperty("角色权限(查看飞行员资质时返回) 机长：pilot, 机务：maintenance")
    private String roleKey;

    @ApiModelProperty("角色类型(查看航班机组信息时返回) 1.机长 2.副驾 4.机务人员")
    private Integer roleType;

    public static CrewVO setting(WxUser wxUser, WxQualification wxQualification) {
        CrewVO wxUserVO = new CrewVO();
        wxUserVO.setUserId(wxUser.getUserId());
        wxUserVO.setUserName(wxUser.getUserName());
        if (Objects.nonNull(wxQualification)) {
            wxUserVO.setValidityOfLicense(wxQualification.getValidityOfLicense());
            wxUserVO.setValidityPeriodOfPhysicalExamination(wxQualification.getValidityPeriodOfPhysicalExamination());
            wxUserVO.setProficiencyCheckStandardMonth(wxQualification.getProficiencyCheckStandardMonth());
            wxUserVO.setProficiencyCheckValidity(wxQualification.getProficiencyCheckValidity());
            wxUserVO.setValidityPeriodOfRouteTrainingInspection(wxQualification.getValidityPeriodOfRouteTrainingInspection());
            wxUserVO.setDangerTrainingInspection(wxQualification.getDangerTrainingInspection());
            wxUserVO.setChineseLanguageValidity(wxQualification.getChineseLanguageValidity());
            wxUserVO.setValidityOfRegistrationCertificate(wxQualification.getValidityOfRegistrationCertificate());
            wxUserVO.setY12Inspection(wxQualification.getY12Inspection());
            wxUserVO.setC208Inspection(wxQualification.getC208Inspection());
            wxUserVO.setB300Inspection(wxQualification.getB300Inspection());
            wxUserVO.setAircraftStyle(wxQualification.getAircraftStyle());
        }
        return wxUserVO;
    }
}
