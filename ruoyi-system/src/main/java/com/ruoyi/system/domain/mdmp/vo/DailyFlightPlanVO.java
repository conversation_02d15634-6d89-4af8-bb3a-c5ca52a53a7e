package com.ruoyi.system.domain.mdmp.vo;

import com.ruoyi.system.domain.mdmp.DailyFlightPlan;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;


/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "当日航班计划返回参数")
public class DailyFlightPlanVO {

    /**
     * 航班计划id
     */
    @ApiModelProperty(value = "航班计划id", example = "14")
    private Long flightPlanId;

    /**
     * 计划名称
     */
    @ApiModelProperty(value = "计划名称",example = "0529当日计划")
    private String name;

    /**
     * 飞机计划对应的飞机机尾号
     */
    @ApiModelProperty(value = "飞机计划对应的飞机机尾号",example = "B3118")
    private List<String> tailNumberList;

    public static DailyFlightPlanVO setting(DailyFlightPlan flightPlan, List<String> tailNumberList) {
        DailyFlightPlanVO vo = new DailyFlightPlanVO();
        vo.setFlightPlanId(flightPlan.getId());
        vo.setName(flightPlan.getName());
        vo.setTailNumberList(tailNumberList);
        return vo;
    }
}
