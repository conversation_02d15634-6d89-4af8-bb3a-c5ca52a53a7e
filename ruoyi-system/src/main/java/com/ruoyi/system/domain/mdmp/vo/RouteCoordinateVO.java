package com.ruoyi.system.domain.mdmp.vo;

import com.ruoyi.system.domain.mdmp.FlightPlanRoute;
import com.ruoyi.system.domain.mdmp.FlightPlanWork;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "监控航线集合返回参数")
public class RouteCoordinateVO {

    /**
     * 航线集合
     */
    @ApiModelProperty(value = "航线")
    private List<FlightPlanRoute> flightPlanRouteList;

    /**
     * 作业区
     */
    @ApiModelProperty(value = "作业区")
    private List<FlightPlanWork> flightPlanWorkList;


    @ApiModelProperty(value = "告警格子")
    List<MapDataVo> mapDataVos;


}
