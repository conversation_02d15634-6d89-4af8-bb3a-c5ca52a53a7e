package com.ruoyi.system.domain.oc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "新增机场基地列表入参")
public class AddAirportBaseDTO {

    @ApiModelProperty(value = "机场名称", required = true)
    @NotBlank(message = "机场名称不能为空")
    private String name;

    @ApiModelProperty(value = "三字码", required = true)
    @NotBlank(message = "机场三字码不能为空")
    private String threeAirportCode;

    @ApiModelProperty("四字码")
    private String fourAirportCode;
}
