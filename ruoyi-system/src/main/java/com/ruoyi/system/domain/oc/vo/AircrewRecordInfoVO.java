package com.ruoyi.system.domain.oc.vo;

import com.alibaba.fastjson2.annotation.JSONField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.domain.oc.entity.AircrewRecord;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行员记录返回信息")
public class AircrewRecordInfoVO {

    /**
     * 记录编号
     */
    @ApiModelProperty("记录编号")
    private Long aircrewRecordId;

    /**
     * 文件第一类型
     * 1:技术文档，2:满足条款要求记录，3:措施记录
     */
    @ApiModelProperty("文件第一类型")
    private Integer firstFileType;

    /**
     * 文件第二类型
     */
    @JSONField(serialize = false)
    private Integer secondFileType;

    /**
     * 文件名
     */
    @ApiModelProperty("文件名")
    private String fileName;

    /**
     * 上传人
     */
    @ApiModelProperty("上传人")
    private String createBy;

    /**
     * 上传时间
     */
    @ApiModelProperty("上传时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 文件简介
     */
    @ApiModelProperty("文件简介")
    private String remark;


    public static List<AircrewRecordInfoVO> convert(List<AircrewRecord> aircrewRecords) {
        List<AircrewRecordInfoVO> infoVos = new ArrayList<>();
        for (AircrewRecord aircrewRecord : aircrewRecords) {
            AircrewRecordInfoVO infoVo = new AircrewRecordInfoVO();
            BeanUtils.copyProperties(aircrewRecord, infoVo);
            infoVos.add(infoVo);
        }
        return infoVos;
    }
}
