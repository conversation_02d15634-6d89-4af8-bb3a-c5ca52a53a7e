package com.ruoyi.system.domain.type;

/**
 * <AUTHOR>
 * @date ：Created in 2024/12/2 15:37
 * @description：
 * @modified By：
 * @version: $
 */
public class MeteType {

    public static final String U_WIND_ISOBARIC = "U_WIND_ISOBARIC";

    public static final String V_WIND_ISOBARIC = "V_WIND_ISOBARIC";

    //雨
    public static final String PRECIPITATION = "PRECIPITATION";

    //能见度
    public static final String VIS = "VIS";

    //高空温度
    public static final String TEMP_ISOBARIC = "TEMP_ISOBARIC";

    public static final String RADAR = "RADAR";

    public static final String DUST = "DUST";

    public static String getWeatherConstant(int constant) {
        switch (constant) {
            //风
            case 1:
                return U_WIND_ISOBARIC;
            case 2:
                return V_WIND_ISOBARIC;
            case 3:
                return PRECIPITATION;
            case 4:
                return VIS;
            case 5:
                return TEMP_ISOBARIC;
            case 6:
                return RADAR;
            case 7:
                return DUST;
            default:
                return "Invalid input"; // Optional: handle invalid inputs
        }
    }

    public static Integer getWeatherConstant(String constant) {
        switch (constant) {
            case U_WIND_ISOBARIC:
                return 1;
            case V_WIND_ISOBARIC:
                return 2;
            case PRECIPITATION:
                return 3;
            case VIS:
                return 4;
            case TEMP_ISOBARIC:
                return 5;
            case RADAR:
                return 6;
            default:
                return 0; // Optional: handle invalid inputs
        }
    }
}
