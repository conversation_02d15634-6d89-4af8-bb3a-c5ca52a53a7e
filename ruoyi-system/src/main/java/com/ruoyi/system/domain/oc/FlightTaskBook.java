package com.ruoyi.system.domain.oc;

import lombok.Data;

import java.util.Date;

@Data
public class FlightTaskBook {
    private Long id;

    private String aircraftType;

    private String registrationNumber;

    private String callSign;

    private String transponderCode;

    private String flightDate;

    private String departure;

    private String plannedTime;

    private String firstLandingPoint;

    private String estimatedTime;

    private String fuelEndurance;

    private String circuitSpeed;

    private String captain;

    private String phone;

    private String address;

    private String issueDate;

    private String taskBookNumber;

    private Date createdAt;

    private Date updatedAt;

    private Integer taskStatus;

    private String companyCode;

    private String versionNumber;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAircraftType() {
        return aircraftType;
    }

    public void setAircraftType(String aircraftType) {
        this.aircraftType = aircraftType == null ? null : aircraftType.trim();
    }

    public String getRegistrationNumber() {
        return registrationNumber;
    }

    public void setRegistrationNumber(String registrationNumber) {
        this.registrationNumber = registrationNumber == null ? null : registrationNumber.trim();
    }

    public String getCallSign() {
        return callSign;
    }

    public void setCallSign(String callSign) {
        this.callSign = callSign == null ? null : callSign.trim();
    }

    public String getTransponderCode() {
        return transponderCode;
    }

    public void setTransponderCode(String transponderCode) {
        this.transponderCode = transponderCode == null ? null : transponderCode.trim();
    }

    public String getFlightDate() {
        return flightDate;
    }

    public void setFlightDate(String flightDate) {
        this.flightDate = flightDate == null ? null : flightDate.trim();
    }

    public String getDeparture() {
        return departure;
    }

    public void setDeparture(String departure) {
        this.departure = departure == null ? null : departure.trim();
    }

    public String getPlannedTime() {
        return plannedTime;
    }

    public void setPlannedTime(String plannedTime) {
        this.plannedTime = plannedTime == null ? null : plannedTime.trim();
    }

    public String getFirstLandingPoint() {
        return firstLandingPoint;
    }

    public void setFirstLandingPoint(String firstLandingPoint) {
        this.firstLandingPoint = firstLandingPoint == null ? null : firstLandingPoint.trim();
    }

    public String getEstimatedTime() {
        return estimatedTime;
    }

    public void setEstimatedTime(String estimatedTime) {
        this.estimatedTime = estimatedTime == null ? null : estimatedTime.trim();
    }

    public String getFuelEndurance() {
        return fuelEndurance;
    }

    public void setFuelEndurance(String fuelEndurance) {
        this.fuelEndurance = fuelEndurance == null ? null : fuelEndurance.trim();
    }

    public String getCircuitSpeed() {
        return circuitSpeed;
    }

    public void setCircuitSpeed(String circuitSpeed) {
        this.circuitSpeed = circuitSpeed == null ? null : circuitSpeed.trim();
    }

    public String getCaptain() {
        return captain;
    }

    public void setCaptain(String captain) {
        this.captain = captain == null ? null : captain.trim();
    }

    public String getPhone() {
        return phone;
    }

    public void setPhone(String phone) {
        this.phone = phone == null ? null : phone.trim();
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address == null ? null : address.trim();
    }

    public String getIssueDate() {
        return issueDate;
    }

    public void setIssueDate(String issueDate) {
        this.issueDate = issueDate == null ? null : issueDate.trim();
    }

    public String getTaskBookNumber() {
        return taskBookNumber;
    }

    public void setTaskBookNumber(String taskBookNumber) {
        this.taskBookNumber = taskBookNumber == null ? null : taskBookNumber.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public Integer getTaskStatus() {
        return taskStatus;
    }

    public void setTaskStatus(Integer taskStatus) {
        this.taskStatus = taskStatus;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }

    public String getVersionNumber() {
        return versionNumber;
    }

    public void setVersionNumber(String versionNumber) {
        this.versionNumber = versionNumber == null ? null : versionNumber.trim();
    }
}