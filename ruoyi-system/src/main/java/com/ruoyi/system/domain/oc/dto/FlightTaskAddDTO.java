package com.ruoyi.system.domain.oc.dto;

import com.ruoyi.system.domain.oc.FlightTaskBook;
import com.ruoyi.system.domain.oc.FlightTaskInfo;
import lombok.Data;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 飞行任务书添加
 * @date 2025/7/18 14:45:48
 */
@Data
public class FlightTaskAddDTO extends FlightTaskBook {
    private List<FlightTaskInfo> batches;

    public static void updateFlightTaskBook(FlightTaskAddDTO dto, FlightTaskBook dbTaskBook) {
        dbTaskBook.setAircraftType(dto.getAircraftType());
        dbTaskBook.setRegistrationNumber(dto.getRegistrationNumber());
        dbTaskBook.setCallSign(dto.getCallSign());
        dbTaskBook.setTransponderCode(dto.getTransponderCode());
        dbTaskBook.setFlightDate(dto.getFlightDate());
        dbTaskBook.setDeparture(dto.getDeparture());
        dbTaskBook.setPlannedTime(dto.getPlannedTime());
        dbTaskBook.setFirstLandingPoint(dto.getFirstLandingPoint());
        dbTaskBook.setEstimatedTime(dto.getEstimatedTime());
        dbTaskBook.setFuelEndurance(dto.getFuelEndurance());
        dbTaskBook.setCircuitSpeed(dto.getCircuitSpeed());
        dbTaskBook.setCaptain(dto.getCaptain());
        dbTaskBook.setPhone(dto.getPhone());
        dbTaskBook.setAddress(dto.getAddress());
        dbTaskBook.setIssueDate(dto.getIssueDate());
        dbTaskBook.setTaskStatus(0);
        dbTaskBook.setVersionNumber(dto.getVersionNumber());
    }

    /**
     * 替换占位符参数
     *
     * @param dto
     * @return
     */
    public static Map<String, String> getExportWordTaskBookDataParam(FlightTaskAddDTO dto) {
        Map<String, String> params = new HashMap<>();
        params.put("version", dto.getVersionNumber());
        params.put("aircraftType", dto.getAircraftType());
        params.put("registrationNumber", dto.getRegistrationNumber());
        params.put("callSign", dto.getCallSign());
        params.put("transponderCode", dto.getTransponderCode());
        params.put("flightDate", dto.getFlightDate());
        params.put("departure", dto.getDeparture());
        params.put("plannedTime", dto.getPlannedTime());
        params.put("firstLandingPoint", dto.getFirstLandingPoint());
        params.put("estimatedTime", dto.getEstimatedTime());
        params.put("fuelEndurance", dto.getFuelEndurance());
        params.put("circuitSpeed", dto.getCircuitSpeed());
        params.put("captain", dto.getCaptain());
        params.put("phone", dto.getPhone());
        params.put("address", dto.getAddress());
        params.put("issueDate", dto.getIssueDate());
        return params;
    }


    public static String[][] getExportWordTaskInfoDataParam(FlightTaskAddDTO dto) {
        String[][] datas = new String[dto.getBatches().size()][];
        for (int i = 0; i < dto.getBatches().size(); i++) {

            FlightTaskInfo taskInfo = dto.getBatches().get(i);
            datas[i] = new String[]{
                    taskInfo.getBatch(),
                    taskInfo.getTaskType(),
                    taskInfo.getRouteOrAirspace(),
                    taskInfo.getRouteOrAirspaceCode(),
                    taskInfo.getAltitude(),
                    taskInfo.getAlternateAirport(),
                    taskInfo.getFlightRules(),
                    taskInfo.getCaptainWeatherStandard(),
                    taskInfo.getCrewRole1(),
                    taskInfo.getCrewName1(),
                    taskInfo.getCrewRole2(),
                    taskInfo.getCrewName2(),
                    taskInfo.getCrewPassengerCount()
            };
        }
        return datas;
    }
}
