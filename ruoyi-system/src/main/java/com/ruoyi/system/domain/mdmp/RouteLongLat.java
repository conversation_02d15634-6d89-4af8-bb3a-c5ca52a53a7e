package com.ruoyi.system.domain.mdmp;

import java.math.BigDecimal;

import com.ruoyi.system.domain.type.PointType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import com.ruoyi.common.annotation.Excel;


/**
 * 航线经纬度对象 route_long_lat
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@ApiModel(value = "RouteLongLat", description = "航线经纬度实体")
@Data
public class RouteLongLat
{
    private static final long serialVersionUID = 1L;

    /** 自增id */
    @ApiModelProperty("id")
    private Long id;

    /** 航线id */
    @ApiModelProperty("航线id")
    @Excel(name = "航线id")
    private Long routeId;

    /** 经度 */
    @ApiModelProperty("经度111°12′34")
    @Excel(name = "经度")
    private String longitude;

    /** 纬度 */
    @ApiModelProperty("纬度111°12′34")
    @Excel(name = "纬度")
    private String latitude;

    /** 经度 */
    @ApiModelProperty("转换后的经度123.456789")
    @Excel(name = "转换后的经度")
    private Double doubleLongitude;

    /** 纬度 */
    @ApiModelProperty("转换后的纬度123.456789")
    @Excel(name = "转换后的纬度")
    private Double doubleLatitude;

    /** 高度 */
    @ApiModelProperty("高度")
    @Excel(name = "高度")
    private BigDecimal height;

    /** 排序号 */
    @ApiModelProperty("排序号")
    @Excel(name = "排序号")
    private Integer sortNumber;

    /** 点名称 */
    @ApiModelProperty("点名称")
    @Excel(name = "点名称")
    private String coordinateName;

    @ApiModelProperty("1经纬度 0度分秒")
    private Integer pointType= PointType.LAL;
}
