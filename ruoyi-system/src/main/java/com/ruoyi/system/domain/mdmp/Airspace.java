package com.ruoyi.system.domain.mdmp;

import java.math.BigDecimal;
import java.util.List;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.Max;
import javax.validation.constraints.Min;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 空域对象 airspace
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@ApiModel(value = "Airspace", description = "空域实体")
public class Airspace
{
    private static final long serialVersionUID = 1L;

    /** 自增ID */
    @ApiModelProperty("ID")
    private Long id;

    /** 空域名称 */
    @ApiModelProperty("空域名称")
    @Excel(name = "空域名称")
    @NotBlank(message = "空域名称不能为空")
    private String airspaceName;

    /** 空域类型;1管制空域,2电子围栏,3障碍物 */
    @ApiModelProperty("空域类型;1管制空域,2电子围栏,3障碍物")
    @Excel(name = "空域类型;1管制空域,2电子围栏,3障碍物")
    @Min(value = 1, message = "空域类型取值范围：1-3")
    @Max(value = 3, message = "空域类型取值范围：1-3")
    private Integer airspaceType;

    /** 起始高度 */
    @ApiModelProperty("起始高度")
    @Excel(name = "起始高度")
    private Integer startingHeight;

    /** 终止高度 */
    @ApiModelProperty("终止高度")
    @Excel(name = "终止高度")
    private Integer terminationHeight;

    /** 距离 */
    @ApiModelProperty("距离:米")
    @Excel(name = "距离")
    private BigDecimal distance;

    /** 基准点(障碍物);经度 */
    @ApiModelProperty("基准点(障碍物);经度 ")
    @Excel(name = "基准点(障碍物);经度")
    private String benchmarkLong;


    /** 基准点(障碍物);纬度 */
    @ApiModelProperty("基准点(障碍物);纬度")
    @Excel(name = "基准点(障碍物);纬度")
    private String benchmarkLat;

    /** 磁方位 */
    @Excel(name = "磁方位")
    @ApiModelProperty("磁方位")
    private BigDecimal magneticBearing;

    /** 图形种类;0圆，1多边形，2扇形，3椭圆*/
    @ApiModelProperty("图形种类;0圆，1多边形，2扇形，3椭圆")
    @Excel(name = "图形种类;0圆，1多边形，2扇形，3椭圆")
    private Integer graphicsType;

    /** 有效开始日期;2024-01-01 */
    @ApiModelProperty("有效开始日期;2024-01-01")
    @Excel(name = "有效开始日期;2024-01-01")
    private String effectiveStartDate;

    /** 有效截至日期;2024-01-03 */
    @ApiModelProperty("有效截至日期;2024-01-03")
    @Excel(name = "有效截至日期;2024-01-03")
    private String effectiveEndDate;

    /** 圆心;经度 */
    @ApiModelProperty("圆心;经度")
    @Excel(name = "圆心;经度")
    private String circleCenterLong;

    /** 圆心纬度 */
    @ApiModelProperty("圆心纬度")
    @Excel(name = "圆心纬度")
    private String circleCenterLat;

    /** 半径;20.22 */
    @ApiModelProperty("半径")
    @Excel(name = "半径;20.22")
    private BigDecimal radius;

    /** 角度 */
    @ApiModelProperty("角度")
    @Excel(name = "角度")
    private BigDecimal angle;

    /** 圆心角 */
    @ApiModelProperty("圆心角")
    @Excel(name = "圆心角")
    private BigDecimal centralAngle;

    /** 长轴 */
    @ApiModelProperty("长轴")
    @Excel(name = "长轴")
    private BigDecimal longAxis;

    /** 半轴 */
    @ApiModelProperty("半轴")
    @Excel(name = "半轴")
    private BigDecimal halfShaft;

    /** 飞行开始时间;12:00 */
    @ApiModelProperty("飞行开始时间12:00")
    @Excel(name = "飞行开始时间;12:00")
    private String flightStartTime;

    /** 飞行截至时间;13:00 */
    @ApiModelProperty("飞行截至时间;13:00")
    @Excel(name = "飞行截至时间;13:00")
    private String flightEndTime;

    @ApiModelProperty("经纬度实体")
    private List<AirspaceLongLat> airspaceLongLatList;

    @ApiModelProperty("偏移量")
    @Excel(name = "偏移量")
    private BigDecimal airspaceOffset;

    @ApiModelProperty("0是向内偏移，1是向外偏移")
    private Integer positiveOrNegative;

    @ApiModelProperty("格子集合")
    List<MapData> mapDataList;

    @ApiModelProperty("1经纬度 0度分秒")
    private Integer pointType;

    @ApiModelProperty("颜色类型")
    private Integer colorType;

    @ApiModelProperty("机型信息")
    private List<Aircraft> aircraftList;

    private String deptCode;

    private List<String> deptCodeList;

}
