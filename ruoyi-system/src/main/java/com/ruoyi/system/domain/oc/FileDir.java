package com.ruoyi.system.domain.oc;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiOperation;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * 文件目录对象 oc_file_dir
 *
 * <AUTHOR>
 * @date 2021-12-23
 */
@ApiOperation(value = "FileDir", notes = "文件目录")
@Data
public class FileDir {
    private static final long serialVersionUID = 1L;

    /**
     * 文件目录id
     */
    @ApiModelProperty("文件目录id")
    private Long fileDirId;

    /**
     * 父文件目录id
     */
    @ApiModelProperty("父文件目录id")
    @Excel(name = "父文件目录id")
    private Long parentId;

    /**
     * 祖级列表
     */
    @ApiModelProperty("祖级列表")
    @Excel(name = "祖级列表")
    private String ancestors;

    /**
     * 文件目录名称
     */
    @ApiModelProperty("文件目录名称")
    @Excel(name = "文件目录名称")
    private String fileDirName;

    @ApiModelProperty("文件名称")
    @Excel(name = "文件名称")
    private String fileName;

    @ApiModelProperty("类型：1、文件夹，2、文件")
    private Integer type;

    @ApiModelProperty("是否有东西")
    private Boolean haveChildren;

    @ApiModelProperty("保存地址")
    private String saveUrl;

    /**
     * 显示顺序
     */
    @Excel(name = "显示顺序")
    private Long orderNum;


    /**
     * 子文件目录
     */
    @ApiModelProperty("子文件目录")
    private List<FileDir> children;

    /**
     * 创建者
     */
    @ApiModelProperty("创建者")
    private String createBy;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 更新者
     */
    @ApiModelProperty("更新者")
    private String updateBy;

    /**
     * 更新时间
     */
    @ApiModelProperty("更新时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;


    private Long id;
    private String label;


    public void addChildren(FileDir fileDir) {
        if (this.children == null) {
            this.children = new ArrayList<>();
        }
        children.add(fileDir);
    }
}
