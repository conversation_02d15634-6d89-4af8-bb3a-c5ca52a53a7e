package com.ruoyi.system.domain.type;

import lombok.Getter;

/**
 * <AUTHOR>
 */

@Getter
public enum CodeEnum {

    /**
     * 航班状态
     */
    FLIGHT_STATUS_1(1,"执行中"),
    FLIGHT_STATUS_2(2,"已执行"),
    FLIGHT_STATUS_3(3,"已取消"),
    /**航班进出港标识*/
    IO_SIGN_I(0,"进港"),
    IO_SIGN_O(1,"离港"),
    /**航班进离场顺序*/
    PROGRESS_IO("进离场区", "进离场区"),
    PROGRESS_W("等待区", "等待区"),
    PROGRESS_R("跑道", "跑道"),
    PROGRESS_G("地面", "地面"),
    PROGRESS_C("完成", "完成");

    private int code;
    private String str;
    private final String desc;

    CodeEnum(int code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    CodeEnum(String str, String desc) {
        this.str = str;
        this.desc = desc;
    }
}
