package com.ruoyi.system.domain.oc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "添加公司信息入参")
public class AddCompanyDTO {

    /**
     * 航司名称
     */
    @ApiModelProperty(value = "航司名称", required = true)
    private String companyName;

    /**
     * 航司代码
     */
    @ApiModelProperty(value = "航司代码", required = true)
    private String companyCode;

    /**
     * 公司类型（0:管理类公司, 1:通航公司）
     */
    @ApiModelProperty(value = "公司类型（0:管理类公司, 1:通航公司）", required = true)
    private Integer companyType;

}
