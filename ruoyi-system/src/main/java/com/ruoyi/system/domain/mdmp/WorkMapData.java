package com.ruoyi.system.domain.mdmp;

import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import lombok.Data;

/**
 * 作业区地图数据对象 route_map_data
 *
 * <AUTHOR>
 * @date 2024-09-25
 */
@Data
@ApiModel(value = "作业区地图数据")
public class WorkMapData extends BaseEntity {
    private static final long serialVersionUID = 1L;

    private Long id;

    private Long workId;

    private Integer value;

    private Integer index;
    private Integer groupNo;
    /**
     * 组类型 0默认作业区 其他状态为进出点作业区类型
     */
    private Integer groupType;


}
