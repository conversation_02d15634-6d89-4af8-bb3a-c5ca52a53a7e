package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.springframework.data.annotation.Transient;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalTime;
import java.util.List;

/**
 * 当日计划飞行对象 daily_flight_plan
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Data
@ApiModel(value = "当日放行计划")
public class DailyFlightPlan extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    private Long id;

    /**
     * 计划名称
     */
    @Excel(name = "计划名称")
    @ApiModelProperty(value = "计划名称", example = "计划名称", dataType = "String")
    @NotBlank(message = "计划名称不能为空")
    private String name;
    /**
     * 次日计划id
     */
    @Excel(name = "次日计划id")
    @ApiModelProperty(value = "次日计划id", example = "1", dataType = "Long")
    private Long nextDayFlightPlanId;

    /**
     * 公司名称
     */
    @Excel(name = "公司名称")
    @ApiModelProperty(value = "公司名称", example = "凯亚", dataType = "String")
    private String companyName;

    /**
     * 公司代码
     */
    @Excel(name = "公司代码")
    @ApiModelProperty(value = "公司代码", example = "KY", dataType = "String")
    private String companyCode;

    /**
     * 任务类型;枚举
     */
    @Excel(name = "任务类型;枚举")
    @ApiModelProperty(value = "任务类型", example = "航空护林", dataType = "String")
    @NotBlank(message = "任务类型不能为空")
    private String taskType;


    /**
     * 计划飞行日期
     */
    @Excel(name = "计划飞行日期")
    @ApiModelProperty(value = "计划飞行日期", example = "2024-01-01", dataType = "String")
    @NotBlank(message = "计划飞行日期不能为空")
    private String flightDate;

    /**
     * 是否临时计划;0 非临时计划 1 临时计划
     */
    @Excel(name = "是否临时计划;0 非临时计划 1 临时计划")
    @ApiModelProperty(value = "是否临时计划( 0,非临时计划;1, 临时计划)", required = true, allowableValues = "0,1")
    @NotNull(message = "是否临时计划不能为空")
    private Integer temporary;

    /**
     * 拒绝说明
     */
    @Excel(name = "拒绝说明")
    @ApiModelProperty(value = "拒绝说明", example = "拒绝", dataType = "String")
    private String refusalExplain;

    /**
     * 是否删除;0 未删除 1已删除
     */
    @Excel(name = "是否删除;0 未删除 1已删除")
    @ApiModelProperty(value = "是否删除(0,未删除;1, 已删除)", required = true, allowableValues = "0,1")
    private Integer isDelete;

    /**
     * 审核人
     */
    @Excel(name = "审核人")
    @ApiModelProperty(value = "审核人", example = "张三", dataType = "String")
    private String auditor;

    /**
     * 审核时间
     */
    @Excel(name = "审核时间")
    @ApiModelProperty(value = "审核时间", example = "2024-01-01 12:22", dataType = "String")
    private String auditTime;

    /**
     * 创建人
     */
    @Excel(name = "创建人")
    @ApiModelProperty(value = "创建人", example = "admin", dataType = "String")
    private String creator;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    @ApiModelProperty(value = "创建时间", example = "2024-01-01 12:22", dataType = "String")
    private String creationTime;

    /**
     * 联系人姓名
     */
    @Excel(name = "联系人姓名")
    @ApiModelProperty(value = "联系人姓名", example = "张三", dataType = "String")
    @NotBlank(message = "联系人姓名不能为空")
    private String contactName;

    /**
     * 联系人电话
     */
    @Excel(name = "联系人电话")
    @ApiModelProperty(value = "联系人电话", example = "12345645787", dataType = "String")
    @NotBlank(message = "联系人电话不能为空")
    private String contactPhone;

    /**
     * 编号
     */
    @Excel(name = "编号")
    @ApiModelProperty(value = "编号", example = "P2024454545665465", dataType = "String")
    private String serialNo;

    /**
     * 状态;0草稿/1 已提交/ 2 已通过/ 3审核中/ 4拒绝
     */
    @Excel(name = "状态;0草稿/1 已提交/ 2 已通过/ 3审核中/ 4拒绝")
    @ApiModelProperty(value = "状态( 0,草稿;1, 已提交;2, 已通过;3, 审核中;4, 拒绝)", required = true, allowableValues = "0,1,2,3,4")
    @NotNull(message = "状态不能为空")
    private Integer status;

    /**
     * 应答机编码（次日计划）
     */
    @Excel(name = "应答机编码", readConverterExp = "次=日计划")
    @ApiModelProperty(value = "应答机编码", example = "202154", dataType = "String")
    @NotBlank(message = "应答机编码不能为空")
    private String answeringMachineCode;

    /**
     * 计划起飞时间(次日计划)
     */
    @Excel(name = "计划起飞时间(次日计划)")
    @ApiModelProperty(value = "计划起飞时间", example = "12:00", dataType = "String")
    @NotNull(message = "计划起飞时间不能为空")
    private LocalTime explainDepartureTime;

    /**
     * 计划到达时间(次日计划)
     */
    @Excel(name = "计划到达时间(次日计划)")
    @ApiModelProperty(value = "计划到达时间", example = "12:00", dataType = "String")
    @NotNull(message = "计划到达时间不能为空")
    private LocalTime explainArrivalTime;

    /**
     * 无线电频率(次日计划)
     */
    @Excel(name = "无线电频率(次日计划)")
    @ApiModelProperty(value = "无线电频率", example = "123", dataType = "String")
    @NotBlank(message = "无线电频率不能为空")
    private String radioFrequency;

    /**
     * 实际起飞时间
     */
    @Excel(name = "实际起飞时间")
    @ApiModelProperty(value = "实际起飞时间", example = "13:00", dataType = "String")
    private LocalTime actualDepartureTime;

    /**
     * 实际到达时间
     */
    @Excel(name = "实际到达时间")
    @ApiModelProperty(value = "实际到达时间", example = "13:00", dataType = "String")
    private LocalTime actualArrivalTime;

    /**
     * 是否作业;1作业 0 航线（ 不作业）
     */
    @Excel(name = "是否作业;1作业 0 航线", readConverterExp = "不=作业")
    @ApiModelProperty(value = "是否作业(0,航线;1, 作业)", required = true, allowableValues = "0,1")
    private Integer isWork;


    /**
     * 关联表
     * <p>
     * 计划 机型机号关联表
     * 计划 机场关联表
     * 计划 航线关联表
     * 计划 作业区关联表
     * 计划 附件关联表
     */

    @Excel(name = "机型机号关联")
    @ApiModelProperty(required = true, value = "机型机号关联")
    @Valid
    private List<FlightPlanAircraftModel> planModelAircraftList;


    @Excel(name = "机场关联表")
    @ApiModelProperty(required = true, value = "机场关联表")
    @Valid
    private List<FlightPlanAirport> flightPlanAirportList;


    @Excel(name = "航线关联表")
    @ApiModelProperty(value = "航线关联表")
    @Valid
    private List<FlightPlanRoute> flightPlanRouteList;


    @Excel(name = "附件关联表")
    @ApiModelProperty(value = "附件关联表")
    @Valid
    private List<FlightPlanFile> flightPlanFileList;


    @Excel(name = "作业区关联表")
    @ApiModelProperty(value = "作业区关联表")
    @Valid
    private List<FlightPlanWork> flightPlanWorkList;


    @ApiModelProperty(value = "新增修改审核冲突验证")
    private boolean verify = false;

    @ApiModelProperty(value = "航线冲突说明", example = "航线冲突说明", dataType = "String")
    private String routeConflictDescription;

    @ApiModelProperty(value = "作业区冲突说明", example = "作业区冲突说明", dataType = "String")
    private String workConflictDescription;


    @Excel(name = "保障协议类型", readConverterExp = "0=信息通报,1=空管保障,2=训练")
    @ApiModelProperty(value = "保障协议类型;0信息通报 1 空管保障 2训练)", required = true)
    @NotNull(message = "保障协议类型不能为空")
    private Integer agreementType;
    @ApiModelProperty(value = "协议开始日期", example = "协议开始日期", dataType = "String")
    @NotBlank(message = "协议开始日期不能为空")
    private String agreementStartDate;
    @ApiModelProperty(value = "协议结束日期", example = "协议结束日期", dataType = "String")
    @NotBlank(message = "协议结束日期不能为空")
    private String agreementEndDate;

    private Integer planType;

    private String deptCode;

    private List<String> deptCodeList;

}
