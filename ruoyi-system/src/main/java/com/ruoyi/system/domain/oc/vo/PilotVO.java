package com.ruoyi.system.domain.oc.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 飞行员信息返回
 *
 * <AUTHOR>
 */
@Data
@ApiModel(value = "PilotVO", description = "飞行员信息返回")
public class PilotVO {

    @ApiModelProperty("用户ID")
    private Long userId;
    @ApiModelProperty("用户名")
    private String userName;
    @ApiModelProperty("一段时间内飞行时间之和")
    private Integer flyTime;
    @ApiModelProperty("小时")
    private Integer hours;
    @ApiModelProperty("分钟")
    private Integer minutes;
    @ApiModelProperty(value = "航班架次")
    private Integer flightFrequency;

}
