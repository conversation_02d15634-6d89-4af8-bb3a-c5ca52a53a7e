package com.ruoyi.system.domain.mdmp.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date ：Created in 2024/12/30 14:32
 * @description：格子气象信息
 * @modified By：
 * @version: $
 */
@Data
public class MeteInfo {

    @ApiModelProperty("U")
    private double u;

    @ApiModelProperty("V")
    private double v;

    @ApiModelProperty("状态")
    private int stateCode;

    @ApiModelProperty("温度")
    private double temperature;

    @ApiModelProperty("降雨")
    private double rainfall;

    @ApiModelProperty("能见度")
    private double visibility;

    @ApiModelProperty("雷暴")
    private Integer strom;

    @ApiModelProperty(value = "格子index", example = "0", dataType = "Long")
    private Integer index;
}
