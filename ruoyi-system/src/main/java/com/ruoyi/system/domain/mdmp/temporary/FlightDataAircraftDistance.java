package com.ruoyi.system.domain.mdmp.temporary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 飞机实时坐标与最近无人机的距离
 *
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞机实时坐标与最近无人机的距离")
public class FlightDataAircraftDistance {

    /**
     * 飞机坐标ID
     */
    @ApiModelProperty(value = "飞机坐标ID", example = "5")
    private Long flightDataId;

    /**
     * 当前飞机呼号
     */
    @ApiModelProperty(value = "当前飞机呼号", example = "FL9999")
    private String callSign;

    /**
     * 另一个飞机呼号
     */
    @ApiModelProperty(value = "最近飞机呼号", example = "AA1234")
    private String otherCallSign;

    /**
     * 距离
     */
    @ApiModelProperty(value = "距离", example = "1984.00")
    private Double distance;

    /**
     * 垂直距离
     */
    @ApiModelProperty(value = "垂直距离", example = "3948.00")
    private Double verticalDistance;

    /**
     * 水平距离
     */
    @ApiModelProperty(value = "水平距离", example = "847.00")
    private Double horizontalDistance;
}
