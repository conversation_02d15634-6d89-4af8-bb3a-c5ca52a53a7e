package com.ruoyi.system.domain.oc.vo;

import com.ruoyi.common.enums.runcontrol.AircrewRecordType;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "AircrewMeetTermsVO", description = "满足条款要求记录返回")
public class AircrewMeetTermsVO {
    @ApiModelProperty("航路检查")
    private List<AircrewRecordInfoVO> routeInspectRecords;
    @ApiModelProperty("飞机和航路资格审定")
    private List<AircrewRecordInfoVO> qualificationReviewRecords;
    @ApiModelProperty("体检鉴定和疾病治疗")
    private List<AircrewRecordInfoVO> physicalExaminationTreatmentRecords;
    @ApiModelProperty("飞行执勤休息时间记录")
    private List<AircrewRecordInfoVO> dutyRestRecords;

    public static AircrewMeetTermsVO setting(Map<Integer, List<AircrewRecordInfoVO>> map) {
        AircrewMeetTermsVO vo = new AircrewMeetTermsVO();
        vo.setRouteInspectRecords(map.get(AircrewRecordType.SECOND_FILETYPE_1.getCode()) == null ? new ArrayList<>() : map.get(AircrewRecordType.SECOND_FILETYPE_1.getCode()));
        vo.setQualificationReviewRecords(map.get(AircrewRecordType.SECOND_FILETYPE_2.getCode()) == null ? new ArrayList<>() : map.get(AircrewRecordType.SECOND_FILETYPE_2.getCode()));
        vo.setPhysicalExaminationTreatmentRecords(map.get(AircrewRecordType.SECOND_FILETYPE_3.getCode()) == null ? new ArrayList<>() : map.get(AircrewRecordType.SECOND_FILETYPE_3.getCode()));
        vo.setDutyRestRecords(map.get(AircrewRecordType.SECOND_FILETYPE_4.getCode()) == null ? new ArrayList<>() : map.get(AircrewRecordType.SECOND_FILETYPE_4.getCode()));
        return vo;
    }
}
