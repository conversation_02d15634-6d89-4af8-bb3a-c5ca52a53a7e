package com.ruoyi.system.domain.oc.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

/**
 * 航班架次对象 oc_flight_sorties
 *
 * <AUTHOR>
 * @date 2021-11-01
 */
@ApiModel(value = "FlightSorties", description = "航班班次集合")
@Data
public class FlightSorties extends BaseEntity {

    public FlightSorties() {

    }

    public FlightSorties(Integer flightSortiesStatus, Long flightplanId) {
        this.flightSortiesStatus = flightSortiesStatus;
        this.flightplanId = flightplanId;

    }

    private static final long serialVersionUID = 1L;

    /**
     * 航班班次编号
     */
    @ApiModelProperty("航班班次编号")
    private Long flightSortiesId;


    /**
     * 航班班次号
     */
    @ApiModelProperty("航班班次号")
    private String flightSortiesNo;
    /**
     * 任务类型（1.短途运输  2.航空护林 3.调机飞行）
     */
    @ApiModelProperty("任务类型")
    @Excel(name = "任务类型")
    private String flightPurpose;

    /**
     * 航班计划编号
     */
    @ApiModelProperty("航班计划编号")
    @Excel(name = "航班计划编号")
    private Long flightplanId;

    /**
     * 航班班次状态 1 计划中   2 已执行  3 已取消
     */
    @ApiModelProperty("航班班次状态")
    @Excel(name = "航班班次状态", readConverterExp = "1=计划中,2=已执行,3=已取消")
    private Integer flightSortiesStatus;

    /**
     * 计划起飞时间
     */
    @ApiModelProperty("计划起飞时间")
    private String planDepartTime;

    /**
     * 计划到达时间
     */
    @ApiModelProperty("计划到达时间")
    private String planArriveTime;

    /**
     * 燃油
     */
    @ApiModelProperty("燃油")
    @Excel(name = "燃油")
    private Long fuel;


    /**
     * 飞行问答状态
     */
    @ApiModelProperty("飞行问答状态 0 代表未完成  1代表已完成")
    @Excel(name = "飞行问答状态", readConverterExp = "0=未完成,1=已完成")
    private Integer flyQaStatus;
    private List<FlightSortiesQa> qas;


    /**
     * 航线资料状态
     */
    @ApiModelProperty("航线资料状态 0(默认):无数据，1:未完成 2 已完成")
    @Excel(name = "航线资料状态", width = 30, readConverterExp = "0=无数据,1=未完成,2=已完成")
    private Integer routeInfoStatus;
    private List<FlightSortiesRouteInfo> routeInfos;


    /**
     * 气象资料检核状态
     */
    @ApiModelProperty("气象资料检核状态 0(默认):无数据，1:未完成 2 已拒绝  3已通过")
    @Excel(name = "气象资料检核状态", readConverterExp = "0=无数据,1=未完成,2=已拒绝,3=已通过")
    private Integer meteReviewStatus;
    private List<FlightSortiesMeteReview> meteReviews;



    /**
     * 电子舱单状态
     */
    @ApiModelProperty("电子舱单状态 0(默认):无数据，1:未完成 2 已拒绝  3已通过")
    @Excel(name = "电子舱单状态", readConverterExp = "0=无数据,1=未完成,2=已拒绝,3=已通过")
    private Integer eleManifestStatus;
    private List<FlightSortiesEleManifest> eleManifests;


    /**
     * 放行单状态
     */
    @ApiModelProperty("放行单状态 0(默认):无数据，1:未完成 2 已拒绝  3已通过")
    @Excel(name = "放行单状态", readConverterExp = "0=无数据,1=未完成,2=已拒绝,3=已通过")
    private Integer releasenoteStatus;
    private List<FlightSortiesReleasenote> releasenotes;



    /**
     * 飞行时刻录入状态
     */
    @ApiModelProperty("飞行时刻录入状态 0 未完成  1已完成")
    @Excel(name = "飞行时刻录入状态", readConverterExp = "0=未完成,1=已完成")
    private Integer flyMomentStatus;
    private List<FlightSortiesFlyMoment> flyMoments;





    /**
     * 空中时间 （落地时间-起飞时间） 时刻 计算得到
     */
    @ApiModelProperty("空中飞行时间")
    private Integer flyTime;

    /**
     * 飞行含滑行时间时（滑行结束-滑行开始）时刻 计算得到
     */
    @ApiModelProperty("飞行含滑行时间时间")
    private Integer slideTime;


    /**
     * 机长的用户id
     */
    @ApiModelProperty("机长的用户id，用,隔开")
    @Excel(name = "机长的用户id")
    private String captainUserId;

    /**
     * 副驾驶的用户id
     */
    @ApiModelProperty("副驾驶的用户id，用,隔开")
    @Excel(name = "副驾驶的用户id")
    private String copilotUserId;

    @ApiModelProperty("机务人员id，用,隔开")
    @Excel(name = "机务id")
    private String maintenanceId;

    /**
     * 运控值班的用户id
     */
    @ApiModelProperty("运控值班的用户id，用,隔开")
    @Excel(name = "运控值班的用户id")
    private String ocUserId;

    /**
     * 总值班领导的用户id
     */
    @ApiModelProperty("总值班领导的用户id")
    @Excel(name = "总值班领导的用户id")
    private Long generalleaderUserId;

    /**
     * 值班领导的用户id
     */
    @ApiModelProperty("值班领导的用户id")
    @Excel(name = "值班领导的用户id")
    private Long leaderUserId;

    /**
     * 载客数
     */
    @ApiModelProperty("载客数")
    @Excel(name = "载客数")
    private Long seatNum;

    /**
     * 飞行器id
     */
    @ApiModelProperty("飞行器id")
    @Excel(name = "飞行器id")
    private Long aircraftId;

    /**
     * 机尾号
     */
    @ApiModelProperty("机尾号")
    @Excel(name = "机尾号")
    private String aircraftTailNo;


    /**
     * 飞行高度(M)
     */
    @ApiModelProperty("飞行高度(M)")
    @Excel(name = "飞行高度(M)")
    private Integer flightAltitude;

    /**
     * 机长对飞行任务是否同意放行(0:未查看，1：同意，2：拒绝，3,：其他)
     */
    @ApiModelProperty("机长对飞行任务是否同意放行(0:未查看，1：同意，2：拒绝，3,：其他(班次取消))")
    @Excel(name = "机长对飞行任务是否同意放行(0:未查看，1：同意，2：拒绝，3,：其他(班次取消))")
    private Integer releaseStatus;


    /**
     * 飞行是否正常(0:否，1：是，2：其他)
     */
    @ApiModelProperty("飞行是否正常(0:否，1：是，2：其他)")
    @Excel(name = "飞行是否正常(0:否，1：是，2：其他)")
    private Integer flightNormalStatus;


    /**
     * 机长飞行后总结点评
     */
    @ApiModelProperty("机长飞行后总结点评")
    @Excel(name = "机长飞行后总结点评")
    private String flightSummaryComments;


    /**
     * 上传任务书状态(0:未，1：已)
     */
    @ApiModelProperty("上传任务书状态(0:未，1：已)")
    @Excel(name = "上传任务书状态(0:未，1：已)")
    private Integer uploadAssignmentStatus;


    /**
     * 上传任务书文件地址
     */
    @ApiModelProperty("上传任务书文件地址")
    @Excel(name = "上传任务书文件地址")
    private String uploadAssignmentFileUrl;

    /**
     * 上传任务书文件名
     */
    @ApiModelProperty("上传任务书文件名")
    @Excel(name = "上传任务书文件名")
    private String uploadAssignmentFileName;

    /**
     * 滑行开始时间
     */
    @ApiModelProperty("滑行开始时间")
    @Excel(name = "滑行开始时间")
    @JsonFormat(pattern = "yyyy-MM-dd hh:ss")
    private Date taxiStartTime;

    /**
     * 起飞时间
     */
    @ApiModelProperty("起飞时间")
    @Excel(name = "起飞时间")
    @JsonFormat(pattern = "yyyy-MM-dd hh:ss")
    private Date departureTime;
    /**
     * 降落时间
     */
    @ApiModelProperty("降落时间")
    @Excel(name = "降落时间")
    @JsonFormat(pattern = "yyyy-MM-dd hh:ss")
    private Date landingTime;
    /**
     * 关车时间
     */
    @ApiModelProperty("关车时间")
    @Excel(name = "关车时间")
    @JsonFormat(pattern = "yyyy-MM-dd hh:ss")
    private Date shutdownTime;

    /**
     * 飞行时间(分钟)
     */
    @ApiModelProperty("飞行时间(分钟)")
    @Excel(name = "飞行时间(分钟)")
    private Integer flightTime;
    /**
     * 特殊情况
     */
    @ApiModelProperty("特殊情况")
    @Excel(name = "特殊情况")
    private String exceptionalCase;
    /**
     * 特殊情况发送状态(0:未，1：已)
     */
    @ApiModelProperty("特殊情况发送状态(0:未，1：已)")
    @Excel(name = "特殊情况发送状态(0:未，1：已)")
    private Integer specialCaseSendingStatus;
    /**
     * 特殊情况机长查看状态(0:未，1：已)
     */
    @ApiModelProperty("特殊情况机长查看状态(0:未，1：已)")
    @Excel(name = "特殊情况机长查看状态(0:未，1：已)")
    private Integer exceptionalCaseForCaptain;
    /**
     * 特殊情况副机长查看状态(0:未，1：已)
     */
    @ApiModelProperty("特殊情况副机长查看状态(0:未，1：已)")
    @Excel(name = "特殊情况副机长查看状态(0:未，1：已)")
    private Integer exceptionalCaseForCopilot;
    /**
     * 特殊情况机务查看状态(0:未，1：已)
     */
    @ApiModelProperty("特殊情况机务查看状态(0:未，1：已)")
    @Excel(name = "特殊情况机务查看状态(0:未，1：已)")
    private Integer exceptionalCaseForMaintenance;
    /**
     * 特殊情况运控查看状态(0:未，1：已)
     */
    @ApiModelProperty("特殊情况运控查看状态(0:未，1：已)")
    @Excel(name = "特殊情况运控查看状态(0:未，1：已)")
    private Integer exceptionalCaseForOc;
    /**
     * 副机长类型(0:副驾驶，1：第二机长)
     */
    @ApiModelProperty("副机长类型(0:副驾驶，1：第二机长)")
    @Excel(name = "副机长类型(0:副驾驶，1：第二机长)")
    private Integer copilotType;

    /**
     * 航班日期
     */
    @ApiModelProperty("航班日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date sortiesDate;
    /**
     * 预先准备审批状态（0：未审批，1：同意，2：拒绝，3：其他）
     */
    @ApiModelProperty("预先准备审批状态（0：未审批，1：同意，2：拒绝，3：其他）")
    @Excel(name = "预先准备审批状态（0：未审批，1：同意，2：拒绝，3：其他）")
    private Integer preApprovalStatus;

    /**
     * 预先准备审批用户ID
     */
    @ApiModelProperty("预先准备审批用户ID")
    @Excel(name = "预先准备审批用户ID")
    private long preApprovalUserId;

    /**
     * 预先准备审批用户名
     */
    @ApiModelProperty("预先准备审批用户名")
    @Excel(name = "预先准备审批用户名")
    private String preApprovalUserName;

    /**
     * 预先准备审批评语
     */
    @ApiModelProperty("预先准备审批评语")
    @Excel(name = "预先准备审批评语")
    private String approvalComments;

    /**
     * 班次飞行状态（0：飞行前，1：飞行中，2：飞行后）
     */
    @ApiModelProperty("班次飞行状态（0：飞行前，1：飞行中，2：飞行后）")
    private Integer flyStatus;

    public Long getFlightsortiesId() {
        return flightSortiesId;
    }

    public void setFlightsortiesId(Long flightSortiesId) {
        this.flightSortiesId = flightSortiesId;
    }

    /**
     * 以航班计划分组后的班次集合
     */
    public static HashMap<Long, List<FlightSorties>> getMap(List<FlightSorties> list) {
        HashMap<Long, List<FlightSorties>> map = new HashMap<>();
        for (FlightSorties flightSorties : list) {
            Long flightPlanId = flightSorties.getFlightplanId();
            if (map.containsKey(flightPlanId)) {
                map.get(flightPlanId).add(flightSorties);
            } else {
                List<FlightSorties> flightSortiesList = new ArrayList<>();
                flightSortiesList.add(flightSorties);
                map.put(flightPlanId, flightSortiesList);
            }
        }
        return map;
    }
}
