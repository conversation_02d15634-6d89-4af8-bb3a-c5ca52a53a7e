package com.ruoyi.system.domain.oc.dto;

import com.fasterxml.jackson.annotation.JsonFormat;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "FlightStatisticsDTO", description = "修改飞行员资质请求参数")
public class PilotQualificationDTO {

    @ApiModelProperty(value = "用户名", required = true)
    private Long userId;

    @ApiModelProperty("机型")
    private String aircraftStyle;

    @ApiModelProperty("执照有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityOfLicense;

    @ApiModelProperty("体检合格证有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityPeriodOfPhysicalExamination;

    @ApiModelProperty("熟练检查基准月")
    private String proficiencyCheckStandardMonth;

    @ApiModelProperty("熟练检查到期日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date proficiencyCheckValidity;

    @ApiModelProperty("机长航线练检查到期日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityPeriodOfRouteTrainingInspection;

    @ApiModelProperty("危险品训练有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dangerTrainingInspection;

    @ApiModelProperty("汉语言有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date chineseLanguageValidity;

    @ApiModelProperty("空勤登记证有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityOfRegistrationCertificate;

    @ApiModelProperty("Y12放行授权有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date y12Inspection;

    @ApiModelProperty("C208放行授权有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date c208Inspection;

    @ApiModelProperty("B300放行授权有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date b300Inspection;
}
