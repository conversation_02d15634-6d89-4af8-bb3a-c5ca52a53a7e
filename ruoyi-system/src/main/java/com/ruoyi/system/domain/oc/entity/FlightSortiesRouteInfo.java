package com.ruoyi.system.domain.oc.entity;

import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 记录班次下航线资料对象 oc_flight_sorties_route_info
 *
 * <AUTHOR>
 * @date 2021-12-27
 */
public class FlightSortiesRouteInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 航班班次编号
     */
    private Long id;

    /**
     * 航班计划编号
     */
    @Excel(name = "航班计划编号")
    private Long flightSortiesId;

    /**
     * 航线资料上传时间
     */
    @Excel(name = "航线资料上传时间")
    private String uploadRouteInfoTime;

    /**
     * 航线资料完成时间
     */
    @Excel(name = "航线资料完成时间")
    private String routeInfoTime;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setFlightSortiesId(Long flightSortiesId) {
        this.flightSortiesId = flightSortiesId;
    }

    public Long getFlightSortiesId() {
        return flightSortiesId;
    }

    public void setUploadRouteInfoTime(String uploadRouteInfoTime) {
        this.uploadRouteInfoTime = uploadRouteInfoTime;
    }

    public String getUploadRouteInfoTime() {
        return uploadRouteInfoTime;
    }

    public void setRouteInfoTime(String routeInfoTime) {
        this.routeInfoTime = routeInfoTime;
    }

    public String getRouteInfoTime() {
        return routeInfoTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("flightSortiesId", getFlightSortiesId())
                .append("uploadRouteInfoTime", getUploadRouteInfoTime())
                .append("routeInfoTime", getRouteInfoTime())
                .append("createBy", getCreateBy())
                .append("createTime", getCreateTime())
                .append("updateBy", getUpdateBy())
                .append("updateTime", getUpdateTime())
                .append("remark", getRemark())
                .toString();
    }
}
