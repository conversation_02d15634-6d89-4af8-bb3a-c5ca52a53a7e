package com.ruoyi.system.domain.oc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "FlightStatisticsDTO", description = "查询飞行员资质请求参数")
public class QueryPilotQualificationDTO {

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty(value = "角色(飞行员：pilot, 机务：maintenance)", required = true)
    private String roleKey;

    @ApiModelProperty("航司二字码(前端不传，后端自己从请求头获取)")
    private String companyCode;
}
