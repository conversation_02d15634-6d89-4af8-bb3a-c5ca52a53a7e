package com.ruoyi.system.domain.oc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "添加公司菜单入参")
public class AddCompanyMenuDTO {

    /**
     * 航司代码
     */
    @ApiModelProperty(value = "航司代码", required = true)
    private String companyCode;

    /**
     * 菜单Ids
     */
    @ApiModelProperty(value = "菜单Ids", required = true)
    private Long[] menuIds;
}
