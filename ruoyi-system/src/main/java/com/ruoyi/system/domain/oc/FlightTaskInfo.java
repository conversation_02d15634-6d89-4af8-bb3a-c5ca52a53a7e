package com.ruoyi.system.domain.oc;

import lombok.Data;

import java.util.Date;

@Data
public class FlightTaskInfo {
    private Long id;

    private String batch;

    private String taskType;

    private String routeOrAirspace;

    private String routeOrAirspaceCode;

    private String altitude;

    private String alternateAirport;

    private String flightRules;

    private String captainWeatherStandard;

    private String crewRole1;

    private String crewName1;

    private String crewRole2;

    private String crewName2;

    private String crewPassengerCount;

    private String taskBookNumber;

    private Date createdAt;

    private Date updatedAt;

    private String companyCode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getBatch() {
        return batch;
    }

    public void setBatch(String batch) {
        this.batch = batch == null ? null : batch.trim();
    }

    public String getTaskType() {
        return taskType;
    }

    public void setTaskType(String taskType) {
        this.taskType = taskType == null ? null : taskType.trim();
    }

    public String getRouteOrAirspace() {
        return routeOrAirspace;
    }

    public void setRouteOrAirspace(String routeOrAirspace) {
        this.routeOrAirspace = routeOrAirspace == null ? null : routeOrAirspace.trim();
    }

    public String getRouteOrAirspaceCode() {
        return routeOrAirspaceCode;
    }

    public void setRouteOrAirspaceCode(String routeOrAirspaceCode) {
        this.routeOrAirspaceCode = routeOrAirspaceCode == null ? null : routeOrAirspaceCode.trim();
    }

    public String getAltitude() {
        return altitude;
    }

    public void setAltitude(String altitude) {
        this.altitude = altitude == null ? null : altitude.trim();
    }

    public String getAlternateAirport() {
        return alternateAirport;
    }

    public void setAlternateAirport(String alternateAirport) {
        this.alternateAirport = alternateAirport == null ? null : alternateAirport.trim();
    }

    public String getFlightRules() {
        return flightRules;
    }

    public void setFlightRules(String flightRules) {
        this.flightRules = flightRules == null ? null : flightRules.trim();
    }

    public String getCaptainWeatherStandard() {
        return captainWeatherStandard;
    }

    public void setCaptainWeatherStandard(String captainWeatherStandard) {
        this.captainWeatherStandard = captainWeatherStandard == null ? null : captainWeatherStandard.trim();
    }

    public String getCrewRole1() {
        return crewRole1;
    }

    public void setCrewRole1(String crewRole1) {
        this.crewRole1 = crewRole1 == null ? null : crewRole1.trim();
    }

    public String getCrewName1() {
        return crewName1;
    }

    public void setCrewName1(String crewName1) {
        this.crewName1 = crewName1 == null ? null : crewName1.trim();
    }

    public String getCrewRole2() {
        return crewRole2;
    }

    public void setCrewRole2(String crewRole2) {
        this.crewRole2 = crewRole2 == null ? null : crewRole2.trim();
    }

    public String getCrewName2() {
        return crewName2;
    }

    public void setCrewName2(String crewName2) {
        this.crewName2 = crewName2 == null ? null : crewName2.trim();
    }

    public String getCrewPassengerCount() {
        return crewPassengerCount;
    }

    public void setCrewPassengerCount(String crewPassengerCount) {
        this.crewPassengerCount = crewPassengerCount == null ? null : crewPassengerCount.trim();
    }

    public String getTaskBookNumber() {
        return taskBookNumber;
    }

    public void setTaskBookNumber(String taskBookNumber) {
        this.taskBookNumber = taskBookNumber == null ? null : taskBookNumber.trim();
    }

    public Date getCreatedAt() {
        return createdAt;
    }

    public void setCreatedAt(Date createdAt) {
        this.createdAt = createdAt;
    }

    public Date getUpdatedAt() {
        return updatedAt;
    }

    public void setUpdatedAt(Date updatedAt) {
        this.updatedAt = updatedAt;
    }

    public String getCompanyCode() {
        return companyCode;
    }

    public void setCompanyCode(String companyCode) {
        this.companyCode = companyCode == null ? null : companyCode.trim();
    }
}