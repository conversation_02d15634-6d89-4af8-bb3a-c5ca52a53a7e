package com.ruoyi.system.domain.mdmp;

import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 最小温度对象 r_mintemperatureres
 *
 * <AUTHOR>
 * @date 2025-05-12
 */
@Data
public class RMintemperatureres {
    private static final long serialVersionUID = 1L;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 报文表ID
     */
    @Excel(name = "报文表ID")
    private Long decodeReportId;

    /**
     * 温度
     */
    @Excel(name = "温度")
    private Integer temperature;

    /**
     * 温度时间;UTC时间戳
     */
    @Excel(name = "温度时间;UTC时间戳")
    private Long time;

    private Integer colourType;
}
