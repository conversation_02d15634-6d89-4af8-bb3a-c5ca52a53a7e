package com.ruoyi.system.domain.oc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "新增任务类型入参")
public class AddFlightPurposeDTO {

    @ApiModelProperty(value = "任务类型名称", required = true)
    @NotBlank(message = "任务类型名称不能为空")
    private String name;
}
