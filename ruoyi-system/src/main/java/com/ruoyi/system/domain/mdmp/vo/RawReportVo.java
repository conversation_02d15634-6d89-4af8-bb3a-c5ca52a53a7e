package com.ruoyi.system.domain.mdmp.vo;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2025/5/20 9:27
 * @description：
 * @modified By：
 * @version: $
 */
@Data
@ApiModel(description = "报文数据")
public class RawReportVo {

    @ApiModelProperty("原始报文内容")
    List<String> contentList;

    @ApiModelProperty("分解报文信息")
    List<DecodeReportVo> decodeReportVoList;
}
