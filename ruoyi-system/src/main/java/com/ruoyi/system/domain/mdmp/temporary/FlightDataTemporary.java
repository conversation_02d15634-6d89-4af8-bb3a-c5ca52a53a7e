package com.ruoyi.system.domain.mdmp.temporary;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行数据临时(生产不会用)")
public class FlightDataTemporary {

    /**
     * ID
     */
    @ApiModelProperty("ID")
    private Long id;

    /**
     * 航班计划ID
     */
    @ApiModelProperty("航班计划ID")
    private Long flightPlanId;

    /**
     * 呼号
     */
    @ApiModelProperty("呼号")
    private String callSign;

    @ApiModelProperty(value = "飞机注册号", example = "B-7962")
    private String aircraftReg;
    @ApiModelProperty(value = "运营机构", example = "中信海洋直升机")
    private String operationAgency;
    @ApiModelProperty(value = "设备编号", example = "10001")
    private String deviceId;
    @ApiModelProperty(value = "UTC 年", example = "2024")
    private Integer utcYear;
    @ApiModelProperty(value = "UTC 月", example = "4")
    private Integer utcMonth;
    @ApiModelProperty(value = "UTC 日", example = "1")
    private Integer utcDay;
    @ApiModelProperty(value = "UTC 时", example = "12")
    private Integer utcHour;
    @ApiModelProperty(value = "UTC 分", example = "33")
    private Integer utcMinute;
    @ApiModelProperty(value = "UTC 秒", example = "54")
    private Integer utcSecond;
    @ApiModelProperty("日期(yyyy-MM-dd)")
    private String flightDate;
    @ApiModelProperty("飞行坐标的动态时间(HH:mm:ss)")
    private String dynamicTime;
    @ApiModelProperty(value = "定位指示（0=无效解，1=单点解，2=差分解，4=固定解，5=浮点解，6=惯导解）", example = "0")
    private Integer gpsFlag;
    @ApiModelProperty(value = "纬度", example = "25.37")
    private BigDecimal latitude;
    @ApiModelProperty(value = "经度", example = "116.48")
    private BigDecimal longitude;
    @ApiModelProperty(value = "海拔高度", example = "89.67")
    private Float elevation;
    @ApiModelProperty(value = "北向速度", example = "12.2")
    private Float northSpeed;
    @ApiModelProperty(value = "东向速度", example = "9.3")
    private Float eastSpeed;
    @ApiModelProperty(value = "天顶方向速度", example = "2.4")
    private Float verticalSpeed;
    @ApiModelProperty(value = "航向角度", example = "34.2")
    private Float headingAngle;
    @ApiModelProperty(value = "俯仰角度", example = "12.3")
    private Float pitchAngle;
    @ApiModelProperty(value = "横滚角度", example = "1.2")
    private Float rollAngle;
    @ApiModelProperty(value = "航速", example = "202154")
    private Float speed;
}
