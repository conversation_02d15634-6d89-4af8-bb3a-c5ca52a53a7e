package com.ruoyi.system.domain.oc.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * 动态信息对象 oc_flight_dynamic_info
 *
 * <AUTHOR>
 * @date 2024-01-01
 */
@ApiModel(value = "FlightDynamicInfo", description = "动态信息")
@Data
@TableName("oc_flight_dynamic_info")
public class FlightDynamicInfo extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @ApiModelProperty("主键ID")
    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    /**
     * 任务书编号
     */
    @ApiModelProperty("任务书编号")
    @Excel(name = "任务书编号")
    private String taskBookNumber;

    /**
     * 批次
     */
    @ApiModelProperty("批次")
    @Excel(name = "批次")
    private String batch;

    /**
     * 始发地
     */
    @ApiModelProperty("始发地")
    @Excel(name = "始发地")
    private String departureLocation;

    /**
     * 目的地
     */
    @ApiModelProperty("目的地")
    @Excel(name = "目的地")
    private String arrivalLocation;

    /**
     * 开车时刻
     */
    @ApiModelProperty("开车时刻")
    @Excel(name = "开车时刻")
    private String carStartTime;

    /**
     * 起飞时刻
     */
    @ApiModelProperty("起飞时刻")
    @Excel(name = "起飞时刻")
    private String takeOffTime;

    /**
     * 着陆时刻
     */
    @ApiModelProperty("着陆时刻")
    @Excel(name = "着陆时刻")
    private String landingTime;

    /**
     * 关车时刻
     */
    @ApiModelProperty("关车时刻")
    @Excel(name = "关车时刻")
    private String carStopTime;

    /**
     * 地面时间（分钟）
     */
    @ApiModelProperty("地面时间（分钟）")
    @Excel(name = "地面时间（分钟）")
    private Integer groundTimeMin;

    /**
     * 空中时间（分钟）
     */
    @ApiModelProperty("空中时间（分钟）")
    @Excel(name = "空中时间（分钟）")
    private Integer airTimeMin;

    /**
     * 时间小计（分钟）
     */
    @ApiModelProperty("时间小计（分钟）")
    @Excel(name = "时间小计（分钟）")
    private Integer totalTimeMin;

    /**
     * 架次
     */
    @ApiModelProperty("架次")
    @Excel(name = "架次")
    private Integer sortieCount;

    /**
     * 所属航司代码
     */
    @ApiModelProperty("所属航司代码")
    private String companyCode;

    /**
     * 公司部门code
     */
    @ApiModelProperty("公司部门code")
    private String deptCode;
}