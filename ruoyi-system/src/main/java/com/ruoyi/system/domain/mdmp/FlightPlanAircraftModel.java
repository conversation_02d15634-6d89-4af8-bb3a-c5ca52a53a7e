package com.ruoyi.system.domain.mdmp;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.hibernate.validator.constraints.Range;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;

/**
 * 航班计划关联机型对象 flight_plan_aircraft_model
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@ApiModel(value = "航班计划关联机型对象")
@Data
public class FlightPlanAircraftModel extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键标识
     */
    private Long id;

    /**
     * 机型名称
     */
    @Excel(name = "机型名称")
    @ApiModelProperty(value = "机型名称", example = "787", dataType = "String")
    @NotBlank(message = "机型名称不能为空")
    @Size(min = 0, max = 90, message = "机型名称不能超过90个字符")
    private String aircraftName;

    /**
     * 架数
     */
//    @Excel(name = "架数")
//    @ApiModelProperty(value = "架数", example = "1", dataType = "Integer")
//    @NotNull(message = "架数不能为空")
//    @Size(min = 0, max = 11, message = "架数不能超过11个字符")
//    private Integer aircraftNumber;

    /**
     * 机尾号
     */
    @Excel(name = "机尾号")
    @ApiModelProperty(value = "机尾号", example = "TN-21", dataType = "String")
    @NotBlank(message = "机尾号不能为空")
    @Size(min = 0, max = 255, message = "机尾号不能超过255个字符")
    private String tailNumber;

    /**
     * 呼号
     */
    @Excel(name = "呼号")
    @ApiModelProperty(value = "呼号", example = "TT001", dataType = "String")
    @NotBlank(message = "呼号不能为空")
    private String callsign;

    /**
     * 航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划
     */
    @Excel(name = "航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划")
    @ApiModelProperty(value = "航班计划类型(1,长期计划;2, 次日计划;3,当日放行计划 4 单一飞行计划)", required = true, allowableValues = "1,2,3")
    private Integer planType;

    /**
     * 飞行计划Id
     */
    @Excel(name = "飞行计划Id")
    @ApiModelProperty(value = "飞行计划Id", example = "121", dataType = "Long")
    private Long flightPlanId;


}
