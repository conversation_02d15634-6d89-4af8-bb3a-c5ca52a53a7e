package com.ruoyi.system.domain.oc.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.system.domain.oc.WxQualification;
import com.ruoyi.system.domain.oc.WxUser;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.springframework.beans.BeanUtils;

import java.util.Date;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "飞行员个人信息")
public class PilotInfoVO {

    @ApiModelProperty("用户名")
    private String userName;

    @ApiModelProperty("实力情况")
    private Integer flyTime;

    @ApiModelProperty("机型")
    private String aircraftStyle;

    @ApiModelProperty("执照有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityOfLicense;

    @ApiModelProperty("体检合格证有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityPeriodOfPhysicalExamination;

    @ApiModelProperty("熟练检查基准月")
    private String proficiencyCheckStandardMonth;

    @ApiModelProperty("熟练检查到期日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date proficiencyCheckValidity;

    @ApiModelProperty("机长航线练检查到期日")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityPeriodOfRouteTrainingInspection;

    @ApiModelProperty("危险品训练有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date dangerTrainingInspection;

    @ApiModelProperty("汉语言有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date chineseLanguageValidity;

    @ApiModelProperty("空勤登记证有效期")
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date validityOfRegistrationCertificate;

    public static PilotInfoVO setting(WxUser wxUser, WxQualification wxQualification) {
        PilotInfoVO pilotInfoVO = new PilotInfoVO();
        if (Objects.nonNull(wxQualification)) {
            BeanUtils.copyProperties(wxQualification, pilotInfoVO);
        }
        pilotInfoVO.setUserName(wxUser.getUserName());
        pilotInfoVO.setFlyTime(wxUser.getFlyTime() / 60);
        return pilotInfoVO;
    }
}
