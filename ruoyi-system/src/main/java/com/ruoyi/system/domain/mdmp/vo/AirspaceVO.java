package com.ruoyi.system.domain.mdmp.vo;

import com.ruoyi.common.annotation.Excel;
import com.ruoyi.system.domain.mdmp.Airspace;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "当日有效空域信息返回参数")
public class AirspaceVO {

    /**
     * 空域ID
     */
    @ApiModelProperty("空域ID")
    private Long airspaceId;

    /** 空域名称 */
    @ApiModelProperty("空域名称")
    private String airspaceName;

    public static List<AirspaceVO> setting(List<Airspace> airspaceList) {
        List<AirspaceVO> voList = new ArrayList<>();
        for (Airspace airspace : airspaceList) {
            AirspaceVO vo = new AirspaceVO();
            vo.setAirspaceId(airspace.getId());
            vo.setAirspaceName(airspace.getAirspaceName());
            voList.add(vo);
        }
        return voList;
    }
}
