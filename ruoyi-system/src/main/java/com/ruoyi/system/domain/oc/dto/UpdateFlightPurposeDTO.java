package com.ruoyi.system.domain.oc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "更新任务类型入参")
public class UpdateFlightPurposeDTO {

    @ApiModelProperty("id")
    @NotNull(message = "任务类型ID为空")
    private Long id;

    @ApiModelProperty("名称")
    @NotBlank(message = "任务类型名称为空")
    private String name;
}
