package com.ruoyi.system.domain.mdmp;

import java.math.BigDecimal;
import java.util.List;

import com.ruoyi.system.domain.mdmp.vo.ConflictRoute;
import com.ruoyi.system.domain.mdmp.vo.ConflictWorkVo;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.core.domain.BaseEntity;
import org.hibernate.validator.constraints.Range;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * 航班计划作业区对象 flight_plan_work
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Data
@ApiModel(value = "航班计划作业区对象")
public class FlightPlanWork extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 租户号
     */
    private Long id;

    /**
     * 航班计划id
     */
    @Excel(name = "航班计划id")
    @ApiModelProperty(value = "飞行计划id", example = "13", dataType = "Long")
    private Long flightPlanId;
    @Excel(name = "作业区id")
    @ApiModelProperty(value = "作业区id", example = "13", dataType = "Long")
    private Long workId;

    /**
     * 航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划
     */
    @Excel(name = "航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划")
    @ApiModelProperty(value = "航班计划类型(1,长期计划;2, 次日计划;3,当日放行计划 4 单一飞行计划)", required = true, allowableValues = "1,2,3")
    private Integer planType;

    /**
     * 航班计划类型;1 长期计划 2次日计划 3 当日放行计划 4 单一飞行计划
     */
    @Excel(name = "作业类型;0区域内作业 1沿线作业,2不作业航线")
    @ApiModelProperty(value = "作业类型(0区域内作业 1沿线作业,2不作业航线)", required = true, allowableValues = "0,1,2")
    @Range(min = 0, max = 2, message = "作业类型必须为 0、1、2")
    private Integer workType;


    /**
     * 图形类型;0圆  1点
     */
    @Excel(name = "图形类型;0圆  1点")
    @ApiModelProperty(value = "图形类型(0,圆;1, 点)", required = true, allowableValues = "0,1")
    private Integer graphType;


    /**
     * 作业区偏移量;默认5000米 图形内
     */
    @Excel(name = "作业区偏移量;默认5000米 图形内")
    @ApiModelProperty(value = "作业区偏移量", example = "5000.00", dataType = "BigDecimal")
    private BigDecimal workOffset;


    /**
     * 作业区偏移量类型
     */
    @Excel(name = "作业区偏移量类型;0是向内偏移，1是向外偏移 ")
    @ApiModelProperty(value = "作业区偏移量类型 0是向内偏移，1是向外偏移", example = "0", dataType = "BigDecimal")
    private Integer positiveOrNegative;





    /**
     * 圆心经度
     */
    @Excel(name = "圆心经度")
    @ApiModelProperty(value = "圆心经度", example = "112.23223", dataType = "String")
    private String circleCenterLong;

    /**
     * 圆心纬度
     */
    @Excel(name = "圆心纬度")
    @ApiModelProperty(value = "圆心纬度", example = "22.22123", dataType = "String")
    private String circleCenterLat;

    /**
     * 半径;单位米
     */
    @Excel(name = "半径;单位米")
    @ApiModelProperty(value = "半径", example = "500", dataType = "BigDecimal")
    private BigDecimal radius;

    /**
     * 作业区名称
     */
    @Excel(name = "作业区名称")
    @ApiModelProperty(value = "作业区名称", example = "作业区", dataType = "String")
    @NotBlank(message = "作业区名称不能为空")
    private String workName;


    /**
     * 最低高度;单位米
     */
    @ApiModelProperty("半径;单位米")
    @Excel(name = "最低高度;单位米")
    private BigDecimal minHeight;

    /**
     * 最高高度;单位米
     */
    @ApiModelProperty("半径;单位米")
    @Excel(name = "最高高度;单位米")
    private BigDecimal maxHeight;


    /**
     * 作业区经纬度关联表
     * 作业区内表
     */
    @Excel(name = "作业区经纬度关联表")
    @ApiModelProperty(value = "作业区经纬度关联表")
    private List<FlightPlanWorkLongLat> flightPlanWorkLongLatList;

    @Excel(name = "作业区内表")
    @ApiModelProperty(value = "作业区内表")
    private List<FlightPlanWorkIn> flightPlanWorkInList;


    @ApiModelProperty("冲突作业区")
    private List<ConflictWorkVo> conflictWorkVoList;

    @ApiModelProperty("冲突航线")
    private List<ConflictRoute> conflictRoute;

    @ApiModelProperty(value = "作业区冲突说明", example = "作业区冲突说明", dataType = "String")
    private String workConflictDescription;



    @ApiModelProperty("作业区地图数据")
    private List<WorkMapData> workMapDataList;
}
