package com.ruoyi.system.domain.mdmp;

import com.ruoyi.system.domain.mdmp.dto.AddHeightDTO;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import org.springframework.beans.BeanUtils;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@Accessors(chain = true)
@ApiModel(description = "高度（进离场信息表中添加）")
public class AddHeight {
    /** ID */
    @ApiModelProperty(value = "ID")
    private Integer id ;
    /** 进离场信息表ID */
    @ApiModelProperty(value = "进离场信息表ID")
    private Integer enterLeaveId ;
    /** 属性;0:标准海平面气压, A:修正海平面气压, H:场面气压 */
    @ApiModelProperty(value = "属性(0:标准海平面气压, A:修正海平面气压, H:场面气压)", example = "A")
    private String attribute ;
    /** 高度值 */
    @ApiModelProperty(value = "高度值", example = "0480")
    private String height ;
    /** 高度类型(上升/下降);0:下降, 1:上升 */
    @ApiModelProperty(value = "高度类型(0:下降, 1:上升)", example = "1")
    private Integer heightType ;
    /** 高度是否保持;0:否, 1:保持 */
    @ApiModelProperty(value = "高度是否保持(0:否, 1:保持)", example = "0")
    private Integer keep ;

    public static List<AddHeight> setting(List<AddHeightDTO> params) {
        List<AddHeight> heightList = new ArrayList<>();
        for (AddHeightDTO param : params) {
            AddHeight height = new AddHeight();
            BeanUtils.copyProperties(param, height);
            heightList.add(height);
        }
        return heightList;
    }
}
