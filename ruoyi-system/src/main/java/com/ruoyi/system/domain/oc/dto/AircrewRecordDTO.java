package com.ruoyi.system.domain.oc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(value = "AircrewRecordDTO", description = "修改飞行员记录文件请求参数")
public class AircrewRecordDTO {
    /**
     * 记录编号
     */
    @ApiModelProperty("记录编号")
    private Long aircrewRecordId;

    /**
     * 文件名
     */
    @ApiModelProperty("文件名")
    private String fileName;


    /**
     * 文件简介
     */
    @ApiModelProperty("文件简介")
    private String remark;
}
