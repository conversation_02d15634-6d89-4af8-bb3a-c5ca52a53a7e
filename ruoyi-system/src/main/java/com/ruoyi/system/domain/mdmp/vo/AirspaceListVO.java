package com.ruoyi.system.domain.mdmp.vo;

import com.ruoyi.system.domain.mdmp.Airspace;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
@Data
@ApiModel(description = "空域信息返回参数")
public class AirspaceListVO {


    @ApiModelProperty("空域ID")
    private Long id;


    @ApiModelProperty("空域名称")
    private String airspaceName;

    @ApiModelProperty("颜色类型")
    private Integer colorType;

    @ApiModelProperty("空域类型")
    private Integer airspaceType;


    @ApiModelProperty("空域数据")
    List<AirspaceMapDataListVO> mapDataList;

}
