package com.ruoyi.system.domain.oc.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "更新机场基地列表入参")
public class UpdateAirportBaseDTO {

    @ApiModelProperty(value = "id", required = true)
    @NotNull(message = "机场ID为空")
    private Long id;

    @ApiModelProperty(value = "机场名称", required = true)
    @NotBlank(message = "机场名称ID为空")
    private String name;

    @ApiModelProperty(value = "三字码", required = true)
    @NotBlank(message = "三字码为空")
    private String threeAirportCode;

    @ApiModelProperty("四字码")
    private String fourAirportCode;
}
