package com.ruoyi.system.validator;

import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.system.domain.mdmp.Airspace;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date ：Created in 2024/4/10 11:21
 * @description：
 * @modified By：
 * @version: $
 */
public class AirspaceValidator {
    private static final String INVALID_RADIUS_MESSAGE = "半径不能为空，并且不能为负数";
    private static final String NULL_CIRCLE_CENTER_LONG_MESSAGE = "圆心经度不能为空";
    private static final String NULL_CIRCLE_CENTER_LAT_MESSAGE = "圆心纬度不能为空";
    private static final String NULL_CIRCLE_BENCHMARK_LAT_MESSAGE = "基准点纬度不能为空";
    private static final String NULL_CIRCLE_BENCHMARK_LONG_MESSAGE = "基准点经度不能为空";
    private static final String INVALID_MAGNETIC_BEARING_MESSAGE = "磁方位不能为空，并且不能为负数";
    private static final String INVALID_OFFSET_MESSAGE = "偏移量不能为空，并且不能为负数";
    private static final String INVALID_DISTANCE_MESSAGE = "距离不能为空，并且不能为负数";

    public static void processCircularAirspace(Airspace airspace) {
        validateRadius(airspace);
        validateCircleCenterLong(airspace);
        validateCircleCenterLat(airspace);
        validateOffset(airspace);
        // 如果验证通过，可以继续处理airspace的其他逻辑
    }

    private static void validateRadius(Airspace airspace) {
        BigDecimal radius = airspace.getRadius();
        if (radius == null || radius.compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(INVALID_RADIUS_MESSAGE);
        }
    }

    private static void validateCircleCenterLong(Airspace airspace) {
        if (airspace.getCircleCenterLong() == null || airspace.getCircleCenterLong().isEmpty()) {
            throw new ServiceException(NULL_CIRCLE_CENTER_LONG_MESSAGE);
        }
    }

    private static void validateCircleCenterLat(Airspace airspace) {
        if (airspace.getCircleCenterLat() == null || airspace.getCircleCenterLat().isEmpty()) {
            throw new ServiceException(NULL_CIRCLE_CENTER_LAT_MESSAGE);
        }
    }


    private static void validateOffset(Airspace airspace) {
        BigDecimal offset = airspace.getAirspaceOffset();
        if (offset == null || offset.compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(INVALID_OFFSET_MESSAGE);
        }
    }

    private static void validateMagneticBearing(Airspace airspace) {
        BigDecimal magneticBearing = airspace.getMagneticBearing();
        if (magneticBearing == null || magneticBearing.compareTo(BigDecimal.ZERO) < 0) {
            throw new ServiceException(INVALID_MAGNETIC_BEARING_MESSAGE);
        }
    }


    private static void validateDistance(Airspace airspace) {
        BigDecimal distance = airspace.getDistance();
        if (distance == null || distance.compareTo(BigDecimal.ZERO) <= 0) {
            throw new ServiceException(INVALID_DISTANCE_MESSAGE);
        }
    }

    public static void processCircularObstacles(Airspace airspace) {
        // 处理障碍物逻辑
        if (airspace.getBenchmarkLong() == null || airspace.getBenchmarkLong().isEmpty()) {
            throw new ServiceException(NULL_CIRCLE_BENCHMARK_LONG_MESSAGE);
        } else if (airspace.getBenchmarkLat() == null || airspace.getBenchmarkLat().isEmpty()) {
            throw new ServiceException(NULL_CIRCLE_BENCHMARK_LAT_MESSAGE);
        }
        validateDistance(airspace);
        validateRadius(airspace);
        validateMagneticBearing(airspace);
    }
}
