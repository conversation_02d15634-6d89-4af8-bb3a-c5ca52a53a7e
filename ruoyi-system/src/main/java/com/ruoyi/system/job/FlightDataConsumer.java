package com.ruoyi.system.job;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.config.websocket.CustomWebSocketHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.amqp.core.AmqpTemplate;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.jms.DeliveryMode;
import java.util.*;


/**
 * <AUTHOR>
 */

@Component
@RequiredArgsConstructor
public class FlightDataConsumer {
    private static final Logger logger = LoggerFactory.getLogger(FlightDataConsumer.class);

    private final CustomWebSocketHandler webSocketHandler;
    private final AmqpTemplate amqpTemplate;

    @RabbitListener(queues = "receiveAbsFlightData.queue", concurrency = "1-5")
    public void receiveQueue(String message) {
        // 1. 解析消息并获取基础数据
        WebsocketFlightData flightData = JSON.parseObject(message, WebsocketFlightData.class);
        // 2. 第一次发送：基础数据（无告警）
        flightData.setSendTime(DateUtils.getTime());
        Map<String, Object> initialMap = new HashMap<>();
        initialMap.put("flightData", flightData);
        initialMap.put("historyData", new ArrayList<>());
        webSocketHandler.broadcastMessage(JSON.toJSONString(initialMap));
        //异步处理
        amqpTemplate.convertAndSend("uac-abs-async.data","", JSON.toJSONString(flightData));
    }

//    @JmsListener(destination = "receiveAbsFlightDataSH.queue", containerFactory = "flightDataJmsFactory")
//    public void receiveQueueSH(String message) {
//        // 1. 解析消息并获取基础数据
//        WebsocketFlightData flightData = JSON.parseObject(message, WebsocketFlightData.class);
//        logger.info("接收到GPS数据坐标数据...");
//        // 2. 第一次发送：基础数据（无告警）
//        Map<String, Object> initialMap = new HashMap<>();
//        initialMap.put("flightData", flightData);
//        initialMap.put("historyData", new ArrayList<>());
//        webSocketHandler.broadcastMessage(JSON.toJSONString(initialMap));
//    }
}
