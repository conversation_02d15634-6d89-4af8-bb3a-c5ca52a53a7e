package com.ruoyi.system.job;

import ch.qos.logback.classic.LoggerContext;
import ch.qos.logback.core.util.StatusPrinter;
import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.config.rule.HomeMapDataRuleConfig;
import com.ruoyi.common.config.websocket.CustomWebSocketHandler;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import com.ruoyi.system.domain.mdmp.vo.QueryMapDataLongLatVo;
import com.ruoyi.system.domain.type.AlarmType;
import com.ruoyi.system.mapper.mdmp.*;
import com.ruoyi.system.service.mdmp.IFlightAlertsService;
import com.ruoyi.system.service.mdmp.IMapDataService;
import com.ruoyi.system.util.App;
import com.ruoyi.system.util.CircularUtil;
import com.ruoyi.system.util.NearestAnyTimestamp;
import com.ruoyi.system.util.ThreadPoolManager;
import lombok.RequiredArgsConstructor;
import org.locationtech.jts.geom.Coordinate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.amqp.rabbit.annotation.RabbitListener;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */

@Component
@RequiredArgsConstructor
public class AsynchronousConsumer {

    private static final Logger dynamicLogger = LoggerFactory.getLogger("DYNAMIC_LOGGER");


    private static final Logger log = LoggerFactory.getLogger(AsynchronousConsumer.class);

    private final FlightDataMapper flightDataMapper;
    private final CustomWebSocketHandler webSocketHandler;

    @Resource
    private IMapDataService mapDataService;

    @Resource
    private IFlightAlertsService flightAlertsService;


    @Resource
    private HomeMapDataRuleConfig homeMapRule;


    @Resource
    @Qualifier("threadPoolTaskExecutor") // 对应步骤1配置的线程池Bean
    private ThreadPoolTaskExecutor taskExecutor;
    //处理历史轨迹的线程
    private static final ExecutorService historicalTrackExecutor = Executors.newCachedThreadPool();
    //回调发送socket消息的线程
    private static final ExecutorService callbackExecutor = Executors.newCachedThreadPool();

    @Resource
    private FlightAlertsMapper flightAlertsMapper;

    @Resource
    private AircraftMpMapper aircraftMpMapper;

    @Resource
    private AirspaceMapper airspaceMapper;


    // 配置全局线程池（替代路由线程池）
    private final ExecutorService globalExecutor = Executors.newFixedThreadPool(100); // 根据CPU核数调整


    @RabbitListener(queues = "asynchronousProcessing.queue", concurrency = "1-5")
    public void receiveQueue(String message) {
        // 仅做快速反序列化
        WebsocketFlightData flightData = JSON.parseObject(message, WebsocketFlightData.class);

        // 全局线程池异步处理
        globalExecutor.submit(() -> {
            String tailNumber = flightData.getAircraftReg();
            String safeDirName = normalizeDirName(tailNumber);

            try (MDC.MDCCloseable ignored = MDC.putCloseable("logFileName", safeDirName)) {
                dynamicLogger.info("收到消息: {}", JSON.toJSONString(flightData));
                processFlightData(flightData);
                dynamicLogger.info("处理完成: {}", flightData.getUniqueId());
            }
        });
    }

    private String normalizeDirName(String rawName) {
        return rawName.replaceAll("[^a-zA-Z0-9_]", "_").replaceAll("^[^a-zA-Z_]+", "reg_");
    }

    //    @JmsListener(destination = "asynchronousProcessing.queue", containerFactory = "flightDataJmsFactory")
//    public void receiveQueue(String message) {
//
//        // 1. 解析消息并获取基础数据
//        WebsocketFlightData flightData = JSON.parseObject(message, WebsocketFlightData.class);
//        getMapData(flightData);
//        // 2. 异步处理告警（多线程）
//        if (flightData.getMapData() != null) {
//            List<FlightAlerts> allFlightAlerts = processAlertsInParallel(flightData);
//            if (!CollectionUtils.isEmpty(allFlightAlerts)) {
//                FlightData flightData2 = new FlightData();
//                BeanUtils.copyProperties(flightData, flightData2);
//                flightData2.setFlightAlertsList(allFlightAlerts);
//                flightData2.setLongitude(null);
//                flightData2.setLatitude(null);
//                flightData2.setElevation(null);
//                // 第三次发送：完整数据（包含告警）
//                Map<String, Object> finalMap = new HashMap<>();
//                finalMap.put("flightData", flightData2);
//                finalMap.put("historyData", new ArrayList<>());
//                webSocketHandler.broadcastMessage2(JSON.toJSONString(finalMap));
//            }
//        }
//    }


//    @RabbitListener(queues = "asynchronousProcessing.queue", concurrency = "1-5")
//    public void receiveQueue(String message) {
//        log.error("参数:" + message);
//        WebsocketFlightData flightData = JSON.parseObject(message, WebsocketFlightData.class);
//        // 获取机尾号作为路由键 (示例字段，根据实际字段名调整)
//        String tailNumber = flightData.getAircraftReg();
//        // 将任务提交到路由线程池
//        ThreadPoolManager.submitByKey(tailNumber, () -> {
//            try {
//               // log.error("参数:" + flightData.getUniqueId() + "," + JSON.toJSONString(flightData));
//                // 安全目录名：只允许字母、数字、下划线
//                String safeDirName = flightData.getAircraftReg()
//                        .replaceAll("[^a-zA-Z0-9_]", "_")   // 特殊字符替换
//                        .replaceAll("^[0-9]+", "reg_");      // 防止数字开头
//
//                MDC.clear();
//                MDC.put("logFileName", safeDirName);  // 现在这是目录名
//                dynamicLogger.info("参数:" + JSON.toJSONString(flightData));
//                processFlightData(flightData);
//                dynamicLogger.info("告警信息:" + JSON.toJSONString(flightData));
//
//                // ...其他处理逻辑...
//            } finally {
//                MDC.remove("logFileName");
//            }
//        });
//    }

    private void processFlightData(WebsocketFlightData flightData) {
        getMapData(flightData);
        // 异步处理告警
        log.info("开始ID" + flightData.getUniqueId() + "开始时间:" + DateUtils.getCurrentUTCTime());
        processAlertsInParallel(flightData);
        log.info("结束ID" + flightData.getUniqueId() + "结束时间:" + DateUtils.getCurrentUTCTime());

        // 组装最终数据
        flightData.setLongitude(null);
        flightData.setLatitude(null);
        flightData.setElevation(null);
        // 发送WebSocket消息
        Map<String, Object> finalMap = new HashMap<>();
        finalMap.put("flightData", flightData);
        finalMap.put("historyData", new ArrayList<>());
        log.error(JSON.toJSONString(finalMap));
        webSocketHandler.broadcastMessage2(JSON.toJSONString(finalMap));
    }


    /**
     * 多线程处理告警逻辑
     */
    private HashSet<FlightAlerts> processAlertsInParallel(WebsocketFlightData flightData) {
        // 获取最大版本号
        Long maxVersion = flightAlertsMapper.selectMaxVersion(flightData.getAircraftReg());
        if (maxVersion == null) {
            maxVersion = 0L;
        }
        HashSet<FlightAlerts> result = new HashSet<>();
        // 获取未发送的老告警
        List<FlightAlerts> oldAlerts = flightAlertsService.getOldAlerts(flightData, maxVersion);
        // 使用流操作分割列表
        Map<Boolean, List<FlightAlerts>> partitioned = oldAlerts.stream().collect(Collectors.partitioningBy(alert -> alert.getAlertType() == 8));
        List<FlightAlerts> type8Alerts = partitioned.get(true);     // alertType=8的列表
        List<FlightAlerts> nonType8Alerts = partitioned.get(false); // alertType≠8的列表
        result.addAll(nonType8Alerts);
        // 标记为已发送
        if (!oldAlerts.isEmpty()) {
            flightAlertsService.markAlertsAsSent(oldAlerts);
        }
        //获取未发送的连线取消
        Set<FlightAlerts> existingSet = flightData.getConnectionInfoList();
        if (existingSet == null) {
            existingSet = new HashSet<>();
        }
        existingSet.addAll(type8Alerts);
        flightData.setConnectionInfoList(existingSet);
        //判断飞机是否在电子围栏内

        //查询电子围栏
        Airspace airspace = airspaceMapper.selectAirspaceById(47L);
        if (airspace == null || airspace.getAirspaceLongLatList().isEmpty()) {
            dynamicLogger.info("查询电子围栏为空==========");
            return result;
        }
        Coordinate[] coordinates = new Coordinate[airspace.getAirspaceLongLatList().size()];
        airspace.getAirspaceLongLatList().forEach(e -> {
            coordinates[airspace.getAirspaceLongLatList().indexOf(e)] = new Coordinate(e.getDoubleLongitude(), e.getDoubleLatitude());
        });
        App app = new App();
        //  boolean range = app.judgeContains(flightData.getLongitude().doubleValue(), flightData.getLatitude().doubleValue(), coordinates);
        //double range = CircularUtil.distance(flightData.getLongitude().doubleValue(), flightData.getLatitude().doubleValue(), 85.92, 44.24);
        //  if (range) {
        // result.addAll(flightAlertsService.getAlerts(flightData, maxVersion));
        result.addAll(flightAlertsService.getFlightAlerts(flightData, maxVersion));
        //   result.addAll(flightAlertsService.getUnregisteredAlerts(flightData, maxVersion));
//        List<AircraftMp> list = aircraftMpMapper.getAircraftMpListByAircraftReg(flightData.getAircraftReg());
//        for (AircraftMp aircraftMp : list) {
//            long timestamp = NearestAnyTimestamp.getNearestTimestamp();
//            Integer alarmType = aircraftMp.getAlarmType();
//            List<FlightAlerts> alerts;
//            if (alarmType == AlarmType.WIND) {
//                alerts = flightAlertsService.getUAndVAlerts(timestamp, aircraftMp, flightData, maxVersion);
//            } else if (alarmType == AlarmType.RAINFALL) {
//                alerts = flightAlertsService.getRainfallAlerts(timestamp, aircraftMp, flightData, maxVersion);
//            } else if (alarmType == AlarmType.VISIBILITY) {
//                alerts = flightAlertsService.getVisibilityAlerts(timestamp, aircraftMp, flightData, maxVersion);
//            } else if (alarmType == AlarmType.TEMPERATURE) {
//                alerts = flightAlertsService.getTemperatureAlerts(timestamp, aircraftMp, flightData, maxVersion);
//            } else if (alarmType == AlarmType.THUNDERSTORMS) {
//                alerts = flightAlertsService.getThunderstormAlerts(timestamp, aircraftMp, flightData, maxVersion);
//            } else {
//                alerts = Collections.emptyList();
//            }
//            result.addAll(alerts);
        //  }
        //}
        flightData.setFlightAlertsList(result);
        return result;

        // 并行任务容器（使用泛型明确结果类型）
//        List<CompletableFuture<List<FlightAlerts>>> allFutures = new ArrayList<>();
//
//        // 1. 单独处理旧告警任务（第一个元素）
//        CompletableFuture<List<FlightAlerts>> oldAlertsFuture = CompletableFuture.supplyAsync(
//                () -> flightAlertsService.getOldAlerts(flightData, maxVersion),
//                taskExecutor
//        );
//        allFutures.add(oldAlertsFuture);
//        if (flightData.getMapData() != null) {
//            // 2. 并行处理基础告警任务
//            allFutures.add(CompletableFuture.supplyAsync(
//                    () -> flightAlertsService.getAlerts(flightData, maxVersion),
//                    taskExecutor
//            ));
//
//            // 3. 其他常规告警
//            allFutures.add(CompletableFuture.supplyAsync(
//                    () -> flightAlertsService.getFlightAlerts(flightData, maxVersion),
//                    taskExecutor
//            ));
//
//            // 4. 未注册告警分支
//            allFutures.add(CompletableFuture.supplyAsync(() -> {
//                List<FlightAlerts> unregistered = flightAlertsService.getUnregisteredAlerts(flightData, maxVersion);
//                return (null == unregistered || unregistered.isEmpty())
//                        ? flightAlertsService.getNoFlightPlanAlerts(flightData, maxVersion)
//                        : unregistered;
//            }, taskExecutor));
//
//            // 提交任务：AircraftMp 相关告警
//        CompletableFuture<?>[] futuresArray = allFutures.toArray(new CompletableFuture[0]);
//        return CompletableFuture.allOf(futuresArray)
//                .thenApplyAsync(v -> {
//                    List<FlightAlerts> result = new ArrayList<>();
//                    result.addAll(oldAlertsFuture.join());
//                    allFutures.stream()
//                            .skip(1)
//                            .map(CompletableFuture::join)
//                            .forEach(result::addAll);
//                    return result;
//                }, taskExecutor).join();
    }


    /**
     * 通过经纬度高度获取是在哪个格子
     */
    private WebsocketFlightData getMapData(WebsocketFlightData flightData) {
        QueryMapDataLongLatVo param = new QueryMapDataLongLatVo();
        param.setLatitude(flightData.getLatitude());
        param.setLongitude(flightData.getLongitude());
        param.setHeight(flightData.getElevation());
        MapData mapData = mapDataService.mapData(param);
        flightData.setMapData(mapData);
        return flightData;
    }

    /**
     * 获取当前飞机的历史轨迹
     */
    private List<FlightData> getFlightData(WebsocketFlightData flightData) {
        return flightDataMapper.selectByFlightDateAndAircraftReg(flightData.getFlightDate(), flightData.getAircraftReg(), flightData.getDynamicTime());
    }
}
