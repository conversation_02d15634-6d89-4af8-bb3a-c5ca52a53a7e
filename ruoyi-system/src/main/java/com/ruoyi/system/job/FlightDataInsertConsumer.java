package com.ruoyi.system.job;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.FlightData;
import com.ruoyi.system.mapper.mdmp.DailyFlightPlanMapper;
import com.ruoyi.system.mapper.mdmp.FlightDataMapper;
import com.ruoyi.system.mapper.mdmp.FlightPlanAircraftModelMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.jms.core.JmsTemplate;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
@RequiredArgsConstructor
public class FlightDataInsertConsumer {

    private final FlightDataMapper flightDataMapper;
    private final JmsTemplate jmsTemplate;
    private final DailyFlightPlanMapper dailyFlightPlanMapper;
    private final FlightPlanAircraftModelMapper flightPlanAircraftModelMapper;

    @JmsListener(destination = "insertDatabase.queue", containerFactory = "flightDataJmsFactory")
    public void receiveQueue(String message) {

        JSONObject jsonObject = JSON.parseObject(message, JSONObject.class);
        if (Objects.isNull(jsonObject)) {
            return;
        }
        //坐标数据入库
        FlightData flightData = jsonObject.getObject("flightData", FlightData.class);
        flightData.setDynamicTime(DateUtils.getHms());
        flightDataMapper.insertFlightData(flightData);
        Long flightPlanId = dailyFlightPlanMapper.selectByFlightDateAndTailNumber(flightData.getFlightDate(), flightData.getAircraftReg()).getId();
        String callSign = flightPlanAircraftModelMapper.selectByFlightPlanIdAndTailNumber(flightPlanId, flightData.getAircraftReg()).getCallsign();
        flightData.setFlightPlanId(flightPlanId);
        flightData.setCallSign(callSign);
        flightDataMapper.updateFlightData(flightData);

        //发给无人机系统
        jmsTemplate.convertAndSend("receiveAbsFlightData.queue", JSON.toJSONString(flightData));
        System.out.println(JSON.toJSONString(flightData));
    }
}
