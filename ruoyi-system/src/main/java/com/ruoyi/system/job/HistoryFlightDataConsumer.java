package com.ruoyi.system.job;

import com.alibaba.fastjson2.JSON;
import com.ruoyi.common.config.websocket.CustomWebSocketHandler;
import com.ruoyi.system.domain.mdmp.FlightData;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import com.ruoyi.system.mapper.mdmp.FlightDataMapper;
import lombok.RequiredArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.jms.annotation.JmsListener;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * <AUTHOR>
 */

@Component
@RequiredArgsConstructor
public class HistoryFlightDataConsumer {
    private static final Logger logger = LoggerFactory.getLogger(HistoryFlightDataConsumer.class);

    private final FlightDataMapper flightDataMapper;
    private final CustomWebSocketHandler webSocketHandler;
    private String temporaryAircraftReg = "";

    @Resource
    @Qualifier("threadPoolTaskExecutor2")
    private ThreadPoolTaskExecutor taskExecutor2;

//    @JmsListener(destination = "historyFlightData.queue", containerFactory = "flightDataJmsFactory")
    public void receiveQueue(String message) {

        // 1. 解析消息并获取基础数据
        WebsocketFlightData flightData = JSON.parseObject(message, WebsocketFlightData.class);

//        CompletableFuture
//                // 提交任务并返回结果
//                .supplyAsync(() -> getFlightData(flightData), historicalTrackExecutor)
//                // 处理结果并发送消息（使用任务返回的值）
//                .thenAcceptAsync(result -> {
//                    if (!CollectionUtils.isEmpty(result)) {
//                        // 第二次发送：加上历史轨迹
//                        Map<String, Object> finalMap = new HashMap<>();
//                        FlightData flightData1 = new FlightData();
//                        BeanUtils.copyProperties(flightData, flightData1);
//                        flightData1.setLongitude(null);
//                        flightData1.setLatitude(null);
//                        flightData1.setElevation(null);
//                        finalMap.put("flightData", flightData1);
//                        finalMap.put("historyData", result);
//                        webSocketHandler.broadcastMessage2(JSON.toJSONString(finalMap));
//                    }
//                }, callbackExecutor)
//                // 异常处理：任务失败时发送错误消息
//                .exceptionally(ex -> {
//                    logger.error("获取历史轨迹异常:[{}]", ex.getMessage());
//                    return null;
//                });

        if (StringUtils.hasText(temporaryAircraftReg)) {
            CompletableFuture.runAsync(() -> {
                List<FlightData> result = getFlightData(flightData);
                if (!CollectionUtils.isEmpty(result)) {
                    // 第二次发送：加上历史轨迹
                    Map<String, Object> finalMap = new HashMap<>();
                    FlightData flightData1 = new FlightData();
                    BeanUtils.copyProperties(flightData, flightData1);
                    flightData1.setLongitude(null);
                    flightData1.setLatitude(null);
                    flightData1.setElevation(null);
                    finalMap.put("flightData", flightData1);
                    finalMap.put("historyData", result);
                    webSocketHandler.broadcastMessage2(JSON.toJSONString(finalMap));
                }
            }, taskExecutor2); // taskExecutor 是注入的线程池 Bean
        }




    }
    /**
     * 获取当前飞机的历史轨迹
     */
    private List<FlightData> getFlightData(WebsocketFlightData flightData) {
        logger.info("当前临时飞机机尾号[{}]", temporaryAircraftReg);
        return flightDataMapper.selectByFlightDateAndAircraftReg(flightData.getFlightDate(), temporaryAircraftReg, flightData.getDynamicTime());
    }


    public void getTemporaryAircraftReg(Integer status, String temporaryAircraftReg) {
        if (status == 1) {
            this.temporaryAircraftReg = temporaryAircraftReg;
            logger.info("指定飞机:[{}]历史轨迹", temporaryAircraftReg);
        }else {
            if (temporaryAircraftReg.equals(this.temporaryAircraftReg)) {
                this.temporaryAircraftReg = "";
            }
        }

    }
}
