package com.ruoyi.web.controller.mdmp;


import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.redis.RedisCache;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.mdmp.Airspace;
import com.ruoyi.system.domain.mdmp.MapData;
import com.ruoyi.system.domain.mdmp.TopographicMapData;
import com.ruoyi.system.domain.mdmp.vo.*;
import com.ruoyi.system.mapper.mdmp.TemperatureMpDataMapper;
import com.ruoyi.system.param.mdmp.MapDataParam;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.service.mdmp.*;
import com.ruoyi.system.util.Point3D;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@RestController
@Api(value = "地图数据管理", tags = "地图数据管理")
@RequestMapping("/mdmp/mapData")
public class MapDataController extends BaseController {


    @Resource
    private IMapDataService mapDataService;

    @Resource
    private ITopographicMapDataService topographicMapDataService;

    @Resource
    private IUMpDataService iuMpDataService;


    @Resource
    private IVMpDataService ivMpDataService;


    @Resource
    private IAirspaceMapDataService iAirspaceMapDataService;

    @Resource
    private ITopographicMapDataService iTopographicMapDataService;


    @Resource
    private ITemperatureMpDataService iTemperatureMpDataService;

    @Resource
    private IVisibilityMpDataService iVisibilityMpDataService;

    @Resource
    private IRainfallMpDataService iRainfallMpDataService;


    /**
     * 获取首页地图默认数据 地图生成规则和障碍物
     */
    @Log(title = "获取首页地图默认数据 地图生成规则和障碍物", businessType = BusinessType.INSERT)
    @PostMapping("/homeMapRule")
    @ApiOperation("获取首页地图默认数据 地图生成规则和障碍物")
    public AjaxResult homeMapRule() {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        return success(mapDataService.homeMapRule(deptCodeList));
    }


    /**
     * 航路规划
     * 根据两个点 生成航线 避开空域
     */
    @Log(title = "航路规划 根据两个点 生成航线 避开空域", businessType = BusinessType.INSERT)
    @PostMapping("/routePlanning")
    @ApiOperation("航路规划 根据两个点 生成航线 避开空域")
    public AjaxResult routePlanning(@Validated @RequestBody QueryRoutePlanningVo routePlanningVo) {
        return success(mapDataService.routePlanning(routePlanningVo,getLoginUser().getDeptCodeList()));
    }


    /**
     * 查询地图数据
     */
    @PreAuthorize("@ss.hasPermi('system:mapData:list')")
    @GetMapping("/list")
    @ApiOperation("查询地图数据")
    public TableDataInfo list() {
//        startPage();
//        QueryMapDataVo queryMapDataVo = new QueryMapDataVo();
//        queryMapDataVo.setStartHeight(1000);
//        queryMapDataVo.setEndHeight(3000);
//        List<MapDataLongLatVo> mapDataLongLatVoList = new ArrayList<>();
//        MapDataLongLatVo mapDataLongLatVo1 = new MapDataLongLatVo();
//        mapDataLongLatVo1.setLongitude(new BigDecimal(85.37));
//        mapDataLongLatVo1.setLatitude(new BigDecimal(44.60));
//        mapDataLongLatVo1.setSortNumber(1);
//
//        MapDataLongLatVo mapDataLongLatVo2 = new MapDataLongLatVo();
//        mapDataLongLatVo2.setLongitude(new BigDecimal(85.36));
//        mapDataLongLatVo2.setLatitude(new BigDecimal(44.34));
//        mapDataLongLatVo2.setSortNumber(2);
//
//        MapDataLongLatVo mapDataLongLatVo3 = new MapDataLongLatVo();
//        mapDataLongLatVo3.setLongitude(new BigDecimal(86.08));
//        mapDataLongLatVo3.setLatitude(new BigDecimal(44.32));
//        mapDataLongLatVo3.setSortNumber(3);
//
//        MapDataLongLatVo mapDataLongLatVo4 = new MapDataLongLatVo();
//        mapDataLongLatVo4.setLongitude(new BigDecimal(86.01));
//        mapDataLongLatVo4.setLatitude(new BigDecimal(44.63));
//        mapDataLongLatVo4.setSortNumber(4);
//
//        mapDataLongLatVoList.add(mapDataLongLatVo1);
//        mapDataLongLatVoList.add(mapDataLongLatVo2);
//        mapDataLongLatVoList.add(mapDataLongLatVo3);
//        mapDataLongLatVoList.add(mapDataLongLatVo4);
//        queryMapDataVo.setMapDataLongLatVoList(mapDataLongLatVoList);
//

//        List<MapData> list = mapDataService.queryMapData(queryMapDataVo);
        List<MapData> list = mapDataService.selectMapDataList();
        return getDataTable(list);
    }


    /**
     * 新增地图数据
     */
    @Log(title = "新增地图数据", businessType = BusinessType.INSERT)
    @PostMapping("/add")
    @ApiOperation("新增地图数据")
    public AjaxResult add() {

        return toAjax(mapDataService.insertMapData());
    }


    @Log(title = "删除地图数据", businessType = BusinessType.INSERT)
    @PostMapping("/delete")
    @ApiOperation("删除地图数据")
    public AjaxResult delete() {

        return toAjax(mapDataService.deleteMapData());
    }

    @Log(title = "查询区域内的地图数据", businessType = BusinessType.INSERT)
    @PostMapping("/query/map")
    @ApiOperation("查询区域内的地图数据")
    public TableDataInfo queryMapData(@Validated @RequestBody QueryMapDataVo queryMapDataVo) {
        return getDataTable(mapDataService.queryMapData(queryMapDataVo));
    }

    /**
     * 查询空域
     */
    @ApiOperation("查询格子")
    @PostMapping("/mapData")
    public AjaxResult mapData(@RequestBody QueryMapDataLongLatVo param) {
        MapData mapData = mapDataService.mapData(param);
        return success(mapData);
    }


    @ApiOperation("新增地形")
    @PostMapping("/add/topographic")
    public AjaxResult addTopographic(@RequestBody List<AddTopographicVo> param) {
        int topographic = mapDataService.topographic(param);
        return success(topographic);
    }

    @ApiOperation("查询地形")
    @PostMapping("/query/topographic")
    public AjaxResult queryTopographic() {
        List<TopographicMapData> list = topographicMapDataService.selectTopographicMapDataList(new TopographicMapData());
        return success(list);
    }

    @ApiOperation("修改地形")
    @PostMapping("/edit/topographic")
    public AjaxResult editTopographic(@RequestBody List<TopographicMapData> param) {
        int topographic = topographicMapDataService.editTopographic(param);
        return success(topographic);
    }

    @ApiOperation("根据格子index查询格子信息")
    @GetMapping("/getMeteInfo")
    public CommonResult<MeteInfo> getMeteInfo(MeteInfoParam meteInfoParam) throws IOException {
        MeteInfo meteInfo = mapDataService.getMeteInfo(meteInfoParam);
//        double u = iuMpDataService.selectUMpDataByMeteInfo(meteInfoParam);
//        double v = ivMpDataService.selectVMpDataByMeteInfo(meteInfoParam);
//        meteInfo.setU(u);
//        meteInfo.setV(v);
        int stateCode = iAirspaceMapDataService.selectAirspaceMapData(meteInfoParam);
        if (stateCode == 0) {
            //查询地形
            stateCode = iTopographicMapDataService.selectTopographicMapData(meteInfoParam);
        }
        meteInfo.setStateCode(stateCode);
//        double temperature = iTemperatureMpDataService.selectTemperatureMpDataByMeteInfo(meteInfoParam);
//        meteInfo.setTemperature(temperature);
//        double visibility = iVisibilityMpDataService.selectVisibilityMpDataByMeteInfo(meteInfoParam);
//        meteInfo.setVisibility(visibility);
//        double rainfall = iRainfallMpDataService.selectRainfallMpDataByMeteInfo(meteInfoParam);
//        meteInfo.setRainfall(rainfall);


//        QueryMapDataLongLatVo param = new QueryMapDataLongLatVo();
//        param.setLongitude(new BigDecimal(Double.toString(meteInfoParam.getLongitude())));
//        param.setLatitude(new BigDecimal(Double.toString(meteInfoParam.getLatitude())));
//        param.setHeight(meteInfoParam.getAltitude());
//        MapData mapData = mapDataService.getMapData(param);
//        if (null != mapData)
//            meteInfo.setIndex(mapData.getIndex());

        return CommonResult.success(meteInfo);
    }

}
