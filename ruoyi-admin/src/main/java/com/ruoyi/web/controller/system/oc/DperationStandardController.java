package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.oc.entity.DperationStandard;
import com.ruoyi.system.service.oc.DperationStandardService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/24 12:01
 * @mood 功能
 */
@Api("运行标准管理")
@RestController
@RequestMapping("/system/dperationStandard")
public class DperationStandardController extends BaseController {
    @Resource
    private DperationStandardService dperationStandardService;

    /**
     * 查询运行标准列表
     */
    @ApiOperation("查询运行标准列表")
    @PreAuthorize("@ss.hasPermi('system:dperationStandard:list')")
    @GetMapping("/list")
    public List<?> list(DperationStandard param) {
        List<DperationStandard> list = dperationStandardService.selectList(param);
        return list;
    }

    /**
     * 查询单个运行标准
     */
    @ApiOperation("查询运行标准列表")
    @GetMapping("/getOne/{id}")
    public AjaxResult selectOne(@PathVariable("id") Long id) {
        DperationStandard dperationStandard = dperationStandardService.selectOneById(id);
        return AjaxResult.success(dperationStandard);
    }

    /**
     * 新增运行标准
     */
    @ApiOperation("新增运行标准")
    @PostMapping("/add")
    public AjaxResult insertOne(@RequestBody DperationStandard param) {
        return toAjax(dperationStandardService.insertOne(param));
    }

    /**
     * 修改运行标准
     */
    @ApiOperation("修改运行标准")
    @PostMapping("/update")
    public AjaxResult updateOne(@RequestBody DperationStandard param) {
        return toAjax(dperationStandardService.updateOne(param));
    }

    /**
     * 删除运行标准
     */
    @ApiOperation("删除运行标准")
    @PostMapping("/delete/{ids}")
    public AjaxResult deleteAll(@PathVariable Long[] ids) {
        return toAjax(dperationStandardService.deleteAllByIds(ids));
    }
}
