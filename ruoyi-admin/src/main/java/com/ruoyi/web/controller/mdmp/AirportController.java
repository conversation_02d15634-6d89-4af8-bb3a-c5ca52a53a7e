package com.ruoyi.web.controller.mdmp;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.annotation.Anonymous;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.mdmp.Airport;
import com.ruoyi.system.service.mdmp.IAirportService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 机场Controller
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@Anonymous
@Api(value = "机场信息管理", tags = "机场信息管理")
@RestController
@RequestMapping("/mdmp/airport")
public class AirportController extends BaseController {
    @Autowired
    private IAirportService airportService;


    /**
     * 查询机场列表
     */
    @ApiOperation("查询机场列表")
    //@PreAuthorize("@ss.hasPermi('mdmp:airport:list')")
    @GetMapping("/list")
    public TableDataInfo list(Airport airport) {
        startPage();
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        airport.setDeptCodeList(deptCodeList);
        List<Airport> list = airportService.selectAirportList(airport);
        return getDataTable(list);
    }

    /**
     * 导出机场列表
     */
    // @PreAuthorize("@ss.hasPermi('mdmp:airport:export')")
    @Log(title = "机场", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Airport airport) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        airport.setDeptCodeList(deptCodeList);
        List<Airport> list = airportService.selectAirportList(airport);
        ExcelUtil<Airport> util = new ExcelUtil<Airport>(Airport.class);
        util.exportExcel(response, list, "机场数据");
    }

    /**
     * 获取机场详细信息
     */
    @ApiOperation("获取机场详细信息")
    // @PreAuthorize("@ss.hasPermi('mdmp:airport:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(airportService.selectAirportById(id));
    }

    /**
     * 新增机场
     */
    @ApiOperation("新增机场")
    // @PreAuthorize("@ss.hasPermi('mdmp:airport:add')")
    @Log(title = "机场", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody Airport airport) {
        airport.setDeptCode(getLoginUser().getDeptCode());
        return toAjax(airportService.insertAirport(airport));
    }

    /**
     * 修改机场
     */
    @ApiOperation("修改机场")
    // @PreAuthorize("@ss.hasPermi('mdmp:airport:edit')")
    @Log(title = "机场", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody Airport airport) {
        return toAjax(airportService.updateAirport(airport));
    }

    /**
     * 删除机场
     */
    @ApiOperation("删除机场")
    //  @PreAuthorize("@ss.hasPermi('mdmp:airport:remove')")
    @Log(title = "机场", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(airportService.deleteAirportById(id));
    }
}
