package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.system.domain.oc.dto.AircrewRecordDTO;
import com.ruoyi.system.domain.oc.dto.PilotDetailDTO;
import com.ruoyi.system.domain.oc.dto.PilotQualificationDTO;
import com.ruoyi.system.domain.oc.dto.QueryPilotQualificationDTO;
import com.ruoyi.system.domain.oc.vo.PilotDetailVO;
import com.ruoyi.system.domain.oc.vo.PilotQualificationVO;
import com.ruoyi.system.service.oc.PilotService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * 首页
 *
 * <AUTHOR>
 */
@Api("飞行员管理")
@Validated
@RestController
@RequestMapping("/system/pilot")
public class PilotController extends BaseController {

    @Resource
    private PilotService pilotService;

    /**
     * 飞行员资质查询
     */
    @ApiOperation("飞行员资质查询")
    @GetMapping("/queryQualification")
    public TableDataInfo getPilotQualification(QueryPilotQualificationDTO dto, @RequestHeader("CompanyCode") String companyCode) {
        startPage();
        List<PilotQualificationVO> voList = pilotService.getPilotQualification(dto, companyCode);
        return getDataTable(voList);
    }

    /**
     * 飞行员资质编辑
     */
    @ApiOperation("飞行员资质编辑")
    @PostMapping("/editQualification")
    public AjaxResult editQualification(@RequestBody PilotQualificationDTO dto) {
        return toAjax(pilotService.editQualification(dto));
    }

    /**
     * 飞行员详情
     */
    @ApiOperation("飞行员详情")
    @PostMapping("/pilotDetails")
    public CommonResult<PilotDetailVO> pilotDetails(@RequestBody PilotDetailDTO dto) {
        logger.info("查询飞行员详情");
        return pilotService.pilotDetails(dto);
    }

    /**
     * 飞行员记录文件上传
     */
    @ApiOperation("飞行员记录文件上传")
    @PostMapping("/uploadAircrewRecord")
    public AjaxResult uploadAircrewRecord(@RequestParam(value = "file") MultipartFile[] files,
                                          @RequestParam("userId") @NotNull(message = "用户不能为空") Long userId,
                                          @RequestParam("firstFileType") @NotNull(message = "文件类型不能为空") Integer firstFileType,
                                          @RequestParam("secondFileType") @NotNull(message = "文件类型不能为空") Integer secondFileType,
                                          @RequestParam("remark") String remark,
                                          @RequestHeader("authId") Long authId) {
        return pilotService.uploadAircrewRecord(files, userId, firstFileType, secondFileType, remark, authId);
    }

    /**
     * 飞行员记录查询
     */
    @ApiOperation("飞行员记录查询")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "userId", value = "用户ID", required = true, dataType = "Long", dataTypeClass = Long.class),
            @ApiImplicitParam(name = "firstFileType", value = "文件第一类型(1:技术文档, 2:满足条款要求记录, 3:措施记录)", required = true, dataType = "Integer", dataTypeClass = Integer.class)
    })
    @GetMapping("/queryAircrewRecord/{userId}/{firstFileType}")
    public AjaxResult queryAircrewRecord(@PathVariable("userId") Long userId,
                                         @PathVariable("firstFileType") Integer firstFileType) {
        return pilotService.queryAircrewRecord(userId, firstFileType);
    }

    /**
     * 飞行员记录查询（单个）
     */
    @ApiOperation("飞行员记录查询（单个）")
    @ApiImplicitParam(name = "aircrewRecordId", value = "飞行员记录ID", required = true, dataType = "Long", dataTypeClass = Long.class)
    @PostMapping("/selectAircrewRecord/{aircrewRecordId}")
    public AjaxResult selectAircrewRecord(@PathVariable("aircrewRecordId") Long aircrewRecordId) {
        return pilotService.selectAircrewRecord(aircrewRecordId);
    }

    /**
     * 飞行员记录修改
     */
    @ApiOperation("飞行员记录修改")
    @PostMapping("/updateAircrewRecord")
    public AjaxResult updateAircrewRecord(@RequestBody AircrewRecordDTO aircrewRecordDTO,
                                          @RequestHeader("authId") Long authId) {
        return toAjax(pilotService.updateAircrewRecord(aircrewRecordDTO, authId));
    }

    /**
     * 飞行员记录删除
     */
    @ApiOperation("飞行员记录删除")
    @PostMapping("/deleteAircrewRecord/{aircrewRecordId}")
    public AjaxResult deleteAircrewRecord(@PathVariable("aircrewRecordId") Long aircrewRecordId) {
        return toAjax(pilotService.deleteAircrewRecord(aircrewRecordId));
    }

    /**
     * 文件预览
     */
    @ApiOperation("文件预览")
    @PostMapping("/filePreview/{aircrewRecordId}")
    public AjaxResult filePreview(@PathVariable("aircrewRecordId") Long aircrewRecordId) {
        return pilotService.filePreview(aircrewRecordId);
    }

    /**
     * IO流读取文件
     */
    @ApiOperation("IO流读取文件")
    @ApiImplicitParams({
            @ApiImplicitParam(name = "fileType", value = "文件类型(后缀名)", required = true, dataType = "String", dataTypeClass = String.class),
            @ApiImplicitParam(name = "newFileName", value = "文件名", required = true, dataType = "String", dataTypeClass = String.class)})
    @GetMapping("/file/{fileType}/{newFileName}")
    public void getFilePreview(HttpServletResponse response,
                               @PathVariable("fileType") String fileType,
                               @PathVariable("newFileName") String newFileName) {
        pilotService.getFilePreview(response, fileType, newFileName);
    }

    /**
     * 飞行员记录文件下载
     */
    @ApiOperation("飞行员记录文件下载")
    @ApiImplicitParam(name = "aircrewRecordId", value = "飞行员记录ID", required = true, dataType = "Long", dataTypeClass = Long.class)
    @GetMapping("/downloadAircrewRecord/{aircrewRecordId}")
    public void downloadAircrewRecord(@PathVariable("aircrewRecordId") Long aircrewRecordId,
                                      HttpServletResponse response) {
        pilotService.downloadAircrewRecord(aircrewRecordId, response);
    }
}
