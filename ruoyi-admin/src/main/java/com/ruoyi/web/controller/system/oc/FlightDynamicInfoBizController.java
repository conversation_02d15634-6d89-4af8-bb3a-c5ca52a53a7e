package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.service.oc.IFlightDynamicInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 动态信息Controller
 *
 * <AUTHOR>
 */
@Api("动态信息管理")
@RestController
@RequestMapping("/system/flightDynamicInfoBiz")
@Slf4j
public class FlightDynamicInfoBizController extends BaseController {
    
    @Resource
    private IFlightDynamicInfoService flightDynamicInfoService;

    /**
     * 获取动态信息详细信息
     */
    @ApiOperation("获取动态信息详细信息")
    @PreAuthorize("@ss.hasPermi('system:flightDynamicInfo:query')")
    @GetMapping(value = "/selectFlightDynamicInfoById")
    public AjaxResult selectFlightDynamicInfoById(@RequestHeader("companyCode") String companyCode, 
                                                  @RequestParam("flightTaskBookId") Integer flightTaskBookId) {
        return success(flightDynamicInfoService.selectFlightDynamicInfoById(flightTaskBookId, companyCode));
    }

    /**
     * 导出动态信息Word文档
     */
    @ApiOperation("导出动态信息Word文档")
    @PreAuthorize("@ss.hasPermi('system:flightDynamicInfo:exportWord')")
    @GetMapping("/exportWord/{flightTaskBookId}")
    public void exportDynamicInfoWord(@ApiIgnore HttpServletResponse response, 
                                      @PathVariable("flightTaskBookId") Integer flightTaskBookId, 
                                      @RequestHeader("companyCode") String companyCode) {
        try {
            log.info("开始导出动态信息Word文档，导出日期：{}, 任务书id:{}", DateUtils.getDate(), flightTaskBookId);
            flightDynamicInfoService.exportDynamicInfoWord(response, flightTaskBookId, companyCode);
            log.info("动态信息Word文档导出完成");
        } catch (Exception e) {
            log.error("导出动态信息Word文档失败", e);
            throw new ServiceException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 导出动态信息PDF文档
     */
    @ApiOperation("导出动态信息PDF文档")
    @PreAuthorize("@ss.hasPermi('system:flightDynamicInfo:exportPdf')")
    @GetMapping("/exportPdf/{flightTaskBookId}")
    public void exportDynamicInfoPdf(@ApiIgnore HttpServletResponse response, 
                                     @PathVariable("flightTaskBookId") Integer flightTaskBookId, 
                                     @RequestHeader("companyCode") String companyCode) {
        try {
            log.info("开始导出动态信息PDF文档，导出日期：{}, 任务书id:{}", DateUtils.getDate(), flightTaskBookId);
            flightDynamicInfoService.exportDynamicInfoPdf(response, flightTaskBookId, companyCode);
            log.info("动态信息PDF文档导出完成");
        } catch (Exception e) {
            log.error("导出动态信息PDF文档失败", e);
            throw new ServiceException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 批量导出动态信息PDF文档
     */
    @ApiOperation("批量导出动态信息PDF文档")
    @PreAuthorize("@ss.hasPermi('system:flightDynamicInfo:exportPdfBatch')")
    @PostMapping("/exportPdfBatch")
    public void exportDynamicInfoPdfBatch(@ApiIgnore HttpServletResponse response, 
                                          @RequestBody List<Integer> flightTaskBookIds, 
                                          @RequestHeader("companyCode") String companyCode) {
        try {
            log.info("开始批量导出动态信息PDF文档，导出日期：{}, 任务书数量:{}", DateUtils.getDate(), flightTaskBookIds.size());
            flightDynamicInfoService.exportDynamicInfoPdfBatch(response, flightTaskBookIds, companyCode);
            log.info("批量动态信息PDF文档导出完成");
        } catch (Exception e) {
            log.error("批量导出动态信息PDF文档失败", e);
            throw new ServiceException("导出失败：" + e.getMessage());
        }
    }
}
