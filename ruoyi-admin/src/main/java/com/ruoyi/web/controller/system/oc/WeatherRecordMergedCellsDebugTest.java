package com.ruoyi.web.controller.system.oc;

import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.util.word.WeatherRecordWordUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 气象信息记录页表格整合调试测试类
 * 整合能见度和QNH列，优化表格结构，调整公章位置
 *
 * <AUTHOR>
 * @date 2025-01-30
 */
public class WeatherRecordMergedCellsDebugTest {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherRecordMergedCellsDebugTest.class);
    
    public static void main(String[] args) {
        WeatherRecordMergedCellsDebugTest test = new WeatherRecordMergedCellsDebugTest();
        test.runMergedCellsDebugTest();
    }
    
    /**
     * 运行表格整合调试测试
     */
    public void runMergedCellsDebugTest() {
        logger.info("开始运行气象信息记录页表格整合调试测试");
        
        try {
            // 1. 检查模板文件
            checkTemplateFile();
            
            // 2. 创建测试数据
            WordMeteorologicalVo testData = createMergedCellsDebugTestData();
            
            // 3. 生成文档
            byte[] documentBytes = generateDocument(testData);
            
            // 4. 保存文档
            String outputPath = saveDocument(documentBytes);
            
            // 5. 验证结果
            verifyDocument(outputPath, documentBytes.length);
            
            logger.info("表格整合调试测试完成！");
            printSuccessInfo(outputPath);

        } catch (Exception e) {
            logger.error("表格整合调试测试失败", e);
            printErrorInfo(e);
        }
    }
    
    /**
     * 检查模板文件
     */
    private void checkTemplateFile() throws Exception {
        String templatePath = "ruoyi-admin/src/main/resources/word/气象信息和动态信息模板.docx";
        Path path = Paths.get(templatePath);
        
        if (!Files.exists(path)) {
            throw new Exception("模板文件不存在: " + templatePath);
        }
        
        logger.info("模板文件检查通过: {}", templatePath);
    }
    
    /**
     * 创建表格整合调试测试数据
     */
    private WordMeteorologicalVo createMergedCellsDebugTestData() {
        logger.info("创建表格整合调试测试数据");
        
        WordMeteorologicalVo data = new WordMeteorologicalVo();
        
        // 基本信息
        data.setAircraftType("BELL429");
        data.setRegistrationNumber("B-7613");
        data.setFlightDate("2025-05-20");
        data.setFuel("740b");
        
        // 统计信息
        data.setGroundTimeMinTotal("11");
        data.setAirTimeMinTotal("51");
        data.setTotalTimeMinTotal("62");
        data.setSortieCountTotal("3");
        
        // 始发地气象信息
        List<WordFlightWeatherInfoVo> departureWeatherList = new ArrayList<>();
        WordFlightWeatherInfoVo departureWeather = new WordFlightWeatherInfoVo();
        departureWeather.setBatch("1");
        departureWeather.setLocationName("星野");
        departureWeather.setWeather("晴天");
        departureWeather.setCloudHeight("无影响");
        departureWeather.setTemperature("26");
        departureWeather.setWindDirection("160");
        departureWeather.setWindSpeed("4");
        departureWeather.setVisibility("9999");
        departureWeather.setQnh("1004");
        departureWeatherList.add(departureWeather);
        data.setDepartureWeatherInfoList(departureWeatherList);
        
        // 目的地气象信息
        List<WordFlightWeatherInfoVo> arrivalWeatherList = new ArrayList<>();
        WordFlightWeatherInfoVo arrivalWeather = new WordFlightWeatherInfoVo();
        arrivalWeather.setBatch("1");
        arrivalWeather.setLocationName("星野");
        arrivalWeather.setWeather("晴天");
        arrivalWeather.setCloudHeight("无影响");
        arrivalWeather.setTemperature("26");
        arrivalWeather.setWindDirection("160");
        arrivalWeather.setWindSpeed("4");
        arrivalWeather.setVisibility("9999");
        arrivalWeather.setQnh("1004");
        arrivalWeatherList.add(arrivalWeather);
        data.setArrivalWeatherInfoList(arrivalWeatherList);
        
        // 动态信息
        List<WordFlightWeatherDynamicVo> dynamicInfoList = new ArrayList<>();
        
        // 第一行动态数据
        WordFlightWeatherDynamicVo dynamic1 = new WordFlightWeatherDynamicVo();
        dynamic1.setBatch("1");
        dynamic1.setDepartureLocation("星野");
        dynamic1.setArrivalLocation("星野");
        dynamic1.setCarStartTime("19:43");
        dynamic1.setTakeOffTime("19:47");
        dynamic1.setLandingTime("20:03");
        dynamic1.setCarStopTime("20:05");
        dynamic1.setGroundTimeMin("6");
        dynamic1.setAirTimeMin("16");
        dynamic1.setTotalTimeMin("22");
        dynamic1.setSortieCount("1");
        dynamicInfoList.add(dynamic1);
        
        // 第二行动态数据
        WordFlightWeatherDynamicVo dynamic2 = new WordFlightWeatherDynamicVo();
        dynamic2.setBatch("2");
        dynamic2.setDepartureLocation("星野");
        dynamic2.setArrivalLocation("星野");
        dynamic2.setCarStartTime("20:37");
        dynamic2.setTakeOffTime("20:40");
        dynamic2.setLandingTime("21:15");
        dynamic2.setCarStopTime("21:17");
        dynamic2.setGroundTimeMin("5");
        dynamic2.setAirTimeMin("35");
        dynamic2.setTotalTimeMin("40");
        dynamic2.setSortieCount("2");
        dynamicInfoList.add(dynamic2);
        
        data.setDynamicInfoList(dynamicInfoList);
        
        logger.info("表格整合调试测试数据创建完成");
        return data;
    }
    
    /**
     * 生成文档
     */
    private byte[] generateDocument(WordMeteorologicalVo data) throws Exception {
        logger.info("开始生成Word文档 - 表格整合调试版本");
        
        String templatePath = "ruoyi-admin/src/main/resources/word/气象信息和动态信息模板.docx";
        String sealPath = "ruoyi-admin/src/main/resources/word/公章.png";
        
        // 检查公章文件是否存在
        if (!Files.exists(Paths.get(sealPath))) {
            logger.warn("公章文件不存在，将跳过公章添加: {}", sealPath);
            sealPath = null;
        }
        
        byte[] documentBytes = WeatherRecordWordUtils.generateWeatherRecordDocument(templatePath, data, sealPath);
        
        logger.info("Word文档生成完成，大小: {} 字节", documentBytes.length);
        return documentBytes;
    }
    
    /**
     * 保存文档
     */
    private String saveDocument(byte[] documentBytes) throws IOException {
        String outputDir = "D:/temp/weather_record_table_integration_test/";
        
        // 创建输出目录
        Path dirPath = Paths.get(outputDir);
        try {
            if (!Files.exists(dirPath)) {
                Files.createDirectories(dirPath);
                logger.info("创建输出目录: {}", outputDir);
            }
        } catch (IOException e) {
            logger.warn("无法创建目录 {}, 使用当前目录", outputDir);
            outputDir = "./";
        }
        
        String fileName = "weather_record_table_integration_" + System.currentTimeMillis() + ".docx";
        String outputPath = outputDir + fileName;
        
        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            fos.write(documentBytes);
            fos.flush();
        }
        
        logger.info("文档已保存到: {}", outputPath);
        return outputPath;
    }
    
    /**
     * 验证文档
     */
    private void verifyDocument(String outputPath, int documentSize) throws Exception {
        logger.info("验证生成的文档...");
        
        // 检查文件是否存在
        Path filePath = Paths.get(outputPath);
        if (!Files.exists(filePath)) {
            throw new Exception("文档文件不存在: " + outputPath);
        }
        
        // 检查文件大小
        try {
            long fileSize = Files.size(filePath);
            logger.info("文件大小验证: 内存中 {} 字节, 磁盘上 {} 字节", documentSize, fileSize);
            
            if (fileSize < 1024) {
                throw new Exception("文档文件可能太小，可能生成有问题");
            }
            
            logger.info("文档验证通过");
            
        } catch (IOException e) {
            throw new Exception("无法获取文件大小", e);
        }
    }
    
    /**
     * 打印成功信息
     */
    private void printSuccessInfo(String outputPath) {
        System.out.println("\n=== ✨ 表格整合调试测试完成 ✨ ===");
        System.out.println();
        System.out.println("生成的文档位置：" + outputPath);
        System.out.println();
        System.out.println("🔧 当前测试方案：");
        System.out.println("✓ 智能合并方案：仅对气象信息行进行合并");
        System.out.println("✓ 气象信息行：第8-9列（能见度）和第10-11列（QNH）合并");
        System.out.println("✓ 动态信息行：保持独立列结构，不进行合并");

        System.out.println("✓ 判断逻辑：根据行内容智能识别行类型");
        System.out.println();
        System.out.println("📊 气象信息表格结构（11列-代码合并）：");
        System.out.println("1. 批次");
        System.out.println("2. 始发地/目的地");
        System.out.println("3. 天气");
        System.out.println("4. 云高(m)");
        System.out.println("5. 温度(℃)");
        System.out.println("6. 风向(°)");
        System.out.println("7. 风速(m/s)");
        System.out.println("8. 能见度(m) - 主列（有数据）");
        System.out.println("9. [空列] - 将与第8列合并");
        System.out.println("10. QNH(hPa) - 主列（有数据）");
        System.out.println("11. [空列] - 将与第10列合并");
        System.out.println();
        System.out.println("🎯 期望结果：");
        System.out.println("- ✅ 气象信息行：第8-9列（能见度）和第10-11列（QNH）合并");
        System.out.println("- ✅ 动态信息行：所有列保持独立，不进行合并");

        System.out.println("- ✅ 智能识别：自动区分气象行和动态行");
        System.out.println();
        System.out.println("🔍 验证要点：");
        System.out.println("1. 检查控制台中的'已为新行设置合并单元格'日志");
        System.out.println("2. 确认第8列有能见度数据，第9列为空");
        System.out.println("3. 确认第10列有QNH数据，第11列为空");
        System.out.println("4. 验证新插入的数据行第8-9列是否合并");
        System.out.println("5. 验证新插入的数据行第10-11列是否合并");

        System.out.println("6. 对比表头合并效果与数据行合并效果");
        System.out.println();
        System.out.println("✨ 智能合并方案优势：");
        System.out.println("- 智能识别不同类型的数据行");
        System.out.println("- 只对需要合并的行进行处理");
        System.out.println("- 保持动态信息行的原有结构");
        System.out.println("- 避免误合并导致的显示问题");
        System.out.println();
        System.out.println("请打开生成的Word文档验证最终结果！");
        System.out.println("重点检查气象行和动态行是否按预期分别处理，合并效果是否正确。");
        System.out.println();
    }
    
    /**
     * 打印错误信息
     */
    private void printErrorInfo(Exception e) {
        System.err.println("\n=== ❌ 表格整合调试测试失败 ===");
        System.err.println();
        System.err.println("错误信息: " + e.getMessage());
        System.err.println();
        System.err.println("调试建议：");
        System.err.println("1. 检查控制台中的数据构建日志");
        System.err.println("2. 确认表格列数和结构");
        System.err.println("3. 验证合并单元格处理逻辑");
        System.err.println("4. 检查数据插入过程");
        System.err.println();
    }
}
