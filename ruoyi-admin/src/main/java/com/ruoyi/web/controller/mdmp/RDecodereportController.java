package com.ruoyi.web.controller.mdmp;

/**
 * <AUTHOR>
 * @date ：Created in 2025/5/20 10:08
 * @description：
 * @modified By：
 * @version: $
 */

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.mdmp.vo.DecodeReportVo;
import com.ruoyi.system.domain.mdmp.vo.RawReportVo;
import com.ruoyi.system.domain.mdmp.vo.RouteCoordinateVO;
import com.ruoyi.system.param.mdmp.MonitorRouteParams;
import com.ruoyi.system.service.mdmp.IRDecodereportService;
import io.swagger.annotations.Api;

import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;


@Api(value = "报文管理", tags = "报文管理")
@RestController
@RequestMapping("/mdmp/report")
public class RDecodereportController {

    @Resource
    private IRDecodereportService irDecodereportService;

    @ApiOperation("查询红绿灯")
    @PostMapping("/query")
    public CommonResult<RawReportVo> query() {
        RawReportVo rawReportVo = new RawReportVo();
        rawReportVo.setDecodeReportVoList(irDecodereportService.selectRDecodereportList());
        rawReportVo.setContentList(irDecodereportService.selectNewContentList());
        return CommonResult.success(rawReportVo);
    }
}
