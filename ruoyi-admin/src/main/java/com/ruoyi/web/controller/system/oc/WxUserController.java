package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.system.domain.oc.WxUser;
import com.ruoyi.system.domain.oc.dto.UpdateWxUserDTO;
import com.ruoyi.system.domain.oc.dto.WxUserDTO;
import com.ruoyi.system.domain.oc.vo.CrewUserVO;
import com.ruoyi.system.domain.oc.vo.CrewVO;
import com.ruoyi.system.domain.oc.vo.WxUserInfoVO;
import com.ruoyi.system.domain.oc.vo.WxUserVO;
import com.ruoyi.system.service.oc.WxUserService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.util.List;
import java.util.Map;

/**
 * 微信用户管理
 *
 * <AUTHOR>
 */
@Api("微信用户管理")
@RestController
@RequestMapping("/system/wxUser/")
public class WxUserController extends BaseController {
    @Resource
    private WxUserService wxUserService;

    /**
     * 查询角色对应的微信用户(添加航班计划时)
     */
    @ApiOperation("查询角色对应的微信用户")
    @GetMapping("/listUserByRole")
    public CommonResult<Map<String, List<CrewUserVO>>> listUserByRole(@RequestHeader("companyCode") String companyCode) {
        return wxUserService.listUserByRole(companyCode);
    }

    /**
     * 获取机组人员信息
     */
    @ApiOperation("获取机组人员信息")
    @GetMapping("/userArr/list/{userIds}/{flightPlanId}")
    public CommonResult<List<CrewVO>> getUserArr(@PathVariable("userIds") Long[] userIds,
                                                 @PathVariable("flightPlanId") Long flightPlanId) {
        return wxUserService.getUserArr(userIds, flightPlanId);
    }

    /**
     * 获取飞行员详细信息
     */
    @ApiOperation("获取飞行员详细信息（包括资质）")
    @GetMapping("/getInfo/{userId}/{roleKey}")
    public CommonResult<CrewVO> getInfo(@PathVariable("userId") Long userId, @PathVariable("roleKey") String roleKey) {
        return wxUserService.getInfo(userId, roleKey);
    }

    /**
     * 查询微信用户列表
     */
    @ApiOperation("查询微信用户列表")
    @GetMapping("/list")
    public PageCommonResult<List<WxUserVO>> list(WxUser wxUser, @RequestHeader("companyCode") String companyCode) {
        Integer pageNum = TableSupport.buildPageRequest().getPageNum();
        Integer pageSize = TableSupport.buildPageRequest().getPageSize();
        return wxUserService.list(wxUser, companyCode, pageNum, pageSize);
    }

    /**
     * 查询微信用户详情
     */
    @ApiOperation("查询微信用户详情")
    @GetMapping("/selectOne")
    public CommonResult<WxUserInfoVO> selectOne(WxUserDTO dto) {
        return wxUserService.selectOne(dto);
    }

    /**
     * 修改微信用户
     */
    @ApiOperation("修改微信用户")
    @PostMapping("/update")
    public CommonResult<String> update(@RequestBody UpdateWxUserDTO dto) {
        return wxUserService.updateWxUser(dto);
    }

    /**
     * IO流读取图片
     */
    @ApiOperation("读取头像图片")
    @ApiImplicitParam(name = "fileNameAndSuffix", value = "文件名+后缀", required = true, dataType = "String", dataTypeClass = String.class)
    @GetMapping("/img/{openId}/{fileNameAndSuffix}")
    public void getAvatar(HttpServletResponse response,
                          @PathVariable("openId") String openId,
                          @PathVariable("fileNameAndSuffix") String fileNameAndSuffix) {
        logger.info("IO流读取用户头像图片");
        wxUserService.getAvatar(response, openId, fileNameAndSuffix);
    }
}
