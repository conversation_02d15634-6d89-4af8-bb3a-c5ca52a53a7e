package com.ruoyi.web.controller.mdmp;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.LongTermFlightPlan;
import com.ruoyi.system.domain.mdmp.NextDayFlightPlan;
import com.ruoyi.system.param.mdmp.NexDayFlightPlanListParam;
import com.ruoyi.system.service.mdmp.ILongTermFlightPlanService;
import com.ruoyi.system.service.mdmp.INextDayFlightPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 次日计划飞行Controller
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@RestController
@Api(value = "A次日飞行计划管理", tags = "A次日飞行计划管理")
@RequestMapping("/mdmp/nextDayFlightPlan")
public class NextDayFlightPlanController extends BaseController {
    @Autowired
    private INextDayFlightPlanService nextDayFlightPlanService;

    @Autowired
    private ILongTermFlightPlanService longTermFlightPlanService;

    /**
     * 查询次日计划飞行列表
     */
    //@PreAuthorize("@ss.hasPermi('system:plan:list')")
    @GetMapping("/list")
    @ApiOperation("查询次日计划飞行列表")
    public TableDataInfo list(NexDayFlightPlanListParam param) {
        startPage();
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        param.setDeptCodeList(deptCodeList);
        List<NextDayFlightPlan> list = nextDayFlightPlanService.selectNextDayFlightPlanList(param);
        return getDataTable(list);
    }

    /**
     * 导出次日计划飞行列表
     */
    //@PreAuthorize("@ss.hasPermi('system:plan:export')")
    @Log(title = "次日计划飞行", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出次日计划飞行列表")
    public void export(HttpServletResponse response, NexDayFlightPlanListParam param) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        param.setDeptCodeList(deptCodeList);
        List<NextDayFlightPlan> list = nextDayFlightPlanService.selectNextDayFlightPlanList(param);
        ExcelUtil<NextDayFlightPlan> util = new ExcelUtil<NextDayFlightPlan>(NextDayFlightPlan.class);
        util.exportExcel(response, list, "次日计划飞行数据");
    }

    /**
     * 获取次日计划飞行详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:plan:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation("获取次日计划飞行详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(nextDayFlightPlanService.selectNextDayFlightPlanById(id));
    }


    /**
     * 验证次日计划飞行
     */
    //@PreAuthorize("@ss.hasPermi('system:plan:verify')")
    @Log(title = "验证次日计划飞行")
    @PostMapping(value = "/verify")
    @ApiOperation("验证次日计划飞行")
    public AjaxResult verifyFlightPlan(@Validated @RequestBody NextDayFlightPlan nextDayFlightPlan) {
        return success(nextDayFlightPlanService.verifyNextDayFlightPlan(nextDayFlightPlan));
    }


    /**
     * 验证次日计划飞行 根据id获取冲突
     */
    //@PreAuthorize("@ss.hasPermi('system:plan:verifyById')")
    @Log(title = "验证次日计划飞行 根据id获取冲突")
    @GetMapping(value = "/verifyById/{id}")
    @ApiOperation("验证次日计划飞行 根据id获取冲突")
    public AjaxResult verifyNextDayFlightPlan(@PathVariable("id") Long id) {
        return success(longTermFlightPlanService.verifyLongTermFlightPlan(nextDayFlightPlanService.selectNextDayFlightPlanById(id)));
    }


    /**
     * 新增次日计划飞行
     */
    //@PreAuthorize("@ss.hasPermi('system:plan:add')")
    @Log(title = "次日计划飞行", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增次日计划飞行")
    public AjaxResult add(@Validated  @RequestBody NextDayFlightPlan nextDayFlightPlan) {
        nextDayFlightPlan.setCreator(getUsername());
        nextDayFlightPlan.setCreationTime(DateUtils.getTime());
        nextDayFlightPlan.setDeptCode(getLoginUser().getDeptCode());
        return toAjax(nextDayFlightPlanService.insertNextDayFlightPlan(nextDayFlightPlan));
    }

    /**
     * 修改次日计划飞行
     */
    //@PreAuthorize("@ss.hasPermi('system:plan:edit')")
    @Log(title = "次日计划飞行", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改次日计划飞行")
    public AjaxResult edit(@Validated @RequestBody NextDayFlightPlan nextDayFlightPlan) {
        return toAjax(nextDayFlightPlanService.updateNextDayFlightPlan(nextDayFlightPlan));
    }

    /**
     * 删除次日计划飞行
     */
    //@PreAuthorize("@ss.hasPermi('system:plan:remove')")
    @Log(title = "次日计划飞行", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除次日计划飞行")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(nextDayFlightPlanService.deleteNextDayFlightPlanByIds(ids));
    }

    /**
     * 审核次日飞行计划
     */
    //@PreAuthorize("@ss.hasPermi('system:plan:auditing')")
    @Log(title = "审核次日飞行计划", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/auditing")
    @ApiOperation("审核次日飞行计划")
    public AjaxResult auditing(@RequestBody NextDayFlightPlan nextDayFlightPlan) {
        return toAjax(nextDayFlightPlanService.auditing(nextDayFlightPlan));
    }
}
