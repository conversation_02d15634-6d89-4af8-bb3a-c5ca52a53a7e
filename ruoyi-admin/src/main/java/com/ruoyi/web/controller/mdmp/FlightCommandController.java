package com.ruoyi.web.controller.mdmp;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.mdmp.FlightCommandLog;
import com.ruoyi.system.domain.mdmp.dto.FlightCommandDTO;
import com.ruoyi.system.domain.mdmp.dto.PushToDroneSystemDTO;
import com.ruoyi.system.domain.mdmp.dto.QueryFlightCommandLogDTO;
import com.ruoyi.system.service.mdmp.FlightCommandService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;


/**
 * <AUTHOR>
 */
@Api(value = "电子进程单指令管理", tags = "电子进程单指令管理")
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/mdmp/flightCommand")
public class FlightCommandController {

    private final FlightCommandService flightCommandService;

    @ApiOperation("接收无人机系统飞行指令执行结果")
    @PostMapping("/executionResult")
    public CommonResult<String> executionResult(@RequestBody FlightCommandDTO dto) {
        return flightCommandService.executionResult(dto);
    }

    @ApiOperation("有无人机前端查询指令消息")
    @PostMapping("/query")
    public CommonResult<List<FlightCommandLog>> query(@RequestBody QueryFlightCommandLogDTO dto) {
        return flightCommandService.query(dto);
    }

    @ApiOperation("生成指令消息并推送给无人机")
    @PostMapping("/pushToDroneSystem")
    public CommonResult<String> pushToDroneSystem(@RequestBody PushToDroneSystemDTO dto) {
        return flightCommandService.pushToDroneSystem(dto);
    }
}
