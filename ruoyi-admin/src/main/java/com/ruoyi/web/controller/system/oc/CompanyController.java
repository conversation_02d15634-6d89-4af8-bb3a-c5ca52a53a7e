package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.constant.UserConstants;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.system.domain.oc.Company;
import com.ruoyi.system.domain.oc.CompanyMenu;
import com.ruoyi.system.domain.oc.dto.*;
import com.ruoyi.system.service.ISysUserService;
import com.ruoyi.system.service.oc.CompanyService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/18 10:07
 * @mood 功能
 */
@Api("公司管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/company")
public class CompanyController extends BaseController {
    @Resource
    private CompanyService companyService;
    @Resource
    private ISysUserService userService;

    /**
     * 查询公司信息列表
     */
    @ApiOperation("查询公司信息列表")
    @GetMapping("/list")
    public PageCommonResult<List<Company>> list(Company company) {
        Integer pageNum = TableSupport.buildPageRequest().getPageNum();
        Integer pageSize = TableSupport.buildPageRequest().getPageSize();
        return companyService.selectList(company, pageNum, pageSize);
    }

    /**
     * 查询单个公司信息
     */
    @ApiOperation("查询单个公司信息")
    @PostMapping("/companyInfo")
    public CommonResult<Company> companyInfo(@RequestBody CompanyDTO dto) {
        return companyService.companyInfo(dto);
    }

    /**
     * 添加公司信息
     */
    @ApiOperation("添加公司信息")
    @PostMapping("/add")
    public CommonResult<String> addCompany(@RequestBody AddCompanyDTO dto) {
        return companyService.addCompany(dto);
    }

    /**
     * 修改公司信息
     */
    @ApiOperation("修改公司信息")
    @PostMapping("/update")
    public CommonResult<String> updateCompany(@RequestBody UpdateCompanyDTO dto) {
        return companyService.updateCompany(dto);
    }

    /**
     * 删除公司信息
     */
    @ApiOperation("删除公司信息")
    @PostMapping("/delete")
    public CommonResult<String> deleteCompany(@RequestBody CompanyDTO dto) {
        return companyService.deleteCompany(dto);
    }

    /**
     * 查询公司的菜单权限
     */
    @ApiOperation("查询公司的菜单权限")
    @PostMapping("/selectCompanyMenu")
    public CommonResult<List<CompanyMenu>> selectCompanyMenu(@RequestBody CompanyMenuDTO dto) {
        return companyService.selectCompanyMenu(dto);
    }

    /**
     * 添加公司菜单权限
     */
    @ApiOperation("添加公司菜单权限")
    @PostMapping("/addCompanyMenu")
    public CommonResult<String> addCompanyMenu(@RequestBody AddCompanyMenuDTO dto) {
        return companyService.addCompanyMenu(dto);
    }

    /**
     * 航司初始化管理员
     */
    @ApiOperation("初始化管理员")
    @PostMapping("/initCompanyAdmin")
    public CommonResult<String> initCompanyAdmin(@RequestBody InitCompanyAdminDTO dto) {
        if (UserConstants.NOT_UNIQUE == userService.checkUserNameUnique(dto.getUserName())) {
            return CommonResult.error("新增用户'" + dto.getUserName() + "'失败，登录账号已存在");
        }
        return companyService.initCompanyAdmin(dto);
    }
}
