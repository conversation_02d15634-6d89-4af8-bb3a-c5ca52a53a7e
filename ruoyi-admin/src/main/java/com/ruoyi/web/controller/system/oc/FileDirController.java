package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.oc.FileDir;
import com.ruoyi.system.mapper.oc.FileDirMapper;
import com.ruoyi.system.service.oc.IFileDirService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * 文件目录Controller
 *
 * <AUTHOR>
 * @date 2021-12-23
 */
@Api(tags = "文件目录")
@RestController
@RequestMapping("/system/dir")
public class FileDirController extends BaseController {
    @Autowired
    private IFileDirService fileDirService;

    @Resource
    private FileDirMapper fileDirMapper;

    /**
     * 查询文件目录列表
     */
    @PreAuthorize("@ss.hasPermi('system:dir:list')")
    @GetMapping("/list")
    public TableDataInfo list(FileDir fileDir) {
        startPage();
        List<FileDir> list = fileDirService.selectFileDirList(fileDir);
        return getDataTable(list);
    }

    /**
     * 导出文件目录列表
     */
    @PreAuthorize("@ss.hasPermi('system:dir:export')")
    @Log(title = "文件目录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(FileDir fileDir) {
        List<FileDir> list = fileDirService.selectFileDirList(fileDir);
        ExcelUtil<FileDir> util = new ExcelUtil<FileDir>(FileDir.class);
        return util.exportExcel(list, "文件目录数据");
    }

    /**
     * 获取文件目录详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:dir:query')")
    @GetMapping(value = "/getInfo/{fileDirId}")
    public AjaxResult getInfo(@PathVariable("fileDirId") Long fileDirId) {
        return AjaxResult.success(fileDirService.selectFileDirByFileDirId(fileDirId));
    }

    /**
     * 新增文件目录
     */
    @PreAuthorize("@ss.hasPermi('system:dir:add')")
    @Log(title = "文件目录", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@RequestBody FileDir fileDir) {
        return toAjax(fileDirService.insertFileDir(fileDir));
    }

    /**
     * 修改文件目录
     */
    @PreAuthorize("@ss.hasPermi('system:dir:edit')")
    @Log(title = "文件目录", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@RequestBody FileDir fileDir) {
        return toAjax(fileDirService.updateFileDir(fileDir));
    }

    /**
     * 删除文件目录
     */
    @PreAuthorize("@ss.hasPermi('system:dir:remove')")
    @Log(title = "文件目录", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{fileDirIds}")
    public AjaxResult remove(@PathVariable Long[] fileDirIds) {
        return toAjax(fileDirService.deleteFileDirByFileDirIds(fileDirIds));
    }


    @ApiOperation("用于树的展现")
    @GetMapping("/treeListDir")
    public AjaxResult treeListDir() {
        FileDir fileDir = new FileDir();
        fileDir.setParentId(0L);
        List<FileDir> fileDirs = fileDirMapper.selectFileDirList(fileDir);
        List<FileDir> rootFileDir = fileDirMapper.selectFileDirList(new FileDir());
        for (FileDir f : fileDirs) {
            f.setId(f.getFileDirId());
            f.setLabel(f.getFileDirName());
            f.setChildren(getChild(f.getFileDirId(), rootFileDir));
        }
        return AjaxResult.success(fileDirs);
    }


    @ApiOperation("用于选择树的展现")
    @GetMapping("/selectTreeListDir")
    public AjaxResult selectTreeListDir() {
        FileDir fileDir = new FileDir();
        fileDir.setParentId(0L);
        List<FileDir> fileDirs = fileDirMapper.selectFileDirList(fileDir);

        List<FileDir> rootFileDir = fileDirMapper.selectFileDirList(new FileDir());
        for (FileDir f : fileDirs) {
            f.setId(f.getFileDirId());
            f.setLabel(f.getFileDirName());
            f.setChildren(getChild(f.getFileDirId(), rootFileDir));
        }
        FileDir save = new FileDir();
        save.setId(0L);
        save.setLabel("顶级目录");
        save.setChildren(fileDirs);
        List<FileDir> list = new ArrayList<>();
        list.add(save);
        return AjaxResult.success(list);
    }


    @PostMapping("/importFile")
    public AjaxResult importFile(MultipartFile file, Long fileDirId) {
        return fileDirService.importFile(file, fileDirId);
    }

    public List<FileDir> getChild(Long id, List<FileDir> root) {
        // 子菜单
        List<FileDir> childList = new ArrayList<>();
        for (FileDir fileDir : root) {
            fileDir.setId(fileDir.getFileDirId());
            fileDir.setLabel(fileDir.getFileDirName());
            // 遍历所有节点，将父菜单id与传过来的id比较
            if (fileDir.getParentId() != 0 && fileDir.getParentId().equals(id)) {
                childList.add(fileDir);
            }
        }

        // 把子菜单的子菜单再循环一遍
        for (FileDir fileDir : childList) {// 没有url子菜单还有子菜单
            fileDir.setId(fileDir.getFileDirId());
            fileDir.setLabel(fileDir.getFileDirName());
            // 看看现在id有没有作为父，即有没有子节点
            List<FileDir> chs = fileDirMapper.selectFileDirByParentId(fileDir.getFileDirId());
            if (chs.size() > 0) {
                // 递归
                fileDir.setChildren(getChild(fileDir.getFileDirId(), root));
            }
        }
        if (childList.size() == 0) {
            return null;
        }
        return childList;
    }

}
