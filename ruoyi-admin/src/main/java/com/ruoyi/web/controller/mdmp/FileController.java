package com.ruoyi.web.controller.mdmp;

import com.ruoyi.common.config.MinioClientConfig;
import com.ruoyi.common.exception.UtilException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.FileUploadUtils;
import com.ruoyi.common.utils.MinioUtil;
import com.ruoyi.common.utils.StringUtils;
import com.ruoyi.web.controller.common.CommonController;
import io.lettuce.core.dynamic.annotation.Param;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.io.IOUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;

@RestController
@Api(value = "文件管理模块", tags = "文件管理模块")
@RequestMapping("/mdmp/file")
public class FileController {

    private static final Logger log = LoggerFactory.getLogger(FileController.class);

    @Autowired
    private MinioUtil minioUtil;

    @Autowired
    private MinioClientConfig minioClientConfig;

    /**
     * 上传材料
     */
    @PostMapping("/upload")
    @ApiOperation("上传文件")
    public String upload(@RequestPart(value = "file") MultipartFile file) throws IOException {
        log.info("uploadFile:上传文件[file:{}]", file);
        try {
            //获取后缀
            String originalFilename = FileUploadUtils.getFileExtension(file);
            //判断文件类型
            FileUploadUtils.assertAllowed(originalFilename);
            //文件名 随机生成5位数
            String fileName = DateUtils.dateTimeNow() + ((int) ((Math.random() * 100000))) + "." + originalFilename;
//            String newFileName = System.currentTimeMillis() + "." + StringUtils.substringAfterLast(fileName, ".");
            String contentType = file.getContentType();
            log.info("uploadFile:上传文件成功");
            return minioUtil.uploadFile(minioClientConfig.getBucketName(), file, "mdmp", fileName, contentType);
        } catch (Exception e) {
            throw new UtilException("上传文件失败", e);
        }

    }

    /**
     * 删除
     *
     * @param fileName
     */
    @DeleteMapping("/delete")
    @ApiOperation("删除文件")
    public void delete(@RequestParam("fileName") String fileName) {
        log.info("delete:删除文件[fileName:{}]", fileName);
        minioUtil.removeFile(minioClientConfig.getBucketName(), fileName);
        log.info("delete:删除文件 成功");
    }

    /**
     * 获取文件信息
     *
     * @param fileName
     * @return
     */
    @ApiOperation("获取文件信息")
    @GetMapping("/info")
    public String getFileStatusInfo(@RequestParam("fileName") String fileName) {
        return minioUtil.getFileStatusInfo(minioClientConfig.getBucketName(), fileName);
    }

    /**
     * 获取文件外链
     *
     * @param fileName
     * @return
     */
    @GetMapping("/url")
    @ApiOperation("获取文件外链")
    public String getPresignedObjectUrl(@RequestParam("fileName") String fileName) {
        return minioUtil.getPresignedObjectUrl(minioClientConfig.getBucketName(), fileName);
    }

    /**
     * 文件下载
     *
     * @param fileName
     * @param response
     */
    @GetMapping("/download")
    @ApiOperation("文件下载")
    public void download(@RequestParam("fileName") String fileName, HttpServletResponse response) {
        log.info("download:文件下载[fileName:{}]", fileName);
        try {
            InputStream fileInputStream = minioUtil.getObject(minioClientConfig.getBucketName(), fileName);
            response.setHeader("Content-Disposition", "attachment;filename=" + fileName);
            response.setContentType("application/force-download");
            response.setCharacterEncoding("UTF-8");
            IOUtils.copy(fileInputStream, response.getOutputStream());
        } catch (Exception e) {
            log.error("download:下载失败");
        }
        log.error("download:下载成功");
    }


}
