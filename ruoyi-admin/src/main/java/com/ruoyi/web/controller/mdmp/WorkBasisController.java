package com.ruoyi.web.controller.mdmp;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.domain.mdmp.WorkInBasis;
import com.ruoyi.system.service.mdmp.IWorkBasisService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.mdmp.WorkBasis;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 作业区基础信息Controller
 *
 * <AUTHOR>
 * @date 2024-04-02
 */
@Api(value = "作业区基础信息", tags = "作业区基础信息")
@RestController
@RequestMapping("/mdmp/workBasis")
public class WorkBasisController extends BaseController {
    @Autowired
    private IWorkBasisService workBasisService;

    /**
     * 查询作业区基础信息列表
     */
    @ApiOperation("查询作业区基础信息列表")
    //@PreAuthorize("@ss.hasPermi('mdmp:basis:list')")
    @GetMapping("/list")
    public TableDataInfo list(WorkBasis workBasis) {
        startPage();
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        workBasis.setDeptCodeList(deptCodeList);
        List<WorkBasis> list = workBasisService.selectWorkBasisList(workBasis);
        return getDataTable(list);
    }

    /**
     * 导出作业区基础信息列表
     */
    // @PreAuthorize("@ss.hasPermi('mdmp:basis:export')")
    @Log(title = "作业区基础信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WorkBasis workBasis) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        workBasis.setDeptCodeList(deptCodeList);
        List<WorkBasis> list = workBasisService.selectWorkBasisList(workBasis);
        ExcelUtil<WorkBasis> util = new ExcelUtil<WorkBasis>(WorkBasis.class);
        util.exportExcel(response, list, "作业区基础信息数据");
    }

    /**
     * 获取作业区基础信息详细信息
     */

    @ApiOperation("获取作业区基础信息详细信息")
    //@PreAuthorize("@ss.hasPermi('mdmp:basis:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(workBasisService.selectWorkBasisById(id));
    }

    /**
     * 新增作业区基础信息
     */
    @ApiOperation("新增作业区基础信息")
    // @PreAuthorize("@ss.hasPermi('mdmp:basis:add')")
    @Log(title = "作业区基础信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody WorkBasis workBasis) {
        workBasis.setDeptCode(getLoginUser().getDeptCode());
        if (workBasis.getGraphType() == 0 && null == workBasis.getRadius()) {
            return error("作业区圆必须输入半径");
        }
        if (null != workBasis.getWorkInBasisList() && workBasis.getWorkInBasisList().size() > 0) {
            for (WorkInBasis workInBasis : workBasis.getWorkInBasisList()) {
                if (workInBasis.getGraphType() == 0 && null == workInBasis.getRadius()) {
                    return error("作业区内圆必须输入半径");
                }
            }
        }
        return toAjax(workBasisService.insertWorkBasis(workBasis));
    }

    /**
     * 修改作业区基础信息
     */
    @ApiOperation("修改作业区基础信息")
    //@PreAuthorize("@ss.hasPermi('mdmp:basis:edit')")
    @Log(title = "作业区基础信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody WorkBasis workBasis) {
        if (workBasis.getGraphType() == 0 && null == workBasis.getRadius()) {
            return error("作业区圆必须输入半径");
        }
        if (null != workBasis.getWorkInBasisList() && workBasis.getWorkInBasisList().size() > 0) {
            for (WorkInBasis workInBasis : workBasis.getWorkInBasisList()) {
                if (workInBasis.getGraphType() == 0 && null == workInBasis.getRadius()) {
                    return error("作业区内圆必须输入半径");
                }
            }
        }
        return toAjax(workBasisService.updateWorkBasis(workBasis));
    }

    /**
     * 删除作业区基础信息
     */
    @ApiOperation("删除作业区基础信息")
    //@PreAuthorize("@ss.hasPermi('mdmp:basis:remove')")
    @Log(title = "作业区基础信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(workBasisService.deleteWorkBasisByIds(ids));
    }
}
