package com.ruoyi.web.controller.mdmp;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.system.param.mdmp.SingleFlightTaskListParam;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.system.domain.mdmp.SingleFlightTask;
import com.ruoyi.system.service.mdmp.ISingleFlightTaskService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 单一飞行任务Controller
 *
 * <AUTHOR>
 * @date 2025-01-07
 */
@RestController
@RequestMapping("/system/task")
@Api(value = "A单一飞行计划管理", tags = "A单一飞行计划管理")
public class SingleFlightTaskController extends BaseController
{
    @Autowired
    private ISingleFlightTaskService singleFlightTaskService;

    /**
     * 查询单一飞行任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:task:list')")
    @GetMapping("/list")
    @ApiOperation("查询单一飞行任务列表")
    public TableDataInfo list(SingleFlightTaskListParam param)
    {
        startPage();
        List<SingleFlightTask> list = singleFlightTaskService.selectSingleFlightTaskList(param);
        return getDataTable(list);
    }

    /**
     * 导出单一飞行任务列表
     */
    @PreAuthorize("@ss.hasPermi('system:task:export')")
    @Log(title = "单一飞行任务", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出单一飞行任务列表")
    public void export(HttpServletResponse response, SingleFlightTaskListParam singleFlightTask)
    {
        List<SingleFlightTask> list = singleFlightTaskService.selectSingleFlightTaskList(singleFlightTask);
        ExcelUtil<SingleFlightTask> util = new ExcelUtil<SingleFlightTask>(SingleFlightTask.class);
        util.exportExcel(response, list, "单一飞行任务数据");
    }

    /**
     * 获取单一飞行任务详细信息
     */
    @PreAuthorize("@ss.hasPermi('system:task:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation("获取单一飞行任务详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return success(singleFlightTaskService.selectSingleFlightTaskById(id));
    }

    /**
     * 新增单一飞行任务
     */
    @PreAuthorize("@ss.hasPermi('system:task:add')")
    @Log(title = "单一飞行任务", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增单一飞行任务")
    public AjaxResult add(@RequestBody SingleFlightTask singleFlightTask)
    {
        return toAjax(singleFlightTaskService.insertSingleFlightTask(singleFlightTask));
    }

    /**
     * 修改单一飞行任务
     */
    @PreAuthorize("@ss.hasPermi('system:task:edit')")
    @Log(title = "单一飞行任务", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改单一飞行任务")
    public AjaxResult edit(@RequestBody SingleFlightTask singleFlightTask)
    {
        return toAjax(singleFlightTaskService.updateSingleFlightTask(singleFlightTask));
    }

    /**
     * 删除单一飞行任务
     */
    @PreAuthorize("@ss.hasPermi('system:task:remove')")
    @Log(title = "单一飞行任务", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    @ApiOperation("删除单一飞行任务")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(singleFlightTaskService.deleteSingleFlightTaskByIds(ids));
    }
}
