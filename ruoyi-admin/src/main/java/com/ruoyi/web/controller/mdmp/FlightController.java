package com.ruoyi.web.controller.mdmp;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.mdmp.Flight;
import com.ruoyi.system.domain.mdmp.dto.*;
import com.ruoyi.system.domain.mdmp.vo.FlightQueueVO;
import com.ruoyi.system.service.mdmp.FlightService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(value = "电子行程单中的航班管理", tags = "电子行程单中的航班管理")
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/mdmp/flight")
public class FlightController extends BaseController {

    private final FlightService flightService;

    /**
     * 航班列表
     */
    @ApiOperation("航班列表")
    @PostMapping("/list")
    public PageCommonResult<List<Flight>> list(@RequestBody QueryFlightListDTO dto) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        dto.setDeptCodeList(deptCodeList);
        return flightService.list(dto);
    }

    /**
     * 获取报文并批量新增航班(待定)
     */
    @ApiOperation("获取报文并批量新增航班(待定)")
    @PostMapping("/insertBatch")
    public CommonResult<String> insertBatch(@RequestBody List<Flight> flights) {
        return flightService.insertBatch(flights);
    }

    /**
     * 手动新增航班
     */
    @ApiOperation("手动新增航班")
    @PostMapping("/insert")
    public CommonResult<String> insert(@Valid @RequestBody AddFlightDTO dto) {
        dto.setDeptCode(getLoginUser().getDeptCode());
        return flightService.insert(dto);
    }

    /**
     * 查询航班详情
     */
    @ApiOperation("查询航班详情")
    @PostMapping("/getInfo")
    public CommonResult<Flight> getInfo(@Valid @RequestBody QueryFlightInfoDTO dto) {
        return flightService.getInfo(dto.getFlightId());
    }

    /**
     * 手动修改航班
     */
    @ApiOperation("手动修改航班")
    @PostMapping("/update")
    public CommonResult<String> update(@Valid @RequestBody UpdateFlightDTO dto) {
        return flightService.update(dto);
    }

    /**
     * 手动删除航班
     */
    @ApiOperation("手动删除航班")
    @PostMapping("/delete")
    public CommonResult<String> delete(@Valid @RequestBody DeleteFlightDTO dto) {
        return flightService.delete(dto.getFlightId());
    }

    /**
     * 查询当天航班集合
     */
    @ApiOperation("查询当天航班集合(等待区)")
    @PostMapping("/flightList")
    public CommonResult<List<Flight>> flightList() {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        return flightService.flightList(deptCodeList);
    }

    /**
     * 查询当天航班队列
     */
    @ApiOperation("查询当天航班队列(进离场航班排序)")
    @PostMapping("/flightQueue")
    public CommonResult<FlightQueueVO> flightQueue() {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        return flightService.flightQueue(deptCodeList);
    }

    /**
     * 修改航班进场或离场顺序
     */
    @ApiOperation("修改航班进场或离场顺序")
    @PostMapping("/updateProgress")
    public CommonResult<String> updateProgress(@RequestBody UpdateProgressDTO dto) {
        return flightService.updateProgress(dto);
    }


}
