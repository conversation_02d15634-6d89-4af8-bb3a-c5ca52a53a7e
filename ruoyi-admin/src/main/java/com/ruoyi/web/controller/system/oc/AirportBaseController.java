package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.system.domain.oc.AirportBase;
import com.ruoyi.system.domain.oc.dto.AddAirportBaseDTO;
import com.ruoyi.system.domain.oc.dto.QueryAirportBaseDTO;
import com.ruoyi.system.domain.oc.dto.UpdateAirportBaseDTO;
import com.ruoyi.system.service.oc.AirportBaseService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/18 10:05
 * @mood 功能
 */
@Api(tags = "基地管理")
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/system/airport")
public class AirportBaseController extends BaseController {
    @Resource
    private AirportBaseService airportBaseService;

    /**
     * 查询机场信息列表
     */
    @ApiOperation("查询机场信息列表")
    @GetMapping("/list")
    public PageCommonResult<List<AirportBase>> list(QueryAirportBaseDTO dto, @RequestHeader("CompanyCode") String companyCode) {
        Integer pageNum = TableSupport.buildPageRequest().getPageNum();
        Integer pageSize = TableSupport.buildPageRequest().getPageSize();
        return airportBaseService.selectList(dto, pageNum, pageSize, companyCode);
    }

    /**
     * 查询机场信息列表
     */
    @ApiOperation("查询所有机场信息")
    @GetMapping("/queryAll")
    public PageCommonResult<List<AirportBase>> queryAll(@RequestHeader("CompanyCode") String companyCode) {
        return airportBaseService.queryAll(companyCode);
    }

    /**
     * 查询单个机场信息
     */
    @ApiOperation("查询机场详情")
    @PostMapping("/getOne/{id}")
    public CommonResult<AirportBase> selectOne(@PathVariable("id") Long id) {
        return airportBaseService.selectOneById(id);
    }

    /**
     * 新增机场
     */
    @ApiOperation("新增机场")
    @PostMapping("/add")
    public CommonResult<String> insertOne(@Valid @RequestBody AddAirportBaseDTO dto, @RequestHeader("CompanyCode") String companyCode) {
        return airportBaseService.insertOne(dto, companyCode);
    }

    /**
     * 修改机场信息
     */
    @ApiOperation("修改机场信息")
    @PostMapping("/update")
    public CommonResult<String> updateOne(@Valid @RequestBody UpdateAirportBaseDTO dto) {
        return airportBaseService.updateOne(dto);
    }

    /**
     * 删除任务类型
     */
    @ApiOperation("删除机场信息")
    @PostMapping("/delete/{ids}")
    public CommonResult<String> deleteAll(@PathVariable Long[] ids) {
        return airportBaseService.deleteAllByIds(ids);
    }
}
