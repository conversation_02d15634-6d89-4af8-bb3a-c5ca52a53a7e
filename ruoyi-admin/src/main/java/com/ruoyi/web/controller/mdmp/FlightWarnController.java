package com.ruoyi.web.controller.mdmp;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.mdmp.FlightWarn;
import com.ruoyi.system.domain.mdmp.temporary.WebsocketFlightData;
import com.ruoyi.system.service.mdmp.FlightWarnService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 飞机告警信息管理
 *
 * <AUTHOR>
 */
@Api(value = "飞机告警信息管理",tags = "飞机告警信息管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/mdmp/flightWarn")
public class FlightWarnController {

    private final FlightWarnService flightWarnService;

    /**
     * 查询当天所有告警信息
     */
    @ApiOperation("查询当天所有告警信息")
    @PostMapping("/todayFlightWarnList")
    public CommonResult<List<FlightWarn>> airspaceList() {
        return flightWarnService.todayFlightWarnList();
    }

    /**
     * test 查看websocket回参的接口（不调用）
     */
    @ApiOperation("查看websocket回参的接口（没有实际用处）")
    @PostMapping("/websocketParam")
    public WebsocketFlightData websocketParam() {
        return new WebsocketFlightData();
    }
}
