package com.ruoyi.web.controller.mdmp;

import cn.hutool.core.collection.ListUtil;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.TestParam;
import com.ruoyi.system.util.GeoCalculatorUtil;
import com.ruoyi.system.util.LongLatUtil;
import com.wsp.sdk.service.base.R;
import com.wsp.sdk.service.dto.request.AwosTimeReqDTO;
import com.wsp.sdk.service.dto.response.AwosDataResDto;
import com.wsp.sdk.service.dto.response.OpenRawReportResDTO;
import com.wsp.sdk.service.dto.response.ReportDecodeResDTO;
import com.wsp.sdk.service.dto.response.ReportReqDTO;
import com.wsp.sdk.service.service.ReportDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/11/5 10:50
 * @description：
 * @modified By：
 * @version: $
 */
@Api(value = "测试", tags = "测试")
@RestController
@RequestMapping("/mdmp/tests")
public class TestsController extends BaseController {


    @Resource
    private ReportDataService reportDataService;

    @Log(title = "查询另一个点的经纬度", businessType = BusinessType.INSERT)
    @PostMapping("/quyer")
    @ApiOperation("查询另一个点的经纬度")
    public AjaxResult query(TestParam testParam) {
        double benchmarkLong = LongLatUtil.convertDMSToDD(testParam.getLon());
        double benchmarkLat = LongLatUtil.convertDMSToDD(testParam.getLat());
        return success(GeoCalculatorUtil.calculateDestinationPoint(benchmarkLat, benchmarkLong, testParam.getBearing(), testParam.getDistance() / 1000));
    }


    @PostMapping("/test")
    @ApiOperation("测试")
    public AjaxResult test(TestParam testParam) {
        ReportReqDTO reportReqDTO = new ReportReqDTO();
        reportReqDTO.setStartTime((DateUtils.getUTCMinusTwoHours(1)));
        reportReqDTO.setEndTime(DateUtils.getCurrentUTCTime());
        List<String> icaoList = new ArrayList<>();
        icaoList.add("ZWHZ");
        reportReqDTO.setIcaoList(icaoList);
        List<String> typeList = new ArrayList<>();
        typeList.add("SA");
        reportReqDTO.setTypeList(typeList);
        R<List<ReportDecodeResDTO>> r = reportDataService.getDecodeReport(reportReqDTO);
        System.out.println(r);
        return success(r);
    }


    @PostMapping("/test1")
    @ApiOperation("测试")
    public AjaxResult test1(TestParam testParam) {
        ReportReqDTO reportReqDTO = new ReportReqDTO();
        reportReqDTO.setStartTime((DateUtils.getUTCMinusTwoHours(3)));
        reportReqDTO.setEndTime(DateUtils.getCurrentUTCTime());
        List<String> icaoList = new ArrayList<>();
        icaoList.add("ZWHZ");
        reportReqDTO.setIcaoList(icaoList);
        List<String> typeList = new ArrayList<>();
        typeList.add("FC");
        reportReqDTO.setTypeList(typeList);
        R<List<ReportDecodeResDTO>> r = reportDataService.getDecodeReport(reportReqDTO);
        System.out.println(r);
        return success(r);
    }
}
