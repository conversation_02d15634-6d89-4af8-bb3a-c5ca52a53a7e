package com.ruoyi.web.controller.mdmp;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.mdmp.Airspace;
import com.ruoyi.system.domain.mdmp.vo.AirspaceListVO;
import com.ruoyi.system.param.mdmp.MapDataParam;
import com.ruoyi.system.service.mdmp.IAirspaceService;
import com.ruoyi.system.service.mdmp.IMapDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

import static com.ruoyi.common.utils.SecurityUtils.getLoginUser;

/**
 * 空域Controller
 *
 * <AUTHOR>
 * @date 2024-04-01
 */
@Api(value = "空域信息管理", tags = "空域信息管理")
@RestController
@RequestMapping("/mdmp/airspace")
public class AirspaceController extends BaseController {
    @Autowired
    private IAirspaceService airspaceService;


    @Autowired
    private IMapDataService mapDataService;

    /**
     * 查询空域列表
     */
    @ApiOperation("查询空域列表")
    //@PreAuthorize("@ss.hasPermi('mdmp:airspace:list')")
    @GetMapping("/list")
    public TableDataInfo list(Airspace airspace) {
        startPage();
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        airspace.setDeptCodeList(deptCodeList);
        List<Airspace> list = airspaceService.selectAirspaceList(airspace);
        return getDataTable(list);
    }

    /**
     * 查询空域
     */
    @ApiOperation("查询空域格子")
    //@PreAuthorize("@ss.hasPermi('mdmp:airspace:list')")
    @PostMapping("/mapData")
    public AjaxResult mapData(@RequestBody MapDataParam param) {
        List<AirspaceListVO> list = airspaceService.selectAirspaceListAndMap(param.getIds());
        return success(list);
    }


    /**
     * 导出空域列表
     */
    //@PreAuthorize("@ss.hasPermi('mdmp:airspace:export')")
    @Log(title = "空域", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Airspace airspace) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        airspace.setDeptCodeList(deptCodeList);
        List<Airspace> list = airspaceService.selectAirspaceList(airspace);
        ExcelUtil<Airspace> util = new ExcelUtil<Airspace>(Airspace.class);
        util.exportExcel(response, list, "空域数据");
    }

    /**
     * 获取空域详细信息
     */

    @ApiOperation("获取空域详细信息")
    //@PreAuthorize("@ss.hasPermi('mdmp:airspace:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(airspaceService.selectAirspaceById(id));
    }

    /**
     * 新增空域
     */
//    @ApiOperation("新增空域")
//    @PreAuthorize("@ss.hasPermi('mdmp:airspace:add')")
//    @Log(title = "空域", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody List<Airspace> airspaceList) {
//        for (Airspace airspace:airspaceList) {
//            airspaceService.insertAirspace(airspace);
//        }
//        return toAjax(1);
//    }
//
//    /**
//     * 修改空域
//     */
//    @ApiOperation("修改空域")
//    @PreAuthorize("@ss.hasPermi('mdmp:airspace:edit')")
//    @Log(title = "空域", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody List<Airspace> airspaceList) {
//        for (Airspace airspace:airspaceList) {
//            airspaceService.updateAirspace(airspace);
//        }
//        return toAjax(1);
//    }
    @ApiOperation("新增空域")
    //@PreAuthorize("@ss.hasPermi('mdmp:airspace:add')")
    @Log(title = "空域", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody Airspace airspace) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd");
        LocalDate startDate = LocalDate.parse(airspace.getEffectiveStartDate(), formatter);
        LocalDate endDate = LocalDate.parse(airspace.getEffectiveEndDate(), formatter);

        // 校验逻辑
        if (!endDate.isAfter(startDate)) {
            return error("有效截至日期必须晚于有效开始日期");
        }
        // 设置空域所属部门编码
        airspace.setDeptCode(getLoginUser().getDeptCode());
        // 插入空域
        return toAjax(airspaceService.insertAirspace(airspace));
    }

    /**
     * 修改空域
     */
    @ApiOperation("修改空域")
    // @PreAuthorize("@ss.hasPermi('mdmp:airspace:edit')")
    @Log(title = "空域", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody Airspace airspace) {
        return toAjax(airspaceService.updateAirspace(airspace, getLoginUser().getDeptCodeList()));
    }

    /**
     * 删除空域
     */
    @ApiOperation("删除空域")
    //@PreAuthorize("@ss.hasPermi('mdmp:airspace:remove')")
    @Log(title = "空域", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(airspaceService.deleteAirspaceById(id,getLoginUser().getDeptCodeList()));
    }
}
