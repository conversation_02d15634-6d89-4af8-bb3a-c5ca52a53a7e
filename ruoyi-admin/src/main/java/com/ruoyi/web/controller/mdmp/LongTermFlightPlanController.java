package com.ruoyi.web.controller.mdmp;

import java.util.List;
import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.LongTermFlightPlan;
import com.ruoyi.system.param.mdmp.LongTermFlightPlanListParam;
import com.ruoyi.system.service.mdmp.ILongTermFlightPlanService;
import com.ruoyi.system.service.mdmp.IMapDataService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 长期飞行计划Controller
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@RestController
@Api(value = "A长期飞行计划管理", tags = "A长期飞行计划管理")
@RequestMapping("/mdmp/longTermFlightPlan")
public class LongTermFlightPlanController extends BaseController {
    @Autowired
    private ILongTermFlightPlanService longTermFlightPlanService;

    /**
     * 查询长期飞行计划列表
     */
    //@PreAuthorize("@ss.hasPermi('system:longTermFlightPlan:list')")
    @GetMapping("/list")
    @ApiOperation("查询长期飞行计划列表")
    public TableDataInfo list(LongTermFlightPlanListParam param) {
        startPage();
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        param.setDeptCodeList(deptCodeList);
        List<LongTermFlightPlan> list = longTermFlightPlanService.selectLongTermFlightPlanList(param);
        return getDataTable(list);
    }

    /**
     * 导出长期飞行计划列表
     */
    // @PreAuthorize("@ss.hasPermi('system:longTermFlightPlan:export')")
    @Log(title = "长期飞行计划", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出长期飞行计划列表")
    public void export(HttpServletResponse response, LongTermFlightPlanListParam param) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        param.setDeptCodeList(deptCodeList);
        List<LongTermFlightPlan> list = longTermFlightPlanService.selectLongTermFlightPlanList(param);
        ExcelUtil<LongTermFlightPlan> util = new ExcelUtil<LongTermFlightPlan>(LongTermFlightPlan.class);
        util.exportExcel(response, list, "长期飞行计划数据");
    }

    @Resource
    private IMapDataService mapDataService;

    /**
     * 获取长期飞行计划详细信息
     */
    // @PreAuthorize("@ss.hasPermi('system:longTermFlightPlan:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation("获取长期飞行计划详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
//        mapDataService.insertMapData();
        return success(longTermFlightPlanService.selectLongTermFlightPlanById(id));
    }

    /**
     * 验证长期计划飞行
     */
    // @PreAuthorize("@ss.hasPermi('system:longTermFlightPlan:verify')")
    @Log(title = "验证长期计划飞行")
    @PostMapping(value = "/verify")
    @ApiOperation("验证长期计划飞行")
    public AjaxResult verifyLongTermFlightPlan(@Validated @RequestBody LongTermFlightPlan longTermFlightPlan) {

        return success(longTermFlightPlanService.verifyLongTermFlightPlan(longTermFlightPlan));
    }


    /**
     * 验证长期计划飞行 根据id获取冲突
     */
    // @PreAuthorize("@ss.hasPermi('system:longTermFlightPlan:verifyById')")
    @Log(title = "验证长期计划飞行 根据id获取冲突")
    @GetMapping(value = "/verifyById/{id}")
    @ApiOperation("验证长期计划飞行 根据id获取冲突")
    public AjaxResult verifyLongTermFlightPlan(@PathVariable("id") Long id) {
        return success(longTermFlightPlanService.verifyLongTermFlightPlan(longTermFlightPlanService.selectLongTermFlightPlanById(id)));
    }


    /**
     * 新增长期飞行计划
     */
    // @PreAuthorize("@ss.hasPermi('system:longTermFlightPlan:add')")
    @Log(title = "长期飞行计划", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增长期飞行计划")
    public AjaxResult add(@Validated @RequestBody LongTermFlightPlan longTermFlightPlan) {
        longTermFlightPlan.setCreator(getUsername());
        longTermFlightPlan.setCreationTime(DateUtils.getTime());
        longTermFlightPlan.setCompanyCode(getUsername());
        longTermFlightPlan.setDeptCode(getLoginUser().getDeptCode());
        return toAjax(longTermFlightPlanService.insertLongTermFlightPlan(longTermFlightPlan));
    }

    /**
     * 修改长期飞行计划
     */
    // @PreAuthorize("@ss.hasPermi('system:longTermFlightPlan:edit')")
    @Log(title = "长期飞行计划", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改长期飞行计划")
    public AjaxResult edit(@Validated @RequestBody LongTermFlightPlan longTermFlightPlan) {
        return toAjax(longTermFlightPlanService.updateLongTermFlightPlan(longTermFlightPlan));

    }

    /**
     * 删除长期飞行计划
     */
    // @PreAuthorize("@ss.hasPermi('system:longTermFlightPlan:remove')")
    @Log(title = "长期飞行计划", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除长期飞行计划")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(longTermFlightPlanService.deleteLongTermFlightPlanByIds(ids));
    }


    /**
     * 审核长期飞行计划
     */
    //@PreAuthorize("@ss.hasPermi('system:longTermFlightPlan:auditing')")
    @Log(title = "审核长期飞行计划", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/auditing")
    @ApiOperation("审核长期飞行计划")
    public AjaxResult auditing(@RequestBody LongTermFlightPlan longTermFlightPlan) {
        return toAjax(longTermFlightPlanService.auditing(longTermFlightPlan));
    }
}
