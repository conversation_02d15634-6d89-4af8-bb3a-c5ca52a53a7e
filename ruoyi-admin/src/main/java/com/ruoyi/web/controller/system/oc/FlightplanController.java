package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.oc.dto.AddFlightPlanDTO;
import com.ruoyi.system.domain.oc.dto.QueryFlightPlanDTO;
import com.ruoyi.system.domain.oc.entity.Flightplan;
import com.ruoyi.system.service.oc.IFlightplanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 航班计划信息Controller
 *
 * <AUTHOR>
 * @date 2021-09-22
 */
@Api("航班计划管理")
@RestController
@RequestMapping("/system/flightplan")
public class FlightplanController extends BaseController {
    @Resource
    private IFlightplanService flightplanService;

    /**
     * 查询航班计划信息列表
     */
    @ApiOperation("查询航班计划信息列表")
    @PreAuthorize("@ss.hasPermi('system:flightplan:list')")
    @GetMapping("/list")
    public TableDataInfo list(QueryFlightPlanDTO dto, @RequestHeader("companyCode") String companyCode) {
        Integer pageNum = TableSupport.buildPageRequest().getPageNum();
        Integer pageSize = TableSupport.buildPageRequest().getPageSize();
        List<Flightplan> list = flightplanService.selectFlightplanList(dto, companyCode,pageNum, pageSize);
        return getDataTable(list);
    }


    /**
     * 导出航班计划信息列表
     */
    @ApiOperation("导出航班计划信息列表")
    @PreAuthorize("@ss.hasPermi('system:flightplan:export')")
    @Log(title = "航班计划信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(QueryFlightPlanDTO dto, @RequestHeader("companyCode") String companyCode) {
        List<Flightplan> list = flightplanService.selectFlightplan(dto, companyCode);
        ExcelUtil<Flightplan> util = new ExcelUtil<>(Flightplan.class);
        return util.exportExcel(list, "航班计划信息数据");
    }

    /**
     * 获取航班计划信息详细信息
     */
    @ApiOperation("获取航班计划信息详细信息")
    @PreAuthorize("@ss.hasPermi('system:flightplan:query')")
    @GetMapping(value = "/getInfo/{flightplanId}")
    public AjaxResult getInfo(@PathVariable("flightplanId") Long flightplanId) {
        return AjaxResult.success(flightplanService.selectFlightplanByFlightplanId(flightplanId));
    }

    /**
     * 新增航班计划信息
     */
    @ApiOperation("新增航班计划信息")
    @PreAuthorize("@ss.hasPermi('system:flightplan:add')")
    @Log(title = "航班计划信息", businessType = BusinessType.INSERT)
    @PostMapping(value = "/add")
    public AjaxResult add(@RequestBody AddFlightPlanDTO dto, @RequestHeader("companyCode") String companyCode) {
        return toAjax(flightplanService.insertFlightplan(dto, companyCode, getUsername()));
    }

    /**
     * 修改航班计划信息
     */
    @ApiOperation("修改航班计划信息")
    @PreAuthorize("@ss.hasPermi('system:flightplan:edit')")
    @Log(title = "航班计划信息", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/edit")
    public AjaxResult edit(@RequestBody Flightplan flightplan, @RequestHeader("companyCode") String companyCode) {
        flightplan.setCompanyCode(companyCode);
        return toAjax(flightplanService.updateFlightplan(flightplan));
    }

    /**
     * 删除航班计划信息
     */
    @ApiOperation("删除航班计划信息")
    @PreAuthorize("@ss.hasPermi('system:flightplan:remove')")
    @Log(title = "航班计划信息", businessType = BusinessType.DELETE)
    @PostMapping("/remove/{flightplanIds}")
    public AjaxResult remove(@PathVariable Long[] flightplanIds) {
        return toAjax(flightplanService.deleteFlightplanByFlightplanIds(flightplanIds));
    }

}
