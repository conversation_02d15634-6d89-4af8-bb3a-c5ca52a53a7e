package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.FlightTaskWordUtils;
import com.ruoyi.system.domain.oc.FlightTaskBook;
import com.ruoyi.system.domain.oc.dto.FlightTaskAddDTO;
import com.ruoyi.system.domain.oc.dto.FlightTaskQueryDTO;
import com.ruoyi.system.service.oc.FlightTaskService;
import io.swagger.annotations.Api;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 飞行任务书
 * @date 2025/7/18 14:23:13
 */
@Api("飞行任务书")
@RestController
@RequestMapping("/system/flight-task")
public class FlightTaskController extends BaseController {


    @Resource
    private FlightTaskService flightTaskService;

    @Value("${flightBookWordTemplate}")
    private String flightBookWordTemplate;

    @RequestMapping("/add")
    public AjaxResult add(@RequestHeader("companyCode") String companyCode, @RequestBody FlightTaskAddDTO dto) {

        boolean result = flightTaskService.add(companyCode, dto);
        return result ? success() : error("添加失败");
    }

    //修改航班任务
    @RequestMapping("/update")
    public AjaxResult update(@RequestHeader("companyCode") String companyCode, @RequestBody FlightTaskAddDTO dto) {

        boolean result = flightTaskService.update(companyCode, dto);
        return result ? success() : error("修改失败");
    }

    //删除航班任务
    @RequestMapping("/delete/{id}")
    public AjaxResult delete(@RequestHeader("companyCode") String companyCode, @PathVariable("id") Long id) {
        boolean result = flightTaskService.delete(companyCode, id);
        return result ? success() : error("删除失败");
    }

    //查询航班任务
    @RequestMapping("/list")
    public TableDataInfo list(@RequestHeader("companyCode") String companyCode, @RequestBody FlightTaskQueryDTO dto) {
        startPage();

        List<FlightTaskBook> list = flightTaskService.queryFlightTaskInfo(companyCode, dto);

        return getDataTable(list);
    }


    //查询详情
    @RequestMapping("/getInfo/{id}")
    public AjaxResult getInfo(@RequestHeader("companyCode") String companyCode, @PathVariable("id") Long id) {
        FlightTaskAddDTO info = flightTaskService.getInfo(companyCode, id);
        return info != null ? AjaxResult.success(info) : error("查询失败");
    }


    //导出word文档
    @RequestMapping("/exportWord/{id}")
    public void exportWord(@RequestHeader("companyCode") String companyCode, @PathVariable("id") Long id, HttpServletResponse response) {
        FlightTaskAddDTO flightTaskAddDTO = flightTaskService.getInfo(companyCode, id);

        try (InputStream inputStream = getClass().getClassLoader().getResourceAsStream(flightBookWordTemplate);
             XWPFDocument document = FlightTaskWordUtils.writeWord(
                     inputStream,
                     FlightTaskAddDTO.getExportWordTaskBookDataParam(flightTaskAddDTO),
                     FlightTaskAddDTO.getExportWordTaskInfoDataParam(flightTaskAddDTO)
             ))  {

            String fileName = flightTaskAddDTO.getFlightDate() + " " + flightTaskAddDTO.getRegistrationNumber() + "飞行任务书.docx";
            String encodedFileName = URLEncoder.encode(fileName, StandardCharsets.UTF_8.toString()).replaceAll("\\+", "%20");

            response.setCharacterEncoding("UTF-8"); // 显式设置响应编码
            response.setContentType("application/vnd.openxmlformats-officedocument.wordprocessingml.document");
//            response.setHeader("Content-Disposition", "attachment; filename=\"" + fileName + "\"; filename*=UTF-8''" + encodedFileName);
            response.setHeader("Content-Disposition", "attachment; filename*=UTF-8''" + encodedFileName);
            document.write(response.getOutputStream());
        } catch (Exception e) {
            throw new RuntimeException("导出失败!", e);
        }
    }

}
