package com.ruoyi.web.controller.system.oc;

import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherDynamicVo;
import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.util.word.DynamicInfoWordUtils;
import com.ruoyi.system.util.word.WeatherInfoWordUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 气象信息和动态信息导出测试类
 */
public class WeatherAndDynamicExportTest {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherAndDynamicExportTest.class);
    
    public static void main(String[] args) {
        WeatherAndDynamicExportTest test = new WeatherAndDynamicExportTest();
        
        try {
            // 测试气象信息导出
            test.testWeatherInfoExport();
            
            // 测试动态信息导出
            test.testDynamicInfoExport();
            
            logger.info("所有测试完成");
        } catch (Exception e) {
            logger.error("测试失败", e);
        }
    }
    
    /**
     * 测试气象信息导出
     */
    public void testWeatherInfoExport() throws Exception {
        logger.info("开始测试气象信息导出");
        
        // 检查模板文件
        checkWeatherTemplateFile();
        
        // 创建测试数据
        WordMeteorologicalVo data = createWeatherTestData();
        
        // 生成文档
        byte[] documentBytes = generateWeatherDocument(data);
        
        // 保存文档
        saveDocument(documentBytes, "test_weather_info.docx");
        
        logger.info("气象信息导出测试完成");
    }
    
    /**
     * 测试动态信息导出
     */
    public void testDynamicInfoExport() throws Exception {
        logger.info("开始测试动态信息导出");
        
        // 检查模板文件
        checkDynamicTemplateFile();
        
        // 创建测试数据
        WordMeteorologicalVo data = createDynamicTestData();
        
        // 生成文档
        byte[] documentBytes = generateDynamicDocument(data);
        
        // 保存文档
        saveDocument(documentBytes, "test_dynamic_info.docx");
        
        logger.info("动态信息导出测试完成");
    }
    
    /**
     * 检查气象信息模板文件
     */
    private void checkWeatherTemplateFile() throws Exception {
        String templatePath = "ruoyi-admin/src/main/resources/word/气象信息模板.docx";
        Path path = Paths.get(templatePath);
        
        if (!Files.exists(path)) {
            throw new Exception("气象信息模板文件不存在: " + templatePath);
        }
        
        logger.info("气象信息模板文件检查通过: {}", templatePath);
    }
    
    /**
     * 检查动态信息模板文件
     */
    private void checkDynamicTemplateFile() throws Exception {
        String templatePath = "ruoyi-admin/src/main/resources/word/动态信息模板.docx";
        Path path = Paths.get(templatePath);
        
        if (!Files.exists(path)) {
            throw new Exception("动态信息模板文件不存在: " + templatePath);
        }
        
        logger.info("动态信息模板文件检查通过: {}", templatePath);
    }
    
    /**
     * 创建气象信息测试数据
     */
    private WordMeteorologicalVo createWeatherTestData() {
        logger.info("创建气象信息测试数据");
        
        WordMeteorologicalVo data = new WordMeteorologicalVo();
        
        // 基本信息
        data.setAircraftType("BELL429");
        data.setRegistrationNumber("B-7613");
        data.setFlightDate("2025-05-20");
        data.setFuel("740b");
        
        // 统计信息
        data.setGroundTimeMinTotal("11");
        data.setAirTimeMinTotal("51");
        data.setTotalTimeMinTotal("62");
        data.setSortieCountTotal("3");
        
        // 始发地气象信息
        List<WordFlightWeatherInfoVo> departureWeatherInfoList = new ArrayList<>();
        WordFlightWeatherInfoVo departureWeather1 = new WordFlightWeatherInfoVo();
        departureWeather1.setBatch("1");
        departureWeather1.setLocationName("星野");
        departureWeather1.setWeather("晴天");
        departureWeather1.setCloudHeight("无云层");
        departureWeather1.setTemperature("26");
        departureWeather1.setWindDirection("160");
        departureWeather1.setWindSpeed("4");
        departureWeather1.setVisibility("9999");
        departureWeather1.setQnh("44");
        departureWeatherInfoList.add(departureWeather1);
        
        WordFlightWeatherInfoVo departureWeather2 = new WordFlightWeatherInfoVo();
        departureWeather2.setBatch("2");
        departureWeather2.setLocationName("星野");
        departureWeather2.setWeather("晴天");
        departureWeather2.setCloudHeight("无云层");
        departureWeather2.setTemperature("26");
        departureWeather2.setWindDirection("160");
        departureWeather2.setWindSpeed("4");
        departureWeather2.setVisibility("9999");
        departureWeather2.setQnh("44");
        departureWeatherInfoList.add(departureWeather2);
        
        data.setDepartureWeatherInfoList(departureWeatherInfoList);
        
        // 目的地气象信息
        List<WordFlightWeatherInfoVo> arrivalWeatherInfoList = new ArrayList<>();
        WordFlightWeatherInfoVo arrivalWeather1 = new WordFlightWeatherInfoVo();
        arrivalWeather1.setBatch("1");
        arrivalWeather1.setLocationName("星野");
        arrivalWeather1.setWeather("晴天");
        arrivalWeather1.setCloudHeight("无云层");
        arrivalWeather1.setTemperature("26");
        arrivalWeather1.setWindDirection("160");
        arrivalWeather1.setWindSpeed("4");
        arrivalWeather1.setVisibility("9999");
        arrivalWeather1.setQnh("44");
        arrivalWeatherInfoList.add(arrivalWeather1);
        
        data.setArrivalWeatherInfoList(arrivalWeatherInfoList);
        
        // 动态信息设为空（仅气象信息）
        data.setDynamicInfoList(new ArrayList<>());
        
        return data;
    }
    
    /**
     * 创建动态信息测试数据
     */
    private WordMeteorologicalVo createDynamicTestData() {
        logger.info("创建动态信息测试数据");
        
        WordMeteorologicalVo data = new WordMeteorologicalVo();
        
        // 基本信息
        data.setAircraftType("BELL429");
        data.setRegistrationNumber("B-7613");
        data.setFlightDate("2025-05-20");
        
        // 统计信息
        data.setGroundTimeMinTotal("11");
        data.setAirTimeMinTotal("51");
        data.setTotalTimeMinTotal("62");
        data.setSortieCountTotal("3");
        
        // 动态信息
        List<WordFlightWeatherDynamicVo> dynamicInfoList = new ArrayList<>();
        
        WordFlightWeatherDynamicVo dynamic1 = new WordFlightWeatherDynamicVo();
        dynamic1.setBatch("1");
        dynamic1.setDepartureLocation("星野");
        dynamic1.setArrivalLocation("星野");
        dynamic1.setCarStartTime("19:43");
        dynamic1.setTakeOffTime("19:47");
        dynamic1.setLandingTime("20:03");
        dynamic1.setCarStopTime("20:05");
        dynamic1.setGroundTimeMin("6");
        dynamic1.setAirTimeMin("16");
        dynamic1.setTotalTimeMin("22");
        dynamic1.setSortieCount("1");
        dynamicInfoList.add(dynamic1);
        
        WordFlightWeatherDynamicVo dynamic2 = new WordFlightWeatherDynamicVo();
        dynamic2.setBatch("2");
        dynamic2.setDepartureLocation("星野");
        dynamic2.setArrivalLocation("星野");
        dynamic2.setCarStartTime("20:37");
        dynamic2.setTakeOffTime("20:40");
        dynamic2.setLandingTime("21:15");
        dynamic2.setCarStopTime("21:17");
        dynamic2.setGroundTimeMin("5");
        dynamic2.setAirTimeMin("35");
        dynamic2.setTotalTimeMin("40");
        dynamic2.setSortieCount("1");
        dynamicInfoList.add(dynamic2);
        
        data.setDynamicInfoList(dynamicInfoList);
        
        // 气象信息设为空（仅动态信息）
        data.setDepartureWeatherInfoList(new ArrayList<>());
        data.setArrivalWeatherInfoList(new ArrayList<>());
        
        return data;
    }
    
    /**
     * 生成气象信息文档
     */
    private byte[] generateWeatherDocument(WordMeteorologicalVo data) throws Exception {
        logger.info("开始生成气象信息Word文档");
        
        String templatePath = "ruoyi-admin/src/main/resources/word/气象信息模板.docx";
        String sealPath = "ruoyi-admin/src/main/resources/word/公章.png";
        
        // 检查公章文件是否存在
        if (!Files.exists(Paths.get(sealPath))) {
            logger.warn("公章文件不存在，将跳过公章添加: {}", sealPath);
            sealPath = null;
        }
        
        byte[] documentBytes = WeatherInfoWordUtils.generateWeatherInfoDocument(templatePath, data, sealPath);
        
        logger.info("气象信息Word文档生成完成，大小: {} 字节", documentBytes.length);
        return documentBytes;
    }
    
    /**
     * 生成动态信息文档
     */
    private byte[] generateDynamicDocument(WordMeteorologicalVo data) throws Exception {
        logger.info("开始生成动态信息Word文档");
        
        String templatePath = "ruoyi-admin/src/main/resources/word/动态信息模板.docx";
        String sealPath = "ruoyi-admin/src/main/resources/word/公章.png";
        
        // 检查公章文件是否存在
        if (!Files.exists(Paths.get(sealPath))) {
            logger.warn("公章文件不存在，将跳过公章添加: {}", sealPath);
            sealPath = null;
        }
        
        byte[] documentBytes = DynamicInfoWordUtils.generateDynamicInfoDocument(templatePath, data, sealPath);
        
        logger.info("动态信息Word文档生成完成，大小: {} 字节", documentBytes.length);
        return documentBytes;
    }
    
    /**
     * 保存文档
     */
    private void saveDocument(byte[] documentBytes, String fileName) throws IOException {
        String outputPath = "test_output/" + fileName;
        
        // 创建输出目录
        Path outputDir = Paths.get("test_output");
        if (!Files.exists(outputDir)) {
            Files.createDirectories(outputDir);
        }
        
        // 保存文件
        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            fos.write(documentBytes);
            fos.flush();
        }
        
        logger.info("文档已保存到: {}", outputPath);
    }
}
