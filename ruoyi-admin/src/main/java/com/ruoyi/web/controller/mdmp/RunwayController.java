package com.ruoyi.web.controller.mdmp;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.mdmp.Runway;
import com.ruoyi.system.domain.mdmp.dto.UpdateRunwayStatusDTO;
import com.ruoyi.system.service.mdmp.RunwayService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(value = "电子行程单中的跑道管理",tags = "电子行程单中的跑道管理")
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/mdmp/runway")
public class RunwayController {

    private final RunwayService runwayService;

    /**
     * 查询所有跑道
     */
    @ApiOperation("查询所有跑道")
    @PostMapping("/queryAll")
    public CommonResult<List<Runway>> queryAll() {
        return runwayService.queryAll();
    }

    /**
     * 修改跑道状态
     */
    @ApiOperation("修改跑道状态")
    @PostMapping("/updateRunwayStatus")
    public CommonResult<String> updateRunwayStatus(@Valid @RequestBody UpdateRunwayStatusDTO dto) {
        return runwayService.updateRunwayStatus(dto);
    }
}
