package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.oc.WxMenu;
import com.ruoyi.system.service.oc.WxMenuService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2024/6/4 15:14
 * @description：
 * @modified By：
 * @version: $
 */
@Api("微信菜单管理")
@RestController
@RequestMapping("/system/wxMenu/")
public class WxMenuController {


    @Resource
    private WxMenuService wxMenuService;


    /**
     * 查询所有微信权限菜单
     */
    @ApiOperation("查询所有微信权限菜单")
    @PostMapping("/listAll")
    public CommonResult<List<WxMenu>> list() {
        return wxMenuService.list();
    }
}
