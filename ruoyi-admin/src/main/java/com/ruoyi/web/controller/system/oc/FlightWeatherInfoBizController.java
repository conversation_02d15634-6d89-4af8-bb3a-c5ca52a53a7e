package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.exception.ServiceException;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.oc.dto.FlightWeatherDynamicDto;
import com.ruoyi.system.domain.oc.entity.FlightWeatherInfo;
import com.ruoyi.system.service.oc.IFlightWeatherInfoService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;

/**
 * 气象信息Controller
 *
 * <AUTHOR>
 */
@Api("气象信息管理")
@RestController
@RequestMapping("/system/flightWeatherInfo")
@Slf4j
public class FlightWeatherInfoBizController extends BaseController {
    @Resource
    private IFlightWeatherInfoService flightWeatherInfoService;

    @ApiOperation("导出气象信息列表")
    @PreAuthorize("@ss.hasPermi('system:flightWeatherInfo:export')")
    @PostMapping("/export")
    public void exportWeatherInfoToWord(HttpServletResponse response, Integer flightTaskBookId, @RequestHeader("companyCode") String companyCode) {
        try {
            log.info("开始导出气象信息Word文档，导出日期：{},任务书id:{}", DateUtils.getDate(), flightTaskBookId);
            flightWeatherInfoService.exportWeatherInfoToWord(response, flightTaskBookId, companyCode);
            log.info("气象信息Word文档导出完成");
        } catch (Exception e) {
            log.error("导出气象信息Word文档失败", e);
            throw new ServiceException("导出失败：" + e.getMessage());
        }
    }

    /**
     * 获取气象信息详细信息
     */
    @ApiOperation("获取气象信息详细信息")
    @PreAuthorize("@ss.hasPermi('system:flightWeatherInfo:query')")
    @GetMapping(value = "/selectFlightWeatherInfoById")
    public AjaxResult selectFlightWeatherInfoById(@RequestHeader("companyCode") String companyCode, @RequestParam("flightTaskBookId") Long flightTaskBookId) {
        return success(flightWeatherInfoService.selectFlightWeatherInfoById(flightTaskBookId, companyCode));
    }

    /**
     * 新增气象信息
     */
    @ApiOperation("新增气象信息")
    @PreAuthorize("@ss.hasPermi('system:flightWeatherInfo:add')")
    @PostMapping("/add")
    public AjaxResult add(@RequestHeader("companyCode") String companyCode, @RequestBody FlightWeatherDynamicDto flightWeatherDynamicDto) {
        return toAjax(flightWeatherInfoService.insertFlightWeatherDynamic(companyCode, flightWeatherDynamicDto));
    }

    /**
     * 修改气象信息
     */
    @ApiOperation("修改气象信息")
    @PreAuthorize("@ss.hasPermi('system:flightWeatherInfo:edit')")
    @PutMapping("/edit")
    public AjaxResult edit(@RequestHeader("companyCode") String companyCode, @RequestBody FlightWeatherDynamicDto flightWeatherDynamicDto) {
        return toAjax(flightWeatherInfoService.updateFlightWeatherDynamic(companyCode, flightWeatherDynamicDto));
    }

}