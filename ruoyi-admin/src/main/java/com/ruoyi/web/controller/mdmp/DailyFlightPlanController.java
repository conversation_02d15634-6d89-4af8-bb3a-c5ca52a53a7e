package com.ruoyi.web.controller.mdmp;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.system.domain.mdmp.DailyFlightPlan;
import com.ruoyi.system.domain.mdmp.NextDayFlightPlan;
import com.ruoyi.system.param.mdmp.DailyFlightPlanActualTimeParam;
import com.ruoyi.system.param.mdmp.DailyFlightPlanListParam;
import com.ruoyi.system.service.mdmp.IDailyFlightPlanService;
import com.ruoyi.system.service.mdmp.ILongTermFlightPlanService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 当日计划飞行Controller
 *
 * <AUTHOR>
 * @date 2024-03-29
 */
@RestController
@Api(value = "A当日计划飞行管理", tags = "A当日计划飞行管理")
@RequestMapping("/mdmp/dailyFlightPlan")
public class DailyFlightPlanController extends BaseController {
    @Autowired
    private IDailyFlightPlanService dailyFlightPlanService;

    @Autowired
    private ILongTermFlightPlanService longTermFlightPlanService;

    /**
     * 查询当日计划飞行列表
     */
    //@PreAuthorize("@ss.hasPermi('system:dailyFlightPlan:list')")
    @ApiOperation("查询当日计划飞行列表")
    @GetMapping("/list")
    public TableDataInfo list(DailyFlightPlanListParam param) {
        startPage();
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        param.setDeptCodeList(deptCodeList);
        List<DailyFlightPlan> list = dailyFlightPlanService.selectDailyFlightPlanList(param);
        return getDataTable(list);
    }

    /**
     * 导出当日计划飞行列表
     */
    //@PreAuthorize("@ss.hasPermi('system:dailyFlightPlan:export')")
    @Log(title = "当日计划飞行", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    @ApiOperation("导出当日计划飞行列表")
    public void export(HttpServletResponse response, DailyFlightPlanListParam param) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        param.setDeptCodeList(deptCodeList);
        List<DailyFlightPlan> list = dailyFlightPlanService.selectDailyFlightPlanList(param);
        ExcelUtil<DailyFlightPlan> util = new ExcelUtil<DailyFlightPlan>(DailyFlightPlan.class);
        util.exportExcel(response, list, "当日计划飞行数据");
    }

    /**
     * 获取当日计划飞行详细信息
     */
    //@PreAuthorize("@ss.hasPermi('system:dailyFlightPlan:query')")
    @GetMapping(value = "/{id}")
    @ApiOperation("获取当日计划飞行详细信息")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(dailyFlightPlanService.selectDailyFlightPlanById(id));
    }


    /**
     * 验证当日计划飞行
     */
    //@PreAuthorize("@ss.hasPermi('system:dailyFlightPlan:verify')")
    @Log(title = "验证当日计划飞行")
    @PostMapping(value = "/verify")
    @ApiOperation("验证当日计划飞行")
    public AjaxResult verifyFlightPlan(@Validated @RequestBody DailyFlightPlan dailyFlightPlan) {
        return success(dailyFlightPlanService.verifyDailyFlightPlan(dailyFlightPlan));
    }


    /**
     * 验证当日计划飞行 根据id获取冲突
     */
    //@PreAuthorize("@ss.hasPermi('system:dailyFlightPlan:verifyById')")
    @Log(title = "验证当日计划飞行 根据id获取冲突")
    @GetMapping(value = "/verifyById/{id}")
    @ApiOperation("验证当日计划飞行 根据id获取冲突")
    public AjaxResult verifyDailyFlightPlan(@PathVariable("id") Long id) {
        return success(longTermFlightPlanService.verifyLongTermFlightPlan(dailyFlightPlanService.selectDailyFlightPlanById(id)));
    }


    /**
     * 新增当日计划飞行
     */
    //@PreAuthorize("@ss.hasPermi('system:dailyFlightPlan:add')")
    @Log(title = "当日计划飞行", businessType = BusinessType.INSERT)
    @PostMapping
    @ApiOperation("新增当日计划飞行")
    public AjaxResult add(@Validated @RequestBody DailyFlightPlan dailyFlightPlan) {
        dailyFlightPlan.setCreator(getUsername());
        dailyFlightPlan.setCreationTime(DateUtils.getTime());
        dailyFlightPlan.setDeptCode(getLoginUser().getDeptCode());
        return toAjax(dailyFlightPlanService.insertDailyFlightPlan(dailyFlightPlan));
    }

    /**
     * 修改当日计划飞行
     */
    //@PreAuthorize("@ss.hasPermi('system:dailyFlightPlan:edit')")
    @Log(title = "当日计划飞行", businessType = BusinessType.UPDATE)
    @PutMapping
    @ApiOperation("修改当日计划飞行")
    public AjaxResult edit(@Validated @RequestBody DailyFlightPlan dailyFlightPlan) {
        return toAjax(dailyFlightPlanService.updateDailyFlightPlan(dailyFlightPlan));
    }

    /**
     * 修改当日计划飞行 实际时间
     * 修改实际起飞到达时间
     * 参数 id  实际起飞 实际到达
     */
    //@PreAuthorize("@ss.hasPermi('system:dailyFlightPlan:editActualTime')")
    @Log(title = "当日计划飞行", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/editActualTime")
    @ApiOperation("修改当日计划飞行")
    public AjaxResult editActualTime(@RequestBody DailyFlightPlanActualTimeParam param) {
        return toAjax(dailyFlightPlanService.updateDailyFlightPlanActualTime(param));
    }


    /**
     * 删除当日计划飞行
     */
    // @PreAuthorize("@ss.hasPermi('system:dailyFlightPlan:remove')")
    @Log(title = "当日计划飞行", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    @ApiOperation("删除当日计划飞行")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(dailyFlightPlanService.deleteDailyFlightPlanByIds(ids));
    }

    /**
     * 审核当日飞行计划
     */
    //@PreAuthorize("@ss.hasPermi('system:plan:auditing')")
    @Log(title = "审核当日飞行计划", businessType = BusinessType.UPDATE)
    @PostMapping(value = "/auditing")
    @ApiOperation("审核当日飞行计划")
    public AjaxResult auditing(@RequestBody DailyFlightPlan dailyFlightPlan) {

        return toAjax(dailyFlightPlanService.auditing(dailyFlightPlan));
    }
}
