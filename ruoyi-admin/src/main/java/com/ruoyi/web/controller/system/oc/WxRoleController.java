package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.oc.WxRole;
import com.ruoyi.system.service.oc.WxRoleService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 微信用户管理
 *
 * <AUTHOR>
 */
@Api("微信角色管理")
@RestController
@RequestMapping("/system/wxRole/")
public class WxRoleController {
    @Resource
    private WxRoleService wxRoleService;

    /**
     * 查询所有微信角色
     */
    @ApiOperation("查询所有微信角色")
    @GetMapping("/listAll")
    public CommonResult<List<WxRole>> list() {
        return wxRoleService.list();
    }
}
