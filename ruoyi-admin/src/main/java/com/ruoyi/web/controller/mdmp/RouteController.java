package com.ruoyi.web.controller.mdmp;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.mdmp.Route;
import com.ruoyi.system.service.mdmp.IRouteService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 航线Controller
 *
 * <AUTHOR>
 * @date 2024-03-29
 */

@Api(value = "航线信息管理", tags = "航线信息管理")
@RestController
@RequestMapping("/mdmp/route")
public class RouteController extends BaseController {
    @Autowired
    private IRouteService routeService;

    /**
     * 查询航线列表
     */
    @ApiOperation("查询航线列表")
    // @PreAuthorize("@ss.hasPermi('mdmp:route:list')")
    @GetMapping("/list")
    public TableDataInfo list(Route route) {
        startPage();
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        route.setDeptCodeList(deptCodeList);
        List<Route> list = routeService.selectRouteList(route);
        return getDataTable(list);
    }

    /**
     * 导出航线列表
     */
    //@PreAuthorize("@ss.hasPermi('mdmp:route:export')")
    @Log(title = "航线", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Route route) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        route.setDeptCodeList(deptCodeList);
        List<Route> list = routeService.selectRouteList(route);
        ExcelUtil<Route> util = new ExcelUtil<Route>(Route.class);
        util.exportExcel(response, list, "航线数据");
    }

    /**
     * 获取航线详细信息
     */
    @ApiOperation("获取航线详细信息")
    // @PreAuthorize("@ss.hasPermi('mdmp:route:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {

        return success(routeService.selectRouteById(id));
    }

    /**
     * 新增航线
     */
    @ApiOperation("新增航线")
    //@PreAuthorize("@ss.hasPermi('mdmp:route:add')")
    @Log(title = "航线", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody Route route) {
        route.setDeptCode(getLoginUser().getDeptCode());
        return toAjax(routeService.insertRoute(route));
    }

    /**
     * 修改航线
     */
    @ApiOperation("修改航线")
    //@PreAuthorize("@ss.hasPermi('mdmp:route:edit')")
    @Log(title = "航线", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody Route route) {
        return toAjax(routeService.updateRoute(route));
    }

    /**
     * 删除航线
     */
    @ApiOperation("删除航线")
    //@PreAuthorize("@ss.hasPermi('mdmp:route:remove')")
    @Log(title = "航线", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(routeService.deleteRouteById(id));
    }
}
