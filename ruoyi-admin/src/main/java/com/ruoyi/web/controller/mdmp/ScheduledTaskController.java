package com.ruoyi.web.controller.mdmp;

import com.ruoyi.system.mapper.mdmp.FlightDataMapper;
import com.ruoyi.system.task.SimulationSendCoordinateTask;
import lombok.RequiredArgsConstructor;
import org.apache.activemq.ActiveMQConnection;
import org.apache.activemq.ActiveMQConnectionFactory;
import org.apache.activemq.command.ActiveMQQueue;
import org.springframework.web.bind.annotation.*;


import org.apache.activemq.broker.jmx.QueueViewMBean;

import javax.management.MalformedObjectNameException;
import javax.management.ObjectName;
import javax.jms.ConnectionFactory;
import javax.management.MBeanServerConnection;
import javax.management.remote.JMXConnector;
import javax.management.remote.JMXConnectorFactory;
import javax.management.remote.JMXServiceURL;

import javax.jms.*;
import java.io.IOException;
import java.net.MalformedURLException;
import java.util.HashMap;

import java.net.HttpURLConnection;
import java.net.URL;

@RestController
@RequiredArgsConstructor
@RequestMapping("/mdmp/task")
public class ScheduledTaskController {

    private final SimulationSendCoordinateTask sendCoordinateTask;
    private final FlightDataMapper flightDataMapper;
    private final ConnectionFactory connectionFactory;

    @GetMapping("/startTask")
    public String startTask() {
        // 当按钮被点击时，定时任务将会开始运行
        sendCoordinateTask.startTask();
        return "Task started";
    }

    @GetMapping("/stopTask/{date}/{targetIdentification}")
    public String stopTask(@PathVariable("date") String date, @PathVariable("targetIdentification") String targetIdentification) throws IOException {
        sendCoordinateTask.stopTask();
        //清空队列消息
//        extracted("receiveAbsFlightData.queue");
//        delete();
        //删除飞机刚飞过的历史记录
        int i = flightDataMapper.deleteAll(date, targetIdentification);
        System.out.println("历史记录:"+i);
        return "Task stopped";
    }



    /**清空队列中的消息*/
    private void extracted(String queueName){
        String brokerURL = "tcp://localhost:61616";

//        ConnectionFactory connectionFactory = new ActiveMQConnectionFactory("admin", "admin", brokerURL);
        Connection connection = null;
        Session session = null;
        MessageConsumer consumer = null;

        try {
            // 创建连接
            connection = connectionFactory.createConnection();
            connection.start();
            // 创建会话
            session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE); // 使用自动确认模式
            // 创建队列目标
            Destination queue = session.createQueue(queueName);
            // 创建消费者
            consumer = session.createConsumer(queue);
            // 消费消息直到队列为空
            while (true) {
                Message message = consumer.receive(1000); // 设置超时时间为1000毫秒
                if (message == null) {
                    // 如果没有收到消息，则认为队列已空（或超时）
                    break;
                }
                // 处理消息（在这里我们只是简单地打印消息ID，并不做其他处理）
                // 在AUTO_ACKNOWLEDGE模式下，消息一旦被接收并处理，就会自动确认并从队列中移除
//                System.out.println("Consumed message ID: " + message.getJMSMessageID());
            }
            System.out.println("All messages consumed from queue: " + queueName);

        } catch (JMSException e) {
            e.printStackTrace();
        } finally {
            // 关闭资源
            closeQuietly(consumer);
            closeQuietly(session);
            closeQuietly(connection);
        }
    }

    // 辅助方法，用于安全地关闭JMS资源
    private static void closeQuietly(AutoCloseable resource) {
        if (resource != null) {
            try {
                resource.close();
            } catch (Exception e) {
                // 忽略关闭时的异常
            }
        }
    }

    /**清空队列中的消息*/
//    private void delete() throws IOException {
//        String ACTIVEMQ_REST_API_URL = "http://localhost:8161/api/jolokia/read/org.apache.activemq:type=Broker,brokerName=localhost,destinationType=Queue,destinationName=receiveAbsFlightData.queue/Purge";
//        String ACTIVEMQ_USERNAME = "admin";
//        String ACTIVEMQ_PASSWORD = "admin";
//        URL url = new URL(ACTIVEMQ_REST_API_URL);
//        HttpURLConnection connection = (HttpURLConnection) url.openConnection();
//        connection.setRequestMethod("GET");
//        connection.setRequestProperty("Authorization", "Basic " + java.util.Base64.getEncoder().encodeToString((ACTIVEMQ_USERNAME + ":" + ACTIVEMQ_PASSWORD).getBytes()));
//
//        int responseCode = connection.getResponseCode();
//        if (responseCode == HttpURLConnection.HTTP_OK) {
//            System.out.println("Queue purged successfully.");
//        } else {
//            System.err.println("Failed to purge queue. Response code: " + responseCode);
//        }
//
//        connection.disconnect();
//
//    }

    /**清空队列中的消息*/
//    private static String extracted(String queueName){
//        // 连接ActiveMQ的参数
//        String user = "admin";
//        String password = "admin";
//        String brokerURL = "tcp://localhost:61616";
////        String queueName = "your.queue.name";
//
//        ConnectionFactory connectionFactory = new ActiveMQConnectionFactory(user, password, brokerURL);
//
//        try (Connection connection = connectionFactory.createConnection();
//             Session session = connection.createSession(false, Session.AUTO_ACKNOWLEDGE);
//             MessageConsumer consumer = session.createConsumer(new ActiveMQQueue(queueName))) {
//
//            connection.start();
//
//            // 清除队列中的所有消息
//            consumer.receiveNoWait(); // 清除消息，不阻塞
//
//            System.out.println("Queue purged successfully.");
//        } catch (JMSException e) {
//            e.printStackTrace();
//        }
//        return "";
//    }
}
