package com.ruoyi.web.controller.mdmp;

import java.util.List;
import javax.servlet.http.HttpServletResponse;

import com.ruoyi.common.core.domain.AjaxResult;
import com.ruoyi.common.core.domain.entity.SysUser;
import com.ruoyi.common.core.domain.model.LoginUser;
import com.ruoyi.system.domain.mdmp.Aircraft;
import com.ruoyi.system.service.ISysDeptService;
import com.ruoyi.system.service.mdmp.IAircraftService;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ruoyi.common.annotation.Log;
import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.enums.BusinessType;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.core.page.TableDataInfo;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2024-10-18
 */
@RestController
@RequestMapping("/system/aircraft")
public class AircraftController extends BaseController {
    @Autowired
    private IAircraftService aircraftService;

    @Autowired
    private ISysDeptService deptService;

    /**
     * 查询【请填写功能名称】列表
     */
    @ApiOperation("查询机型列表")
    //@PreAuthorize("@ss.hasPermi('system:aircraft:list')")
    @GetMapping("/list")
    public TableDataInfo list(Aircraft aircraft) {
        startPage();
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        aircraft.setDeptCodeList(deptCodeList);
        List<Aircraft> list = aircraftService.selectAircraftList(aircraft);
        return getDataTable(list);
    }

    /**
     * 导出【请填写功能名称】列表
     */
    @PreAuthorize("@ss.hasPermi('system:aircraft:export')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, Aircraft aircraft) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        aircraft.setDeptCodeList(deptCodeList);
        List<Aircraft> list = aircraftService.selectAircraftList(aircraft);
        ExcelUtil<Aircraft> util = new ExcelUtil<Aircraft>(Aircraft.class);
        util.exportExcel(response, list, "【请填写功能名称】数据");
    }

    /**
     * 获取【请填写功能名称】详细信息
     */
    @ApiOperation("获取机型详细信息")
    //@PreAuthorize("@ss.hasPermi('system:aircraft:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return success(aircraftService.selectAircraftById(id));
    }

    /**
     * 新增【请填写功能名称】
     */
    @ApiOperation("新增")
    //@PreAuthorize("@ss.hasPermi('system:aircraft:add')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody Aircraft aircraft) {
        boolean overlap = aircraftService.checkAircraftOverlap(aircraft);
        if (overlap) {
            return  error("新增航空器失败，航空器有效时间与已有数据重合");
        }
        aircraft.setDeptCode(getLoginUser().getDeptCode());
        return toAjax(aircraftService.insertAircraft(aircraft));
    }

    /**
     * 修改【请填写功能名称】
     */
    @ApiOperation("修改")
    //@PreAuthorize("@ss.hasPermi('system:aircraft:edit')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody Aircraft aircraft) {
        boolean overlap = aircraftService.checkAircraftOverlapWhenUpdate(aircraft);
        if (overlap) {
            return  error("修改航空器失败，航空器有效时间与已有数据重合");
        }
        //aircraft.setDeptCode(getLoginUser().getDeptCode());
        return toAjax(aircraftService.updateAircraft(aircraft));
    }

    /**
     * 删除【请填写功能名称】
     */
    @ApiOperation("删除")
    // @PreAuthorize("@ss.hasPermi('system:aircraft:remove')")
    @Log(title = "【请填写功能名称】", businessType = BusinessType.DELETE)
    @DeleteMapping("/{id}")
    public AjaxResult remove(@PathVariable Long id) {
        return toAjax(aircraftService.deleteAircraftById(id));
    }
}
