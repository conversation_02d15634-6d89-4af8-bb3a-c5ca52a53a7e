package com.ruoyi.web.controller.mdmp;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.system.domain.mdmp.EnterLeavePort;
import com.ruoyi.system.domain.mdmp.dto.QueryEnterLeavePortInfoDTO;
import com.ruoyi.system.domain.mdmp.dto.UpdateEnterLeavePortDTO;
import com.ruoyi.system.service.mdmp.EnterLeavePortService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * <AUTHOR>
 */
@Api(value = "电子行程单中的进离场区管理",tags = "电子行程单中的进离场区管理")
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/mdmp/enterLeavePort")
public class EnterLeavePortController {

    private final EnterLeavePortService enterLeavePortService;

    /**
     * 根据航班ID查询进离场区详情
     */
    @ApiOperation("根据航班ID查询进离场区详情")
    @PostMapping("/getInfo")
    public CommonResult<EnterLeavePort> getInfo(@Valid @RequestBody QueryEnterLeavePortInfoDTO dto) {
        return enterLeavePortService.getInfo(dto.getFlightId());
    }

    /**
     * 修改进离场区信息
     */
    @ApiOperation("修改进离场区信息")
    @PostMapping("/update")
    public CommonResult<String> update(@Valid @RequestBody UpdateEnterLeavePortDTO dto) {
        return enterLeavePortService.update(dto);
    }

}
