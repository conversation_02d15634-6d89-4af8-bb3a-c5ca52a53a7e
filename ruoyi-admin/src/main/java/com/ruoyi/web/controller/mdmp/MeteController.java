package com.ruoyi.web.controller.mdmp;


import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.common.core.page.TableDataInfo;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.mdmp.vo.MeteInfo;
import com.ruoyi.system.domain.mdmp.vo.MeteVo;
import com.ruoyi.system.domain.type.DataSourceType;
import com.ruoyi.system.domain.type.MeteType;
import com.ruoyi.system.mapper.mdmp.VisibilityVoMapper;
import com.ruoyi.system.param.mdmp.MeteInfoParam;
import com.ruoyi.system.param.mdmp.MeteParam;
import com.ruoyi.system.service.mdmp.*;
import com.ruoyi.system.service.mdmp.impl.UMpDataServiceImpl;
import com.ruoyi.system.service.mdmp.impl.VMpDataServiceImpl;
import com.ruoyi.system.util.DataExtractor;
import com.ruoyi.system.util.InputStreamToFileUtil;
import com.ruoyi.system.util.MeteUtil;
import com.wsp.sdk.service.api.WspMeteoDataApi;
import com.wsp.sdk.service.base.R;
import com.wsp.sdk.service.dto.request.GetMeteoDataTimeLineReqDTO;
import com.wsp.sdk.service.dto.request.GetMeteoImgReqDTO;
import com.wsp.sdk.service.dto.request.GetMeteoJsonDataReqDTO;
import com.wsp.sdk.service.dto.response.netcdf.NetcdfTimeSequenceDto;
import com.wsp.sdk.service.service.MeteoDataService;
import feign.Response;
import io.swagger.annotations.ApiOperation;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.DataBufferByte;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.nio.ByteBuffer;
import java.nio.ByteOrder;
import java.util.*;
import java.util.List;
import java.util.zip.GZIPInputStream;

/**
 * <AUTHOR>
 * @date ：Created in 2024/11/25 13:37
 * @description：
 * @modified By：
 * @version: $
 */
@RestController
@RequestMapping("/system/mete")
public class MeteController {

    @Resource
    private MeteoDataService meteoDataService;

    @Resource
    private UMpDataServiceImpl uMpDataService;

    @Resource
    private VMpDataServiceImpl vMpDataService;

    @Resource
    private IRainfallMpDataService iRainfallMpDataService;


    @Resource
    private IVisibilityMpDataService iVisibilityMpDataService;

    @Resource
    private ITemperatureMpDataService iTemperatureMpDataService;

    @Resource
    private IThunderstormMpDataService iThunderstormMpDataService;

    @Resource
    private VisibilityVoMapper visibilityVoMapper;


    @Resource
    private ISanddustMpDataService iSanddustMpDataService;


    @ApiOperation("获取数据时间列表接口")
    @GetMapping("/getTimeLine")
    public CommonResult<RadarContent> getTimeLine(int type) throws IOException {
        // 创建一个GetMeteoDataTimeLineReqDTO对象
        GetMeteoDataTimeLineReqDTO param = new GetMeteoDataTimeLineReqDTO();
        // 设置数据源为1
        param.setDataSource(1);
        // 根据type的值设置数据源
        if (type == DataSourceType.H) {
            param.setDataSource(DataSourceType.H);
        } else if (type == 7) {
            param.setDataSource(DataSourceType.SD);
        }
        // 设置数据代码
        param.setDataCode(MeteType.getWeatherConstant(type));
        // 调用meteoDataService的getTimeLine方法获取数据时间列表
        R<NetcdfTimeSequenceDto> r = meteoDataService.getTimeLine(param);
        // 获取预测时间序列
        List<Long> fcstTimeSequence = r.getContent().getFcstTimeSequence();
        // 获取实时时间序列
        List<Long> reatimeTimeSequence = r.getContent().getReatimeTimeSequence();
        // 创建一个RadarContent对象
        RadarContent radarContent = new RadarContent(fcstTimeSequence, reatimeTimeSequence);
        // 返回CommonResult对象
        return CommonResult.success(radarContent);
    }


    @ApiOperation("查询风数据")
    @GetMapping("/getJsonData")
    public CommonResult<List<List<Double>>> getJsonData(MeteParam meteParam) throws IOException {
//        List<Double> u = fetchWindData(meteParam, "U_WIND_ISOBARIC");
//        List<Double> v = fetchWindData(meteParam, "V_WIND_ISOBARIC");
//        List<List<Double>> result = new ArrayList<>();
//        if (u.size() == v.size()) {
//            // 遍历 u 和 v 列表，将它们组合成子列表
//            for (int i = 0; i < u.size(); i++) {
//                List<Double> pair = new ArrayList<>();
//                pair.add(u.get(i)); // 添加 u 值
//                pair.add(v.get(i)); // 添加 v 值
//                result.add(pair); // 将子列表添加到嵌套列表中
//            }
//        }
//        return CommonResult.success(result);

        List<MpData> u = uMpDataService.getList(meteParam);
        List<VMpData> v = vMpDataService.getList(meteParam);
        // 用于存储结果的 List
        List<List<Double>> result = new ArrayList<>();
        // 假设 u 和 v 的长度相同
        if (u.size() == v.size()) {
            for (int i = 0; i < u.size(); i++) {
                // 获取 u 和 v 中的 dataValue
                List<Double> pair = new ArrayList<>();
                pair.add(u.get(i).getDataValue());
                pair.add(v.get(i).getDataValue());
                result.add(pair);
            }
        }
        return CommonResult.success(result);
    }


    public List<Integer> radarDate(GetMeteoImgReqDTO getMeteoImgReqDTO) {
        List<Integer> values = new ArrayList<>();
        Response response = meteoDataService.getImgInputStream(getMeteoImgReqDTO);
        try {
            InputStream inputStream = response.body().asInputStream();
            BufferedImage originalImage = ImageIO.read(inputStream);

            if (originalImage == null) {
                System.out.println("无法读取图像数据");
                return values;
            }

            // 创建一个新的 BufferedImage 对象，类型为灰度图
            BufferedImage grayImage = new BufferedImage(
                    originalImage.getWidth(),
                    originalImage.getHeight(),
                    BufferedImage.TYPE_BYTE_GRAY
            );

            // 获取 Graphics2D 对象，用于在灰度图像上绘图
            Graphics2D g2d = grayImage.createGraphics();

            // 将原始图像绘制到灰度图像上
            g2d.drawImage(originalImage, 0, 0, null);

            // 释放图形上下文
            g2d.dispose();

            // 获取图像的宽度和高度
            int width = grayImage.getWidth();
            int height = grayImage.getHeight();
            // 打印图像尺寸信息
            System.out.println("图像宽度: " + width);
            System.out.println("图像高度: " + height);

            int[][] pixels = new int[width][height];


            // 获取图像中的所有像素数据 每个像素由一个字节表示，范围是0（黑色）到255（白色）
            byte[] pixelData = ((DataBufferByte) grayImage.getRaster().getDataBuffer()).getData();

            // 遍历所有像素并填充到二维数组中
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    // 计算一维数组中的索引位置
                    int index = y * width + x;
                    // 将字节转换为无符号整数（0-255）
                    pixels[x][y] = pixelData[index] & 0xff;
                }
            }

            // 目标经纬度范围
            double minLat = 43.7;
            double maxLat = 44.9;
            double minLon = 84.80;
            double maxLon = 86.45;

            // 计算目标范围在数组中的索引
            int startLatIndex = (int) Math.round((54.2 - maxLat) / 0.01); // 纬度起始索引
            int endLatIndex = (int) Math.round((54.2 - minLat) / 0.01); // 纬度结束索引
            int startLonIndex = (int) Math.round((minLon - 73) / 0.01); // 经度起始索引
            int endLonIndex = (int) Math.round((maxLon - 73) / 0.01); // 经度结束索引

            // 提取目标范围的像素值（纬度从小到大，经度从小到大）
            for (int i = endLatIndex; i > startLatIndex; i--) { // 纬度从小到大

                for (int j = startLonIndex; j < endLonIndex; j++) { // 经度从小到大
                    int pixelValue = pixels[i][j];
                    values.add(pixelValue);
                    BigDecimal currentLat = BigDecimal.valueOf(54.2)
                            .subtract(BigDecimal.valueOf(i).multiply(BigDecimal.valueOf(0.01)));
                    BigDecimal currentLon = BigDecimal.valueOf(73).add(BigDecimal.valueOf(j).multiply(BigDecimal.valueOf(0.01)));
                    System.out.println(currentLat + "," + currentLon);
                }
            }
            return values;
        } catch (IOException e) {
            throw new RuntimeException(e);
        }

    }


    public List<Double> fetchWindData(MeteParam meteParam, String dataCode) {
        GetMeteoJsonDataReqDTO getMeteoImgReqDTO = new GetMeteoJsonDataReqDTO();
        BeanUtils.copyProperties(meteParam, getMeteoImgReqDTO);
        getMeteoImgReqDTO.setDataCode(dataCode);
        Response response = meteoDataService.getJsonData(getMeteoImgReqDTO);
        Response.Body body = response.body();
        List<Double> doubles = new ArrayList<>();
        try (InputStream inputStream = body.asInputStream();
             GZIPInputStream in = new GZIPInputStream(inputStream)) {
            String json = IoUtil.readUtf8(in);
            MeteVo meteVo = JSONUtil.toBean(json, MeteVo.class);
            if (dataCode.equals(MeteType.PRECIPITATION) || dataCode.equals(MeteType.VIS) || dataCode.equals(MeteType.TEMP_ISOBARIC)) {
                MeteUtil gridMapping = new MeteUtil(meteVo.getData(), meteVo.getBboxLonMin(), meteVo.getBboxLonMax(), meteVo.getBboxLatMin(), meteVo.getBboxLatMax(), meteVo.getLonRes());
                doubles = gridMapping.mapDataToB();
            } else {
                Map<String, Double> resultMap = MeteUtil.convertListToMap(meteVo, meteVo.getData());
                // 提取数据
                Map<String, Double> smallBoxData = DataExtractor.extractSmallBoxData(resultMap);
                // 自定义排序
                List<Map.Entry<String, Double>> entries = new ArrayList<>(smallBoxData.entrySet());

                // 自定义排序
                Collections.sort(entries, new Comparator<Map.Entry<String, Double>>() {
                    @Override
                    public int compare(Map.Entry<String, Double> o1, Map.Entry<String, Double> o2) {
                        // 拆分经度和纬度
                        String[] key1 = o1.getKey().split("_");
                        String[] key2 = o2.getKey().split("_");

                        // 比较经度
                        double longitude1 = Double.parseDouble(key1[0]);
                        double longitude2 = Double.parseDouble(key2[0]);
                        int comp = Double.compare(longitude1, longitude2);
                        if (comp != 0) {
                            return comp; // 如果经度不同，返回比较结果
                        }

                        // 比较纬度
                        double latitude1 = Double.parseDouble(key1[1]);
                        double latitude2 = Double.parseDouble(key2[1]);
                        return Double.compare(latitude1, latitude2); // 返回纬度比较结果
                    }
                });
                for (Map.Entry<String, Double> entry : entries) {
                    doubles.add(entry.getValue());
                }
            }
            return doubles;
        } catch (IOException e) {
            return doubles;
        }
    }

    @ApiOperation("查询降雨数据")
    @GetMapping("/getPrecipitationJsonData")
    public CommonResult<List<Double>> getPrecipitationJsonData(MeteParam meteParam) throws IOException {
        // List<Double> u = fetchWindData(meteParam, MeteType.getWeatherConstant(3));
        List<Double> u = iRainfallMpDataService.getList(meteParam);
        return CommonResult.success(u);
    }

    @ApiOperation("查询能见度数据")
    @GetMapping("/getVisJsonData")
    public CommonResult<List<Double>> getVisJsonData(MeteParam meteParam) throws IOException {
        // List<Double> u = fetchWindData(meteParam, MeteType.getWeatherConstant(4));
        List<Double> u = iVisibilityMpDataService.getList(meteParam);
        return CommonResult.success(u);
    }

    @ApiOperation("查询沙尘数据")
    @GetMapping("/getDustJsonData")
    public CommonResult<List<Double>> getDustJsonData(MeteParam meteParam) throws IOException {
        // List<Double> u = fetchWindData(meteParam, MeteType.getWeatherConstant(4));
        List<Double> u = iSanddustMpDataService.getList(meteParam);
        return CommonResult.success(u);
    }


    @ApiOperation("查询雷达数据")
    @GetMapping("/getRadarDate")
    public CommonResult<List<Integer>> getRadarDate(MeteParam meteParam) throws IOException {
        GetMeteoImgReqDTO getMeteoImgReqDTO = new GetMeteoImgReqDTO();
        getMeteoImgReqDTO.setDataCode(MeteType.RADAR);
        getMeteoImgReqDTO.setDataSource(MeteType.getWeatherConstant(MeteType.RADAR));
        getMeteoImgReqDTO.setTime(meteParam.getTime() * 1000);
        //List<Integer> u = iThunderstormMpDataService.getList(meteParam);
        return CommonResult.success(radarDate(getMeteoImgReqDTO));
    }

    @ApiOperation("查询温度数据")
    @GetMapping("/getTempIsobaric")
    public CommonResult<List<Double>> getTempIsobaric(MeteParam meteParam) throws IOException {
        //List<Double> u = fetchWindData(meteParam, MeteType.getWeatherConstant(5));
        List<Double> u = iTemperatureMpDataService.getList(meteParam);
        return CommonResult.success(u);
    }

    @ApiOperation("查询Int8Array数据")
    @GetMapping("/data")
    public ResponseEntity<byte[]> getData() {
//        List<Double> doubleList = visibilityVoMapper.getDoubleList();
//        List<List<Double>> a=new ArrayList<>();
//        ByteBuffer buffer = ByteBuffer.allocate(doubleList.size() * 4)
//                .order(ByteOrder.LITTLE_ENDIAN);
//        for (double d : doubleList) {
//            buffer.putFloat((float) d); // 强制转换为 float
//        }
//        byte[] byteArray = buffer.array();
//        return ResponseEntity.ok()
//                .contentType(MediaType.APPLICATION_OCTET_STREAM)
//                .body(byteArray);
        List<List<Double>> a = new ArrayList<>();
        int totalElements = a.stream().mapToInt(List::size).sum();
        ByteBuffer buffer = ByteBuffer.allocate(totalElements * 4)
                .order(ByteOrder.LITTLE_ENDIAN);
        // 遍历二维列表并填充缓冲区
        for (List<Double> row : a) {
            for (Double value : row) {
                buffer.putFloat(value.floatValue()); // 强制转换为float
            }
        }

        // 转换为字节数组
        byte[] byteArray = buffer.array();

        // 返回二进制响应
        return ResponseEntity.ok()
                .contentType(MediaType.APPLICATION_OCTET_STREAM)
                .body(byteArray);
    }
}
