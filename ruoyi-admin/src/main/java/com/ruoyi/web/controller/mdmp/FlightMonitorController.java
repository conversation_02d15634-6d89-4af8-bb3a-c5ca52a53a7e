package com.ruoyi.web.controller.mdmp;

import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.system.domain.mdmp.Airspace;
import com.ruoyi.system.domain.mdmp.FlightData;
import com.ruoyi.system.domain.mdmp.FlightPlanAircraftModel;
import com.ruoyi.system.domain.mdmp.vo.*;
import com.ruoyi.system.job.HistoryFlightDataConsumer;
import com.ruoyi.system.param.mdmp.MonitorAirspaceParams;
import com.ruoyi.system.param.mdmp.MonitorHistoryFlightDataParams;
import com.ruoyi.system.param.mdmp.MonitorRouteParams;
import com.ruoyi.system.service.mdmp.FlightMonitorService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiImplicitParam;
import io.swagger.annotations.ApiImplicitParams;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 航线监控管理
 *
 * <AUTHOR>
 */
@Api(value = "航线监控管理", tags = "航线监控管理")
@RestController
@RequiredArgsConstructor
@RequestMapping("/mdmp/flightMonitor")
public class FlightMonitorController {

    private final FlightMonitorService flightMonitorService;
    private final HistoryFlightDataConsumer historyFlightDataConsumer;

    /**
     * 查询当天有效空域集合
     */
    @ApiOperation("当日有效空域集合")
    @PostMapping("/airspaceList")
    public PageCommonResult<List<AirspaceVO>> airspaceList() {
        return flightMonitorService.airspaceList();
    }

    /**
     * 查询当天航线集合
     */
    @ApiOperation("当日航线集合")
    @PostMapping("/routeList")
    public PageCommonResult<List<RouteVO>> routeList() {
        return flightMonitorService.routeList();
    }

    /**
     * 查询当日在飞航空器集合
     */
    @ApiOperation("当日在飞航空器集合")
    @PostMapping("/aircraftList")
    public PageCommonResult<List<AircraftVO>> aircraftList() {
        return flightMonitorService.aircraftList();
    }

    /**
     * 查询当日航班计划集合
     */
    @ApiOperation("当日航班计划集合")
    @PostMapping("/flightPlanList")
    public PageCommonResult<List<DailyFlightPlanVO>> flightPanList() {
        return flightMonitorService.flightPanList();
    }

    /**
     * 查询空域
     */
    @ApiOperation("监控空域")
    @PostMapping("/airspace")
    public PageCommonResult<List<Airspace>> queryAirspace(@RequestBody MonitorAirspaceParams airspaceParams) {
        return flightMonitorService.queryAirspace(airspaceParams);
    }

    /**
     * 查询计划航线
     */
    @ApiOperation("监控计划航线")
    @PostMapping("/planRoute")
    public CommonResult<RouteCoordinateVO> queryPlanRoute(@RequestBody MonitorRouteParams routeParams) {
        return flightMonitorService.queryPlanRoute(routeParams);
    }

    /**
     * 查询历史飞行数据
     */
    @ApiOperation("查询历史飞行数据")
    @PostMapping("/historyData")
    public CommonResult<List<FlightData>> historyData(@RequestBody MonitorHistoryFlightDataParams historyDataParams) {
        return flightMonitorService.historyData(historyDataParams);
    }

    /**
     * 根据日期查询当日执飞飞行器
     */
    @ApiOperation("根据日期查询当日执飞飞行器")
    @PostMapping("/queryAircraft")
    public CommonResult<List<FlightPlanAircraftModel>> queryAircraft(@RequestBody String flightDate) {
        return flightMonitorService.queryAircraft(flightDate);
    }

    /**
     * 通过机尾号在socket中获取指定飞机的历史轨迹
     */
    @ApiOperation("根据日期查询当日执飞飞行器")
    @ApiImplicitParams({@ApiImplicitParam(name = "status", value = "状态开关（0:关闭, 1:开启）", required = true, dataType = "Integer", dataTypeClass = Integer.class),
            @ApiImplicitParam(name = "temporaryAircraftReg", value = "指定机尾号", dataType = "String", dataTypeClass = String.class)})
    @PostMapping("/designatedHistory/{status}/{temporaryAircraftReg}")
    public CommonResult<String> designatedHistory(@PathVariable("status") Integer status,
                                                  @PathVariable("temporaryAircraftReg") String temporaryAircraftReg) {
        // 当按钮被点击时，定时任务将会开始运行
        historyFlightDataConsumer.getTemporaryAircraftReg(status, temporaryAircraftReg);
        if (status == 1) {
            return CommonResult.success("指定飞机:" + temporaryAircraftReg + "历史轨迹");
        } else {
            return CommonResult.success("取消发送历史轨迹");
        }
    }


}
