package com.ruoyi.web.controller.system.oc;

import com.ruoyi.system.domain.oc.vo.word.WordFlightWeatherInfoVo;
import com.ruoyi.system.domain.oc.vo.word.WordMeteorologicalVo;
import com.ruoyi.system.util.word.WeatherInfoWordUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;
import java.util.List;

/**
 * 气象信息导出测试类
 */
public class WeatherInfoExportTest {
    
    private static final Logger logger = LoggerFactory.getLogger(WeatherInfoExportTest.class);
    
    public static void main(String[] args) {
        WeatherInfoExportTest test = new WeatherInfoExportTest();
        
        try {
            logger.info("开始气象信息导出测试");
            test.testWeatherInfoExport();
            logger.info("气象信息导出测试完成");
        } catch (Exception e) {
            logger.error("测试失败", e);
        }
    }
    
    /**
     * 测试气象信息导出
     */
    public void testWeatherInfoExport() throws Exception {
        logger.info("开始测试气象信息导出");
        
        // 检查模板文件
        checkTemplateFile();
        
        // 创建测试数据
        WordMeteorologicalVo data = createTestData();
        
        // 生成文档
        byte[] documentBytes = generateDocument(data);
        
        // 保存文档
        saveDocument(documentBytes, "气象信息导出测试.docx");
        
        logger.info("气象信息导出测试完成");
    }
    
    /**
     * 检查模板文件
     */
    private void checkTemplateFile() throws Exception {
        String templatePath = "ruoyi-admin/src/main/resources/word/气象信息模板.docx";
        Path path = Paths.get(templatePath);
        
        if (!Files.exists(path)) {
            // 尝试使用综合模板
            templatePath = "ruoyi-admin/src/main/resources/word/气象信息和动态信息模板.docx";
            path = Paths.get(templatePath);
            
            if (!Files.exists(path)) {
                throw new Exception("模板文件不存在: " + templatePath);
            }
        }
        
        logger.info("模板文件检查通过: {}", templatePath);
    }
    
    /**
     * 创建测试数据
     */
    private WordMeteorologicalVo createTestData() {
        logger.info("创建气象信息测试数据");
        
        WordMeteorologicalVo data = new WordMeteorologicalVo();
        
        // 基本信息
        data.setAircraftType("BELL429");
        data.setRegistrationNumber("B-7613");
        data.setFlightDate("2025-05-20");
        data.setFuel("740b");
        
        // 统计信息
        data.setGroundTimeMinTotal("11");
        data.setAirTimeMinTotal("51");
        data.setTotalTimeMinTotal("62");
        data.setSortieCountTotal("3");
        
        // 始发地气象信息
        List<WordFlightWeatherInfoVo> departureWeatherInfoList = new ArrayList<>();
        
        WordFlightWeatherInfoVo departureWeather1 = new WordFlightWeatherInfoVo();
        departureWeather1.setBatch("1");
        departureWeather1.setLocationName("星野");
        departureWeather1.setWeather("晴天");
        departureWeather1.setCloudHeight("无云层");
        departureWeather1.setTemperature("26");
        departureWeather1.setWindDirection("160");
        departureWeather1.setWindSpeed("4");
        departureWeather1.setVisibility("9999");
        departureWeather1.setQnh("44");
        departureWeatherInfoList.add(departureWeather1);
        
        WordFlightWeatherInfoVo departureWeather2 = new WordFlightWeatherInfoVo();
        departureWeather2.setBatch("2");
        departureWeather2.setLocationName("星野");
        departureWeather2.setWeather("晴天");
        departureWeather2.setCloudHeight("无云层");
        departureWeather2.setTemperature("26");
        departureWeather2.setWindDirection("160");
        departureWeather2.setWindSpeed("4");
        departureWeather2.setVisibility("9999");
        departureWeather2.setQnh("44");
        departureWeatherInfoList.add(departureWeather2);
        
        WordFlightWeatherInfoVo departureWeather3 = new WordFlightWeatherInfoVo();
        departureWeather3.setBatch("3");
        departureWeather3.setLocationName("星野");
        departureWeather3.setWeather("晴天");
        departureWeather3.setCloudHeight("无云层");
        departureWeather3.setTemperature("26");
        departureWeather3.setWindDirection("160");
        departureWeather3.setWindSpeed("4");
        departureWeather3.setVisibility("9999");
        departureWeather3.setQnh("44");
        departureWeatherInfoList.add(departureWeather3);
        
        data.setDepartureWeatherInfoList(departureWeatherInfoList);
        
        // 目的地气象信息（与始发地相同）
        List<WordFlightWeatherInfoVo> arrivalWeatherInfoList = new ArrayList<>();
        
        WordFlightWeatherInfoVo arrivalWeather1 = new WordFlightWeatherInfoVo();
        arrivalWeather1.setBatch("1");
        arrivalWeather1.setLocationName("星野");
        arrivalWeather1.setWeather("晴天");
        arrivalWeather1.setCloudHeight("无云层");
        arrivalWeather1.setTemperature("26");
        arrivalWeather1.setWindDirection("160");
        arrivalWeather1.setWindSpeed("4");
        arrivalWeather1.setVisibility("9999");
        arrivalWeather1.setQnh("44");
        arrivalWeatherInfoList.add(arrivalWeather1);
        
        data.setArrivalWeatherInfoList(arrivalWeatherInfoList);
        
        // 动态信息设为空（仅气象信息）
        data.setDynamicInfoList(new ArrayList<>());
        
        logger.info("测试数据创建完成，始发地数据: {}条，目的地数据: {}条", 
                   departureWeatherInfoList.size(), arrivalWeatherInfoList.size());
        
        return data;
    }
    
    /**
     * 生成文档
     */
    private byte[] generateDocument(WordMeteorologicalVo data) throws Exception {
        logger.info("开始生成气象信息Word文档");
        
        String templatePath = "ruoyi-admin/src/main/resources/word/气象信息模板.docx";
        
        // 如果专用模板不存在，使用综合模板
        if (!Files.exists(Paths.get(templatePath))) {
            templatePath = "ruoyi-admin/src/main/resources/word/气象信息和动态信息模板.docx";
        }
        
        String sealPath = "ruoyi-admin/src/main/resources/word/公章.png";
        
        // 检查公章文件是否存在
        if (!Files.exists(Paths.get(sealPath))) {
            logger.warn("公章文件不存在，将跳过公章添加: {}", sealPath);
            sealPath = null;
        }
        
        byte[] documentBytes = WeatherInfoWordUtils.generateWeatherInfoDocument(templatePath, data, sealPath);
        
        logger.info("气象信息Word文档生成完成，大小: {} 字节", documentBytes.length);
        return documentBytes;
    }
    
    /**
     * 保存文档
     */
    private void saveDocument(byte[] documentBytes, String fileName) throws IOException {
        String outputPath = "test_output/" + fileName;
        
        // 创建输出目录
        Path outputDir = Paths.get("test_output");
        if (!Files.exists(outputDir)) {
            Files.createDirectories(outputDir);
        }
        
        // 保存文件
        try (FileOutputStream fos = new FileOutputStream(outputPath)) {
            fos.write(documentBytes);
            fos.flush();
        }
        
        logger.info("文档已保存到: {}", outputPath);
        
        // 输出绝对路径
        Path absolutePath = Paths.get(outputPath).toAbsolutePath();
        logger.info("文档绝对路径: {}", absolutePath);
    }
}
