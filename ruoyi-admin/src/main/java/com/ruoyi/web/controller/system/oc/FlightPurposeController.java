package com.ruoyi.web.controller.system.oc;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.common.core.page.TableSupport;
import com.ruoyi.system.domain.oc.FlightPurpose;
import com.ruoyi.system.domain.oc.dto.AddFlightPurposeDTO;
import com.ruoyi.system.domain.oc.dto.QueryFlightPurposeDTO;
import com.ruoyi.system.domain.oc.dto.UpdateFlightPurposeDTO;
import com.ruoyi.system.service.oc.FlightPurposeService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/4/24 12:03
 * @mood 功能
 */
@Api(tags = "任务性质管理")
@Validated
@RestController
@RequestMapping("/system/flightPurpose")
public class FlightPurposeController extends BaseController {
    @Resource
    private FlightPurposeService flightPurposeService;

    /**
     * 查询任务类型列表
     */
    @ApiOperation("查询任务性质列表")
    @PreAuthorize("@ss.hasPermi('system:flightPurpose:list')")
    @GetMapping("/list")
    public PageCommonResult<List<FlightPurpose>> list(QueryFlightPurposeDTO dto, @RequestHeader("CompanyCode") String companyCode) {
        Integer pageNum = TableSupport.buildPageRequest().getPageNum();
        Integer pageSize = TableSupport.buildPageRequest().getPageSize();
        return flightPurposeService.selectList(dto, pageNum, pageSize, companyCode);
    }

    @ApiOperation("查询所有任务性质")
    @PreAuthorize("@ss.hasPermi('system:flightPurpose:list')")
    @GetMapping("/queryAll")
    public PageCommonResult<List<FlightPurpose>> queryAll(@RequestHeader("CompanyCode") String companyCode) {
        List<FlightPurpose> list = flightPurposeService.queryAll(companyCode);
        return PageCommonResult.success(list, list.size());
    }

    /**
     * 查询单个任务类型
     */
    @ApiOperation("查询任务性质详情")
    @GetMapping("/getOne/{id}")
    public CommonResult<FlightPurpose> selectOne(@PathVariable("id") Long id) {
        return flightPurposeService.selectOneById(id);
    }

    /**
     * 新增任务类型
     */
    @ApiOperation("新增任务性质")
    @PostMapping("/add")
    public CommonResult<String> insertOne(@Valid @RequestBody AddFlightPurposeDTO dto, @RequestHeader("CompanyCode") String companyCode) {
        return flightPurposeService.insertOne(dto, companyCode);
    }

    /**
     * 修改任务类型
     */
    @ApiOperation("修改任务性质")
    @PostMapping("/update")
    public CommonResult<String> updateOne(@Valid @RequestBody UpdateFlightPurposeDTO dto) {
        return flightPurposeService.updateOne(dto);
    }

    /**
     * 删除任务类型
     */
    @ApiOperation("删除任务性质")
    @PostMapping("/delete/{ids}")
    public CommonResult<String> deleteAll(@PathVariable Long[] ids) {
        return flightPurposeService.deleteAllByIds(ids);
    }
}
