package com.ruoyi.web.controller.mdmp;

import com.ruoyi.common.core.controller.BaseController;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.page.PageCommonResult;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.system.domain.mdmp.FlightLog;
import com.ruoyi.system.domain.mdmp.dto.QueryFlightLogDTO;
import com.ruoyi.system.domain.mdmp.dto.QueryFlightLogListDTO;
import com.ruoyi.system.domain.mdmp.vo.FlightLogVO;
import com.ruoyi.system.service.mdmp.FlightLogService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.util.List;

/**
 * <AUTHOR>
 */
@Api(value = "电子行程单中的航班操作日志",tags = "电子行程单中的航班操作日志")
@Validated
@RestController
@RequiredArgsConstructor
@RequestMapping("/mdmp/flightLog")
public class FlightLogController extends BaseController {
    private final FlightLogService flightLogService;

    /**
     * 通过航班ID查询操作日志
     */
    @ApiOperation("通过航班ID查询操作日志")
    @PostMapping("/queryFlightLog")
    public CommonResult<List<FlightLog>> queryFlightLog(@Valid @RequestBody QueryFlightLogDTO dto) {
        return flightLogService.queryFlightLog(dto.getFlightId());
    }

    /**
     * 查询操作日志列表
     */
    @ApiOperation("查询操作日志列表")
    @PostMapping("/list")
    public PageCommonResult<List<FlightLogVO>> list(@RequestBody QueryFlightLogListDTO dto) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        dto.setDeptCodeList(deptCodeList);
        List<FlightLogVO> voList = flightLogService.list(dto);
        return PageCommonResult.success(voList, voList.size());
    }

    @ApiOperation("导出")
    @PostMapping("/export")
    public void export(HttpServletResponse response, QueryFlightLogListDTO dto) {
        List<String> deptCodeList = getLoginUser().getDeptCodeList();
        dto.setDeptCodeList(deptCodeList);
        List<FlightLogVO> voList = flightLogService.list(dto);
        ExcelUtil<FlightLogVO> util = new ExcelUtil<>(FlightLogVO.class);
        util.exportExcel(response, voList, "操作日志数据");
    }
}
