# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.8.7
  # 版权年份
  copyrightYear: 2024
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: D:/ruoyi/uploadPath
  # 获取ip地址开关
  addressEnabled: false
  # 验证码类型 math 数字计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # 连接数满后的排队数，默认为100
    accept-count: 1000
    threads:
      # tomcat最大线程数，默认为200
      max: 800
      # Tomcat启动初始化的线程数，默认值10
      min-spare: 100

# 日志配置
logging:
  level:
    com.ruoyi: debug
    org.springframework: warn
#    com.ruoyi.quartz.mapper.rmfms.sql: warn

# 用户配置
user:
  password:
    # 密码最大错误次数
    maxRetryCount: 5
    # 密码锁定时间（默认10分钟）
    lockTime: 10

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
    multipart:
      # 单个文件大小
      max-file-size: 50MB
      # 设置总上传的文件大小
      max-request-size: 100MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    #    host: *************
    #    # 端口，默认为6379
    #    port: 6379
    #    # 密码
    #    password: S81ZtzRUZ6agGHU
    host: *************
    port: 32447
    password: th@2025
    # 数据库索引
    database: 7
    # 连接超时时间
    timeout: 20s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  #activemq的配置
  activemq:
    #    broker-url: tcp://*************:61616
    #    user: admin
    #    password: testmq#2022
    broker-url: tcp://*************:30615
    user: gaMq
    password: th@2025
  rabbitmq:
#    host: *************
#    port: 5672
#    username: travel_rabbitmq_user
#    password: XNKYTravel&
    host: *************
    port: 32344
    username: guest
    password: Ga#2025!666
    virtual-host: private_travel_host
  mvc:
    async:
      request-timeout: 120000
token:
  # 令牌自定义标识
  header: Authorization
  # 令牌密钥
  secret: 6X9Q3q7V6nW5hK7M3D9QZJv7Q7dYb3q7k8VZ-2qBfVxSQ
  # 令牌有效期（默认30分钟）
  expireTime: 30

# MyBatis配置
#mybatis:
#  # 搜索指定包别名
#  typeAliasesPackage: com.ruoyi.**.domain
#  # 配置mapper的扫描，找到所有的mapper.xml映射文件
#  mapperLocations: classpath*:mapper/**/*Mapper.xml
#  # 加载全局的配置文件
#  configLocation: classpath:mybatis/mybatis-config.xml
# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.ruoyi.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping:

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice
  # 匹配链接
  urlPatterns: /*

#文件服务器配置
minio:
#  endpoint: http://192.168.1.186:8000 #MinIo服务所在地址
#  bucketName: test #存储桶名称
#  accessKey: airport_user #访问的key
#  secretKey: asj9^JSok #访问的秘钥
  endpoint: http://*************:32239 #MinIo服务所在地址
  bucketName: uac #存储桶名称
  accessKey: kaiya #访问的key
  secretKey: kaiya@2025! #访问的秘钥
wspsdk:
  urlPrefix: https://egs.anfeitech.com:10048
  app-key: 5EAAEDCD07A04D6A9A092C3E76155377
  app-secret: F82C4CCE95914A8FBCEF80291D4E1DA9

asynchronousQueue: asynchronousProcessing.queue
historyFlightDataQueue: historyFlightData.queue
#最高温度（红）
temperature: 38|-38
#温度区间（黄）
temperatureRange: 35,38|-38,-35
#最高风速（红）
windSpeed: 15
#风速区间（黄）
windSpeedRange: 12,15
#能见度（红）
vis: 800
#能见度区间（黄）
visRange: 800,1600
#跑道长度（红）
runwayLength: 550
#跑道长度区间（黄）
runwayLengthRange: 550,800
#云高度（红）
cloudHeight: 90
#云高度区间（黄）
cloudHeightRange: 90,150
pwd:
  secret: Jjsa0a8dkd9skJ98sjz0KS0oJ8SJiJSKAZOS008A9Z

#默认地图格式
home-map-data-rule:
  mapData:
    longitudeStart: 84.80
    longitudeEnd: 86.45
    latitudeStart: 43.7
    latitudeEnd: 44.9
    longitudeIncrease: 0.01
    latitudeIncrease: 0.01
    heightList: [ 354, 750, 825, 1200, 1710, 2460, 3600, 4800, 6300 ]
    pressureList: [ 950, 925, 900, 850, 800, 700, 600, 500 ]
    #地图基准点
  benchmark:
    longitude: 85.81
    latitude: 44.76
  obstacleMultiplier: 10

#上传文件路径（飞行员记录）
aircrewRecordFilePath: D://file/aircrewRecord
#文件预览基础路径
defaultFilePreviewUrl: https://ga.swcares.com.cn/trade/oc/prod-api/system/pilot/file/
#defaultFilePreviewUrl: http://localhost/dev-api/system/pilot/file/
#defaultFilePreviewUrl:  https://airportpe.natappvip.cc/dev-api/system/pilot/file/
defaultAvatarUrl: https://ga.swcares.com.cn/trade/oc/prod-api/system/wxUser/img/
#defaultAvatarUrl: http://localhost/dev-api/system/wxUser/img/

#新空飞行任务书word模版
flightBookWordTemplate: word/飞行任务书模版.docx

#气象信息word模版
weatherInfoWordTemplate: word/气象信息和动态信息模板.docx

#公章图片路径
sealImagePath: word/公章.png

# 气象信息记录页配置
template:
  weatherAndDynamicTemplatePath: word/气象信息和动态信息模板.docx
  dynamicTemplatePath: word/动态信息模板.docx
  weatherTemplatePath: word/气象信息模板.docx
