<?xml version="1.0" encoding="UTF-8"?>
<configuration>
    <!-- 日志存放路径 -->
    <property name="log.path" value="log/ruoyi/logs"/>
    <!-- 日志输出格式 -->
    <property name="log.pattern" value="%d{HH:mm:ss.SSS} [%thread] %-5level %logger{20} - [%method,%line] - %msg%n"/>

    <!-- 控制台输出 -->
    <appender name="console" class="ch.qos.logback.core.ConsoleAppender">
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- 修改为以下配置 -->
    <appender name="DYNAMIC_FILE" class="ch.qos.logback.classic.sift.SiftingAppender">
        <discriminator>
            <key>logFileName</key>
            <defaultValue>default</defaultValue>
        </discriminator>
        <sift>
            <appender name="FILE-{logFileName}" class="ch.qos.logback.core.rolling.RollingFileAppender">
                <!-- 添加 immediateFlush 确保及时写入 -->
                <immediateFlush>true</immediateFlush>
                <file>${log.path}/dynamic/{logFileName}.log</file>
                <rollingPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedRollingPolicy">
                    <!-- 固定文件名模式，包含 %i -->
                    <fileNamePattern>${log.path}/dynamic/{logFileName}.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
                    <!-- 单个文件最大50MB -->
                    <maxFileSize>50MB</maxFileSize>
                    <!-- 最多保留60天 -->
                    <maxHistory>60</maxHistory>
                </rollingPolicy>
                <encoder>
                    <pattern>${log.pattern}</pattern>
                </encoder>
            </appender>
        </sift>
    </appender>

    <!-- 系统日志输出 -->
    <appender name="file_info" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/sys-info.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/sys-info.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>1</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>INFO</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <appender name="file_error" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/sys-error.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/sys-error.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
        <filter class="ch.qos.logback.classic.filter.LevelFilter">
            <level>ERROR</level>
            <onMatch>ACCEPT</onMatch>
            <onMismatch>DENY</onMismatch>
        </filter>
    </appender>

    <!-- 用户访问日志输出 -->
    <appender name="sys-user" class="ch.qos.logback.core.rolling.RollingFileAppender">
        <file>${log.path}/sys-user.log</file>
        <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
            <fileNamePattern>${log.path}/sys-user.%d{yyyy-MM-dd}.log</fileNamePattern>
            <maxHistory>60</maxHistory>
        </rollingPolicy>
        <encoder>
            <pattern>${log.pattern}</pattern>
        </encoder>
    </appender>

    <!-- 专用于动态日志的 Logger（推荐） -->
    <logger name="DYNAMIC_LOGGER" level="INFO">
        <appender-ref ref="DYNAMIC_FILE"/>
    </logger>

    <!-- 系统模块日志级别控制 -->
    <logger name="com.ruoyi" level="info"/>
    <!-- Spring日志级别控制 -->
    <logger name="org.springframework" level="warn"/>

    <!-- ============ 调整：合并 Root Logger 配置 ============ -->
    <root level="info">
        <appender-ref ref="console"/>
        <appender-ref ref="file_info"/>
        <appender-ref ref="file_error"/>
    </root>

    <!-- 系统用户操作日志 -->
    <logger name="sys-user" level="info">
        <appender-ref ref="sys-user"/>
    </logger>
</configuration>
