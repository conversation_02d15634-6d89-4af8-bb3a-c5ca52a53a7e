@echo off
echo 开始编译项目...

REM 编译项目
call mvn clean compile -DskipTests -q

if %ERRORLEVEL% neq 0 (
    echo 编译失败！
    pause
    exit /b 1
)

echo 编译成功！

echo 创建测试输出目录...
if not exist "test_output" mkdir test_output

echo 运行气象信息导出测试...

REM 设置类路径
set CLASSPATH=ruoyi-admin\target\classes;ruoyi-system\target\classes;ruoyi-common\target\classes;ruoyi-framework\target\classes

REM 添加Maven依赖到类路径
for /f %%i in ('mvn dependency:build-classpath -Dmdep.outputFile=classpath.txt -q') do set MAVEN_CP=%%i
if exist classpath.txt (
    set /p MAVEN_CP=<classpath.txt
    del classpath.txt
)

set FULL_CLASSPATH=%CLASSPATH%;%MAVEN_CP%

echo 类路径设置完成

REM 编译测试类
javac -cp "%FULL_CLASSPATH%" -d test_output ruoyi-admin\src\main\java\com\ruoyi\web\controller\system\oc\WeatherInfoExportTest.java

if %ERRORLEVEL% neq 0 (
    echo 测试类编译失败！
    pause
    exit /b 1
)

echo 测试类编译成功！

REM 运行测试
java -cp "test_output;%FULL_CLASSPATH%" com.ruoyi.web.controller.system.oc.WeatherInfoExportTest

echo 测试完成！
pause
