-- ----------------------------
-- 整合运控系统到石河子管理后台，需要变更的sql语句
-- ----------------------------
-- 为 sys_user 表添加 user_wx_open_id 列，类型为 VARCHAR(255)，允许为空
ALTER TABLE sys_user ADD COLUMN user_wx_open_id VARCHAR(100) NULL comment '微信号openid';
ALTER TABLE sys_user ADD COLUMN fly_number int NULL comment '飞行次数';
ALTER TABLE sys_user ADD COLUMN fly_time double DEFAULT '0' comment '飞行的时间 已s为单位  （飞行员用户特有）';
ALTER TABLE sys_user ADD COLUMN carrier_code VARCHAR(4) NULL comment '航司(运控使用)';
ALTER TABLE sys_user ADD COLUMN nationality VARCHAR(50) NULL comment '国籍(运控使用)';
ALTER TABLE sys_user ADD COLUMN role_type int NULL comment '用户角色类型（0管理员  1普通员工）';

-- 补充 sys_role 表添加 company_code 列，类型为 VARCHAR(255)，允许为空
ALTER TABLE sys_role ADD COLUMN company_code VARCHAR(4) NULL comment '所属航司代码(运控使用)';

-- 补充 flight_task_book 表添加 company_code 列，类型为 VARCHAR(255)，允许为空
ALTER TABLE flight_task_book ADD COLUMN company_code VARCHAR(4) NULL comment '所属航司代码(运控使用)';
ALTER TABLE flight_task_book ADD COLUMN version_number VARCHAR(64) NULL comment '版本号';

-- 补充 flight_task_info 表添加 company_code 列，类型为 VARCHAR(255)，允许为空
ALTER TABLE flight_task_info ADD COLUMN company_code VARCHAR(4) NULL comment '所属航司代码(运控使用)';
