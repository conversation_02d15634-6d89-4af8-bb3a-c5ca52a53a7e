package com.ruoyi.common.filter;

import org.apache.commons.lang3.StringEscapeUtils;


/***
 * 过滤恶意字符，跨站点脚本编制
 * <AUTHOR>
 *
 */
public class SpiteCharCheckInterceptor {

    public static boolean checkXSS(String value) {
        if (value == null) {
            return true; // 为空则直接返回合法
        }

        value = value.toLowerCase();

        // 先对转义后的内容进行反转义
        String unescapedValue = StringEscapeUtils.unescapeHtml4(value);

        // 如果反转义后的内容含有 HTML 标签，则直接返回非法
        if (unescapedValue.matches(".*<[^>]+>.*")) {
            return false;
        }

        // 拦截常见的XSS注入关键字
        String[] xssKeywords = {
                "script", "iframe", "object", "embed", "applet", "alert", "prompt", "confirm",
                "onerror", "onload", "onclick", "onmouseover", "onfocus", "onblur",
                "document.cookie", "document.write", "window.location", "innerhtml",
                "javascript:", "vbscript:", "data:text/html", "exec", "insert", "update", "delete", "truncate"
                , "count"
        };

        for (String keyword : xssKeywords) {
            if (value.contains(keyword)) {
                return false;
            }
        }

        // 使用正则表达式检查可能的XSS攻击模式
        String[] xssPatterns = {
                "<script>(.*?)</script>", // 尝试插入 script 标签
                "eval\\((.*?)\\)", // eval 执行
                "expression\\((.*?)\\)", // CSS 表达式
                "javascript:", // 直接 JS 执行
                "vbscript:", // VBScript 执行
                "on[a-z]+\\s*=\\s*['\"]?[^'\">]+['\"]?", // 事件监听 XSS
                "src\\s*=\\s*['\"]?(javascript|data)[^'\">]+" // src 属性的恶意注入
        };

        for (String pattern : xssPatterns) {
            if (value.matches(".*" + pattern + ".*")) {
                return false;
            }
        }


        return true;
    }


}
