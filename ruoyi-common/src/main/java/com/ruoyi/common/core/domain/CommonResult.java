package com.ruoyi.common.core.domain;

import com.ruoyi.common.constant.HttpStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@ApiModel(description = "统一返回结果")
public class CommonResult<T> {
    @ApiModelProperty("状态码")
    private Integer code;
    @ApiModelProperty("返回信息")
    private String msg;
    @ApiModelProperty("数据对象")
    private T data;

    public CommonResult(int code, String msg, T data) {
        this.code = code;
        this.msg = msg;
        this.data = data;
    }

    /**
     * 返回成功消息
     *
     * @return 成功消息
     */
    public static<T> CommonResult<T> success()
    {
        return CommonResult.success("操作成功");
    }

    /**
     * 返回成功数据
     *
     * @return 成功消息
     */
    public static<T> CommonResult<T> success(T data)
    {
        return CommonResult.success("操作成功", data);
    }

    /**
     * 返回成功消息
     *
     * @param msg 返回内容
     * @return 成功消息
     */
    public static<T> CommonResult<T> success(String msg)
    {
        return CommonResult.success(msg, null);
    }

    /**
     * 返回成功消息
     *
     * @param msg 返回内容
     * @param data 数据对象
     * @return 成功消息
     */
    public static<T> CommonResult<T> success(String msg, T data) {
        return new CommonResult<>(HttpStatus.SUCCESS, msg, data);
    }

    /**
     * 返回失败消息
     *
     * @return 失败消息
     */
    public static<T> CommonResult<T> error()
    {
        return CommonResult.error("操作失败");
    }

    /**
     * 返回失败消息
     *
     * @param msg 返回内容
     * @return 失败消息
     */
    public static<T> CommonResult<T> error(String msg)
    {
        return CommonResult.error(msg, null);
    }

    /**
     * 返回失败消息
     *
     * @param msg 失败内容
     * @param data 数据对象
     * @return 失败消息
     */
    public static<T> CommonResult<T> error(String msg, T data) {
        return new CommonResult<>(HttpStatus.ERROR, msg, data);
    }

    public static<T> CommonResult<T> toResult(int rows)
    {
        return rows > 0 ? CommonResult.success() : CommonResult.error();
    }
}
