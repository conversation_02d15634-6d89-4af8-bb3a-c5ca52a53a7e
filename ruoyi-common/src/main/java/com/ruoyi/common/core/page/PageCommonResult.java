package com.ruoyi.common.core.page;

import com.ruoyi.common.constant.HttpStatus;
import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;


/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(description = "分页统一返回结果")
public class PageCommonResult<T> {
    @ApiModelProperty("状态码")
    private Integer code;
    @ApiModelProperty("返回信息")
    private String msg;
    @ApiModelProperty("数据对象")
    private T rows;
    @ApiModelProperty("总数")
    private Long total;


    public static<T> PageCommonResult<T> success(T rows, long total) {
        return PageCommonResult.success("操作成功", rows, total);
    }

    /**
     * 返回成功消息
     *
     * @param msg  返回内容
     * @param rows 数据对象
     * @return 成功消息
     */
    public static<T> PageCommonResult<T> success(String msg, T rows, long total) {
        return new PageCommonResult<>(HttpStatus.SUCCESS, msg, rows, total);
    }
}
