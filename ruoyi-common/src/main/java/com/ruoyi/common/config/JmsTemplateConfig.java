package com.ruoyi.common.config;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jms.core.JmsTemplate;

import javax.jms.ConnectionFactory;

@Configuration
public class JmsTemplateConfig {

    @Autowired
    private ConnectionFactory connectionFactory;
    @Autowired
    private ConnectionFactory sendCommandConnectionFactory;

    @Bean(name = "jmsTemplate")
    public JmsTemplate jmsTemplate() {
        return new JmsTemplate(connectionFactory);
    }
    @Bean(name = "sendCommandJmsTemplate")
    public JmsTemplate sendCommandJmsTemplate() {
        return new JmsTemplate(sendCommandConnectionFactory);
    }  // 可以
}
