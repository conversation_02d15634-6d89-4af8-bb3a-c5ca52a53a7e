package com.ruoyi.common.config.websocket;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;

/**
 * WebSocket连接拦截器.
 */
@Slf4j
@Component
public class WebSocketInterceptor implements HandshakeInterceptor {

    //进入hander之前的拦截
    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse serverHttpResponse,
                                   WebSocketHandler webSocketHandler, Map<String, Object> map) {
        log.info("前端进入hander之前的拦截:");
        if (request instanceof ServletServerHttpRequest) {
            final String requestIp = request.getRemoteAddress().getAddress().toString().substring(1);
            String userId = ((ServletServerHttpRequest) request).getServletRequest().getParameter("userid");
            map.put("userid", userId);
            log.info("前端请求的IP为：{}，来源为：{}，userId为：{}", requestIp, request.getURI(),userId);
        }
        return true;
    }

    @Override
    public void afterHandshake(ServerHttpRequest serverHttpRequest, ServerHttpResponse serverHttpResponse, WebSocketHandler webSocketHandler, Exception e) {
        log.info("前端进来webSocket的afterHandshake拦截器！");
    }
}
