package com.ruoyi.common.config;

import org.bouncycastle.jce.provider.BouncyCastleProvider;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.PostConstruct;
import javax.crypto.Cipher;
import javax.crypto.KeyGenerator;
import javax.crypto.SecretKey;
import javax.crypto.spec.IvParameterSpec;
import java.nio.charset.StandardCharsets;
import java.security.SecureRandom;
import java.security.Security;
import java.util.Base64;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 */
@Component
public class PwdSecUtil {
    private final static byte[] IV = new byte[]{36, -45, 65, -40, -18, 18, 54, 65, 99, -93, 76, 89, -96, 45, -39, 25};
    //private final static String PWD = "Jjsa0a8dkd9skJ98sjz0KS0oJ8SJiJSKAZOS008A9Z";
    @Value("${pwd.secret}")
    private String PWD;
    private static SecretKey secretKey;
    private final static Logger logger = LoggerFactory.getLogger(PwdSecUtil.class);

    // 这个方法在 bean 初始化时执行
    @PostConstruct
    public void init() {
        try {
            // 初始化 SM4 密钥
            Security.addProvider(new BouncyCastleProvider());
            KeyGenerator keyGenerator = KeyGenerator.getInstance("SM4", "BC");
            SecureRandom random = SecureRandom.getInstance("SHA1PRNG");
            random.setSeed(PWD.getBytes(StandardCharsets.UTF_8));
            keyGenerator.init(128, random);
            secretKey = keyGenerator.generateKey();
        } catch (Exception e) {
            logger.error("国密SM4初始化失败！", e);
        }
    }


    // 加密
    public static String encrypt(String plainText) {
        String encrypt = "";
        try {
            if (null == plainText || plainText.isEmpty()) {
                return encrypt;
            }
            encrypt = encrypt(plainText, secretKey, IV);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return encrypt;
    }

    // 解密
    public static String decrypt(String plainText) {
        String decrypt = "";
        try {
            if (null == plainText || plainText.isEmpty()) {
                return decrypt;
            }
            decrypt = decrypt(plainText, secretKey, IV);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return decrypt;
    }

    // 加密
    private static String encrypt(String plainText, SecretKey key, byte[] iv) throws Exception {
        Cipher cipher = Cipher.getInstance("SM4/CBC/PKCS7Padding", "BC");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.ENCRYPT_MODE, key, ivSpec);
        byte[] encrypted = cipher.doFinal(plainText.getBytes(StandardCharsets.UTF_8));
        return Base64.getEncoder().encodeToString(encrypted);
    }

    // 解密
    private static String decrypt(String cipherText, SecretKey key, byte[] iv) throws Exception {
        Cipher cipher = Cipher.getInstance("SM4/CBC/PKCS7Padding", "BC");
        IvParameterSpec ivSpec = new IvParameterSpec(iv);
        cipher.init(Cipher.DECRYPT_MODE, key, ivSpec);
        byte[] decrypted = cipher.doFinal(Base64.getDecoder().decode(cipherText));
        return new String(decrypted, StandardCharsets.UTF_8);
    }

    // 定义正则表达式
    private static final String PASSWORD_PATTERN =
            "^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,16}$";

    private static final Pattern pattern = Pattern.compile(PASSWORD_PATTERN);

    // 验证密码强度
    public static boolean validatePassword(String password) {
        if (!pattern.matcher(password).matches()) {
            return false;
        }
        // 检查是否存在连续4个相同字符
        if (containsRepeatedCharacters(password, 4)) {
            return false;
        }
        return true;
    }

    // 检查是否包含连续重复字符
    private static boolean containsRepeatedCharacters(String password, int repetition) {
        for (int i = 0; i <= password.length() - repetition; i++) {
            boolean repeated = true;
            for (int j = 1; j < repetition; j++) {
                if (password.charAt(i) != password.charAt(i + j)) {
                    repeated = false;
                    break;
                }
            }
            if (repeated) {
                return true;
            }
        }
        return false;
    }


    private static final String LOWERCASE = "abcdefghijklmnopqrstuvwxyz";
    private static final String UPPERCASE = "ABCDEFGHIJKLMNOPQRSTUVWXYZ";
    private static final String DIGITS = "0123456789";
    private static final String SYMBOLS = "@$!%*?&";
    private static final String ALL_CHARS = LOWERCASE + UPPERCASE + DIGITS + SYMBOLS;
    private static final SecureRandom RANDOM = new SecureRandom();

    //生成随机密码
    public static String generatePassword() {
        int length = 8 + RANDOM.nextInt(9); // 密码长度 8 到 16 位
        StringBuilder password = new StringBuilder();

        // 确保密码包含至少一个小写字母、大写字母、数字和符号
        password.append(LOWERCASE.charAt(RANDOM.nextInt(LOWERCASE.length())));
        password.append(UPPERCASE.charAt(RANDOM.nextInt(UPPERCASE.length())));
        password.append(DIGITS.charAt(RANDOM.nextInt(DIGITS.length())));
        password.append(SYMBOLS.charAt(RANDOM.nextInt(SYMBOLS.length())));

        // 生成剩余的字符
        for (int i = 4; i < length; i++) {
            char nextChar = ALL_CHARS.charAt(RANDOM.nextInt(ALL_CHARS.length()));
            // 防止出现连续4个相同字符
            if (i >= 3 && password.charAt(i - 1) == nextChar && password.charAt(i - 2) == nextChar && password.charAt(i - 3) == nextChar) {
                i--; // 如果出现连续4个相同字符，则重试
            } else {
                password.append(nextChar);
            }
        }

        // 随机打乱字符顺序
        return shuffleString(password.toString());
    }

    private static String shuffleString(String input) {
        StringBuilder result = new StringBuilder(input.length());
        char[] characters = input.toCharArray();
        for (int i = characters.length - 1; i >= 0; i--) {
            int randomIndex = RANDOM.nextInt(i + 1);
            result.append(characters[randomIndex]);
            characters[randomIndex] = characters[i];
        }
        return result.toString();
    }

    public static void main(String[] args) throws Exception {

        StopWatch stopWatch = new StopWatch();
        stopWatch.start();
        System.out.println(encrypt("Roq@123"));


        stopWatch.stop();
        System.out.println(stopWatch.getTotalTimeSeconds());

    }
}
