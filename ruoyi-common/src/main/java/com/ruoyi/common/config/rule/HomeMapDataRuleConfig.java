package com.ruoyi.common.config.rule;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 首页地图数据规则 + 障碍物
 */
@Component
@ConfigurationProperties(prefix = "home-map-data-rule")
@Data
public class HomeMapDataRuleConfig {
    /**
     * 地图规则
     */
    private MapDataRule mapData;

    /**
     * 基准点坐标
     */
    private Benchmark benchmark;

    /**
     * 障碍物
     */
    private List<Obstacle> obstacle;


    /**
     * 障碍物缩放系数
     */
    private int obstacleMultiplier;


}
