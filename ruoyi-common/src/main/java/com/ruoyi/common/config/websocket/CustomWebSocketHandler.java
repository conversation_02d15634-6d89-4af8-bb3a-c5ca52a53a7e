package com.ruoyi.common.config.websocket;

import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.CloseStatus;
import org.springframework.web.socket.TextMessage;
import org.springframework.web.socket.WebSocketSession;
import org.springframework.web.socket.handler.TextWebSocketHandler;

import java.io.IOException;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
@Slf4j
public class CustomWebSocketHandler extends TextWebSocketHandler {

    // 用于存储WebSocket会话
    private final Map<String, WebSocketSession> webSocketMap = new ConcurrentHashMap<>();

    @Override
    public void afterConnectionEstablished(WebSocketSession session) {
        String sessionId = session.getId();
        String userId = (String) session.getAttributes().get("userid");
        webSocketMap.put(sessionId, session);
        System.out.println(webSocketMap);
        log.info("WebSocket用户[{}]连接建立成功，当前在线人数为[{}]", userId, webSocketMap.size());
    }

    @Override
    protected void handleTextMessage(WebSocketSession session, TextMessage message) throws Exception {
        String payload = message.getPayload();
        log.info("收到心跳：{}", payload);
        // 发送回复消息
//        String replyMessage = "服务器收到消息：" + payload;
//        session.sendMessage(new TextMessage(replyMessage));
    }

    @Override
    public void afterConnectionClosed(WebSocketSession session, CloseStatus status) {
        String sessionId = session.getId();
        String userId = (String) session.getAttributes().get("userid");
        webSocketMap.remove(sessionId);
        log.info("WebSocket用户[{}]连接关闭,当前在线人数[{}]人", userId, webSocketMap.size());
    }

    @Override
    public void handleTransportError(WebSocketSession session, Throwable exception) {
        String userId = (String) session.getAttributes().get("userid");
        log.error("WebSocket[{}]连接错误,错误原因[{}]", userId, exception.getMessage());
    }

    // 广播消息给所有连接的客户端
    public void broadcastMessage(String message) {
        webSocketMap.values().forEach(session -> {
            String userId = (String) session.getAttributes().get("userid");
            log.debug("发送消息到用户:[{}], 坐标数据[{}]", userId, message);
            try {
                synchronized (session) {
                    session.sendMessage(new TextMessage(message));
                    log.info(message);
                }
            } catch (IOException e) {
                log.error("向用户[{}]推送websocket消息失败！异常如下：[{}]", userId, e.getMessage());
            }
        });
    }

    // 广播消息给所有连接的客户端
    public void broadcastMessage2(String message) {
        webSocketMap.values().forEach(session -> {
            String userId = (String) session.getAttributes().get("userid");
            log.debug("发送异步消息到用户:[{}]", userId);
            try {
                synchronized (session) {
                    session.sendMessage(new TextMessage(message));
                }
            } catch (IOException e) {
                log.error("向用户[{}]推送异步消息失败！异常如下：[{}]", userId, e.getMessage());
            }
        });
    }
}
