package com.ruoyi.common.config.websocket;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;


@Configuration
@EnableWebSocket
public class WebSocketConfig implements WebSocketConfigurer {

    private final CustomWebSocketHandler webSocketHandler;
    private final UpdateCommandHandler updateCommandHandler;
    private final WebSocketInterceptor webSocketInterceptor;

    public WebSocketConfig(CustomWebSocketHandler webSocketHandler, UpdateCommandHandler updateCommandHandler, WebSocketInterceptor webSocketInterceptor) {
        this.webSocketHandler = webSocketHandler;
        this.updateCommandHandler = updateCommandHandler;
        this.webSocketInterceptor = webSocketInterceptor;
    }

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry registry) {
        registry.addHandler(webSocketHandler, "/bd").setAllowedOrigins("*")
                .addInterceptors(webSocketInterceptor);
        registry.addHandler(updateCommandHandler, "/updateCommand").setAllowedOrigins("*")
                .addInterceptors(webSocketInterceptor);

    }

    @Bean
    public WebSocketHandler CustomWebSocketHandler() {
        return new CustomWebSocketHandler();
    }

    @Bean
    public WebSocketHandler UpdateCommandHandler() {
        return new UpdateCommandHandler();
    }
}
