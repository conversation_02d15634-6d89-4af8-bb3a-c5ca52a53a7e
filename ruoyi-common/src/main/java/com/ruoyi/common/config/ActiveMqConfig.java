package com.ruoyi.common.config;

import org.apache.activemq.ActiveMQConnectionFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.jms.annotation.EnableJms;
import org.springframework.jms.config.DefaultJmsListenerContainerFactory;

import javax.jms.ConnectionFactory;

/**
 * <AUTHOR>
 */
@Configuration
@EnableJms
public class ActiveMqConfig {

    @Value("${spring.activemq.broker-url}")
    private String brokerUrl;

    @Value("${spring.activemq.user}")
    private String mqUser;

    @Value("${spring.activemq.password}")
    private String mqPassword;

    @Bean
    public ConnectionFactory connectionFactory() {
        ActiveMQConnectionFactory connectionFactory = new ActiveMQConnectionFactory();
        connectionFactory.setBrokerURL(brokerUrl);
        connectionFactory.setUserName(mqUser);
        connectionFactory.setPassword(mqPassword);
        connectionFactory.setUseAsyncSend(true);
        return connectionFactory;
    }

    @Bean
    public DefaultJmsListenerContainerFactory flightDataJmsFactory() {
        DefaultJmsListenerContainerFactory factory = new DefaultJmsListenerContainerFactory();
        factory.setConnectionFactory(connectionFactory());
        //true为topic，false为queue
        factory.setPubSubDomain(false);
        factory.setRecoveryInterval(1000L);
        return factory;
    }

    @Bean
    public ConnectionFactory sendCommandConnectionFactory() {
        ActiveMQConnectionFactory connectionFactory = new ActiveMQConnectionFactory();
        connectionFactory.setBrokerURL("tcp://192.168.1.177:61611"); // 第二个服务器的地址
        connectionFactory.setUserName("yth-open-admin-jc2021");
        connectionFactory.setPassword("yth@openjc2021");
//        connectionFactory.setBrokerURL("tcp://127.0.0.1:61616"); // 第二个服务器的地址
//        connectionFactory.setUserName("admin");
//        connectionFactory.setPassword("admin");
        return connectionFactory;
    }
}
