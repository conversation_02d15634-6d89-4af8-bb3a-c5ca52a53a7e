package com.ruoyi.common.utils;

import org.apache.poi.xwpf.usermodel.*;

import java.io.InputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @version 1.0
 * @description 飞行任务书word工具
 * @date 2025/7/22 13:36:47
 */
public class FlightTaskWordUtils {
    public static XWPFDocument writeWord(String modelPatch, Map<String, String> params, String[][] data) throws Exception {
        InputStream is = Files.newInputStream(Paths.get(modelPatch));
        XWPFDocument document = new XWPFDocument(is);

        //正文
        replaceInDocumentBody(document, params);

        // 替换段落+表格占位符
        processParagraphsAndTables(document, params);

        // 新增表格行，示例处理第一个表格
        List<XWPFTable> tables = document.getTables();
        if (!tables.isEmpty()) {
            XWPFTable firstTable = tables.get(0);
            appendRows(firstTable, data);
        }
        return document;
    }

    public static XWPFDocument writeWord(InputStream templateInputStream, Map<String, String> params, String[][] data) throws Exception {
        XWPFDocument document = new XWPFDocument(templateInputStream);
        //正文
        replaceInDocumentBody(document, params);

        // 替换段落+表格占位符
        processParagraphsAndTables(document, params);

        // 新增表格行，示例处理第一个表格
        List<XWPFTable> tables = document.getTables();
        if (!tables.isEmpty()) {
            XWPFTable firstTable = tables.get(0);
            appendRows(firstTable, data);
        }
        return document;
    }

    // 替换正文段落中的占位符
    private static void replaceInDocumentBody(XWPFDocument document, Map<String, String> params) {
        List<XWPFParagraph> paragraphs = document.getParagraphs();
        for (XWPFParagraph paragraph : paragraphs) {
            replaceInParagraph(paragraph, params, true);
        }
    }


    // 替换段落和表格里的占位符
    private static void processParagraphsAndTables(XWPFDocument document, Map<String, String> params) {
        for (XWPFParagraph paragraph : document.getParagraphs()) {
            replaceInParagraph(paragraph, params, false);
        }
        for (XWPFTable table : document.getTables()) {
            processTable(table, params);
        }
    }

    private static void processTable(XWPFTable table, Map<String, String> params) {
        for (XWPFTableRow row : table.getRows()) {
            for (XWPFTableCell cell : row.getTableCells()) {
                for (XWPFParagraph paragraph : cell.getParagraphs()) {
                    replaceInParagraph(paragraph, params, false);
                }
                // 嵌套表格递归处理
                for (XWPFTable nestedTable : cell.getTables()) {
                    processTable(nestedTable, params);
                }
            }
        }
    }

    private static void replaceInParagraph(XWPFParagraph paragraph, Map<String, String> params, boolean bold) {
        List<XWPFRun> runs = paragraph.getRuns();
        if (runs == null || runs.isEmpty()) return;

        for (XWPFRun run : runs) {
            String text = run.getText(0);
            if (text == null || text.isEmpty()) continue;

            boolean replaced = false;
            for (Map.Entry<String, String> entry : params.entrySet()) {
                String placeholder = "${" + entry.getKey() + "}";
                if (text.contains(placeholder)) {
                    text = text.replace(placeholder, entry.getValue());
                    replaced = true;
                }
            }

            if (replaced) {
                run.setText(text, 0);
                // 只对替换后的内容设置样式
                run.setFontFamily("宋体");
                run.setFontSize(7);
                run.setBold(bold);
                run.setColor("000000");
            }
        }
    }


    // 新增多行到表格最后，按最后一行列数补齐
    private static void appendRows(XWPFTable table, String[][] data) {
        List<XWPFTableRow> rows = table.getRows();
        XWPFTableRow lastRow = rows.get(rows.size() - 1);
        int columnCount = lastRow.getTableCells().size();

        for (String[] rowData : data) {
            XWPFTableRow newRow = table.createRow();
            while (newRow.getTableCells().size() < columnCount) {
                newRow.addNewTableCell();
            }

            for (int j = 0; j < Math.min(columnCount, rowData.length); j++) {
                XWPFTableCell cell = newRow.getCell(j);

                // 设置垂直居中
                cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

                // 获取单元格中的段落
                XWPFParagraph paragraph;
                if (cell.getParagraphs().isEmpty()) {
                    paragraph = cell.addParagraph();
                } else {
                    paragraph = cell.getParagraphs().get(0);
                }

                // 设置水平居中
                paragraph.setAlignment(ParagraphAlignment.CENTER);

                // 新增 Run，只控制新写入文字的格式
                XWPFRun run = paragraph.createRun();
                run.setText(rowData[j]);

                // 只设置新写入的字体样式
                run.setFontFamily("宋体");
                run.setFontSize(7);
                run.setBold(false);
                run.setColor("000000");
            }
        }
    }


}

