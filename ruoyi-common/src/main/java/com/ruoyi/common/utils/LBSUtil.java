package com.ruoyi.common.utils;

import org.locationtech.jts.geom.Coordinate;

/**
 * <AUTHOR>
 * @date ：Created in 2024/7/16 10:20
 * @description：
 * @modified By：
 * @version: $
 */
public class LBSUtil {

    /**
     * 赤道半径(m)
     */
    public final static double RC = 6378137;

    /**
     * 极半径(m)
     */
    public final static double RJ = 6356725;

    /**
     * 将角度转化为弧度
     */
    public static double radians(double d) {
        return d * Math.PI / 180.0;
    }
    /**
     * 获取指定角度方向上的经纬度(longitude and latitude)<br>
     * 以原始点的经纬度为基点,构建XY二维坐标线,则angle角度则从x轴起算。<br>
     * 说明:LBS查找,东西南北四个方向的最远点，构建矩形,在矩形框方位内, 才有可能是该距离方位内的坐标,然后利用距离公式从小范围内找坐标
     *
     *
     * @param origLongitude
     *            基点经度
     * @param origLatitude
     *            基点纬度
     * @param distance
     *            距离(m)
     * @param angle
     *            角度(0~360)
     *
     * @return 目标点的经纬度
     */
    public static Coordinate getCoordinate(double origLongitude, double origLatitude, double distance, double angle) {
        double x = distance * Math.sin(angle * Math.PI / 180.0);
        double y = distance * Math.cos(angle * Math.PI / 180.0);
        double r = RJ + (RC - RJ) * (90.0 - origLongitude) / 90.0;//由于地球不是标准的球体，中间粗，两头细，这里計算平均半徑r
        double d = r * Math.cos(origLongitude * Math.PI / 180);
        double newLongitude = (y / r + origLongitude * Math.PI / 180.0) * 180.0 / Math.PI;
        double newLatitude = (x / d + origLatitude * Math.PI / 180.0) * 180.0 / Math.PI;
        return new Coordinate(newLongitude, newLatitude);
    }

    /**
     * 根据两点经纬度((longitude and latitude))坐标计算直线距离
     * <p>
     * S = 2arcsin√sin²(a/2)+cos(lat1)*cos(lat2)*sin²(b/2)￣*6378137
     * <p>
     * 1. lng1 lat1 表示A点经纬度，lng2 lat2 表示B点经纬度；<br>
     * 2. a=lat1 – lat2 为两点纬度之差 b=lng1 -lng2 为两点经度之差；<br>
     * 3. 6378137为地球赤道半径，单位为米；
     *
     * @param longitude1
     *            点1经度
     * @param latitude1
     *            点1纬度
     * @param longitude2
     *            点2经度
     * @param latitude2
     *            点2纬度
     * @return 距离，单位米(M)
     * @see <a href=
     *      "https://zh.wikipedia.org/wiki/%E5%8D%8A%E6%AD%A3%E7%9F%A2%E5%85%AC%E5%BC%8F">
     *      半正矢(Haversine)公式</a>
     */
    public static double getDistanceFrom2LngLat(double longitude1, double latitude1, double longitude2, double latitude2) {
        // 将角度转化为弧度
        double radLongitude1 = radians(longitude1);
        double radLatitude1 = radians(latitude1);
        double radLongitude2 = radians(longitude2);
        double radLatitude2 = radians(latitude2);

        double a = radLatitude1 - radLatitude2;
        double b = radLongitude1 - radLongitude2;

        return 2 * Math.asin(Math.sqrt(Math.sin(a / 2) * Math.sin(a / 2)
                + Math.cos(radLatitude1) * Math.cos(radLatitude2) * Math.sin(b / 2) * Math.sin(b / 2))) * (RC);
    }

    public static void main(String[] args) {
       System.out.println(getDistanceFrom2LngLat(85.92016666666667, 44.240833333333335, 86.04385629902167,44.25347083459609));

        Coordinate c = getCoordinate(85.92016666666667, 44.240833333333335, 11794, 83);
        System.out.println(c.getX() + "," + c.getY());
    }

}
