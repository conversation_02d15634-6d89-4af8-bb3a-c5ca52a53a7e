package com.ruoyi.common.utils;


import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.util.List;

@Slf4j
public class Util {
    private Util() {
    }

    public static void doc2img(String fileName, String imgFilePath) {
        InputStream inStream = null;
        File file = new File(fileName);
        try {
            inStream = new FileInputStream(file);
            List<BufferedImage> wordToImg = null;
            try {
                if (null != inStream) {
                    wordToImg = DocToImg.wordToImg(inStream);
                    BufferedImage mergeImage = DocToImg.mergeImage(false, wordToImg);
                    ImageIO.write(mergeImage, "png", new File(imgFilePath));
                    if (inStream != null) {
                        safeClose(inStream);
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage());
            }

        } catch (FileNotFoundException e) {
            log.error(e.getMessage());
        }


    }

    public static void safeClose(InputStream fis) {
        if (fis != null) {
            try {
                fis.close();
            } catch (IOException e) {
                log.error(e.getMessage());
            }
        }
    }

    public static void pdf2img(String fileName, String imgFilePath) {
        try {
            PdfToImg.pdf2Pic(fileName, imgFilePath);
        } catch (Exception e) {
            // TODO Auto-generated catch block
            log.error(e.getMessage());
        }

    }
}
