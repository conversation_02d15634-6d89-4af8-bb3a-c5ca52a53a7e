package com.ruoyi.common.utils;

import java.math.BigDecimal;
import java.math.RoundingMode;

/**
 * <AUTHOR>
 */
public class ArithmeticUtils {

    /**
     * 分钟转小时，保留两位小数
     */
    public static Double minutesToHour(int minutes) {
        BigDecimal dividend = new BigDecimal(minutes);
        BigDecimal divisor = new BigDecimal(60);
        BigDecimal result = dividend.divide(divisor, 2, RoundingMode.HALF_UP);
        return result.doubleValue();
    }

    /**
     * 分钟转小时，保留一位小数
     */
    public static Double minutesToHour2(int minutes) {
        BigDecimal dividend = new BigDecimal(minutes);
        BigDecimal divisor = new BigDecimal(60);
        BigDecimal result = dividend.divide(divisor, 1, RoundingMode.HALF_UP);
        return result.doubleValue();
    }
}
