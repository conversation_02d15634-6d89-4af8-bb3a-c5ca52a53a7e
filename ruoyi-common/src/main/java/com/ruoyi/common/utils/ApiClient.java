package com.ruoyi.common.utils;

import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.TypeReference;
import com.ruoyi.common.core.domain.CommonResult;
import com.ruoyi.common.core.domain.HuaxiaMessage;
import okhttp3.*;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date ：Created in 2024/11/8 11:01
 * @description：
 * @modified By：
 * @version: $
 */
public class ApiClient {

    // 基础URL
    private static final String BASE_URL = "https://egs.anfeitech.com:10048/";
    // 认证头名称
    private static final String AUTH_HEADER = "Authorization";
    // 存储的token
    private String token;
    // OkHttpClient实例，用于发送请求
    private final OkHttpClient client;
    // 用于刷新token的appKey和appSecret
    private final String appKey;
    private final String appSecret;

    // 构造函数，初始化token、appKey、appSecret和OkHttpClient
    public ApiClient(String appKey, String appSecret) throws IOException {
        this.appKey = appKey;
        this.appSecret = appSecret;
        this.client = new OkHttpClient();
    }

    /**
     * 向指定路径发送POST请求，并附带JSON请求体
     *
     * @param resourcePath 资源路径
     * @param jsonBody     JSON格式的请求体
     * @return 服务器响应的数据
     * @throws IOException 如果请求失败
     */
    public String postResource(String resourcePath, String jsonBody) throws IOException {
        Request request = buildRequest(resourcePath, jsonBody);

        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                return response.body().string();
            } else if (response.code() == 401) {
                // Token过期，尝试刷新token
                String newToken = refreshToken();
                if (newToken != null) {
                    // 更新token并重试请求
                    this.token = newToken;
                    request = buildRequest(resourcePath, jsonBody); // 重新构建请求，因为token已经更新
                    return client.newCall(request).execute().body().string(); // 重新发送请求
                } else {
                    throw new IOException("Failed to refresh token: " + response.message());
                }
            } else {
                throw new IOException("Unexpected HTTP response code: " + response.code() + " " + response.message());
            }
        }
    }

    /**
     * 构建HTTP请求
     *
     * @param resourcePath 资源路径
     * @param jsonBody     JSON格式的请求体
     * @return 构建好的Request对象
     */
    private Request buildRequest(String resourcePath, String jsonBody) {
        HttpUrl url = HttpUrl.parse(BASE_URL + resourcePath);
        RequestBody body = RequestBody.create(
                jsonBody, MediaType.parse("application/json; charset=utf-8"));
        return new Request.Builder()
                .url(url)
                .post(body)
                .addHeader(AUTH_HEADER, token)
                .build();
    }

    /**
     * 刷新token的方法
     * <p>
     * 向身份验证服务器发送POST请求以获取新token。
     *
     * @return 新token，如果无法获取则返回null
     * @throws IOException 如果请求失败
     */
    private String refreshToken() throws IOException {
        // 准备刷新token的请求体
        String refreshRequestBody = "{\"appKey\":\"" + appKey + "\",\"appSecret\":\"" + appSecret + "\"}";
        RequestBody body = RequestBody.create(
                refreshRequestBody, MediaType.parse("application/json; charset=utf-8"));

        // 构建刷新token的请求
        Request request = new Request.Builder()
                .url("https://egs.anfeitech.com:10048/egs/in/token/auth")
                .post(body)
                .build();

        // 发送请求并获取响应
        try (Response response = client.newCall(request).execute()) {
            if (response.isSuccessful()) {
                String responseBody = response.body().string();
                CommonResult<HuaxiaMessage> apiResult = JSON.parseObject(responseBody, new TypeReference<CommonResult<HuaxiaMessage>>() {
                });
                if (apiResult.getCode().equals(200))
                    return apiResult.getData().getToken();
                else
                    throw new IOException("Failed to refresh token: " + response.message());
            } else {
                throw new IOException("Failed to refresh token: " + response.message());
            }
        }
    }

    public static void main(String[] args) throws IOException {
        // 初始化token、appKey和appSecret（在实际应用中，这些值应该是从安全存储中获取的）
        String appKey = "5EAAEDCD07A04D6A9A092C3E76155377";
        String appSecret = "F82C4CCE95914A8FBCEF80291D4E1DA9";
        ApiClient apiClient = new ApiClient( appKey, appSecret);
        apiClient.token=apiClient.refreshToken();
        System.out.println(apiClient.token);
        // 示例JSON请求体
//        String jsonBody = "{\"key\":\"value\"}";
//
//        try {
//            // 发送POST请求并获取响应数据
//            String responseData = apiClient.postResource("egs/in/wsp/data/api/calc/alarmPoint", jsonBody);
//            System.out.println("服务器响应: " + responseData);
//        } catch (IOException e) {
//            // 处理IO异常，例如打印错误消息或记录日志
//            e.printStackTrace();
//        }
    }
}
