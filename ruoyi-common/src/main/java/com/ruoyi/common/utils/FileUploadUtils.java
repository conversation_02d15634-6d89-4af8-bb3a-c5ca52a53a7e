package com.ruoyi.common.utils;

import com.ruoyi.common.exception.UtilException;
import com.ruoyi.common.exception.file.FileUploadException;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.web.multipart.MultipartFile;

import static com.ruoyi.common.utils.file.MimeTypeUtils.getExtension;

/**
 * <AUTHOR>
 * @date ：Created in 2025/4/24 14:51
 * @description：
 * @modified By：
 * @version: $
 */
public class FileUploadUtils {
    private static final String[] ALLOWED_EXTENSION = {
            // 图片
            "bmp", "gif", "jpg", "jpeg", "png",
            // 文档
            "doc", "docx", "xls", "xlsx", "pdf",
            // 文本
            "txt"
    };

    public static String getFileExtension(MultipartFile file) {
        if (file == null || file.isEmpty()) {
            return null; // 或返回空字符串 ""
        }
        String originalFilename = file.getOriginalFilename();
        if (originalFilename == null || originalFilename.lastIndexOf(".") == -1) {
            return ""; // 无后缀名时返回空
        }
        // 获取最后一个点的位置
        int lastDotIndex = originalFilename.lastIndexOf(".");
        // 截取后缀名并转为小写（统一格式）
        return originalFilename.substring(lastDotIndex + 1).toLowerCase();
    }


    /**
     * 校验文件类型
     */
    public static void assertAllowed(String filename) throws FileUploadException {
        // 获取文件扩展名
        //  String extension = getExtension(filename);
        // 检查扩展名是否合法
        if (!isAllowedExtension(filename)) {
            throw new UtilException("文件类型不支持，仅允许上传Excel、Word、PDF、图片、文本文件");
        }
        // 可选：进一步校验MIME类型（更安全）
        // validateMimeType(file.getInputStream());
    }

    // 判断扩展名是否在白名单中
    public static boolean isAllowedExtension(String extension) {
        return ArrayUtils.contains(ALLOWED_EXTENSION, extension.toLowerCase());
    }
}
