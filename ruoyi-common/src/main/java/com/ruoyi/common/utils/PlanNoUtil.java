package com.ruoyi.common.utils;


import org.apache.commons.lang3.RandomUtils;

import java.util.Random;

/**
 * 航班计划编号工具类
 *
 * <AUTHOR>
 */
public class PlanNoUtil {
    /**
     * 生成计划编号
     *
     * @return 计划编号
     */
    public static String generatePlanNo(String companyCode) {
        // 生成计划编号
        //随机生成4位数字
        Random random = new Random();
        int randomNum = random.nextInt(9000) + 1000;
        if (StringUtils.isNotBlank(companyCode)) {
            return companyCode + DateUtils.dateTimeNow() + randomNum;
        } else {
            // 如果没有公司编码，则默认生成一个
            return "RM" + DateUtils.dateTimeNow() + randomNum;
        }

    }

}
