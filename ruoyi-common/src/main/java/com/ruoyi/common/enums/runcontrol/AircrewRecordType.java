package com.ruoyi.common.enums.runcontrol;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Getter
@NoArgsConstructor
@AllArgsConstructor
public enum AircrewRecordType {

    /**文件第一类型*/
    /**
     * , "技术文档"
     */
    FIRST_FILETYPE_1(1),
    /**
     * "满足条款要求记录"
     */
    FIRST_FILETYPE_2(2),
    /**
     * , "对不合格飞行员采取的措施记录"
     */
    FIRST_FILETYPE_3(3),
    /**
     * 文件第二类型
     * 技术文档：1:飞行记录簿, 2:训练和检查记录, 3:事故征候结论, 4:奖励记录, 5:惩罚记录;
     * 满足条款要求记录：1:航路检查, 2:飞机和航路资格审定, 3:体检鉴定和疾病治疗, 4:飞行执勤休息时间记录;
     * 措施记录：无
     */
    SECOND_FILETYPE_1(1),
    SECOND_FILETYPE_2(2),
    SECOND_FILETYPE_3(3),
    SECOND_FILETYPE_4(4),
    SECOND_FILETYPE_5(5);

    private int code;
    private String desc;

    AircrewRecordType(int code) {
        this.code = code;
    }

    public void setCode(int code) {
        this.code = code;
    }

    public void setDesc(String desc) {
        this.desc = desc;
    }


}
