package com.ruoyi.quartz.domain.rmfms;

import lombok.Data;

import java.util.List;

@Data
public class XNMessage {

    private Long messageId;

    // 日期是：2021-10-09
    private String flightDate;

    // 电池电压
    private Integer battery;

    // 业务类型
    private String businessType;

    // 数据频度
    private Integer frequency;

    // 机载终端设备号
    private String id;

    // 数据个数
    private Integer number;

    //接收时间
    private String addTime;

    // MESSAGE原文ID
    private long rawMessageId;

    // 点位集合
    private List<XNMessagePoint> XNMessagePoint;
}
