package com.ruoyi.quartz.domain.rmfms;

import lombok.Data;

@Data
public class XNMessagePoint {


    private Long id;

    // 日期是：2021-10-09
    private String flightDate;

    private Long messageId;

    // elevation	高程
    private Integer elevation;

    // generatedTime	生成时间
    private String generatedTime;

    // latitude	纬度
    private Double latitude;

    // longitude	经度
    private Double longitude;

    // orientation	方位
    private Double orientation;

    // speed	速度    m/s为单位
    private Double speed;

    // state	状态（默认、起飞、降落、遇险、告警）
    private String state;

    //接收时间
    private String addTime;
}
