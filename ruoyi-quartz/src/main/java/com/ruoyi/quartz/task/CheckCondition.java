package com.ruoyi.quartz.task;

import com.ruoyi.system.domain.mdmp.RCloudinfores;
import com.ruoyi.system.domain.mdmp.RWeatherres;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2025/5/15 15:24
 * @description：
 * @modified By：
 * @version: $
 */
@Component
public class CheckCondition {
    private static final Set<String> PHEN_SET_1 = new HashSet<>(Arrays.asList("RA", "DZ", "SN", "SG", "IC", "PL"));
    private static final Set<String> PHEN_SET_2 = new HashSet<>(Arrays.asList("GR", "GS"));
    private static final Set<String> PHEN_SET_3 = new HashSet<>(Arrays.asList("SA", "DU", "FU"));
    private static final Set<String> PHEN_SET_4 = new HashSet<>(Arrays.asList("SS", "DS", "VA", "SQ", "PO", "FC"));
    private static final Set<String> DESC_SET_1 = new HashSet<>(Arrays.asList("BL", "DR"));
    private static final Set<String> DESC_SET_2 = new HashSet<>(Arrays.asList("SA", "DU"));


    /**
     * 温度颜色判定逻辑
     *
     * @param value 当前温度值
     * @return 2-红色 1-黄色 0-正常
     */
    public static int determineLevel(int value, String temperature, String temperatureRange) {
        // 解析红色阈值（支持正负双向）
        String[] redThresholds = temperature.split("\\|"); // 转义正则符号
        int redHigh = Integer.parseInt(redThresholds[0]);
        int redLow = Integer.parseInt(redThresholds[1]);

        // 红色判断（包含边界）
        if (value >= redHigh || value <= redLow) {
            return 2;
        }

        // 解析黄色区间（支持多区间）
        String[] yellowZones = temperatureRange.split("\\|");
        for (String zone : yellowZones) {
            String[] range = zone.split(",");
            int min = Integer.parseInt(range[0]);
            int max = Integer.parseInt(range[1]);

            // 动态识别开闭区间（根据配置顺序）
            boolean lowerInclusive = zone.startsWith("[");
            boolean upperInclusive = zone.endsWith("]");

            // 区间判断逻辑
            if (isInRange(value, min, max, lowerInclusive, upperInclusive)) {
                return 1;
            }
        }

        return 0;
    }

    /**
     * 通用区间判断方法
     */
    private static boolean isInRange(int value, int min, int max,
                                     boolean lowerInclusive, boolean upperInclusive) {
        boolean lowerValid = lowerInclusive ? (value >= min) : (value > min);
        boolean upperValid = upperInclusive ? (value <= max) : (value < max);
        return lowerValid && upperValid;
    }


    public static int windLevel(double value, double windSpeed, String windSpeedRange) {
        String[] range = windSpeedRange.split(",");
        if (value >= windSpeed) {
            return 2;
        } else if (value >= Double.parseDouble(range[0]) && value < Double.parseDouble(range[1])) {
            return 1;
        } else {
            return 0;
        }
    }

    public static int visLevel(int value, int vis, String visRange) {
        String[] range = visRange.split(",");
        if (value >= Integer.parseInt(range[0]) && value < Integer.parseInt(range[1])) {
            return 1;
        } else if (value < vis) {
            return 2;
        } else {
            return 0;
        }
    }

    public static int runwayLevel(long value, long runway, String runwayRange) {
        String[] range = runwayRange.split(",");
        if (value >= Long.parseLong(range[0]) && value < Long.parseLong(range[1])) {
            return 1;
        } else if (value < runway) {
            return 2;
        } else {
            return 0;
        }
    }

    public static int cloudLevel(RCloudinfores entity, int cloud, String cloudRange) {
        // 将cloudRange字符串按照逗号分隔，得到一个字符串数组
        String[] range = cloudRange.split(",");
        // 如果value大于等于range数组的第一个元素，并且小于range数组的第二个元素，则返回1
        Integer cloudHeight = entity.getCloudHeight();
        if (cloudHeight >= Integer.parseInt(range[0]) && cloudHeight < Integer.parseInt(range[1])) {
            return 1;
            // 如果value小于cloud，则返回2
        } else if (cloudHeight < cloud || (null != entity.getCloudShape() && entity.getCloudShape().equals("CB"))) {
            return 2;
        } else {
            return 0;
        }
    }


    public List<RWeatherres> processWeatherList(List<RWeatherres> weatherList) {
        return weatherList.stream()
                .peek(item -> {
                    int result = determineResult(item);
                    item.setColourType(result);
                })
                .collect(Collectors.toList());
    }

    public static int determineResult(RWeatherres item) {
        if (checkCondition2(item)) return 2;
        if (checkCondition1(item)) return 1;
        return 0;
    }

    // 返回2的条件判断（完整实现）
    private static boolean checkCondition2(RWeatherres item) {
        return checkTS(item) ||
                checkPlusPhen(item) ||
                checkPlusOrEmptyGRGS(item) ||
                checkFG(item) ||
                checkBLDR(item) ||
                checkPhenSet4(item);
    }

    // 返回1的条件判断（完整实现）
    private static boolean checkCondition1(RWeatherres item) {
        return checkEmptyIntensity(item) ||
                checkMinusPhen(item) ||
                checkEmptyDesc(item) ||
                checkFZ(item);
    }

    // 详细条件检查方法（完整实现）
    private static boolean checkTS(RWeatherres item) {
        return "TS".equals(item.getDescription());
    }

    private static boolean checkPlusPhen(RWeatherres item) {
        return "+".equals(item.getIntensity()) &&
                (PHEN_SET_1.contains(item.getPhenomenon()) || PHEN_SET_2.contains(item.getPhenomenon()));
    }

    private static boolean checkPlusOrEmptyGRGS(RWeatherres item) {
        return ("+".equals(item.getIntensity()) || isEmpty(item.getIntensity())) &&
                PHEN_SET_2.contains(item.getPhenomenon());
    }

    private static boolean checkFG(RWeatherres item) {
        return "FG".equals(item.getPhenomenon());
    }

    private static boolean checkBLDR(RWeatherres item) {
        return DESC_SET_1.contains(item.getDescription()) &&
                DESC_SET_2.contains(item.getPhenomenon());
    }

    private static boolean checkPhenSet4(RWeatherres item) {
        return PHEN_SET_4.contains(item.getPhenomenon());
    }

    private static boolean checkEmptyIntensity(RWeatherres item) {
        return isEmpty(item.getIntensity()) &&
                PHEN_SET_1.contains(item.getPhenomenon());
    }

    private static boolean checkMinusPhen(RWeatherres item) {
        return "-".equals(item.getIntensity()) &&
                PHEN_SET_2.contains(item.getPhenomenon());
    }

    private static boolean checkEmptyDesc(RWeatherres item) {
        return isEmpty(item.getDescription()) &&
                PHEN_SET_3.contains(item.getPhenomenon());
    }

    private static boolean checkFZ(RWeatherres item) {
        return "FZ".equals(item.getDescription());
    }

    // 工具方法（完整实现）
    private static boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }


}
