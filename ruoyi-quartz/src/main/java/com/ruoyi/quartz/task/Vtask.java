package com.ruoyi.quartz.task;

import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.system.domain.mdmp.MpData;
import com.ruoyi.system.domain.mdmp.MpTimeLine;
import com.ruoyi.system.domain.mdmp.MpTimeList;
import com.ruoyi.system.domain.mdmp.VMpData;
import com.ruoyi.system.domain.mdmp.vo.MeteVo;
import com.ruoyi.system.domain.type.DataSourceType;
import com.ruoyi.system.domain.type.MeteType;
import com.ruoyi.system.mapper.mdmp.MpDataMapper;
import com.ruoyi.system.mapper.mdmp.MpTimeLineMapper;
import com.ruoyi.system.mapper.mdmp.MpTimeListMapper;
import com.ruoyi.system.mapper.mdmp.VMpDataMapper;
import com.ruoyi.system.util.MeteUtil;
import com.wsp.sdk.service.base.R;
import com.wsp.sdk.service.dto.request.GetMeteoDataTimeLineReqDTO;
import com.wsp.sdk.service.dto.request.GetMeteoJsonDataReqDTO;
import com.wsp.sdk.service.dto.response.netcdf.NetcdfTimeSequenceDto;
import com.wsp.sdk.service.service.MeteoDataService;
import feign.Response;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.*;
import java.util.zip.GZIPInputStream;

/**
 * <AUTHOR>
 * @date ：Created in 2024/12/30 11:14
 * @description：
 * @modified By：
 * @version: $
 */
@Component("vtask")
public class Vtask {

    private static final Logger log = LoggerFactory.getLogger(Vtask.class);
    // 批量插入的大小，根据实际情况调整
    private static final int BATCH_SIZE = 10000;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private TransactionDefinition transactionDefinition;

    @Resource
    private MeteoDataService meteoDataService;

    @Resource
    private VMpDataMapper vmpDataMapper;

    public void getDate() {
        GetMeteoDataTimeLineReqDTO param = new GetMeteoDataTimeLineReqDTO();
        param.setDataSource(DataSourceType.ECF);
        param.setDataCode(MeteType.V_WIND_ISOBARIC);
        R<NetcdfTimeSequenceDto> r = meteoDataService.getTimeLine(param);
        List<Long> fcstTimeSequence = r.getContent().getFcstTimeSequence();
        Date addTime = new Date();
        // 改进1：使用合适的线程池大小
        int poolSize = Math.min(DataSourceType.scores.length, Runtime.getRuntime().availableProcessors() * 2);
        ExecutorService executor = Executors.newFixedThreadPool(poolSize);
        // 改进2：使用CountDownLatch同步线程
        CountDownLatch latch = new CountDownLatch(DataSourceType.scores.length);
        TransactionStatus status = transactionManager.getTransaction(transactionDefinition);
        try {
            List<Future<?>> futures = new ArrayList<>();
            for (Double altitude : DataSourceType.scores) {
                futures.add(executor.submit(() -> {
                    try {
                        List<VMpData> batchData = new ArrayList<>(BATCH_SIZE);
                        for (Long time : fcstTimeSequence) {
                            GetMeteoJsonDataReqDTO dto = buildRequestDTO(altitude, time);
                            List<VMpData> mpDataList = fetchWindData(dto, addTime);
                            if (!mpDataList.isEmpty()) {
                                batchData.addAll(mpDataList);
                                // 改进3：每个批次独立事务
                                if (batchData.size() >= BATCH_SIZE) {
                                    TransactionStatus batchStatus = transactionManager.getTransaction(transactionDefinition);
                                    try {
                                        vmpDataMapper.insertMpDataList(batchData);
                                        transactionManager.commit(batchStatus);
                                    } catch (Exception e) {
                                        transactionManager.rollback(batchStatus);
                                        throw new RuntimeException("批量插入失败", e);
                                    }
                                    batchData.clear();
                                }
                            }
                        }
                        // 插入剩余数据
                        if (!batchData.isEmpty()) {
                            TransactionStatus finalStatus = transactionManager.getTransaction(transactionDefinition);
                            try {
                                vmpDataMapper.insertMpDataList(batchData);
                                transactionManager.commit(finalStatus);
                            } catch (Exception e) {
                                transactionManager.rollback(finalStatus);
                                throw new RuntimeException("最终插入失败", e);
                            }
                        }
                    } catch (Exception e) {
                        log.error("处理海拔 {} 数据失败", altitude, e);
                        throw new CompletionException(e);
                    } finally {
                        latch.countDown();
                    }
                }));
            }

            // 改进4：等待所有线程完成
            latch.await();

            // 改进5：检查Future异常
            for (Future<?> future : futures) {
                try {
                    future.get();
                } catch (ExecutionException e) {
                    Throwable cause = e.getCause();
                    if (cause instanceof RuntimeException) throw (RuntimeException) cause;
                    throw new RuntimeException("线程执行异常", cause);
                }
            }

            transactionManager.commit(status);
        } catch (InterruptedException | RuntimeException e) {
            transactionManager.rollback(status);
            throw new RuntimeException("数据处理事务回滚", e);
        } finally {
            // 改进6：安全关闭线程池
            executor.shutdown();
            try {
                if (!executor.awaitTermination(60, TimeUnit.SECONDS)) {
                    executor.shutdownNow();
                }
            } catch (InterruptedException e) {
                executor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    // 构建请求对象的方法封装
    private GetMeteoJsonDataReqDTO buildRequestDTO(Double altitude, Long time) {
        GetMeteoJsonDataReqDTO dto = new GetMeteoJsonDataReqDTO();
        dto.setDataCode(MeteType.V_WIND_ISOBARIC);
        dto.setTime(time * 1000);
        dto.setAltitude(altitude);
        dto.setDataSource(DataSourceType.ECF);
        return dto;
    }

    public List<VMpData> fetchWindData(GetMeteoJsonDataReqDTO getMeteoImgReqDTO, Date addTime) {
        Response response = meteoDataService.getJsonData(getMeteoImgReqDTO);
        Response.Body body = response.body();
        List<VMpData> list = new ArrayList<>();
        try (InputStream inputStream = body.asInputStream();
             GZIPInputStream in = new GZIPInputStream(inputStream)) {
            String json = IoUtil.readUtf8(in);
            MeteVo meteVo = JSONUtil.toBean(json, MeteVo.class);
            MeteUtil gridMapping = new MeteUtil(meteVo.getData(), meteVo.getBboxLonMin(), meteVo.getBboxLonMax(), meteVo.getBboxLatMin(), meteVo.getBboxLatMax(), meteVo.getLonRes());
            list = gridMapping.vMapDataToMpData(getMeteoImgReqDTO.getTime(), getMeteoImgReqDTO.getAltitude(), addTime);
        } catch (IOException e) {
            log.info(getMeteoImgReqDTO.toString() + "获取v:" + e.getMessage());
            return list;
        }
        return list;
    }
}
