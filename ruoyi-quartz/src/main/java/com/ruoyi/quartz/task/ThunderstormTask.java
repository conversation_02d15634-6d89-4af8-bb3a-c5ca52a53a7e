package com.ruoyi.quartz.task;

import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.domain.type.DataSourceType;
import com.ruoyi.system.domain.type.MeteType;
import com.ruoyi.system.mapper.mdmp.MpTimeLineMapper;
import com.ruoyi.system.mapper.mdmp.MpTimeListMapper;
import com.ruoyi.system.mapper.mdmp.TemperatureMpDataMapper;
import com.ruoyi.system.mapper.mdmp.ThunderstormMpDataMapper;
import com.wsp.sdk.service.base.R;
import com.wsp.sdk.service.dto.request.GetMeteoDataTimeLineReqDTO;
import com.wsp.sdk.service.dto.request.GetMeteoImgReqDTO;
import com.wsp.sdk.service.dto.response.netcdf.NetcdfTimeSequenceDto;
import com.wsp.sdk.service.service.MeteoDataService;
import feign.Response;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import javax.imageio.ImageIO;
import java.awt.*;
import java.awt.image.BufferedImage;
import java.awt.image.DataBufferByte;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2025/3/4 11:06
 * @description：雷达数据入库
 * @modified By：
 * @version: $
 */
@Component("thunderstormTask")
public class ThunderstormTask {
    @Resource
    private MeteoDataService meteoDataService;

    @Resource
    private MpTimeListMapper mpTimeListMapper;

    @Resource
    private MpTimeLineMapper mpTimeLineMapper;

    @Resource
    private ThunderstormMpDataMapper thunderstormMpDataMapper;

    public static Long findClosestTimestamp(List<Long> realtimeTimeSequence) {
        if (realtimeTimeSequence == null || realtimeTimeSequence.isEmpty()) {
            return null; // 处理空列表或null输入
        }

        long currentTime = System.currentTimeMillis() / 1000; // 转换为秒级时间戳
        Long closest = null;
        long minDiff = Long.MAX_VALUE;

        for (Long timestamp : realtimeTimeSequence) {
            if (timestamp == null) continue; // 跳过null元素

            long diff = Math.abs(timestamp - currentTime);
            if (diff < minDiff) {
                minDiff = diff;
                closest = timestamp;
            }
        }

        return closest; // 返回找到的最接近的时间戳
    }

    @Transactional
    public void getDate() {
        GetMeteoDataTimeLineReqDTO param = new GetMeteoDataTimeLineReqDTO();
        param.setDataSource(DataSourceType.H);
        param.setDataCode(MeteType.RADAR);
        R<NetcdfTimeSequenceDto> r = meteoDataService.getTimeLine(param);
        List<Long> reatimeTimeSequence = r.getContent().getReatimeTimeSequence();
        Long closest = findClosestTimestamp(reatimeTimeSequence);
        Date addTime = new Date();
        GetMeteoImgReqDTO getMeteoImgReqDTO = new GetMeteoImgReqDTO();
        getMeteoImgReqDTO.setDataCode(MeteType.RADAR);
        getMeteoImgReqDTO.setTime(closest * 1000);
        getMeteoImgReqDTO.setDataSource(DataSourceType.H);
        List<ThunderstormMpData> mpDataList = fetchWindData(getMeteoImgReqDTO, addTime);
        if (mpDataList.size() > 0)
            thunderstormMpDataMapper.insertMpDataList(mpDataList);

//        double altitude = 0;
//        Date addTime = new Date();
//        MpTimeLine mpTimeLine = new MpTimeLine(altitude, MeteType.getWeatherConstant(MeteType.RADAR), DataSourceType.H, addTime);
//        mpTimeLineMapper.insertMpTimeLine(mpTimeLine);
//        for (Long time :
//                fcstTimeSequence) {
//            thunderstormMpDataMapper.deleteThunderstormMpDataByFcstTimeSequence(time, altitude);
//            MpTimeList mpTimeList = new MpTimeList();
//            mpTimeList.setFcstTimeSequence(time);
//            mpTimeList.setMpTimeLineId(mpTimeLine.getId());
//            mpTimeListMapper.insertMpTimeList(mpTimeList);
//            GetMeteoImgReqDTO getMeteoImgReqDTO = new GetMeteoImgReqDTO();
//            getMeteoImgReqDTO.setDataCode(MeteType.RADAR);
//            getMeteoImgReqDTO.setTime(time * 1000);
//            getMeteoImgReqDTO.setDataSource(DataSourceType.H);
//            List<ThunderstormMpData> mpDataList = fetchWindData(getMeteoImgReqDTO, mpTimeList.getId());
//            if (mpDataList.size() > 0)
//                thunderstormMpDataMapper.insertMpDataList(mpDataList);
//        }
//        for (Long time :
//                reatimeTimeSequence) {
//            thunderstormMpDataMapper.deleteThunderstormMpDataByReatimeTimeSequence(time, altitude);
//            MpTimeList mpTimeList = new MpTimeList();
//            mpTimeList.setReatimeTimeSequence(time);
//            mpTimeList.setMpTimeLineId(mpTimeLine.getId());
//            mpTimeListMapper.insertMpTimeList(mpTimeList);
//            GetMeteoImgReqDTO getMeteoImgReqDTO = new GetMeteoImgReqDTO();
//            getMeteoImgReqDTO.setDataCode(MeteType.RADAR);
//            getMeteoImgReqDTO.setTime(time * 1000);
//            getMeteoImgReqDTO.setDataSource(DataSourceType.H);
//            List<ThunderstormMpData> mpDataList = fetchWindData(getMeteoImgReqDTO, mpTimeList.getId());
//            if (mpDataList.size() > 0)
//                thunderstormMpDataMapper.insertMpDataList(mpDataList);
//        }


    }

    public List<ThunderstormMpData> fetchWindData(GetMeteoImgReqDTO getMeteoImgReqDTO, Date addTime) {
        List<ThunderstormMpData> mappedData = new ArrayList<>();
        Response response = meteoDataService.getImgInputStream(getMeteoImgReqDTO);
        try {
            InputStream inputStream = response.body().asInputStream();
            BufferedImage originalImage = ImageIO.read(inputStream);

            if (originalImage == null) {

            }

            // 创建一个新的 BufferedImage 对象，类型为灰度图
            BufferedImage grayImage = new BufferedImage(
                    originalImage.getWidth(),
                    originalImage.getHeight(),
                    BufferedImage.TYPE_BYTE_GRAY
            );

            // 获取 Graphics2D 对象，用于在灰度图像上绘图
            Graphics2D g2d = grayImage.createGraphics();

            // 将原始图像绘制到灰度图像上
            g2d.drawImage(originalImage, 0, 0, null);

            // 释放图形上下文
            g2d.dispose();

            // 获取图像的宽度和高度
            int width = grayImage.getWidth();
            int height = grayImage.getHeight();

            int[][] pixels = new int[width][height];


            // 获取图像中的所有像素数据 每个像素由一个字节表示，范围是0（黑色）到255（白色）
            byte[] pixelData = ((DataBufferByte) grayImage.getRaster().getDataBuffer()).getData();

            // 遍历所有像素并填充到二维数组中
            for (int y = 0; y < height; y++) {
                for (int x = 0; x < width; x++) {
                    // 计算一维数组中的索引位置
                    int index = y * width + x;
                    // 将字节转换为无符号整数（0-255）
                    pixels[x][y] = pixelData[index] & 0xff;
                }
            }

            // 目标经纬度范围
            double minLat = 43.70;
            double maxLat = 44.89;
            double minLon = 84.80;
            double maxLon = 86.44;

            // 计算目标范围在数组中的索引
            int startLatIndex = (int) Math.round((54.2 - maxLat) / 0.01); // 纬度起始索引
            int endLatIndex = (int) Math.round((54.2 - minLat) / 0.01); // 纬度结束索引
            int startLonIndex = (int) Math.round((minLon - 73) / 0.01); // 经度起始索引
            int endLonIndex = (int) Math.round((maxLon - 73) / 0.01); // 经度结束索引


            // 提取目标范围的像素值（纬度从小到大，经度从小到大）
            for (int i = endLatIndex; i >= startLatIndex; i--) { // 纬度从小到大
                for (int j = startLonIndex; j <= endLonIndex; j++) { // 经度从小到大
                    int pixelValue = pixels[i][j];
                    ThunderstormMpData mpData = new ThunderstormMpData();
                    BigDecimal currentLat = BigDecimal.valueOf(54.2)
                            .subtract(BigDecimal.valueOf(i).multiply(BigDecimal.valueOf(0.01)));
                    BigDecimal currentLon = BigDecimal.valueOf(73)
                            .add(BigDecimal.valueOf(j).multiply(BigDecimal.valueOf(0.01)));
                    mpData.setLatitude(currentLat);
                    mpData.setDataValue(pixelValue);
                    mpData.setLongitude(currentLon);
                    mpData.setReatimeTimeSequence(getMeteoImgReqDTO.getTime() / 1000);
                    mpData.setAltitude(new BigDecimal(0.0));
                    mpData.setAddTime(addTime);
                    mappedData.add(mpData);
                }

            }
        } catch (IOException e) {
            return mappedData;
            //throw new RuntimeException(e);
        }
        return mappedData;

    }
}
