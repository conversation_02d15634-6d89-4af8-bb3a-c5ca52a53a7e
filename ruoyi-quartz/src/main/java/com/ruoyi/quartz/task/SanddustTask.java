package com.ruoyi.quartz.task;

import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.system.domain.mdmp.SanddustMpData;
import com.ruoyi.system.domain.mdmp.VisibilityMpData;
import com.ruoyi.system.domain.mdmp.vo.MeteVo;
import com.ruoyi.system.domain.type.DataSourceType;
import com.ruoyi.system.domain.type.MeteType;
import com.ruoyi.system.mapper.mdmp.SanddustMpDataMapper;
import com.ruoyi.system.mapper.mdmp.VisibilityMpDataMapper;
import com.ruoyi.system.util.MeteUtil;
import com.wsp.sdk.service.base.R;
import com.wsp.sdk.service.dto.request.GetMeteoDataTimeLineReqDTO;
import com.wsp.sdk.service.dto.request.GetMeteoJsonDataReqDTO;
import com.wsp.sdk.service.dto.response.netcdf.NetcdfTimeSequenceDto;
import com.wsp.sdk.service.service.MeteoDataService;
import feign.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.zip.GZIPInputStream;

/**
 * <AUTHOR>
 * @date ：Created in 2025/5/22 10:01
 * @description：
 * @modified By：
 * @version: $
 */
@Component("sanddustTask")
public class SanddustTask {

    private static final int BATCH_SIZE = 10000;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private TransactionDefinition transactionDefinition;

    @Resource
    private MeteoDataService meteoDataService;

    @Resource
    private SanddustMpDataMapper sanddustMpDataMapper;


    @Transactional
    public void delete() {
        sanddustMpDataMapper.deleteAll();
    }

    @Transactional
    public void getDate() {
        GetMeteoDataTimeLineReqDTO param = new GetMeteoDataTimeLineReqDTO();
        param.setDataSource(DataSourceType.SD);
        param.setDataCode(MeteType.DUST);
        R<NetcdfTimeSequenceDto> r = meteoDataService.getTimeLine(param);
        List<Long> fcstTimeSequence = r.getContent().getFcstTimeSequence();
        Date addTime = new Date();
        ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        TransactionStatus status = transactionManager.getTransaction(transactionDefinition);
        try {
            for (Double altitude : DataSourceType.score) {
                executor.execute(() -> {
                    List<SanddustMpData> batchData = new ArrayList<>(BATCH_SIZE);
                    for (Long time : fcstTimeSequence) {
                        GetMeteoJsonDataReqDTO dto = buildRequestDTO(altitude, time);
                        List<SanddustMpData> mpDataList = fetchWindData(dto, addTime);
                        if (!mpDataList.isEmpty()) {
                            batchData.addAll(mpDataList);
                            // 批量插入
                            if (batchData.size() >= BATCH_SIZE) {
                                sanddustMpDataMapper.insertsanddustMpDataList(batchData);
                                batchData.clear();
                            }
                        }
                    }
                    // 插入剩余数据
                    if (!batchData.isEmpty()) {
                        sanddustMpDataMapper.insertsanddustMpDataList(batchData);
                    }
                });
            }
            transactionManager.commit(status);
            executor.shutdown();
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw new RuntimeException("处理数据失败", e);
        }

    }


    // 构建请求对象的方法封装
    private GetMeteoJsonDataReqDTO buildRequestDTO(Double altitude, Long time) {
        GetMeteoJsonDataReqDTO dto = new GetMeteoJsonDataReqDTO();
        dto.setDataCode(MeteType.DUST);
        dto.setTime(time * 1000);
        dto.setAltitude(altitude);
        dto.setDataSource(DataSourceType.SD);
        return dto;
    }

    public List<SanddustMpData> fetchWindData(GetMeteoJsonDataReqDTO getMeteoImgReqDTO, Date addTime) {
        Response response = meteoDataService.getJsonData(getMeteoImgReqDTO);
        Response.Body body = response.body();
        List<SanddustMpData> list = new ArrayList<>();
        try (InputStream inputStream = body.asInputStream();
             GZIPInputStream in = new GZIPInputStream(inputStream)) {
            String json = IoUtil.readUtf8(in);
            MeteVo meteVo = JSONUtil.toBean(json, MeteVo.class);
            MeteUtil gridMapping = new MeteUtil(meteVo.getData(), meteVo.getBboxLonMin(), meteVo.getBboxLonMax(), meteVo.getBboxLatMin(), meteVo.getBboxLatMax(), meteVo.getLonRes());
            list = gridMapping.sanddustMpData(getMeteoImgReqDTO.getTime(), getMeteoImgReqDTO.getAltitude(), addTime);
        } catch (IOException e) {
            return list;
        }
        return list;
    }

}
