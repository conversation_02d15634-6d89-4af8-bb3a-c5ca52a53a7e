package com.ruoyi.quartz.task;

import cn.hutool.core.io.IoUtil;
import cn.hutool.json.JSONUtil;
import com.ruoyi.system.domain.mdmp.MpData;
import com.ruoyi.system.domain.mdmp.MpTimeLine;
import com.ruoyi.system.domain.mdmp.MpTimeList;
import com.ruoyi.system.domain.mdmp.TemperatureMpData;
import com.ruoyi.system.domain.mdmp.vo.MeteVo;
import com.ruoyi.system.domain.type.DataSourceType;
import com.ruoyi.system.domain.type.MeteType;
import com.ruoyi.system.mapper.mdmp.MpTimeLineMapper;
import com.ruoyi.system.mapper.mdmp.MpTimeListMapper;
import com.ruoyi.system.mapper.mdmp.RainfallMpDataMapper;
import com.ruoyi.system.mapper.mdmp.TemperatureMpDataMapper;
import com.ruoyi.system.util.MeteUtil;
import com.wsp.sdk.service.base.R;
import com.wsp.sdk.service.dto.request.GetMeteoDataTimeLineReqDTO;
import com.wsp.sdk.service.dto.request.GetMeteoJsonDataReqDTO;
import com.wsp.sdk.service.dto.response.netcdf.NetcdfTimeSequenceDto;
import com.wsp.sdk.service.service.MeteoDataService;
import feign.Response;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.PlatformTransactionManager;
import org.springframework.transaction.TransactionDefinition;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.zip.GZIPInputStream;

/**
 * <AUTHOR>
 * @date ：Created in 2025/1/8 10:34
 * @description：温度
 * @modified By：
 * @version: $
 */
@Component("temperatureTask")
public class TemperatureTask {

    // 批量插入的大小，根据实际情况调整
    private static final int BATCH_SIZE = 10000;

    @Autowired
    private PlatformTransactionManager transactionManager;

    @Autowired
    private TransactionDefinition transactionDefinition;

    @Resource
    private MeteoDataService meteoDataService;

    @Resource
    private TemperatureMpDataMapper temperatureMpDataMapper;



    @Transactional
    public void getDate() {
        GetMeteoDataTimeLineReqDTO param = new GetMeteoDataTimeLineReqDTO();
        param.setDataSource(DataSourceType.ECF);
        param.setDataCode(MeteType.TEMP_ISOBARIC);
        R<NetcdfTimeSequenceDto> r = meteoDataService.getTimeLine(param);
        List<Long> fcstTimeSequence = r.getContent().getFcstTimeSequence();
        Date addTime = new Date();
        ExecutorService executor = Executors.newFixedThreadPool(Runtime.getRuntime().availableProcessors());
        TransactionStatus status = transactionManager.getTransaction(transactionDefinition);
        try {
            for (Double altitude : DataSourceType.scores) {
                executor.execute(() -> {
                    List<TemperatureMpData> batchData = new ArrayList<>(BATCH_SIZE);
                    for (Long time : fcstTimeSequence) {
                        GetMeteoJsonDataReqDTO dto = buildRequestDTO(altitude, time);
                        List<TemperatureMpData> mpDataList = fetchWindData(dto, addTime);
                        if (!mpDataList.isEmpty()) {
                            batchData.addAll(mpDataList);
                            // 批量插入
                            if (batchData.size() >= BATCH_SIZE) {
                                temperatureMpDataMapper.insertMpDataList(batchData);
                                batchData.clear();
                            }
                        }
                    }
                    // 插入剩余数据
                    if (!batchData.isEmpty()) {
                        temperatureMpDataMapper.insertMpDataList(batchData);
                    }
                });
            }
            transactionManager.commit(status);
            executor.shutdown();
        } catch (Exception e) {
            transactionManager.rollback(status);
            throw new RuntimeException("处理数据失败", e);
        }

    }

    // 构建请求对象的方法封装
    private GetMeteoJsonDataReqDTO buildRequestDTO(Double altitude, Long time) {
        GetMeteoJsonDataReqDTO dto = new GetMeteoJsonDataReqDTO();
        dto.setDataCode(MeteType.TEMP_ISOBARIC);
        dto.setTime(time * 1000);
        dto.setAltitude(altitude);
        dto.setDataSource(DataSourceType.ECF);
        return dto;
    }

    public List<TemperatureMpData> fetchWindData(GetMeteoJsonDataReqDTO getMeteoImgReqDTO, Date addTime) {
        Response response = meteoDataService.getJsonData(getMeteoImgReqDTO);
        Response.Body body = response.body();
        List<TemperatureMpData> list = new ArrayList<>();
        try (InputStream inputStream = body.asInputStream();
             GZIPInputStream in = new GZIPInputStream(inputStream)) {
            String json = IoUtil.readUtf8(in);
            MeteVo meteVo = JSONUtil.toBean(json, MeteVo.class);
            MeteUtil gridMapping = new MeteUtil(meteVo.getData(), meteVo.getBboxLonMin(), meteVo.getBboxLonMax(), meteVo.getBboxLatMin(), meteVo.getBboxLatMax(), meteVo.getLonRes());
            list = gridMapping.temperatureMpData(getMeteoImgReqDTO.getTime(), getMeteoImgReqDTO.getAltitude(), addTime);
        } catch (IOException e) {
            return list;
        }
        return list;
    }
}
