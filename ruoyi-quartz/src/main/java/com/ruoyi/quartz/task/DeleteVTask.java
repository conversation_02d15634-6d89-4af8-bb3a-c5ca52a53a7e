package com.ruoyi.quartz.task;

import com.ruoyi.system.mapper.mdmp.MpDataMapper;
import com.ruoyi.system.mapper.mdmp.VMpDataMapper;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2025/1/6 11:01
 * @description：
 * @modified By：
 * @version: $
 */
@Component("deleteVTask")
public class DeleteVTask {

    @Resource
    private VMpDataMapper vMpDataMapper;


    public void delete() {
        int batchSize = 10000;
        int totalDeleted = 0;
        int batchCount = 0;
        long startTime = System.currentTimeMillis();
        while (true) {
            // 使用Spring的JdbcTemplate执行删除
            int deletedInBatch =   vMpDataMapper.deleteOldVMpDataList();
            if (deletedInBatch == 0) {
                break; // 没有可删除的数据，结束循环
            }
            totalDeleted += deletedInBatch;
            batchCount++;
            // 动态调整等待时间（根据批次大小）
            try {
                Thread.sleep(Math.max(300, 1000 - (deletedInBatch * 1000 / batchSize)));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
}
