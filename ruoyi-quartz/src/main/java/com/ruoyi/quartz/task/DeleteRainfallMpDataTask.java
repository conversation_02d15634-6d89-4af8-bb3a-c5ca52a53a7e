package com.ruoyi.quartz.task;

import com.ruoyi.system.mapper.mdmp.RainfallMpDataMapper;
import com.ruoyi.system.mapper.mdmp.VisibilityMpDataMapper;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2025/1/7 14:26
 * @description：
 * @modified By：
 * @version: $
 */
@Component("deleteRainfallMpDataTask")
public class DeleteRainfallMpDataTask {

    @Resource
    private RainfallMpDataMapper rainfallMpDataMapper;


    @Transactional
    public void delete() {
//        int batchSize = 10000;
//        List<Long> longList = rainfallMpDataMapper.selectOldVMpDataList();
//        for (int i = 0; i < longList.size(); i += batchSize) {
//            if (longList.size() > 0) {
//                int end = Math.min(i + batchSize, longList.size());
//                Long[] batchIds = longList.subList(i, end).stream().toArray(Long[]::new);
//                rainfallMpDataMapper.deleteVMpDataByIds(batchIds);
//            }
//        }


        int batchSize = 10000;
        int totalDeleted = 0;
        int batchCount = 0;
        long startTime = System.currentTimeMillis();
        while (true) {
            // 使用Spring的JdbcTemplate执行删除
            int deletedInBatch =  rainfallMpDataMapper.deleteOldMpDataList();
            if (deletedInBatch == 0) {
                break; // 没有可删除的数据，结束循环
            }
            totalDeleted += deletedInBatch;
            batchCount++;
            // 动态调整等待时间（根据批次大小）
            try {
                Thread.sleep(Math.max(300, 1000 - (deletedInBatch * 1000 / batchSize)));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }
}
