package com.ruoyi.quartz.task;

import com.ruoyi.system.mapper.mdmp.TemperatureMpDataMapper;
import com.ruoyi.system.mapper.mdmp.ThunderstormMpDataMapper;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date ：Created in 2025/3/5 9:23
 * @description：删除过期雷达
 * @modified By：
 * @version: $
 */
@Component("deleteThunderstormTask")
public class DeleteThunderstormTask {

    @Resource
    private ThunderstormMpDataMapper thunderstormMpDataMapper;

    @Transactional
    public void delete() {
//        int batchSize = 10000;
//        List<Long> longList = thunderstormMpDataMapper.selectOldVMpDataList();
//        for (int i = 0; i < longList.size(); i += batchSize) {
//            if (longList.size() > 0) {
//                int end = Math.min(i + batchSize, longList.size());
//                Long[] batchIds = longList.subList(i, end).stream().toArray(Long[]::new);
//                thunderstormMpDataMapper.deleteVMpDataByIds(batchIds);
//            }
//        }

        int batchSize = 10000;
        int totalDeleted = 0;
        int batchCount = 0;
        long startTime = System.currentTimeMillis();
        while (true) {
            // 使用Spring的JdbcTemplate执行删除
            int deletedInBatch = thunderstormMpDataMapper.deleteOldMpDataList();
            if (deletedInBatch == 0) {
                break; // 没有可删除的数据，结束循环
            }
            totalDeleted += deletedInBatch;
            batchCount++;
            // 动态调整等待时间（根据批次大小）
            try {
                Thread.sleep(Math.max(300, 1000 - (deletedInBatch * 1000 / batchSize)));
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }

    }
}
