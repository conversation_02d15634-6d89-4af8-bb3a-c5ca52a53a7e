package com.ruoyi.quartz.task;

import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.common.utils.bean.BeanUtils;
import com.ruoyi.quartz.domain.TimeRange;
import com.ruoyi.system.domain.mdmp.*;
import com.ruoyi.system.service.mdmp.*;
import com.wsp.sdk.service.base.R;
import com.wsp.sdk.service.dto.response.*;
import com.wsp.sdk.service.service.ReportDataService;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneOffset;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date ：Created in 2025/5/12 13:25
 * @description：报文定时任务
 * @modified By：
 * @version: $
 */
@Component("decodereportTask")
public class DecodereportTask {

    @Resource
    private IRDecodereportService decodereportService;

    @Resource
    private ReportDataService reportDataService;

    @Resource
    private IRMintemperatureresService irMintemperatureresService;

    @Resource
    private IRMaxtemperatureresService irMaxtemperatureresService;

    @Resource
    private IRCloudinforesService irCloudinforesService;

    @Resource
    private IRDecodereportService irDecodereportService;

    @Resource
    private IRRunwayrvrresService irRunwayrvrresService;

    @Resource
    private IRVisinforesService irVisinforesService;

    @Resource
    private IRWeatherresService irWeatherresService;

    @Resource
    private IRWindinforesService irWindinforesService;

    @Value("${temperature}")
    private String temperature;

    @Value("${temperatureRange}")
    private String temperatureRange;


    @Value("${windSpeed}")
    private Double windSpeed;

    @Value("${windSpeedRange}")
    private String windSpeedRange;


    @Value("${vis}")
    private Integer vis;

    @Value("${visRange}")
    private String visRange;

    @Value("${runwayLength}")
    private Long runwayLength;

    @Value("${runwayLengthRange}")
    private String runwayLengthRange;

    @Value("${cloudHeight}")
    private Integer cloudHeight;

    @Value("${cloudHeightRange}")
    private String cloudHeightRange;

    public void getFcDatas() {
        List<String> icaoList = new ArrayList<>();
        icaoList.add("ZWHZ");
        List<String> typeList = new ArrayList<>();
        typeList.add("FC");
        buildReportRequest(3, icaoList, typeList);
    }


    public void getSaDatas() {
        List<String> icaoList = new ArrayList<>();
        icaoList.add("ZWHZ");
        List<String> typeList = new ArrayList<>();
        typeList.add("SA");
        buildReportRequest(2, icaoList, typeList);
    }


    public void getSpDatas() {
        List<String> icaoList = new ArrayList<>();
        icaoList.add("ZWHZ");
        List<String> typeList = new ArrayList<>();
        typeList.add("SP");
        buildReportRequest(2, icaoList, typeList);
    }

    @Transactional
    public void buildReportRequest(int hoursOffset, List<String> icaoCodes, List<String> reportTypes) {
        ReportReqDTO reportReqDTO = new ReportReqDTO();
        reportReqDTO.setStartTime(DateUtils.getUTCMinusTwoHours(hoursOffset));
        reportReqDTO.setEndTime(DateUtils.getCurrentUTCTime());
        reportReqDTO.setIcaoList(icaoCodes);
        reportReqDTO.setTypeList(reportTypes);

        R<List<ReportDecodeResDTO>> r = reportDataService.getDecodeReport(reportReqDTO);

        if ("200".equals(r.getCode())) {
            List<ReportDecodeResDTO> rDecodereport = r.getContent();
            if (CollectionUtils.isEmpty(rDecodereport)) return;
            // 1. 找到最大的publishTime
            Long latestTimestamp = rDecodereport.stream()
                    .map(ReportDecodeResDTO::getPublishTime)
                    .filter(Objects::nonNull)
                    .max(Long::compare)
                    .orElse(null);
            if (latestTimestamp == null) return;
            // 2. 筛选最新记录
            List<ReportDecodeResDTO> latestReports = rDecodereport.stream()
                    .filter(report -> latestTimestamp.equals(report.getPublishTime()))
                    .collect(Collectors.toList());

            // 3. 检查数据库是否存在
            RDecodereport queryParam = new RDecodereport();
            queryParam.setPublishTime(latestTimestamp);
            queryParam.setType(reportTypes.get(0));
            List<RDecodereport> existingRecords = decodereportService.selectRDecodereportList(queryParam);

            if (CollectionUtils.isEmpty(existingRecords)) {
                for (ReportDecodeResDTO report : latestReports) {
                    processSingleReport(report);
                }
            }
        }
    }

    private void processSingleReport(ReportDecodeResDTO report) {
        Long validTimeBegin = report.getValidTimeBegin();
        Long validTimeEnd = report.getValidTimeEnd();

        // 有效性校验
        if (validTimeBegin == null || validTimeEnd == null || validTimeBegin >= validTimeEnd) {
            return;
        }
        List<TimeRange> intervals = new ArrayList<>();
        // 拆分时间区间
        if (report.getType().equals("FC") || report.getType().equals("SA")) {
            intervals = splitIntoHourIntervals(validTimeBegin, validTimeEnd);
        } else {
            intervals = splitIntoHalfHourIntervals(validTimeBegin, validTimeEnd);
        }

        for (TimeRange interval : intervals) {
            RDecodereport target = new RDecodereport();
            // 复制基础属性
            copyPropertiesWithNullCheck(report, target);
            // 设置拆分后的时间
            target.setValidTimeBegin(interval.getStart());
            target.setValidTimeEnd(interval.getEnd());
            // 插入主表
            decodereportService.insertRDecodereport(target);
            // 深拷贝关联对象
            copyAssociatedDTOs(report, target);
            // 插入关联表
            insertAssociatedData(target);
        }
    }


    private List<TimeRange> splitIntoHalfHourIntervals(long startMillis, long endMillis) {
        List<TimeRange> intervals = new ArrayList<>();

        // 对齐开始时间到前一个半小时整点
        long alignedStart = alignToPreviousHalfHour(startMillis);
        // 对齐结束时间到下一个半小时整点
        long alignedEnd = alignToNextHalfHour(endMillis);

        long current = alignedStart;
        while (current < alignedEnd) {
            long nextInterval = current + 1800_000; // 半小时的毫秒数
            intervals.add(new TimeRange(current, nextInterval));
            current = nextInterval;
        }

        return intervals;
    }

    // 对齐到前一个半小时整点（向过去方向取整）
    private long alignToPreviousHalfHour(long millis) {
        LocalDateTime time = LocalDateTime.ofInstant(Instant.ofEpochMilli(millis), ZoneOffset.UTC);
        int minute = time.getMinute();
        if (minute < 30) {
            return time.withMinute(0).withSecond(0).withNano(0).toInstant(ZoneOffset.UTC).toEpochMilli();
        } else {
            return time.withMinute(30).withSecond(0).withNano(0).toInstant(ZoneOffset.UTC).toEpochMilli();
        }
    }

    // 对齐到下一个半小时整点（向未来方向取整）
    private long alignToNextHalfHour(long millis) {
        LocalDateTime time = LocalDateTime.ofInstant(Instant.ofEpochMilli(millis), ZoneOffset.UTC);
        int minute = time.getMinute();
        if (minute < 30) {
            return time.withMinute(30).withSecond(0).withNano(0).toInstant(ZoneOffset.UTC).toEpochMilli();
        } else {
            return time.plusHours(1).withMinute(0).withSecond(0).withNano(0).toInstant(ZoneOffset.UTC).toEpochMilli();
        }
    }


    /**
     * 按自然半小时拆分时间区间
     * 示例输入：12:10-13:20 → 输出：12:10-12:30, 12:30-13:00, 13:00-13:20
     */
//    private List<TimeRange> splitIntoHalfHourIntervals(long startMillis, long endMillis) {
//        List<TimeRange> intervals = new ArrayList<>();
//        long current = startMillis;
//
//        while (current < endMillis) {
//            LocalDateTime currentTime = LocalDateTime.ofInstant(
//                    Instant.ofEpochMilli(current), ZoneOffset.UTC);
//
//            // 计算下一个半小时间隔
//            LocalDateTime nextHalfHour = currentTime
//                    .withMinute(currentTime.getMinute() < 60 ? 59 : 0)
//                    .withSecond(0)
//                    .withNano(0);
//            if (currentTime.getMinute() >= 60) {
//                nextHalfHour = nextHalfHour.plusHours(1);
//            }
//
//            long nextHalfHourMillis = nextHalfHour.toInstant(ZoneOffset.UTC).toEpochMilli();
//            long intervalEnd = Math.min(nextHalfHourMillis, endMillis);
//
//            if (intervalEnd > current) {
//                intervals.add(new TimeRange(current, intervalEnd));
//            }
//
//            current = intervalEnd; // 移动到下一个区间
//        }
//
//        return intervals;
//    }
    private List<TimeRange> splitIntoHourIntervals(long startMillis, long endMillis) {
        List<TimeRange> intervals = new ArrayList<>();
        long current = startMillis;

        while (current < endMillis) {
            LocalDateTime currentTime = LocalDateTime.ofInstant(
                    Instant.ofEpochMilli(current), ZoneOffset.UTC);

            // 计算下一个小时的整点时间
            LocalDateTime nextHour = currentTime.truncatedTo(ChronoUnit.HOURS).plusHours(1);

            long nextHourMillis = nextHour.toInstant(ZoneOffset.UTC).toEpochMilli();
            long intervalEnd = Math.min(nextHourMillis, endMillis);

            if (intervalEnd > current) {
                intervals.add(new TimeRange(current, intervalEnd));
            }

            current = intervalEnd; // 移动到下一个区间
        }

        return intervals;
    }

    /**
     * 深拷贝关联的DTO对象
     */
    private void copyAssociatedDTOs(ReportDecodeResDTO source, RDecodereport target) {
        // 处理单对象
        convertReportDTOToEntity(source, target);
        decodereportService.updateRDecodereport(target);
    }

    /**
     * 插入关联表数据
     */
    private void insertAssociatedData(RDecodereport target) {
        // 插入单对象关联表
        if (target.getMinTemperatureResDTO() != null) {
            irMintemperatureresService.insertRMintemperatureres(target.getMinTemperatureResDTO());
        }
        if (target.getMaxTemperatureResDTO() != null) {
            irMaxtemperatureresService.insertRMaxtemperatureres(target.getMaxTemperatureResDTO());
        }
        if (target.getWindInfoResDto() != null) {
            irWindinforesService.insertRWindinfores(target.getWindInfoResDto());
        }
        if (target.getVisInfoResDto() != null) {
            irVisinforesService.insertRVisinfores(target.getVisInfoResDto());
        }

        // 插入列表关联表
        if (!CollectionUtils.isEmpty(target.getCloudResDtoList())) {
            irCloudinforesService.insertRCloudinforesList(target.getCloudResDtoList());
        }
        if (!CollectionUtils.isEmpty(target.getRunwayRvrResDtoList())) {
            irRunwayrvrresService.insertRRunwayrvrresList(target.getRunwayRvrResDtoList());
        }
        if (!CollectionUtils.isEmpty(target.getWeatherResDtoList())) {
            irWeatherresService.insertRWeatherresList(target.getWeatherResDtoList());
        }
    }


    @Transactional
    public void buildReportRequestSp(int hoursOffset, List<String> icaoCodes, List<String> reportTypes) {
        ReportReqDTO reportReqDTO = new ReportReqDTO();
        reportReqDTO.setStartTime((DateUtils.getUTCMinusTwoHours(hoursOffset)));
        reportReqDTO.setEndTime(DateUtils.getCurrentUTCTime());
        reportReqDTO.setIcaoList(icaoCodes);
        reportReqDTO.setTypeList(reportTypes);
        R<List<ReportDecodeResDTO>> r = reportDataService.getDecodeReport(reportReqDTO);
        if (r.getCode().equals("200")) {
            List<ReportDecodeResDTO> rDecodereport = r.getContent();
            if (CollectionUtils.isEmpty(rDecodereport)) return;
            // 1. 找到最大的 publishTime
            Optional<Long> latestTimestampOpt = rDecodereport.stream()
                    .map(ReportDecodeResDTO::getPublishTime)
                    .filter(Objects::nonNull)
                    .max(Comparator.naturalOrder());
            if (!latestTimestampOpt.isPresent()) return;
            Long latestTimestamp = latestTimestampOpt.get();
            // 2. 筛选出所有具有最新 publishTime 的记录
            List<ReportDecodeResDTO> latestReports = rDecodereport.stream()
                    .filter(report -> latestTimestamp.equals(report.getPublishTime()))
                    .collect(Collectors.toList());
            // 3. 检查数据库中是否已有该 publishTime 的记录
            RDecodereport queryParam = new RDecodereport();
            queryParam.setPublishTime(latestTimestamp);
            queryParam.setType(reportTypes.get(0)); // 假设按类型检查
            List<RDecodereport> existingRecords = decodereportService.selectRDecodereportList(queryParam);
            // 4. 如果数据库中没有，则保存所有最新记录（包含相同 publishTime 的多个数据）
            if (CollectionUtils.isEmpty(existingRecords)) {
                for (ReportDecodeResDTO report : latestReports) {
                    RDecodereport target = new RDecodereport();
                    copyPropertiesWithNullCheck(report, target);
                    decodereportService.insertRDecodereport(target);
                    convertReportDTOToEntity(report, target);
                    // 插入关联表数据
                    if (null != target.getMinTemperatureResDTO()) {
                        irMintemperatureresService.insertRMintemperatureres(target.getMinTemperatureResDTO());
                    }
                    if (null != target.getMaxTemperatureResDTO()) {
                        irMaxtemperatureresService.insertRMaxtemperatureres(target.getMaxTemperatureResDTO());
                    }
                    if (null != target.getWindInfoResDto()) {
                        irWindinforesService.insertRWindinfores(target.getWindInfoResDto());
                    }
                    if (null != target.getVisInfoResDto()) {
                        irVisinforesService.insertRVisinfores(target.getVisInfoResDto());
                    }
                    irCloudinforesService.insertRCloudinforesList(target.getCloudResDtoList());
                    irRunwayrvrresService.insertRRunwayrvrresList(target.getRunwayRvrResDtoList());
                    irWeatherresService.insertRWeatherresList(target.getWeatherResDtoList());
                }
            }
        }
    }

    private RDecodereport convertReportDTOToEntity(ReportDecodeResDTO source, RDecodereport target) {
        AtomicInteger colourTypeR = new AtomicInteger(0);
        // 初始化所有列表字段 (关键修复点)
        Optional.ofNullable(target.getRunwayRvrResDtoList()).orElseGet(() -> {
            List<RRunwayrvrres> list = new ArrayList<>();
            target.setRunwayRvrResDtoList(list);
            return list;
        });

        Optional.ofNullable(target.getCloudResDtoList()).orElseGet(() -> {
            List<RCloudinfores> list = new ArrayList<>();
            target.setCloudResDtoList(list);
            return list;
        });

        Optional.ofNullable(target.getWeatherResDtoList()).orElseGet(() -> {
            List<RWeatherres> list = new ArrayList<>();
            target.setWeatherResDtoList(list);
            return list;
        });
        if (target.getType().equals("FC") || target.getType().equals("FT")) {
            Optional.ofNullable(source.getMinTemperatureResDTO())
                    .ifPresent(min -> {
                        RMintemperatureres entity = convertTemperature(min, RMintemperatureres::new);
                        entity.setDecodeReportId(target.getId()); // 设置外键
                        if (target.getValidTimeBegin() <= entity.getTime() && target.getValidTimeEnd() >= entity.getTime()) {
                            entity.setColourType(CheckCondition.determineLevel(entity.getTemperature(), temperature, temperatureRange));
                        } else {
                            entity.setColourType(0);
                        }
                        if (entity.getColourType() > colourTypeR.get()) {
                            colourTypeR.set(entity.getColourType());
                        }
                        target.setMinTemperatureResDTO(entity);
                    });
        } else {
            target.setColourType(CheckCondition.determineLevel(target.getTemperature(), temperature, temperatureRange));

        }
        if (target.getType().equals("FC") || target.getType().equals("FT")) {
            Optional.ofNullable(source.getMaxTemperatureResDTO())
                    .ifPresent(max -> {
                        RMaxtemperatureres entity = convertTemperature(max, RMaxtemperatureres::new);
                        entity.setDecodeReportId(target.getId()); // 设置外键
                        if (target.getValidTimeBegin() <= entity.getTime() && target.getValidTimeEnd() >= entity.getTime()) {
                            entity.setColourType(CheckCondition.determineLevel(entity.getTemperature(), temperature, temperatureRange));
                        } else {
                            entity.setColourType(0);
                        }
                        if (entity.getColourType() > colourTypeR.get()) {
                            colourTypeR.set(entity.getColourType());
                        }
                        target.setMaxTemperatureResDTO(entity);
                    });
        } else {
            target.setColourType(CheckCondition.determineLevel(target.getTemperature(), temperature, temperatureRange));
        }
        Optional.ofNullable(source.getWindInfoResDto())
                .ifPresent(wind -> {
                    RWindinfores entity = convertTemperature(wind, RWindinfores::new);
                    entity.setDecodeReportId(target.getId()); // 设置外键
                    entity.setColourType(CheckCondition.windLevel(entity.getWindSpeed(), windSpeed, windSpeedRange));
                    if (entity.getColourType() > colourTypeR.get()) {
                        colourTypeR.set(entity.getColourType());
                    }
                    target.setWindInfoResDto(entity);
                });


        Optional.ofNullable(source.getVisInfoResDto())
                .ifPresent(wind -> {
                    RVisinfores entity = convertTemperature(wind, RVisinfores::new);
                    entity.setDecodeReportId(target.getId()); // 设置外键
                    entity.setColourType(CheckCondition.visLevel(Integer.parseInt(entity.getVis()), vis, visRange));
                    if (entity.getColourType() > colourTypeR.get()) {
                        colourTypeR.set(entity.getColourType());
                    }
                    target.setVisInfoResDto(entity);
                });

        List<RRunwayrvrres> runwayList = safeCopyList(source.getRunwayRvrResDtoList(),
                dto -> {
                    RRunwayrvrres entity = convertGeneric(dto, RRunwayrvrres::new);
                    entity.setDecodeReportId(target.getId());
                    entity.setColourType(CheckCondition.runwayLevel(entity.getRvrMin(), runwayLength, runwayLengthRange));
                    if (entity.getColourType() > colourTypeR.get()) {
                        colourTypeR.set(entity.getColourType());
                    }
                    return entity;
                });
        target.getRunwayRvrResDtoList().addAll(runwayList);

        List<RCloudinfores> cloudList = safeCopyList(source.getCloudResDtoList(),
                dto -> {
                    RCloudinfores entity = convertGeneric(dto, RCloudinfores::new);
                    entity.setDecodeReportId(target.getId());
                    entity.setColourType(CheckCondition.cloudLevel(entity, cloudHeight, cloudHeightRange));
                    if (entity.getColourType() > colourTypeR.get()) {
                        colourTypeR.set(entity.getColourType());
                    }
                    return entity;
                });
        target.getCloudResDtoList().addAll(cloudList);

        List<RWeatherres> weatherList = safeCopyList(source.getWeatherResDtoList(),
                dto -> {
                    RWeatherres entity = convertGeneric(dto, RWeatherres::new);
                    entity.setDecodeReportId(target.getId());
                    entity.setColourType(CheckCondition.determineResult(entity));
                    if (entity.getColourType() > colourTypeR.get()) {
                        colourTypeR.set(entity.getColourType());
                    }
                    return entity;
                });
        target.getWeatherResDtoList().addAll(weatherList);
        target.setColourType(colourTypeR.get());
        return target;
    }

    // 通用属性复制方法（带空安全判断）
    private <S, T> T copyPropertiesWithNullCheck(S source, T target) {
        if (source != null && target != null) {
            BeanUtils.copyProperties(source, target);
        }
        return target;
    }

    // 温度对象转换复用方法
    private <T, R> R convertTemperature(T source, Supplier<R> constructor) {
        if (source == null) return null;
        R target = constructor.get();
        copyPropertiesWithNullCheck(source, target);
        return target;
    }

    // 通用嵌套对象转换方法
    private <S, T> T convertGeneric(S source, Supplier<T> constructor) {
        if (source == null) return null;
        T target = constructor.get();
        BeanUtils.copyProperties(source, target);
        return target;
    }

    private <S, T> List<T> safeCopyList(List<S> sourceList, Function<S, T> converter) {
        if (CollectionUtils.isEmpty(sourceList)) return Collections.emptyList();

        return sourceList.stream()
                .map(converter)
                .collect(Collectors.toList());
    }

}
