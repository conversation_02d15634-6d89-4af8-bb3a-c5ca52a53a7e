<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.quartz.mapper.rmfms.MessageMapper">


    <sql id="selectMessageVo">
        select id            id,
               cat           cat,
               climb         climb,
               dimension     dimension,
               direction     direction,
               dynamic_state dynamicState,
               dynamic_time  dynamicTime,
               flightdate    flightdate,
               height        height,
               humidity      humidity,
               len           len,
               longitude     longitude,
               speed         speed,
               tailno        tailNo,
               temperature   temperature
        from oc_message</sql>

    <!-- 查询条件 -->
    <sql id="sqlwhereSearch">
        <where>
            <if test="tailNo !=null and tailNo!=''">
                and tailNo = #{tailNo}
            </if>
            <if test="flightDate !=null and flightDate != ''">
                and flightdate = #{flightDate}
            </if>
        </where>
    </sql>

    <select id="selectMessageList" parameterType="com.ruoyi.quartz.domain.param.FlightMessage"
            resultType="com.ruoyi.quartz.domain.rmfms.Message">
        <include refid="selectMessageVo"/>
        <include refid="sqlwhereSearch"/>
        order by dynamic_time
    </select>

</mapper>
