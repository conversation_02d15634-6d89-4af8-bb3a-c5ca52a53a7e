<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.quartz.mapper.rmfms.XNMessagePointMapper">


    <sql id="selectMessageVo">
        select id             id,
               elevation      elevation,
               generated_time generatedTime,
               latitude       latitude,
               longitude      longitude,
               orientation    orientation,
               speed          speed,
               flight_state   state,
               message_id     messageId,
               flightdate     flightDate,
               add_time       addTime
        from oc_xn_point</sql>

    <!-- 查询条件 -->
    <sql id="sqlwhereSearch">
        <where>
            <if test="messageId !=null and messageId!=''">
                and message_id = #{messageId}
            </if>
            <if test="flightDate !=null and flightDate != ''">
                and flightdate = #{flightDate}
            </if>
        </where>
    </sql>

    <select id="selectMessageList" parameterType="com.ruoyi.quartz.domain.param.FlightMessage"
            resultType="com.ruoyi.quartz.domain.rmfms.XNMessagePoint">
        <include refid="selectMessageVo"/>
        <include refid="sqlwhereSearch"/>
        order by generated_time
    </select>


</mapper>
