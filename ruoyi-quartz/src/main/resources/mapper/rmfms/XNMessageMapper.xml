<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.quartz.mapper.rmfms.XNMessageMapper">


    <sql id="selectMessageVo">
        select message_id     messageId,
               flightdate     flightDate,
               battery        battery,
               business_type  businessType,
               frequency      frequency,
               flight_id      flightId,
               data_number    number,
               add_time       addTime,
               raw_message_id rawMessageId
        from oc_xn_message</sql>


    <!-- 查询条件 -->
    <sql id="sqlwhereSearch">
        <where>
            <if test="flightId !=null and flightId!=''">
                and flight_id = #{flightId}
            </if>
            <if test="flightDate !=null and flightDate != ''">
                and flightdate = #{flightDate}
            </if>
        </where>
    </sql>


    <select id="selectMessageList" parameterType="com.ruoyi.quartz.domain.param.FlightMessage"
            resultType="com.ruoyi.quartz.domain.rmfms.XNMessage">
        <include refid="selectMessageVo"/>
        <include refid="sqlwhereSearch"/>
        order by add_time
    </select>

</mapper>
