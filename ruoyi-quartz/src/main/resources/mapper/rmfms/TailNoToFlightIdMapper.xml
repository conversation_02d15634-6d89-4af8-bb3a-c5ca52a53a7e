<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.quartz.mapper.rmfms.TailNoToFlightIdMapper">


    <sql id="selectMessageVo">
        select id        id,
               flight_id flightId,
               tailno    tailNo
        from oc_tailno_to_flightid</sql>

    <!-- 查询条件 -->
    <sql id="sqlwhereSearch">
        <where>
            <if test="flightId !=null and flightId!=''">
                and flight_id = #{flightId}
            </if>
            <if test="tailNo !=null and tailNo != ''">
                and tailno = #{tailNo}
            </if>
        </where>
    </sql>

    <select id="selectList" parameterType="com.ruoyi.quartz.domain.param.FlightMessage"
            resultType="com.ruoyi.quartz.domain.rmfms.TailNoToFlightId">
        <include refid="selectMessageVo"/>
        <include refid="sqlwhereSearch"/>
    </select>


</mapper>
