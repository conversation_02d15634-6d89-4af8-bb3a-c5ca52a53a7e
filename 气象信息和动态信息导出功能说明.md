# 气象信息和动态信息导出功能实现说明

## 功能概述

根据您的需求，我已经实现了以下功能：

1. **气象信息导出**：读取气象信息模板，根据气象信息里的表头导出对应数据Word
2. **动态信息导出**：读取动态信息模板数据，导出动态信息的Word
3. **分离接口**：不同的接口导出气象信息和动态信息
4. **保持兼容**：不改变原来的代码结构

## 实现的文件

### 1. 气象信息导出相关

#### 工具类
- `ruoyi-system/src/main/java/com/ruoyi/system/util/word/WeatherInfoWordUtils.java`
  - 专门处理气象信息Word文档生成
  - 支持气象信息模板的表头识别和数据填充
  - 包含始发地和目的地气象信息的处理

#### 服务层更新
- `ruoyi-system/src/main/java/com/ruoyi/system/service/oc/IFlightWeatherInfoService.java`
  - 添加了缺失的接口方法定义
  - 新增了气象信息和动态信息的查询方法

- `ruoyi-system/src/main/java/com/ruoyi/system/service/oc/impl/FlightWeatherInfoServiceImpl.java`
  - 实现了`exportWeatherInfoWord`方法
  - 添加了`convertToWeatherWordVo`方法，专门转换气象信息数据
  - 实现了`generateWordDocument`方法

#### 控制器
- `ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/oc/FlightWeatherInfoBizController.java`
  - 已存在的控制器，提供气象信息导出接口

### 2. 动态信息导出相关

#### 工具类
- `ruoyi-system/src/main/java/com/ruoyi/system/util/word/DynamicInfoWordUtils.java`
  - 专门处理动态信息Word文档生成
  - 支持动态信息模板的表头识别和数据填充
  - 包含批次、始发地、目的地、时间等信息的处理

#### 服务层
- `ruoyi-system/src/main/java/com/ruoyi/system/service/oc/IFlightDynamicInfoService.java`
  - 新增了动态信息导出相关的接口方法
  - 包括Word导出、PDF导出、批量导出等

- `ruoyi-system/src/main/java/com/ruoyi/system/service/oc/impl/FlightDynamicInfoServiceImpl.java`
  - 实现了动态信息的查询和导出功能
  - 包含统计信息的计算逻辑
  - 实现了`convertToDynamicWordVo`方法，专门转换动态信息数据

#### 控制器
- `ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/oc/FlightDynamicInfoBizController.java`
  - 新创建的控制器，专门处理动态信息相关请求
  - 提供动态信息的查询、Word导出、PDF导出等接口

### 3. 测试类
- `ruoyi-admin/src/main/java/com/ruoyi/web/controller/system/oc/WeatherAndDynamicExportTest.java`
  - 用于测试气象信息和动态信息导出功能
  - 包含测试数据的创建和文档生成验证

## API接口说明

### 气象信息相关接口

#### 1. 查询气象信息
```
GET /system/flightWeatherInfoBiz/selectFlightWeatherInfoById
参数：
- companyCode: 公司代码（Header）
- flightTaskBookId: 任务书ID
```

#### 2. 导出气象信息Word
```
POST /system/flightWeatherInfoBiz/export
参数：
- companyCode: 公司代码（Header）
- flightTaskBookId: 任务书ID
```

### 动态信息相关接口

#### 1. 查询动态信息
```
GET /system/flightDynamicInfoBiz/selectFlightDynamicInfoById
参数：
- companyCode: 公司代码（Header）
- flightTaskBookId: 任务书ID
```

#### 2. 导出动态信息Word
```
GET /system/flightDynamicInfoBiz/exportWord/{flightTaskBookId}
参数：
- companyCode: 公司代码（Header）
- flightTaskBookId: 任务书ID（路径参数）
```

#### 3. 导出动态信息PDF
```
GET /system/flightDynamicInfoBiz/exportPdf/{flightTaskBookId}
参数：
- companyCode: 公司代码（Header）
- flightTaskBookId: 任务书ID（路径参数）
```

#### 4. 批量导出动态信息PDF
```
POST /system/flightDynamicInfoBiz/exportPdfBatch
参数：
- companyCode: 公司代码（Header）
- flightTaskBookIds: 任务书ID列表（请求体）
```

## 配置说明

在`ruoyi-admin/src/main/resources/application.yml`中已配置模板路径：

```yaml
template:
  weatherAndDynamicTemplatePath: word/气象信息和动态信息模板.docx
  dynamicTemplatePath: word/动态信息模板.docx
  weatherTemplatePath: word/气象信息模板.docx
```

## 模板文件

需要确保以下模板文件存在：
- `ruoyi-admin/src/main/resources/word/气象信息模板.docx`
- `ruoyi-admin/src/main/resources/word/动态信息模板.docx`
- `ruoyi-admin/src/main/resources/word/气象信息和动态信息模板.docx`（原有的综合模板）

## 数据结构说明

### 气象信息数据结构
- 包含始发地和目的地的气象信息
- 字段：批次、位置名称、天气、云高、温度、风向、风速、能见度、QNH等

### 动态信息数据结构
- 包含飞行动态信息
- 字段：批次、始发地、目的地、开车时刻、起飞时刻、着陆时刻、关车时刻、地面时间、空中时间、时间小计、架次等

## 特点

1. **模块化设计**：气象信息和动态信息分别有独立的工具类和服务
2. **数据分离**：气象信息导出只包含气象数据，动态信息导出只包含动态数据
3. **接口分离**：提供不同的API接口分别处理气象信息和动态信息
4. **向后兼容**：保持原有的综合导出功能不变
5. **灵活配置**：通过配置文件管理不同的模板路径

## 使用方法

1. 确保模板文件存在于指定路径
2. 调用相应的API接口
3. 系统会根据模板自动识别表头并填充数据
4. 返回生成的Word文档

## 注意事项

1. 模板文件中需要包含相应的占位符（如`${aircraftType}`、`${registrationNumber}`等）
2. 表格表头需要包含特定的关键字以便系统识别（如"批次"、"始发地"、"天气"等）
3. 确保数据库中有相应的数据记录
4. PDF导出功能预留了接口，可以后续实现
